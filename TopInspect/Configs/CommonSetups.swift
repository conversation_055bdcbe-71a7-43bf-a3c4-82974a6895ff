//
//  CommonSetups.swift
//  SnapInspect3
//
//  Created by <PERSON> on 2024/6/17.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

import AppFeatures
import Aztec
import Dependencies
import Firebase
import FMDB
import KeychainAccess

// MARK: - Environment Setup

/// Core account management service responsible for handling user accounts and authentication
/// Initialized with the root file URL to manage account-specific file operations
private let accountService = AccountManager(rootURL: CommonHelper.getRootFileURL())

/// Secure keychain service for storing and retrieving sensitive data like tokens and credentials
/// Provides encrypted storage functionality across app launches
private let keychainService = KeychainManager()

// It will be used to run the SQL queries and access the database globally
var environment = AppEnvironment(
    accountService: accountService,
    keychainService: keychainService,
    runSQL: { perform in
        if Constants.bDBQueue, let appContext = context.sharedInstance() {
            appContext.queue.inTransaction { db, _ in perform(db) }
        } else {
            let db = FMDatabase(path: CommonHelper.getDatabaseFilePath())
            // Ensure db is opened before performing operation and closed after
            if db.open() {
                perform(db)
                db.close()
            } else {
                let event = "Failed to open database at path: \(CommonHelper.getDatabaseFilePath())"
                CommonAnalytics.trackEvent(event, meta: nil)
            }
        }
    },
    openURL: { @MainActor in await UIApplication.shared.open($0, options: $1) },
    currencyFormat: {
        let numberFormatter = NumberFormatter()
        numberFormatter.numberStyle = .currency
        numberFormatter.maximumFractionDigits = 2
        numberFormatter.minimumFractionDigits = 2
        numberFormatter.currencySymbol = "$"
        numberFormatter.usesGroupingSeparator = false
        numberFormatter.roundingMode = .halfUp
        return numberFormatter.string(from: NSNumber(value: $0)) ?? ""
    },
    decimalFormat: {
        let numberFormatter = NumberFormatter()
        numberFormatter.numberStyle = .decimal
        numberFormatter.maximumFractionDigits = 2
        numberFormatter.minimumFractionDigits = 2
        numberFormatter.usesGroupingSeparator = false
        numberFormatter.roundingMode = .halfUp
        return numberFormatter.string(from: NSNumber(value: $0)) ?? ""
    },
    prefsOps: { perform in
        let prefsStorage = accountService.currentAccount?.prefsStorage 
            ?? UserDefaultsStorage(userDefaults: .standard)
        let prefsManager = PrefsManager(storage: prefsStorage)
        perform(prefsManager)
    }
)

// MARK: - Storage for LoginAccount

extension LoginAccount {
    var prefsStorage: PrefsStorageProtocol {
        switch userRole {
        case .main:
            return UserDefaultsStorage(userDefaults: .standard)
        case .sub:
            return PlistStorage(fileName: "preferences", inDirectory: subAccountSpecificDirectory)
        }
    }

    private var subAccountSpecificDirectory: URL {
        guard userRole == .sub, let rootURL = CommonHelper.getRootFileURL() else {
            fatalError("Sub account specific directory is only available for sub accounts")
        }
        return rootURL.appendingPathComponent("user_\(iCustomerID)")
    }
}

#if hasFeature(RetroactiveAttribute)
extension AppEnvironment: @retroactive DependencyKey {
    public static var liveValue = environment
}
#else
extension AppEnvironment: DependencyKey {
    public static var liveValue = environment
}
#endif

// MARK: - Objective-C Bridge

/// Bridge class providing Objective-C access to Swift AppEnvironment functionality
/// This class follows the bridge pattern to expose dependency-injected services to Objective-C code
@objc
public class AppEnvironmentBridge: NSObject {
    // MARK: - Database Operations

    /// Executes SQL operations using the configured database environment
    /// - Parameter block: Database operation block to execute
    @objc
    public static func runSQL(_ block: @escaping (FMDatabase) -> Void) {
        environment.runSQL(block)
    }

    // MARK: - Account Management

    /// Access to the account management service
    @objc
    public static var accountService: AccountService {
        return environment.accountService
    }

    // MARK: - Keychain Management

    /// Access to the keychain management service
    @objc
    public static var keychainService: KeychainService {
        return environment.keychainService
    }

    // MARK: - Formatting Utilities

    /// Formats a number as currency using the app's configured formatter
    /// - Parameter amount: The amount to format
    /// - Returns: Formatted currency string
    @objc
    public static func formatCurrency(_ amount: Double) -> String {
        return environment.currencyFormat(amount)
    }

    /// Formats a number as decimal using the app's configured formatter
    /// - Parameter amount: The amount to format
    /// - Returns: Formatted decimal string
    @objc
    public static func formatDecimal(_ amount: Double) -> String {
        return environment.decimalFormat(amount)
    }

    // MARK: - Preferences Operations

    /// Executes preferences operations using the configured preferences manager
    /// - Parameter block: Preferences operation block to execute
    @objc
    public static func executePreferencesOperation(_ block: @escaping (PrefsManager) -> Void) {
        environment.prefsOps(block)
    }

    // MARK: - URL Operations

    /// Opens a URL using the configured URL handler
    /// - Parameters:
    ///   - url: The URL to open
    ///   - completion: Completion handler with success result
    @objc
    public static func openURL(_ url: URL, completion: @escaping (Bool) -> Void) {
        Task {
            let result = await environment.openURL(url, [:])
            await MainActor.run {
                completion(result)
            }
        }
    }
}

// MARK: - CommonSetups

class CommonSetups: NSObject {
    @objc @MainActor static func configure() {
        // Setup UI Appearance
        CommonUI.setupAppearance()

        // initialize Datadog SDK
        CommonAnalytics.initializeDatadog()

        // initialize Firebase SDK
        FirebaseApp.configure()

        // Setup IQKeyboardManager
        let kb = IQKeyboardManager.shared()
        kb.isEnabled = true
        kb.isEnableAutoToolbar = false
        kb.shouldResignOnTouchOutside = true
        // ensure that the tap to dismiss the keyboard doesn't also trigger other UI interactions in the background
        kb.resignFirstResponderGesture.cancelsTouchesInView = true

        // Setup current account if needed
        let mainAccount = LoginAccount.createMainAccount(
            iCustomerID: Int(CommonHelper.ifGetPref(PrefsKeys.iCustomerID) ?? "0") ?? 0,
            iCompanyID: Int(CommonHelper.ifGetPref(PrefsKeys.iCompanyID) ?? "0") ?? 0,
            sName: CommonHelper.ifGetPref(PrefsKeys.sName) ?? "",
            sEmail: CommonHelper.ifGetPref(PrefsKeys.sEmail) ?? "",
            sFirstName: CommonHelper.ifGetPref(PrefsKeys.sFirstName) ?? "",
            sLastName: CommonHelper.ifGetPref(PrefsKeys.sLastName) ?? ""
        )
        environment.accountService.setMainAccountWhenNoAccountExists(
            mainAccount, sToken: CommonUser.currentToken ?? ""
        )
        environment.accountService.initCurrentAccount()
    }
}
