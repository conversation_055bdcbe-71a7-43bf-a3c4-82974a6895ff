//
//  if_NewSchedule.m
//  SnapInspect3
//
//  Created by <PERSON> on 2019/11/12.
//  Copyright © 2019 SnapInspect. All rights reserved.
//

#import "if_NewSchedule.h"
#import "ScrollableTextSegmentedControl.h"
#import "NewScheduleCell.h"
#import "ScheduleHeaderView.h"
#import "FSCalendar.h"
#import "MXLCalendarManager.h"
#import "db_Common.h"
#import "EmptyView.h"
#import "NSArray+HighOrderFunction.h"
#import "if_AssetDetail.h"

#pragma mark - Catagory

@interface FSCalendar (Extensions)
- (void)deselectDate;
@end

@implementation FSCalendar (Extensions)
- (void)deselectDate {
    NSDate *selected = self.selectedDate;
    if (selected == nil) { return; }
    [self deselectDate: selected];
}
@end

typedef NS_ENUM(NSUInteger, ScheduleDateRange) {
    ScheduleDateRangeDay = 0,
    ScheduleDateRangeWeek,
    ScheduleDateRangeMonth,
    ScheduleDateRangeUpcoming,
    ScheduleDateRangeOverdue
};

#define upcomingMonths 3

@interface if_NewSchedule () <
    UITableViewDataSource, UITableViewDelegate, UITextFieldDelegate,
    FSCalendarDelegate, FSCalendarDataSource
>

@property (weak, nonatomic) IBOutlet UIButton *btn_Setting;
@property (weak, nonatomic) IBOutlet UIButton *btn_Add;
@property (weak, nonatomic) IBOutlet UITextField *oSearchBar;
@property (weak, nonatomic) IBOutlet UITableView *oTableView;

@property (weak, nonatomic) IBOutlet ScrollableTextSegmentedControl *oSegmentedControl;
@property (weak, nonatomic) IBOutlet UIView *oCalendarPlaceholderView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *oCalendarPlaceholderViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *oSearchBarHeight;

@property (weak, nonatomic) FSCalendar *calendarView;

/* [[v_Schedule, v_Schedule], [v_Schedule]] */
@property (strong, nonatomic) NSArray *arrGroupedSchedules;
@property (strong, nonatomic) NSArray *arrSchedules;

/* [{v_Schedule: MXLCalendarEvent}] */
@property (strong, nonatomic) NSArray *arrRecurSchedules;

@property (weak, nonatomic) NSDate *selectedDate;
@property (assign, nonatomic) BOOL isSearching;
@property (assign, nonatomic) ScheduleDateRange dateRange;
@property (strong, nonatomic) NSDate *earliestScheduleDate;
@property (strong, nonatomic) EmptyView *noResultsView;
@property (strong, nonatomic) NSIndexPath *processingIndexPath;
@end

@implementation if_NewSchedule

- (void)viewDidLoad {
    [super viewDidLoad];

    [self.navigationController applyBlueTheme];
    [self.navigationController setNavigationBarHidden:YES];
    
    /// ScrollableTextSegmentedControl
    UIFont *textFont = [UIFont SanFranciscoText_Bold: 12.0];
    self.oSegmentedControl.backgroundColor = UIColor.whiteColor;
    self.oSegmentedControl.textNormalFont = textFont;
    self.oSegmentedControl.textSelectedFont = textFont;
    self.oSegmentedControl.textNormalColor = UIColor.color_4A4A4A;
    self.oSegmentedControl.textSelectedColor = UIColor.color_053BFF;
    self.oSegmentedControl.bottomLineBackgroundColor = UIColor.color_053BFF;
    self.oSegmentedControl.bottomLineFixedWidth = 30.0f;
    self.oSegmentedControl.showsTwoSideSpacing = YES;
    self.oSegmentedControl.keepsLastItemAlwaysRight = YES;

    if (UIScreen.mainScreen.bounds.size.width > 375.0) {
        self.oSegmentedControl.itemSpacing = 10.0;
    } else if (UIScreen.mainScreen.bounds.size.width > 320.0) {
        self.oSegmentedControl.itemSpacing = 9.0;
    } else {
        self.oSegmentedControl.itemSpacing = 4.5;
    }
    self.oSegmentedControl.textItems = @[
        @"Today", @"Weekly", @"Monthly", @"Upcoming", @"Overdue"
    ];

    self.oSegmentedControl.destructiveColor = UIColor.color_FF3A3A;
    self.oSegmentedControl.destructiveIndex = self.oSegmentedControl.textItems.count - 1;
    
    @weakify(self);
    self.oSegmentedControl.didSelectItem = ^(NSInteger itemIndex) {
        @strongify(self);
        self.oSearchBar.text = @"";
        [self.oSearchBar resignFirstResponder];

        self.dateRange = (ScheduleDateRange)itemIndex;
        self.oTableView.contentOffset = CGPointZero;

        [self reloadSchedules];
    };
    
    /// FSCalendar
    FSCalendar *calendarView = [[FSCalendar alloc] initWithFrame: self.view.bounds];
    calendarView.delegate = self;
    calendarView.dataSource = self;
    calendarView.allowsMultipleSelection = NO;
    calendarView.rowHeight = 39.0;
    calendarView.weekdayHeight = 26.0;
    calendarView.height = calendarView.headerHeight + calendarView.weekdayHeight + calendarView.rowHeight * 6;
    calendarView.firstWeekday = 1;
    calendarView.backgroundColor = UIColor.whiteColor;
    calendarView.adjustsBoundingRectWhenChangingMonths = YES;
    
    calendarView.appearance.caseOptions = FSCalendarCaseOptionsWeekdayUsesSingleUpperCase;
    calendarView.appearance.headerTitleFont = [UIFont SanFranciscoText_Medium:15.0];
    calendarView.appearance.headerTitleColor = UIColor.color_4A4A4A;
    calendarView.appearance.weekdayFont = [UIFont SanFranciscoText_Medium: 14.0];
    calendarView.appearance.weekdayTextColor = UIColor.color_4A4A4A;
    calendarView.appearance.titleFont = [UIFont SanFranciscoText_Regular: 14.0];
    calendarView.appearance.titleDefaultColor = UIColor.color_4A4A4A;
    calendarView.appearance.selectionColor = [UIColor.color_053BFF colorWithAlphaComponent: 0.3];
    calendarView.appearance.todayColor = [[UIColor colorWithHex:0x5CD791] colorWithAlphaComponent: 0.3];
    calendarView.appearance.titleTodayColor = UIColor.color_4A4A4A;
    calendarView.appearance.todaySelectionColor = [UIColor.color_053BFF colorWithAlphaComponent: 0.3];

    calendarView.hidden = YES;
    [self.view addSubview: calendarView];
    self.calendarView = calendarView;
    
    self.oCalendarPlaceholderView.backgroundColor = UIColor.clearColor;
    self.oCalendarPlaceholderViewHeight.constant = 0.0;
    
    /// UITableView
    if (@available(iOS 15.0, *)) {
        self.oTableView.sectionHeaderTopPadding = 0.0;
    }
    self.oTableView.estimatedSectionHeaderHeight = 40.0f;
    self.oTableView.estimatedRowHeight = 103.f;
    self.oTableView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
    [self.oTableView registerNibWithClass: NewScheduleCell.class];
    [self.oTableView registerHeaderFooterViewNibWithClass: ScheduleHeaderView.class];
    
    /// Events
    self.btn_Setting.hidden = [CommonHelper bKioskMode];
    self.btn_Add.hidden = YES;
    [self.btn_Setting addTarget:self action:@selector(settingAction) forControlEvents: UIControlEventTouchUpInside];
    [self.btn_Add addTarget:self action:@selector(addScheduleAction) forControlEvents: UIControlEventTouchUpInside];
    self.btn_Sync.onTapped = (void (^)(id)) ^{
        @strongify(self);
        [self.oHomeScreenDelegate Sync];
    };
    self.oSearchBar.delegate = self;

    self.selectedDate = NSDate.date;

    [self SearchScheduleWithText: nil];
    
    [self.oSearchBar resignFirstResponder];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear: animated];
    if (self.processingIndexPath != nil) {
        NSIndexPath *indexPath = self.processingIndexPath;
        NSDictionary *map = self.arrGroupedSchedules[(NSUInteger)indexPath.section];
        NSArray *schedules = map.allValues.firstObject;
        if (indexPath.row < schedules.count) {
            v_Schedule *data = schedules[(NSUInteger) indexPath.row];
            v_Schedule *updated = [db_Schedule GetScheduledByID: data.iScheduleID];
            NSInteger dateIdx = [self.arrSchedules indexOfObject: data];
            if (updated != nil && dateIdx != NSNotFound) {
                NSMutableArray *all = [NSMutableArray arrayWithArray: self.arrSchedules];
                all[(NSUInteger) dateIdx] = updated;
                self.arrSchedules = [NSArray arrayWithArray: all];
                self.arrGroupedSchedules = [self GroupSchedulesByDay: self.arrSchedules];
                [self reloadSchedules];
            }
        }
        self.processingIndexPath = nil;
    }
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    if (self.oCalendarPlaceholderView.height > 0) {
        self.calendarView.frame = self.oCalendarPlaceholderView.frame;
    }
}

- (void)SearchScheduleWithText: (NSString *)searchText {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_HIGH, 0), ^{
        NSMutableArray *schedules = [NSMutableArray array];
        [schedules addObjectsFromArray: [self GetScheduledWithDateRangeAndKeyword: searchText]];
        
        [schedules addObjectsFromArray:
            self.arrRecurSchedules = [self GetRecurScheduledWithKeyword: searchText]
         ];
        self.arrGroupedSchedules = [self GroupSchedulesByDay: schedules];
        self.arrSchedules = schedules;
        
        dispatch_main_async_safe(^{
            [self.oTableView reloadData];
            [self showEmptyViewIfNeed];
            if (self.calendarView.selectedDate == nil) {
                [self.calendarView reloadData];
            }
        });
    });
}

- (void)showEmptyViewIfNeed {
    if (self.arrGroupedSchedules.count < 1) {
        [self.noResultsView pinnedToView: self.oTableView];
    } else {
        [self.noResultsView removeFromSuperview];
    }
}

- (NSArray *)GetRecurScheduledWithKeyword: (NSString *)keyword {
    NSArray *schedules = keyword.length > 0 ?
    [db_Schedule GetRecurScheduledByKeyword: keyword] : [db_Schedule GetRecurSchedule_View];
    
    NSMutableArray *arrRecurEvents = [NSMutableArray array];
    MXLCalendarManager *manager = [[MXLCalendarManager alloc] init];
    [schedules enumerateObjectsUsingBlock:^(v_Schedule *schedule, NSUInteger idx, BOOL *stop) {
        NSString *startTime = [schedule.sDate toStringWithFormat: @"yyyyMMdd'T'HHmmss"];
        NSString *ics;
        if (self.dateRange == ScheduleDateRangeOverdue) {
            // find the recurred event between startTime and today
            ics = [NSString stringWithFormat:
                   @"BEGIN:VEVENT\nCREATED:%@\nDTSTART:%@\nRRULE:%@\nEXRULE:%@\nUNTIL:%@\nEND:VEVENT",
                   startTime, startTime,
                   schedule.sRRule, schedule.sEXRule,
                   [NSDate.date toStringWithFormat: @"yyyyMMdd'T'HHmmss"]];
            
        } else {
            ics = [NSString stringWithFormat:
                       @"BEGIN:VEVENT\nCREATED:%@\nDTSTART:%@\nRRULE:%@\nEXRULE:%@\nEND:VEVENT",
                   startTime, startTime,
                   schedule.sRRule, schedule.sEXRule
            ];
        }
        [manager parseICSString: ics withCompletionHandler:^(MXLCalendar *calendar, NSError *error) {
            if (error != nil || calendar == nil) { return; }
            MXLCalendarEvent *event = calendar.events.firstObject;
            if ( event ) {
                [arrRecurEvents addObject: @{schedule: event}];
            }
        }];
    }];
    
    NSArray<NSDate *> *dates = [self GetStartAndEndDates];
    NSDate *startDate = dates.firstObject, *endDate = dates.lastObject;
    
    /// Recur-event Schedules
    NSMutableArray *results = [NSMutableArray array];
    [arrRecurEvents enumerateObjectsUsingBlock:^(NSDictionary *recurItem, NSUInteger idx, BOOL *stop) {
        v_Schedule *schedule = recurItem.allKeys.firstObject;
        MXLCalendarEvent *event = recurItem.allValues.firstObject;
        
        if ([event.eventStartDate timeIntervalSinceDate: endDate] > 0) return;

        NSTimeInterval interval = [startDate.startOfDay timeIntervalSinceDate: event.eventStartDate.startOfDay];
        NSDate *fireDate = nil;
        if (interval > 0) {
            fireDate = [event.eventStartDate dateByAddingTimeInterval: interval];
        } else {
            fireDate = [event.eventStartDate nextDay];
        }

        while ([fireDate isBetweenDate: startDate andDate: endDate]) {
            if ([event checkDate: fireDate]) {
                v_Schedule *nextSchedule = [schedule copy];
                nextSchedule.sDate = fireDate;
                nextSchedule.isRecurred = YES;
                [results addObject: nextSchedule];
            }
            fireDate = [fireDate nextDay];
        }
    }];

    return [NSArray arrayWithArray: results];
}

- (NSArray<NSDate *> *)GetStartAndEndDates {
    NSDate *startDate, *endDate;
    switch (self.dateRange) {
        case ScheduleDateRangeDay: {
            NSDate *today = [NSDate date];
            startDate = today.startOfDay;
            endDate = today.endOfDay;
        }
            break;
        case ScheduleDateRangeWeek:
        case ScheduleDateRangeMonth: {
            if (self.calendarView.selectedDate) {
                startDate = self.calendarView.selectedDate.startOfDay;
                endDate = self.calendarView.selectedDate.endOfDay;
            } else {
                NSDate *currentDate = self.calendarView.currentPage;
                if (self.dateRange == ScheduleDateRangeWeek) {
                    NSInteger firstWeekday = self.calendarView.firstWeekday;
                    startDate = [currentDate startOfWeekWithFirstWeekday: firstWeekday];
                    endDate = [currentDate endOfWeekWithFirstWeekday: firstWeekday];
                } else {
                    startDate = currentDate.startOfMonth;
                    endDate = currentDate.endOfMonth;
                }
            }
        }
            break;
        case ScheduleDateRangeUpcoming: {
            NSDate *today = [NSDate date];
            startDate = today.startOfDay;
            endDate = [today dateAfterMonths: upcomingMonths];
        }
            break;
        case ScheduleDateRangeOverdue: {
            NSDate *today = [NSDate date];
            startDate = self.earliestScheduleDate;
            endDate = today.startOfDay;
        }
            break;
    }
    
    if (startDate == nil || endDate == nil) return @[];
    
    return @[startDate, endDate];
}

- (NSArray *)GetScheduledWithDateRangeAndKeyword: (NSString *)keyword {
    NSArray<NSDate *> *dates = [self GetStartAndEndDates];
    NSDate *startDate = dates.firstObject, *endDate = dates.lastObject;
    NSArray *schedules = @[];
    if ([endDate laterThan: startDate]) {
        if (keyword.length > 0) {
            schedules = [db_Schedule GetScheduledByKeyword:keyword withStartDate:startDate endDate:endDate];
        } else {
            schedules = [db_Schedule GetScheduledWithStartDate:startDate endDate:endDate];
        }
    }
    return schedules;
}

- (void)setDateRange:(ScheduleDateRange)dateRange {
    _dateRange = dateRange;
    switch (dateRange) {
        case ScheduleDateRangeDay:
        case ScheduleDateRangeUpcoming:
        case ScheduleDateRangeOverdue: {
            self.calendarView.hidden = YES;
            self.oCalendarPlaceholderViewHeight.constant = 0.0;
            [self.view layoutIfNeeded];
            self.selectedDate = dateRange == ScheduleDateRangeDay ? NSDate.date : nil;
        }
            break;
        case ScheduleDateRangeWeek:
        case ScheduleDateRangeMonth: {
            self.selectedDate = nil;
            FSCalendarScope scope = FSCalendarScopeWeek;
            if (dateRange == ScheduleDateRangeWeek) {
                scope = FSCalendarScopeWeek;
            } else {
                scope = FSCalendarScopeMonth;
            }
            [self.calendarView deselectDate];
            self.calendarView.currentPage = [NSDate date];
            [self.calendarView setScope: scope animated: !self.calendarView.hidden];
            self.calendarView.hidden = NO;
            self.oCalendarPlaceholderViewHeight.constant = self.calendarView.height;
        }
    }
}

- (NSArray *)GroupSchedulesByDay: (NSArray *)arrSchedule {
    NSMutableDictionary *map = [NSMutableDictionary dictionary];
    NSArray *schedules = [arrSchedule sortedArrayUsingComparator:^NSComparisonResult(v_Schedule *obj1, v_Schedule *obj2) {
        return [obj1.sDate compare: obj2.sDate];
    }];
    
    for (v_Schedule *schedule in schedules) {
        NSDate *day = schedule.sDate.startOfDay;
        if (map[day] == nil) {
            map[day] = [NSMutableArray array];
        }
        NSMutableArray *items = (NSMutableArray *)map[day];
        [items addObject: schedule];
    }

    NSArray *keys = [map.allKeys sortedArrayUsingComparator:^NSComparisonResult(NSDate *first, NSDate *second) {
        return [first compare: second];
    }];

    NSMutableArray *results = [NSMutableArray array];
    for (NSUInteger i = 0; i < keys.count; ++i) {
        NSDate *day = keys[i];
        NSArray *items = map[keys[i]];
        NSArray *completeds = [items filter:^BOOL(v_Schedule *item) {
            return item.isInsCompleted && !item.isRecurred;
        }];
        
        ScheduleHeaderModel *model = [[ScheduleHeaderModel alloc] init];
        model.day = day;
        model.title = day.toScheduleHeaderTitle;
        model.subtitle = [NSString stringWithFormat:@"%ld/%ld", completeds.count, items.count];
        
        [results addObject: @{ model: items }];
    }
    return [NSArray arrayWithArray: results];
}

- (void)setIsSearching:(BOOL)isSearching {
    if (_isSearching != isSearching) {
        _isSearching = isSearching;
    }
}

- (NSDate *)earliestScheduleDate {
    if (_earliestScheduleDate == nil) {
        v_Schedule *first = [db_Schedule GetEarliestSchedule];
        _earliestScheduleDate = first.sDate;
    }
    return _earliestScheduleDate;
}

- (EmptyView *)noResultsView {
    if (_noResultsView == nil) {
        EmptyView *v = (EmptyView *)[UIView viewFromNibWithClass: EmptyView.class];
        v.iconView.image = [UIImage imageNamed: @"icon_empty_schedules"];
        NSArray *allTexts = @[
            @"EMPTY_TEXT_TODAY".localized,
            @"EMPTY_TEXT_WEEKLY".localized,
            @"EMPTY_TEXT_MONTHLY".localized,
            @"EMPTY_TEXT_UPCOMING".localized,
            @"EMPTY_TEXT_OVERDUE".localized
        ];
        v.lbsText.text = allTexts[self.dateRange];
        _noResultsView = v;
    }
    return _noResultsView;
}

#pragma mark - Actions

- (void)reloadSchedules {
    [self SearchScheduleWithText: self.oSearchBar.text];
}

- (void)settingAction {
    [self.oHomeScreenDelegate GoToSetting];
}

- (void)addScheduleAction {

}

#pragma mark - UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    if (self.dateRange == ScheduleDateRangeDay) {
        return MAX(self.arrGroupedSchedules.count, 1);
    }
    return self.arrGroupedSchedules.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (self.arrGroupedSchedules.count > 0) {
        NSDictionary *map = self.arrGroupedSchedules[(NSUInteger)section];
        NSArray *schedules = map.allValues.firstObject;
        return schedules.count;
    }
    return 0;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithClass: NewScheduleCell.class forIndexPath:indexPath];
    [self configureCell:cell forRowAtIndexPath:indexPath];
    return cell;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    ScheduleHeaderView *header = (ScheduleHeaderView *)[tableView dequeueReusableHeaderFooterViewWithClass: ScheduleHeaderView.class];
    BOOL isToday = self.dateRange == ScheduleDateRangeDay && !self.isSearching;
    header.showsBackView = !isToday;
    CGFloat fontSize = isToday ? 15.0 : 12.0;
    header.lbsTitle.font = [UIFont SanFranciscoText_Bold: fontSize];
    header.lbsTitle.textColor = UIColor.color_4A4A4A;
    
    header.lbsSubtitle.font = [UIFont SanFranciscoText_Medium: fontSize];
    header.lbsSubtitle.textColor = UIColor.color_9B9B9B;

    NSDictionary *map = [self.arrGroupedSchedules safe_objectAtIndex: section];
    ScheduleHeaderModel *headerModel = map.allKeys.firstObject;
    header.lbsTitle.text = headerModel.title ?: self.selectedDate.toScheduleHeaderTitle;
    header.lbsSubtitle.text = headerModel.subtitle ?: @"0/0";
    return header;
}

- (void)configureCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    if ([cell isKindOfClass: NewScheduleCell.class]) {
        NSDictionary *map = [self.arrGroupedSchedules safe_objectAtIndex: indexPath.section];
        if (map == nil) return;
        
        NSArray *schedules = map.allValues.firstObject;
        if (indexPath.row < schedules.count) {
            v_Schedule *data = schedules[(NSUInteger)indexPath.row];

            NewScheduleCell *scheduleCell = (NewScheduleCell *)cell;
            scheduleCell.backgroundColor = UIColor.clearColor;
            scheduleCell.lbsInsTitle.text = data.sInsTitle;
            scheduleCell.lbsAddress.text = data.sAddress;
            scheduleCell.lbsAddress.numberOfLines = 0;

            // Hide the second address line
            scheduleCell.lbsAddress2Top.constant = 0.f;
            scheduleCell.lbsAddress2.text = @"";
            scheduleCell.lbsRef.text = data.sRef;

            NSString *dateString;
            if ([data.sDate isEqualToDate:data.sDate.startOfDay]) {
                dateString = @"ALL_DAY".localized;
            } else {
                dateString = [data.sDate toStringWithFormat:@"HH:mm"];
            }
            scheduleCell.lbsDate.text = dateString;
            
            BOOL isOverdue = self.dateRange == ScheduleDateRangeOverdue;
            scheduleCell.lbsDate.textColor = isOverdue ? UIColor.color_FF3A3A : UIColor.color_4A4A4A;

            NSString *accessoryIconName = nil;
            if (!data.isRecurred) {
                if (data.isInsInProcess) {
                    accessoryIconName = @"ic_forward";
                } else if (data.isInsCompleted) {
                    accessoryIconName = @"ic_check";
                }
            }
            if (accessoryIconName.length > 0) {
               UIImageView *iconView = [UIImageView imageNamed:accessoryIconName];
               iconView.size = CGSizeMake(16.0, 16.0);
               cell.accessoryView = iconView;
            } else {
                cell.accessoryView = nil;
            }
        }
    }
}

#pragma mark - UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];

    NSDictionary *map = self.arrGroupedSchedules[(NSUInteger) indexPath.section];
    NSArray *schedules = map.allValues.firstObject;
    v_Schedule *oSchedule = schedules[(NSUInteger) indexPath.row];

    [Navigator showsActionSheetForSchedule:oSchedule
                                sourceView:[tableView cellForRowAtIndexPath:indexPath]
                   inspectionActionHandler:^ {
                       self.processingIndexPath = indexPath;
                   }];
}

#pragma mark - UITextFieldDelegate

- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string {
    NSString *finalText = [textField.text stringByReplacingCharactersInRange:range withString:string];
    self.isSearching = finalText.length > 0;
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(SearchScheduleWithText:) object:nil];
    [self performSelector:@selector(SearchScheduleWithText:) withObject:finalText afterDelay: 0.3];
    return YES;
}

- (BOOL)textFieldShouldClear:(UITextField *)textField {
    self.isSearching = NO;
    [self SearchScheduleWithText: nil];
    [self.oSearchBar resignFirstResponder];
    return YES;
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    [textField resignFirstResponder];
    return YES;
}

#pragma mark - FSCalendarDelegate

- (NSInteger)calendar:(FSCalendar *)calendar numberOfEventsForDate:(NSDate *)date {
    return [self.arrGroupedSchedules contains:^BOOL(NSDictionary *item) {
        ScheduleHeaderModel *headerModel = item.allKeys.firstObject;
        return [date isEqual: headerModel.day];
    }] ? 1 : 0;
}

- (void)calendar:(FSCalendar *)calendar willDisplayCell:(FSCalendarCell *)cell forDate:(NSDate *)date atMonthPosition:(FSCalendarMonthPosition)monthPosition {
    cell.eventIndicator.color = date.isPast ? UIColor.color_FF3A3A : calendar.appearance.selectionColor;
}

- (void)calendar:(FSCalendar *)calendar boundingRectWillChange:(CGRect)bounds animated:(BOOL)animated {
    CGFloat height = CGRectGetHeight(bounds);
    self.oCalendarPlaceholderViewHeight.constant = height;
    [self.view layoutIfNeeded];
}

- (void)calendar:(FSCalendar *)calendar didSelectDate:(NSDate *)date atMonthPosition:(FSCalendarMonthPosition)monthPosition {
    if (monthPosition == FSCalendarMonthPositionNext || monthPosition == FSCalendarMonthPositionPrevious) {
        [calendar setCurrentPage:date animated:YES];
    }
    [self SearchScheduleWithText: self.oSearchBar.text];
    
    FSCalendarCell *cell = [calendar cellForDate: date atMonthPosition: monthPosition];
    cell.eventIndicator.color = date.isPast ? UIColor.color_FF3A3A : calendar.appearance.selectionColor;
}

- (void)calendar:(FSCalendar *)calendar didDeselectDate:(NSDate *)date atMonthPosition:(FSCalendarMonthPosition)monthPosition {
    FSCalendarCell *cell = [calendar cellForDate: date atMonthPosition: monthPosition];
    cell.eventIndicator.color = date.isPast ? UIColor.color_FF3A3A : calendar.appearance.selectionColor;
}

- (void)calendarCurrentPageDidChange:(FSCalendar *)calendar {
    [calendar deselectDate];
    [self SearchScheduleWithText: self.oSearchBar.text];
}

- (int)currentCustomID {
    return [[CommonHelper IFGetPref: @"iCustomerID"] intValue];
}

- (void)loadExternalSchedule:(int)iSScheduleID {
    // if local have this schedule, start this schedule straightaway.
    if (iSScheduleID > 0) {
        O_Schedule *schedule = [db_Schedule GetScheduledBySScheduleID:iSScheduleID];
        v_Schedule *vSchedule = [db_Schedule GetScheduledByID:schedule.iScheduleID];
        if (vSchedule != nil && vSchedule.iScheduleID > 0) {
            [Navigator ProcessInspection:vSchedule onInspectionDidCreate:nil];
            return;
        }
    }

    // otherwise, get the schedule from server and save it to local.
    // https://my.snapinspect.com/ioapi/getschedule?iCustomerID=sToken=iScheduleID=iScheduleID
    MBProgressHUD *hud = [MBProgressHUD si_showHUDAddedTo: self.view animated: YES];
    hud.labelText = @"Downloading...";
    [hud showAnimated: YES whileExecutingBlock: ^{
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        params[@"iCustomerID"] = @(self.currentCustomID).stringValue;
        params[@"sToken"] = [CommonUser currentToken];
        params[@"iScheduleID"] = [NSString stringWithFormat: @"%d", iSScheduleID];

        NSDictionary *response = [IFConnection PostRequest:EndpointGetSchedule oParams: params];
        safe_dispatch_main_async(^{
            if (response != nil && [response[@"success"] boolValue]) {
                NSDictionary *oDic = response[@"oSchedule"];
                if ([[CommonJson GetJsonKeyValue:@"_RequestID" sJson:oDic[@"sCustom1"]] integerValue] > 0) {
                    [self ShowAlert:@"Message" sMessage:@"The inspection can not start due to request inspection."];
                } else if (self.currentCustomID == [oDic[@"iCustomerID"] intValue]) {
                    O_Schedule *schedule = [db_Schedule GetScheduledBySScheduleID:iSScheduleID];
                    if (schedule == nil || schedule.iScheduleID == 0) {
                        schedule = [[O_Schedule alloc] initWithDictionary:oDic];
                        [db_Schedule SaveSchedule:schedule];
                    }
                    v_Schedule *vSchedule = [db_Schedule GetScheduledByID:schedule.iScheduleID];
                    [Navigator ProcessInspection:vSchedule onInspectionDidCreate:nil];
                } else {
                    [self ShowAlert:@"Message" sMessage:@"The schedule can not be started as it is assigned to other team members."];
                }
            } else {
                [self ShowAlert:@"Message" sMessage:response[@"message"]];
            }
        });
    } completionBlock: ^ {
        [hud removeFromSuperview];
    }];
}
@end
