//
//  CommonRequest+Extensions.swift
//  SnapInspect3
//
//  Created by <PERSON> on 10/15/24.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

import Extensions
import Foundation
import AppFeatures

enum TaskRequestError: Error {
    case unknown
    case failed(String)
}

extension TaskRequestError: LocalizedError {
    var localizedDescription: String {
        switch self {
        case .unknown: return "Please connect to internet."
        case .failed(let message): return message
        }
    }
}

extension CommonRequest {
    static func loadTaskDetails(forTaskID taskID: Int) -> Result<O_TaskDetail, TaskRequestError> {
        guard NetworkConnection.isNetworkReachable else {
            return .failure(.failed("Please go online to update the task"))
        }
        let params: [String: Any] = [
            "iTaskID": "\(taskID)",
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken
        ]
        let result = IFConnection.postRequest(.assetTaskDetail, oParams: params, httpBodyFormat: .JSON)
        guard let result = result as? [String: Any], result["success"] as? Bool == true else {
            if let message = result?["message"] as? String {
                return .failure(.failed(message))
            }
            return .failure(.unknown)
        }

        let taskDetails = O_TaskDetail(dictionary: result)
        // update task to database
        if let task = taskDetails.task {
            db_Tasks.save(task: task)
        }
        // update photos to database
        if !taskDetails.photos.isEmpty {
            taskDetails.photos
                .map { photo in
                    guard let copy = db_Media.getPhotoOrCreate(byServerID: photo.iSPhotoID) else {
                        return photo
                    }
                    copy.iInsID = photo.iInsID
                    copy.iInsItemID = photo.iInsItemID
                    copy.sComments = photo.sComments
                    copy.bUploaded = photo.bUploaded
                    copy.iSize = photo.iSize
                    copy.bDeleted = photo.bDeleted
                    copy.sField3 = "\(taskID)"
                    return copy
                }
                .forEach { db_Media.update($0) }
        }

        // update videos to database
        if !taskDetails.videos.isEmpty {
            taskDetails.videos
                .map { video in
                    guard let copy = db_Media.getVideoByServerID(video.iSVideoID) else {
                        return video
                    }
                    copy.iInsID = video.iInsID
                    copy.iInsItemID = video.iInsItemID
                    copy.bUploaded = video.bUploaded
                    copy.sSFile = video.sSFile
                    copy.sSThumb = video.sSThumb
                    copy.iSize = video.iSize
                    copy.bDeleted = video.bDeleted
                    copy.sField3 = "\(taskID)"
                    return copy
                }
                .forEach { db_Media.update($0) }
        }

        // update sub tasks to database
        if !taskDetails.subTasks.isEmpty {
            db_Tasks.save(tasks: taskDetails.subTasks)
        }

        return .success(taskDetails)
    }

    static func saveTaskDetails(
        iTaskID: Int = 0,
        sTitle: String,
        sDescription: String,
        sTaskDue: String,
        iCategoryID: Int?,
        iFollowUpCustomerID: Int,
        iPriority: Int,
        sReminder_EmailTemplate: String,
        sStatus: String,
        sAdditionalCustom1: String,
        arrMember: [String] = [],
        iAssetID: Int,
        sTZID: String? = nil,
        iPTaskID: Int? = nil
    ) -> Result<O_Task, TaskRequestError> {
        guard NetworkConnection.isNetworkReachable else {
            return .failure(.failed("Please go online to save the Task"))
        }

        var params: [String: Any] = [
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken,
            "iTaskID": "\(iTaskID)",
            "sTitle": sTitle,
            "sDescription": sDescription,
            "iPriority": "\(iPriority)",
            "sReminder_EmailTemplate": sReminder_EmailTemplate,
            "sStatus": sStatus,
            "sAdditionalCustom1": sAdditionalCustom1,
            "arrMember": arrMember.joined(separator: ","),
            "iAssetID": "\(iAssetID)"
        ]

        // optional parameters
        if !sTaskDue.isEmpty {
            params["sTaskDue"] = sTaskDue
        }

        if iFollowUpCustomerID > 0 {
            params["iFollowUpCustomerID"] = "\(iFollowUpCustomerID)"
        } else { // default to current user
            params["iFollowUpCustomerID"] = "\(CommonUser.iCustomerID)"
        }

        if let iCategoryID, iCategoryID > 0 {
            params["iCategoryID"] = "\(iCategoryID)"
        }

        if let sTZID = sTZID {
            params["sTZID"] = sTZID
        }

        if let iPTaskID, iPTaskID > 0 {
            params["iPTaskID"] = "\(iPTaskID)"
        }

        let result = IFConnection.postRequest(.taskSaveDetails2024, oParams: params)
        guard let result, result["success"] as? Bool == true,
              let taskResult = result["oTask"] as? [AnyHashable: Any]
        else {
            if let message = result?["message"] as? String {
                return .failure(.failed(message))
            }
            return .failure(.unknown)
        }
        // update task to database
        let task = O_Task(dictionary: taskResult)
        db_Tasks.save(task: task)

        // also update photos to database
        task.sPhotoURL
            .filter(\.isRemote)
            .compactMap { db_Media.getPhotoOrCreate(byServerID: Int32($0.rowID)) }
            .forEach {
                if let photo = db_Media.getPhotoByServerID($0.iSPhotoID) {
                    photo.sField3 = "\(task.iSNotificationID)"
                    db_Media.update(photo)
                }
            }

        // also update videos to database
        if let videoURL = task.sVideoURL, videoURL.isRemote {
            db_Media.getVideoOrCreate(byServerID: Int32(videoURL.rowID))
            if let video = db_Media.getVideoByServerID(Int32(videoURL.rowID)) {
                video.sField3 = "\(task.iSNotificationID)"
                db_Media.update(video)
            }
        }

        return .success(task)
    }

    static func saveTaskPhoto(iPhotoID: Int, iTaskID: Int, bDeleted: Bool = false) -> Result<O_Task, TaskRequestError> {
        let params: [String: Any] = [
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken,
            "iPhotoID": "\(iPhotoID)",
            "iTaskID": "\(iTaskID)",
            "bDeleted": "\(bDeleted)"
        ]
        let result = IFConnection.postRequest(.taskSavePhoto, oParams: params)
        guard let result, result["success"] as? Bool == true,
              let oNotification = result["oNotification"] as? [AnyHashable: Any]
        else {
            if let message = result?["message"] as? String {
                return .failure(.failed(message))
            }
            return .failure(.unknown)
        }
        let task = O_Task(dictionary: oNotification)
        db_Tasks.save(task: task)
        return .success(task)
    }

    static func saveTaskVideo(iVideoID: Int, iTaskID: Int, bDeleted: Bool = false) -> Result<O_Task, TaskRequestError> {
        let params: [String: Any] = [
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken,
            "iVideoID": "\(iVideoID)",
            "iTaskID": "\(iTaskID)",
            "bDeleted": "\(bDeleted)"
        ]
        let result = IFConnection.postRequest(.taskSaveVideo, oParams: params)
        guard let result, result["success"] as? Bool == true,
              let oNotification = result["oNotification"] as? [AnyHashable: Any]
        else {
            if let message = result?["message"] as? String {
                return .failure(.failed(message))
            }
            return .failure(.unknown)
        }
        let task = O_Task(dictionary: oNotification)
        db_Tasks.save(task: task)

        if let videoResult = result["oVideo"] as? [AnyHashable: Any],
           let video = O_Video(dictionary: videoResult),
           let copy = db_Media.getVideoByServerID(video.iSVideoID)
        {
            copy.iInsID = video.iInsID
            copy.iInsItemID = video.iInsItemID
            copy.bUploaded = video.bUploaded
            copy.sSFile = video.sSFile
            copy.sSThumb = video.sSThumb
            copy.iSize = video.iSize
            copy.bDeleted = video.bDeleted
            copy.sField3 = "\(task.iSNotificationID)"
            db_Media.update(copy)
        }
        return .success(task)
    }

    static func completeTask(withTaskID taskID: Int) -> Result<O_Task, TaskRequestError> {
        guard NetworkConnection.isNetworkReachable else {
            return .failure(.failed("Please go online to complete the task"))
        }
        let params: [String: Any] = [
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken,
            "iTaskID": "\(taskID)"
        ]
        let result = IFConnection.postRequest(.completeTask, oParams: params)
        guard let result, result["success"] as? Bool == true,
              let oNotification = result["oNotice"] as? [AnyHashable: Any]
        else {
            if let message = result?["message"] as? String {
                return .failure(.failed(message))
            }
            return .failure(.unknown)
        }
        let task = O_Task(dictionary: oNotification)
        db_Tasks.save(task: task)
        return .success(task)
    }

    static func deleteTask(withTaskID taskID: Int) -> Result<O_Task, TaskRequestError> {
        guard NetworkConnection.isNetworkReachable else {
            return .failure(.failed("Please go online to delete the task"))
        }
        let params: [String: Any] = [
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken,
            "iTaskID": "\(taskID)"
        ]
        let result = IFConnection.postRequest(.deleteTask, oParams: params, httpBodyFormat: .JSON)
        guard let result, result["success"] as? Bool == true,
              let oNotification = result["oNotification"] as? [AnyHashable: Any]
        else {
            if let message = result?["message"] as? String {
                return .failure(.failed(message))
            }
            return .failure(.unknown)
        }
        let task = O_Task(dictionary: oNotification)
        db_Tasks.save(task: task)
        return .success(task)
    }

    static func updateStatus(withTaskID taskID: Int, sStatus: String) -> Result<O_Task, TaskRequestError> {
        let params: [String: Any] = ["iTaskID": "\(taskID)", "sStatus": sStatus]
        let result = IFConnection.postRequest(.updateStatus, oParams: params, httpBodyFormat: .JSON)
        guard let result, result["success"] as? Bool == true,
              let oNotification = result["oNotification"] as? [AnyHashable: Any]
        else {
            if let message = result?["message"] as? String {
                return .failure(.failed(message))
            }
            return .failure(.unknown)
        }
        let task = O_Task(dictionary: oNotification)
        db_Tasks.save(task: task)
        return .success(task)
    }

    @discardableResult
    static func loadAssetTasks(forAssetID assetID: Int, iDisplayLength: Int = Constants.iDisplayLengthDefault) -> [O_Task] {
        var iDisplayStart = 0
        var lsTasks = [O_Task]()
        var newTasks: [O_Task]

        repeat {
            newTasks = loadAssetTasks(forAssetID: assetID, iDisplayStart: iDisplayStart, iDisplayLength: iDisplayLength)
            lsTasks.append(contentsOf: newTasks)
            iDisplayStart += iDisplayLength
        } while newTasks.count >= iDisplayLength

        // save tasks to database
        db_Tasks.save(tasks: lsTasks)

        return lsTasks
    }

    private static func loadAssetTasks(forAssetID assetID: Int, iDisplayStart: Int, iDisplayLength: Int) -> [O_Task] {
        // The api request parameter `dtSync` format is yyyyMMddHHmm
        guard NetworkConnection.isNetworkReachable else {
            return []
        }

        let dtSync = db_Tasks.lastSync(withAssetID: assetID)?.toDate(yyyyMMddTHHmmssSSSZ)?.toString(.custom(yyyyMMddHHmm))
        let params: [String: Any] = [
            "iAssetID": "\(assetID)",
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken,
            "dtSync": dtSync ?? Constants.sSyncDateDefault,
            "iDisplayStart": "\(iDisplayStart)",
            "iDisplayLength": "\(iDisplayLength)"
        ]

        let result = IFConnection.postRequest(.assetTask, oParams: params, httpBodyFormat: .JSON)
        guard let result, result["success"] as? Bool == true,
              let lsResult = result["lsTask"] as? [[AnyHashable: Any]]
        else {
            if let message = result?["message"] as? String, message.isNotEmpty {
                DispatchQueue.main.async {
                    UIApplication.shared.topMost?.showAlert("Error", sMessage: message)
                }
            }
            return []
        }

        // Save sync date
        if let dtSyncDate = result["dtSync"] as? String, lsResult.count > 0 {
            db_Tasks.updateLastSync(withAssetID: assetID, dtLastSync: dtSyncDate)
        }

        return lsResult.map(O_Task.init(dictionary:))
    }

    static func loadTaskComments(
        forTaskID taskID: Int,
        iStart: Int = 0,
        iLength: Int = 10
    ) -> Result<[TaskComment], TaskRequestError> {
        let params: [String: Any] = [
            "iTaskID": "\(taskID)",
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken,
            "iStart": "\(iStart)",
            "iLength": "\(iLength)"
        ]
        let result = IFConnection.postRequest(.taskCommentsGet, oParams: params)
        guard let result, result["success"] as? Bool == true,
              let lsResult = result["lsComments"] as? [[AnyHashable: Any]]
        else {
            if let message = result?["message"] as? String {
                return .failure(.failed(message))
            }
            return .failure(.unknown)
        }
        return .success(lsResult.map { TaskComment(dictionary: $0) })
    }

    static func saveTaskComment(
        forTaskID taskID: Int,
        sComments: String,
        sType: String = TaskComment.kCommentTypeText,
        sSource: String = "o",
        sDateTime: String = Date().toString(.custom(yyyyMMddHHmm))
    ) -> Result<TaskComment, TaskRequestError> {
        let params: [String: Any] = [
            "iTaskID": "\(taskID)",
            "sComments": sComments,
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken,
            "sType": sType,
            "sSource": sSource,
            "sDateTime": sDateTime
        ]
        let result = IFConnection.postRequest(.taskCommentsSave, oParams: params)
        guard let result, result["success"] as? Bool == true,
              let oComment = result["oComment"] as? [AnyHashable: Any]
        else {
            if let message = result?["message"] as? String {
                return .failure(.failed(message))
            }
            return .failure(.unknown)
        }
        return .success(TaskComment(dictionary: oComment))
    }

    static func loadCustomerTasks(
        sDateTime: String,
        iDisplayStart: Int = 0,
        iDisplayLength: Int = 1000
    ) -> Result<CustomerTaskResponse, TaskRequestError> {
        guard NetworkConnection.isNetworkReachable else {
            return .failure(.failed("Please go online to load customer tasks"))
        }

        let params: [String: Any] = [
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken,
            "dtSync": sDateTime,
            "iDisplayStart": iDisplayStart,
            "iDisplayLength": iDisplayLength
        ]

        let result = IFConnection.postRequest(.customerTasks, oParams: params, httpBodyFormat: .JSON)
        guard let result, result["success"] as? Bool == true else {
            if let message = result?["message"] as? String {
                return .failure(.failed(message))
            }
            return .failure(.unknown)
        }
        return .success(CustomerTaskResponse(dictionary: result))
    }

    static func loadCustomerTasksCatch(
        sDateTime: String,
        iDisplayStart: Int = 0,
        iDisplayLength: Int = 1000
    ) -> Result<CustomerTaskResponse, TaskRequestError> {
        guard NetworkConnection.isNetworkReachable else {
            return .failure(.failed("Please go online to load customer tasks"))
        }

        let params: [String: Any] = [
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken,
            "dtSync": sDateTime,
            "iDisplayStart": iDisplayStart,
            "iDisplayLength": iDisplayLength
        ]

        let result = IFConnection.postRequest(.customerTasksCatch, oParams: params, httpBodyFormat: .JSON)
        guard let result, result["success"] as? Bool == true else {
            if let message = result?["message"] as? String {
                return .failure(.failed(message))
            }
            return .failure(.unknown)
        }
        return .success(CustomerTaskResponse(dictionary: result))
    }
}

// async-await wrapper
extension CommonRequest {
    static func completeTask(withTaskID taskID: Int) async -> Result<O_Task, TaskRequestError> {
        await Task.detached(priority: .userInitiated) {
            CommonRequest.completeTask(withTaskID: taskID)
        }.value
    }

    static func deleteTask(withTaskID taskID: Int) async -> Result<O_Task, TaskRequestError> {
        await Task.detached(priority: .userInitiated) {
            CommonRequest.deleteTask(withTaskID: taskID)
        }.value
    }

    static func updateStatus(withTaskID taskID: Int, sStatus: String) async -> Result<O_Task, TaskRequestError> {
        await Task.detached(priority: .userInitiated) {
            CommonRequest.updateStatus(withTaskID: taskID, sStatus: sStatus)
        }.value
    }

    @discardableResult
    static func loadAssetTasks(forAssetID assetID: Int, iDisplayLength: Int = Constants.iDisplayLengthDefault) async -> [O_Task] {
        await Task.detached(priority: .userInitiated) {
            CommonRequest.loadAssetTasks(forAssetID: assetID, iDisplayLength: iDisplayLength)
        }.value
    }

    static func saveTaskDetails(
        iTaskID: Int,
        sTitle: String,
        sDescription: String,
        sTaskDue: String,
        iCategoryID: Int?,
        iFollowUpCustomerID: Int,
        iPriority: Int,
        sReminder_EmailTemplate: String,
        sStatus: String,
        sAdditionalCustom1: String,
        arrMember: [String],
        iAssetID: Int,
        sTZID: String? = nil,
        iPTaskID: Int? = nil
    ) async -> Result<O_Task, TaskRequestError> {
        await Task.detached(priority: .userInitiated) {
            CommonRequest.saveTaskDetails(
                iTaskID: iTaskID,
                sTitle: sTitle,
                sDescription: sDescription,
                sTaskDue: sTaskDue,
                iCategoryID: iCategoryID,
                iFollowUpCustomerID: iFollowUpCustomerID,
                iPriority: iPriority,
                sReminder_EmailTemplate: sReminder_EmailTemplate,
                sStatus: sStatus,
                sAdditionalCustom1: sAdditionalCustom1,
                arrMember: arrMember,
                iAssetID: iAssetID,
                sTZID: sTZID,
                iPTaskID: iPTaskID
            )
        }.value
    }

    @discardableResult
    static func saveTaskDetails(_ task: O_Task) async -> Result<O_Task, TaskRequestError> {
        await CommonRequest.saveTaskDetails(
            iTaskID: task.iSNotificationID,
            sTitle: task.sTitle,
            sDescription: task.sDescription,
            sTaskDue: task.dtDateDue.isValid ? task.dtDateDue.toString(.custom(yyyyMMddHHmmss)) : "",
            iCategoryID: task.iCategoryID,
            iFollowUpCustomerID: task.iFollowUpCustomerID,
            iPriority: task.iPriority.rawValue,
            sReminder_EmailTemplate: task.sReminder,
            sStatus: task.sStatus,
            sAdditionalCustom1: task.sCustom1,
            arrMember: task.taskSettings.members,
            iAssetID: task.iPropertyID,
            iPTaskID: task.iPTaskID
        )
    }

    @discardableResult
    static func createTask(_ task: O_Task) async -> Result<O_Task, TaskRequestError> {
        await CommonRequest.saveTaskDetails(
            iTaskID: 0,
            sTitle: task.sTitle,
            sDescription: task.sDescription,
            sTaskDue: task.dtDateDue.isValid ? task.dtDateDue.toString(.custom(yyyyMMddHHmmss)) : "",
            iCategoryID: task.iCategoryID,
            iFollowUpCustomerID: task.iFollowUpCustomerID,
            iPriority: task.iPriority.rawValue,
            sReminder_EmailTemplate: task.sReminder,
            sStatus: task.sStatus,
            sAdditionalCustom1: task.sCustom1,
            arrMember: task.taskSettings.members,
            iAssetID: task.iPropertyID,
            sTZID: TimeZone.current.identifier,
            iPTaskID: task.iPTaskID
        )
    }

    static func saveTaskPhoto(
        iPhotoID: Int,
        iTaskID: Int,
        bDeleted: Bool = false
    ) async -> Result<O_Task, TaskRequestError> {
        await Task.detached(priority: .userInitiated) {
            CommonRequest.saveTaskPhoto(iPhotoID: iPhotoID, iTaskID: iTaskID, bDeleted: bDeleted)
        }.value
    }

    static func saveTaskVideo(
        iVideoID: Int,
        iTaskID: Int,
        bDeleted: Bool = false
    ) async -> Result<O_Task, TaskRequestError> {
        await Task.detached(priority: .userInitiated) {
            CommonRequest.saveTaskVideo(iVideoID: iVideoID, iTaskID: iTaskID, bDeleted: bDeleted)
        }.value
    }

    @discardableResult
    static func loadTaskDetails(forTaskID taskID: Int) async -> Result<O_TaskDetail, TaskRequestError> {
        await Task.detached(priority: .userInitiated) {
            CommonRequest.loadTaskDetails(forTaskID: taskID)
        }.value
    }

    static func saveTaskComment(forTaskID taskID: Int, sComments: String) async -> Result<TaskComment, TaskRequestError> {
        await Task.detached(priority: .userInitiated) {
            CommonRequest.saveTaskComment(forTaskID: taskID, sComments: sComments)
        }.value
    }

    static func loadTaskComments(
        forTaskID taskID: Int,
        iStart: Int = 0,
        iLength: Int = 10
    ) async -> Result<[TaskComment], TaskRequestError> {
        await Task.detached(priority: .userInitiated) {
            CommonRequest.loadTaskComments(forTaskID: taskID, iStart: iStart, iLength: iLength)
        }.value
    }

    static func loadCustomerTasks(
        sDateTime: String,
        iDisplayStart: Int = 0,
        iDisplayLength: Int = 1000
    ) async -> Result<CustomerTaskResponse, TaskRequestError> {
        await Task.detached(priority: .userInitiated) {
            CommonRequest.loadCustomerTasks(
                sDateTime: sDateTime,
                iDisplayStart: iDisplayStart,
                iDisplayLength: iDisplayLength
            )
        }.value
    }

    static func loadCustomerTasksCatch(
        sDateTime: String,
        iDisplayStart: Int = 0,
        iDisplayLength: Int = 1000
    ) async -> Result<CustomerTaskResponse, TaskRequestError> {
        await Task.detached(priority: .userInitiated) {
            CommonRequest.loadCustomerTasksCatch(
                sDateTime: sDateTime,
                iDisplayStart: iDisplayStart,
                iDisplayLength: iDisplayLength
            )
        }.value
    }

    static func syncCustomerTasks(sDateTime: String, iDisplayLength: Int = 1000) async -> Result<[O_Task], TaskRequestError> {
        var allTaskResponses: [CustomerTaskResponse] = []

        // Load regular tasks
        allTaskResponses.append(contentsOf: await fetchAllTasks(
            using: loadCustomerTasks, sDateTime: sDateTime, iDisplayLength: iDisplayLength
        ))

        // Load catch tasks
        if let savedDate = sDateTime.dateValue, savedDate.isValid {
            allTaskResponses.append(contentsOf: await fetchAllTasks(
                using: loadCustomerTasksCatch, sDateTime: sDateTime, iDisplayLength: iDisplayLength
            ))
        }

        // save last sync date
        if let lastSyncDate = allTaskResponses.first?.dtSync {
            CommonHelper.ifSavePref(PrefsKeys.kSyncMyTasksDate, sValue: lastSyncDate.toString(.custom(yyyyMMddHHmmss)))
        }

        // Save tasks to database
        let tasks = allTaskResponses.flatMap(\.lsTask)
        db_Tasks.save(tasks: tasks)

        return .success(tasks)
    }

    private static func fetchAllTasks(
        using fetchMethod: (String, Int, Int) async -> Result<CustomerTaskResponse, TaskRequestError>,
        sDateTime: String,
        iDisplayLength: Int
    ) async -> [CustomerTaskResponse] {
        var responses: [CustomerTaskResponse] = []

        for iDisplayStart in stride(from: 0, to: .max, by: iDisplayLength) {
            let result = await fetchMethod(sDateTime, iDisplayStart, iDisplayLength)

            if case .success(let response) = result {
                responses.append(response)
                if response.lsTask.count < iDisplayLength {
                    break
                }
            } else if case .failure(let error) = result {
                // break when failed
                print("Error fetching tasks: \(error)")
                break
            }
        }

        return responses
    }
}
