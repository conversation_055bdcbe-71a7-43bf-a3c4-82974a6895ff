//
//  CommonHelper_SendReport.m
//  SnapInspect3
//
//  Created by <PERSON> on 6/04/19.
//  Copyright © 2019 SnapInspect. All rights reserved.
//

#import "CommonHelper_SendReport.h"

#import "CommonUI.h"
#import "BaseViewController.h"
#import "if_ServerEmail.h"
#import <MessageUI/MFMailComposeViewController.h>
#import <MessageUI/MessageUI.h>
#import "db_Asset.h"
#import "O_Contact.h"
#import "db_Inspection.h"
@import AppFeatures;

@implementation CommonHelper_SendReport
+(void)ViewReport_Internal:(MBProgressHUD *)hud oVC:(BaseViewController *)oVC iInsID:(int)iInsID  sTitle:(NSString *)sTitle sType:(NSString *)sType{
    if (![if_AppDelegate.shared bInternet_WithMessage:@"Please connect to Internet to view report."]){
        
        return;
    }
    if (!hud){
        hud = [[MBProgressHUD alloc] si_initWithView:oVC.view];
    }
    [oVC.view addSubview:hud];
    hud.labelText = @"Connecting to Server";
    
    [hud showAnimated:YES whileExecutingBlock:^{
        NSMutableDictionary *oReturn = [[NSMutableDictionary alloc] init];
        //  int iSimpleID =
        O_Inspection *oIns = [db_Inspection GetInspectionBySInsID:iInsID];
        if ([CommonHelper bExternalInspection:oIns.sCustom2]){
            NSMutableDictionary *params = [NSMutableDictionary dictionary];
            params[@"st"] = @"P";
            params[@"iTokenID"] = [CommonJson GetJsonKeyValue:@"iTokenID" sJson:oIns.sCustom2];
            params[@"sToken"] = [CommonJson GetJsonKeyValue:@"sToken" sJson:oIns.sCustom2];
            params[@"iInsID"] = [NSString stringWithFormat:@"%d", oIns.iSInsID];
            oReturn = [[IFConnection PostRequest:EndpointGetExternalInspectionReport oParams:params] mutableCopy];
        } else {
            NSMutableDictionary *params = [NSMutableDictionary dictionary];
            params[@"iCustomerID"] = [CommonHelper IFGetPref:@"iCustomerID"];
            params[@"sToken"] = [CommonUser currentToken];
            params[@"iInsID"] = [NSString stringWithFormat:@"%d", iInsID];
            [hud setDetailsLabelText:@"Processing Request"];
            oReturn = [[IFConnection PostRequest:EndpointGetReport oParams:params] mutableCopy];
        }
        
        if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                [db_Log InsertLog:@"Report" sMessage:[NSString stringWithFormat:@"View Report %d", iInsID]];
                if_WebView *oWebView = [oVC.storyboard instantiateViewControllerWithIdentifier:@"WebView"];
                oWebView.sURL = [oReturn valueForKey:@"sURL"];
                oWebView.sTitle = sTitle;
                [oVC.navigationController pushViewController:oWebView animated:YES];
            }];
        }
        else if (oReturn != nil && (![[oReturn valueForKey:@"success"] boolValue])){
            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                  [oVC showAlertDialog:@"Error" Message:[oReturn valueForKey:@"message"] withCompletionHandler:nil];
            }];
        }
    } completionBlock:^{
        [hud removeFromSuperview];
    }];
}
+(void)SendReport_Internal:(MBProgressHUD *)hud oVC:(BaseViewController *)oVC iInsID:(int)iInsID  sTitle:(NSString *)sTitle sType:(NSString *)sType iSAssetID:(int)iSAssetID{
    if (![if_AppDelegate.shared bInternet_WithMessage:@"Please connect to Internet to view report."]){
        
        return;
    }
    NSMutableArray *arrContacts = [db_Asset GetContacts:iSAssetID];
    NSMutableArray *items = [[NSMutableArray alloc] init];
    for (O_Contact *oTemp in arrContacts){
        if (oTemp.sEmail != nil && [oTemp.sEmail length] > 0){
            [items addObject:[NSString stringWithFormat:@"%@ (%@)", oTemp.sEmail, oTemp.sTag]];
        }
    }
    
    if (items == nil || [items count] == 0){
        if (!hud){
            hud = [[MBProgressHUD alloc] si_initWithView:oVC.view];
        }
        [oVC.view addSubview:hud];
        BOOL toServer = [CommonHelper IFGetPref_Bool:PrefsKeys.bLocalEmailClient];
        if (!toServer) { // send email to server
            
            hud.labelText = @"Connecting to Server";
            [hud setDetailsLabelText:@"Processing Request"];

            [hud showAnimated:YES whileExecutingBlock:^{
                NSMutableDictionary *oVideoToken = [[NSMutableDictionary alloc] initWithObjectsAndKeys:[CommonHelper IFGetPref:@"iCustomerID"], @"iCustomerID", [CommonUser currentToken], @"sToken", [NSString stringWithFormat:@"%d", iInsID], @"iInsID",  sType,@"sType", nil];
                //NSString *cc =[[CommonHelper IFGetPref:@"iCustomerID"] copy];
                NSDictionary *oReturn = [IFConnection PostRequest:EndpointGetReportEmail oParams:oVideoToken];
                if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                        [db_Log InsertLog:@"Report" sMessage:[NSString stringWithFormat:@"Send Report %d", iInsID]];
                        
                        [CommonHelper_SendReport SendSeverEmail:[oReturn valueForKey:@"sToEmail"]
                                                    withSubject:[oReturn valueForKey:@"sSubject"]
                                                       withBody:[oReturn valueForKey:@"sMessageBody"]
                                                          insID:iInsID
                                                            oVC:oVC];
                    }];
                }
                else if (oReturn != nil && (![[oReturn valueForKey:@"success"] boolValue])){
                    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                        [oVC showAlertDialog:@"Error" Message:[oReturn valueForKey:@"message"] withCompletionHandler:nil];
                        
                    }];
                }
            } completionBlock:^{
                [hud removeFromSuperview];
            }];
        } else {
            
            
            hud.labelText = @"Connecting to Server";
            [hud setDetailsLabelText:@"Processing Request"];

            [hud showAnimated:YES whileExecutingBlock:^{
                NSMutableDictionary *oVideoToken = [[NSMutableDictionary alloc] initWithObjectsAndKeys:[CommonHelper IFGetPref:@"iCustomerID"], @"iCustomerID", [CommonUser currentToken], @"sToken", [NSString stringWithFormat:@"%d", iInsID], @"iInsID", sType, @"sType" ,  nil];
                NSDictionary *oReturn = [IFConnection PostRequest:EndpointGetReportEmail oParams:oVideoToken];
                if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                        //   [db_Log InsertLog:@"Report" sMessage:[NSString stringWithFormat:@"Send Report %d", iInsID]];
                        [CommonHelper_SendReport SendEmail: [oReturn valueForKey:@"sToEmail"]  withSubject:[oReturn valueForKey:@"sSubject"] withBody:[oReturn valueForKey:@"sMessageBody"] oVC:oVC];
                    }];
                }
                else if (oReturn != nil && (![[oReturn valueForKey:@"success"] boolValue])){
                    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                        [oVC showAlertDialog:@"Error" Message:[oReturn valueForKey:@"message"] withCompletionHandler:nil];
                    }];
                }
            } completionBlock:^{
                [hud removeFromSuperview];
            }];
        }
    }
    else{
    
    
    
        UIAlertController* alert = [UIAlertController alertControllerWithTitle:@"Send Report"
                                                                       message:@"Please select Recipients to send"
                                                                preferredStyle:UIAlertControllerStyleAlert];
        

        if (!hud){
            hud = [[MBProgressHUD alloc] si_initWithView:oVC.view];
        }
        [alert addPickerWithItems:items isMultipled: YES selection:^(NSArray * _Nullable selectedItems) {

            NSMutableArray *arrTemp = [[NSMutableArray alloc] init];
            for (NSString *cc in selectedItems){

                [arrTemp addObject:[[cc componentsSeparatedByString:@"("][0] stringByTrimmingCharactersInSet:
                        [NSCharacterSet whitespaceAndNewlineCharacterSet]]];
            }
            
            
            BOOL toServer = [CommonHelper IFGetPref_Bool:PrefsKeys.bLocalEmailClient];
            //   hud = [[MBProgressHUD alloc] si_initWithView:oView];
            [oVC.view addSubview:hud];
            hud.labelText = @"Connecting to Server";
            [hud setDetailsLabelText:@"Processing Request"];
            
            [hud showAnimated:YES whileExecutingBlock:^{
                if (!toServer) { // send email to server
                    
                    NSMutableDictionary *oVideoToken = [[NSMutableDictionary alloc] initWithObjectsAndKeys:[CommonHelper IFGetPref:@"iCustomerID"], @"iCustomerID", [CommonUser currentToken], @"sToken", [NSString stringWithFormat:@"%d", iInsID], @"iInsID",  sType,@"sType", nil];
                    
                    //NSString *cc =[[CommonHelper IFGetPref:@"iCustomerID"] copy];
                    NSDictionary *oReturn = [IFConnection PostRequest:EndpointGetReportEmail oParams:oVideoToken];
                    if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                        [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                            [db_Log InsertLog:@"Report" sMessage:[NSString stringWithFormat:@"Send Report %d", iInsID]];
                            
                            [CommonHelper_SendReport SendSeverEmail:[arrTemp componentsJoinedByString:@";"]
                                                        withSubject:[oReturn valueForKey:@"sSubject"]
                                                           withBody:[oReturn valueForKey:@"sMessageBody"]
                                                              insID:iInsID
                                                                oVC:oVC ];
                        }];
                    }
                    else if (oReturn != nil && (![[oReturn valueForKey:@"success"] boolValue])){
                        [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                            [oVC showAlertDialog:@"Error" Message:[oReturn valueForKey:@"message"] withCompletionHandler:nil];
                            
                        }];
                    }
                } else {
                    NSMutableDictionary *oVideoToken = [[NSMutableDictionary alloc] initWithObjectsAndKeys:[CommonHelper IFGetPref:@"iCustomerID"], @"iCustomerID", [CommonUser currentToken], @"sToken", [NSString stringWithFormat:@"%d", iInsID], @"iInsID", sType, @"sType" ,  nil];
                    NSDictionary *oReturn = [IFConnection PostRequest:EndpointGetReportEmail oParams:oVideoToken];
                    if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                        [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                            //   [db_Log InsertLog:@"Report" sMessage:[NSString stringWithFormat:@"Send Report %d", iInsID]];
                            [CommonHelper_SendReport SendEmail:(arrTemp.count == 0 ? [oReturn valueForKey:@"sToEmail"] : [arrTemp componentsJoinedByString:@";"] )  withSubject:[oReturn valueForKey:@"sSubject"] withBody:[oReturn valueForKey:@"sMessageBody"] oVC:oVC];
                        }];
                    }
                    else if (oReturn != nil && (![[oReturn valueForKey:@"success"] boolValue])){
                        [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                            [oVC showAlertDialog:@"Error" Message:[oReturn valueForKey:@"message"] withCompletionHandler:nil];
                        }];
                    }
                }
                
            } completionBlock:^{
                [hud removeFromSuperview];
            }];
        }];
        
        [alert addAction:[UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil]];
        [alert showWithAnimated:YES vibrate:NO completion:nil];
    }
    
    
    
    
    
    

}

+(void)SendEmail:(NSString *)to withSubject:(NSString *)subject withBody:(NSString *)body oVC:(BaseViewController *)oVC{
    @try {
        UINavigationBar.appearance.barTintColor =  [UIColor colorWithRed:78/255.0f green:105/255.0f blue:255/255.0f alpha:1];
        MFMailComposeViewController *mailController = [[MFMailComposeViewController alloc] init];
        [mailController setToRecipients:[to componentsSeparatedByString:@";"]];
        [[mailController navigationBar] setTintColor: [UIColor blackColor]];
        //   [mailController navigationBar] setColo
        //[mailController setToRecipients:[[NSArray alloc] initWithObjects:@"<EMAIL>", nil] ];
        [mailController setSubject:subject];
        [mailController setMessageBody:body isHTML:YES];
        
        mailController.mailComposeDelegate = (id)oVC;
        
        mailController.navigationBar.titleTextAttributes = [NSDictionary dictionaryWithObject:[UIColor whiteColor] forKey: NSForegroundColorAttributeName];
        // UINavigationController *myNavController = [self navigationController];
        
        if ( mailController != nil ) {
            if ([MFMailComposeViewController canSendMail]){
                //[self ]
                [oVC.navigationController presentViewController:mailController animated:YES completion:nil];
            }
        }
    }
    @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
    
    
}
+(void)SendSeverEmail:(NSString *)to withSubject:(NSString *)subject withBody:(NSString *)body insID:(int)iInsID oVC:(BaseViewController *)oVC {
    @try {
        if_ServerEmail *vc = [oVC.storyboard instantiateViewControllerWithIdentifier:@"if_ServerEmail"];
        /*   NSMutableArray *oArray= [[NSMutableArray alloc] init];
         if ([to containsString:@","]){
         oArray = [NSMutableArray arrayWithArray: [to componentsSeparatedByString:@","]];
         @try{
         [oArray removeObjectIdenticalTo:[NSNull null]];
         }@catch(NSException *ex){
         
         }
         }
         else{
         oArray = [NSMutableArray arrayWithArray: [to componentsSeparatedByString:@";"]];
         @try{
         [oArray removeObjectIdenticalTo:[NSNull null]];
         }@catch(NSException *ex){
         
         }
         }*/
        [vc setToRecipients:to];
        //[mailController setToRecipients:[[NSArray alloc] initWithObjects:@"<EMAIL>", nil] ];
        [vc setSubject:subject];
        [vc setBody:body  isHTML:YES];
        [vc setInsID:iInsID];
        [oVC.navigationController pushViewController:vc animated:YES];
    }
    @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
    
    
}
@end
