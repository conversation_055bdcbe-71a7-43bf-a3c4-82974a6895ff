//
// Created by <PERSON> on 2020/3/8.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

import UIKit
import AppFeatures

public extension UIImageView {
    @objc func setThumbPhoto(with photoID: Int) {
        guard let photo = db_Media.getPhoto(Int32(photoID)) else { return }
        guard NSString.isNullOrEmpty(photo.sThumb) || !CommonHelper.if_FileExist(photo.sThumb) else {
            sd_setImage(with: URL(fileURLWithPath: CommonHelper.if_FilePath(photo.sThumb)))
            return
        }
        
        let placeholderImage = UIImage(named: "Image_PlaceHolder")
        sd_imageIndicator = SDWebImageActivityIndicator.gray
        sd_imageIndicator?.startAnimatingIndicator()
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            let params = [
                "iCustomerID": CommonHelper.ifGetPref(PrefsKeys.iCustomerID) ?? "",
                "sToken": CommonUser.currentToken ?? "",
                "iPhotoID": "\(photo.iSPhotoID)"
            ]
            
            guard let result = IFConnection.postRequest(.getPhotoThumb, oParams: params),
                  let success = result["success"] as? Bool, success,
                  let sURL = result["sURL"] as? String,
                  let fileURL = URL(string: sURL),
                  let sPath = if_AppDelegate.getGlobalVar()["sFilePath"] as? NSString,
                  let sUniqueFileName = CommonHelper.getUniqueFileName()
            else { return }
            
            let sThumbName = "pt_\(sUniqueFileName).jpg"
            let sThumbPath = sPath.appendingPathComponent(sThumbName)
            
            DispatchQueue.main.async { [weak self] in
                self?.sd_setImage(
                    with: fileURL, placeholderImage: nil, options: .delayPlaceholder
                ) { [weak self] image, _, _, _ in
                    if let data = image?.sd_imageData() {
                        do {
                            try data.write(to: URL(fileURLWithPath: sThumbPath))
                            photo.sThumb = sThumbName
                            db_Media.update(photo)
                        } catch {}
                    } else {
                        self?.image = placeholderImage
                    }
                }
            }
        }
    }
    
    @objc func setFullPhoto(with photoID: NSNumber) {
        let id = photoID.int32Value
        guard id > 0, let photo = db_Media.getPhoto(id) else { return }
        guard NSString.isNullOrEmpty(photo.sFile) || !CommonHelper.if_FileExist(photo.sFile) else {
            sd_setImage(with: URL(fileURLWithPath: CommonHelper.if_FilePath(photo.sFile)))
            return
        }
        
        sd_imageIndicator = SDWebImageActivityIndicator.gray
        sd_imageIndicator?.startAnimatingIndicator()
        
        let placeholderImage = UIImage(named: "Image_PlaceHolder_Full")
        guard photo.iSPhotoID > 0, photo.bUploaded else {
            image = placeholderImage
            return
        }
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            let params = [
                "iCustomerID": CommonHelper.ifGetPref(PrefsKeys.iCustomerID) ?? "",
                "sToken": CommonUser.currentToken ?? "",
                "iPhotoID": "\(photo.iSPhotoID)"
            ]
            
            guard let result = IFConnection.postRequest(.getPhoto, oParams: params),
                  let success = result["success"] as? Bool, success,
                  let sURL = result["sURL"] as? String,
                  let fileURL = URL(string: sURL),
                  let sPath = if_AppDelegate.getGlobalVar()["sFilePath"] as? NSString,
                  let sUniqueFileName = CommonHelper.getUniqueFileName()
            else { return }
            
            photo.sComments = result["sComments"] as? String
            
            let sPhotoName = "p_\(sUniqueFileName).jpg"
            let sThumbName = "pt_\(sUniqueFileName).jpg"
            let sPhotoPath = sPath.appendingPathComponent(sPhotoName)
            let sThumbPath = sPath.appendingPathComponent(sThumbName)
            
            self.sd_setImage(with: fileURL) { [weak self] image, _, _, _ in
                if let image = image, let data = image.sd_imageData() {
                    do {
                        try data.write(to: URL(fileURLWithPath: sPhotoPath))
                        try image.jpegData(compressionQuality: 0.8)?.write(to: URL(fileURLWithPath: sThumbPath))
                        photo.sFile = sPhotoName
                        photo.sThumb = sThumbName
                        db_Media.update(photo)
                    } catch {}
                } else {
                    DispatchQueue.main.async {
                        self?.image = placeholderImage
                    }
                }
            }
        }
    }
}
