//
//  IFConnection.m
//  InspectionFolio
//
//  Created by <PERSON> on 15/01/14.
//  Copyright (c) 2014 Cloudya. All rights reserved.
//

#import "XMLReader.h"

NSString const *sVer = @"1";
#define API(path) [NSString stringWithFormat:@"%@%@", ifServerURL, path]
#define CONNECT_API(path) [NSString stringWithFormat:@"%@%@", Constants.kBaseConnectURL, path]

Endpoint const EndpointAppServer                            = API(@"/Sync/AppServer");
Endpoint const EndpointInsTypeUpgrade_3_3_1                 = API(@"/AppUpgrade/InsTypeUpgrade_3_3_1");
Endpoint const EndpointGetTemplateAPI                       = API(@"/IOAPI/GetTemplateAPI");
Endpoint const EndpointUploadPhotoSuccessExternalInspection = API(@"/SyncExternal/UploadPhotoSuccess_ExternalInspection");
Endpoint const EndpointUploadPhotoExternalInspection        = API(@"/SyncExternal/UploadPhoto_ExternalInspection");
Endpoint const EndpointRegisterAPI                          = API(@"/Account/RegisterAPI");
Endpoint const EndpointCX1                                  = API(@"/Home/CX1");
Endpoint const EndpointLoginAPI                             = API(@"/Account/LoginAPI");
Endpoint const EndpointGoogleSSOAuth                        = API(@"/IOAPI/GoogleSSOAuth_MobileApp");
Endpoint const EndpointMicrosoftSSOAuth                     = API(@"/IOAPI/MicrosoftSSOAuth_MobileApp");
Endpoint const EndpointGetPhoto                             = API(@"/IOAPI/GetPhoto");
Endpoint const EndpointGetPhotoThumb                        = API(@"/IOAPI/GetPhotoThumb");
Endpoint const EndpointGetInbox                             = API(@"/IOAPI/GetInbox");
Endpoint const EndpointServerApp                            = API(@"/Sync/ServerApp");
Endpoint const EndpointServerAppAction                      = API(@"/Sync/ServerApp_Action");
Endpoint const EndpointUploadPhotoSuccess                   = API(@"/Sync/UploadPhotoSuccess");
Endpoint const EndpointGetPropertyCustomInfo                = API(@"/ioapi/GetPropertyCustomInfo");
Endpoint const EndpointGetExternalInspectionReport          = API(@"/SyncExternal/GetExternalInspectionReport");
Endpoint const EndpointUploadAssetFileSuccess               = API(@"/IOAPI/UploadAssetFileSuccess");
Endpoint const EndpointGetVideoTokenExternalInspection      = API(@"/SyncExternal/GetVideoToken_ExternalInspection");
Endpoint const EndpointUploadVideoSuccessExternalInspection = API(@"/SyncExternal/UploadVideoSuccess_ExternalInspection");
Endpoint const EndpointUpdateToken                          = API(@"/IOAPI/UpdateToken");
Endpoint const EndpointGetVideoToken                        = API(@"/Sync/GetVideoToken");
Endpoint const EndpointUploadVideoSuccess                   = API(@"/Sync/UploadVideoSuccess");
Endpoint const EndpointAttachAssetPhoto                     = API(@"/IOAPI/AttachAssetPhoto");
Endpoint const EndpointUploadAssetFile                      = API(@"/IOAPI/UploadAssetFile");
Endpoint const EndpointGetAssetFile                         = API(@"/IOAPI/GetAssetFile");
Endpoint const EndpointUploadPhoto                          = API(@"/Sync/UploadPhoto");
Endpoint const EndpointSyncSmartComments                    = API(@"/Sync/smartcomments_sync");
Endpoint const EndpointGetFolderToken                       = API(@"/Sync/GetFolderToken");
Endpoint const EndpointExternalGetFolderToken               = API(@"/SyncExternal/GetFolderToken");
Endpoint const EndpointSendEmail                            = API(@"/IOAPI/SendEmail");
Endpoint const EndpointSearchInspectionAPI                  = API(@"/IOAPI/SearchInspectionAPI");
Endpoint const EndpointGetReport                            = API(@"/IOAPI/GetReport");
Endpoint const EndpointGetReportEmail                       = API(@"/IOAPI/GetReportEmail");
Endpoint const EndpointDownloadAssetPhoto                   = API(@"/IOAPI/DownloadAssetPhoto");
Endpoint const EndpointGetExternalPropertyReport            = API(@"/SyncExternal/GetExternalPropertyReport");
Endpoint const EndpointUpdateInspectionStatusAPI            = API(@"/IOAPI/UpdateInspectionStatusAPI");
Endpoint const EndpointDismissInbox                         = API(@"/IOAPI/DismissInbox");
Endpoint const EndpointAddInspectionComments                = API(@"/ioapi/addinspectioncomments");
Endpoint const EndpointGetInspectionComments                = API(@"/ioapi/getinspectioncomments");
Endpoint const EndpointMirrorInspectionAPI                  = API(@"/IOAPI/MirrorInspectionAPI");
Endpoint const EndpointEditInspectionAPI                    = API(@"/IOAPI/EditInspectionAPI");

Endpoint const EndpointGetInspection                        = API(@"/IOAPI/GetInspection");
Endpoint const EndpointGetAsset                             = API(@"/IOAPI/GetAsset");
Endpoint const EndpointGetSchedule                          = API(@"/IOAPI/GetSchedule");

Endpoint const EndpointGetTasks                             = API(@"/IOAPI/GetTasks");
Endpoint const EndpointUpdateProperty                       = API(@"/IOAPI/App_UpdateProperty_V2");
Endpoint const EndpointUpdateContact                        = API(@"/IOAPI/App_UpdateContact");
Endpoint const EndpointDeleteContact                        = API(@"/IOAPI/App_DeleteContact");
Endpoint const EndpointUpdateAssetCustomInfo                = API(@"/IOAPI/UpdateAssetCustomInfo");
Endpoint const EndpointSyncProjects                         = API(@"/IOAPI/SyncProjects");
Endpoint const EndpointGetProjectInspection                 = API(@"/IOAPI/GetProjectInspection");
Endpoint const EndpointLockProjectInspection                = API(@"/IOAPI/LockProjectInspection");
Endpoint const EndpointGetProject                           = API(@"/IOAPI/GetProject");

Endpoint const EndpointGetAssetSchedule                     = API(@"/IOAPI/GetAssetSchedule");
Endpoint const EndpointGetAssetLayoutV2                     = API(@"/IOAPI/GetAssetLayoutLayout_V2");
Endpoint const EndpointGetAssetLayoutV3                     = API(@"/IOAPI/GetAssetLayoutLayout_V3");
Endpoint const EndpointSavePhotoComment                     = API(@"/IOAPI/SavePhotoComment");
Endpoint const EndpointGetAssetFloorPlans                   = API(@"/IOAPI/GetAssetFloorPlans");
Endpoint const EndpointGetFloorPlan                         = API(@"/IOAPI/GetFloorPlan");
Endpoint const EndpointGetItemFloorPlans                    = API(@"/IOAPI/GetItemFloorPlans");
Endpoint const EndpointUploadFloorPlanMarks                 = API(@"/IOAPI/UploadFloorPlan_Marks");
Endpoint const EndpointInitFloorPlan                        = API(@"/IOAPI/InitFloorPlan");

Endpoint const EndpointSyncProduct                          = API(@"/Sync/SyncProduct");
Endpoint const EndpointGetProductPhoto                      = API(@"/IOAPI/GetProductPhoto");

Endpoint const EndpointAssetTask                            = CONNECT_API(@"/app/task/asset_tasks");
Endpoint const EndpointAssetTaskDetail                      = CONNECT_API(@"/app/task/details");
Endpoint const EndpointDeleteTask                           = CONNECT_API(@"/sync/delete_task");
Endpoint const EndpointUpdateStatus                         = CONNECT_API(@"/sync/update_status");
Endpoint const EndpointUpdateTaskDetails                    = CONNECT_API(@"/sync/task_details");

Endpoint const EndpointTaskSaveDetails2024                  = API(@"/App/Task_SaveDetails_2024");
Endpoint const EndpointTaskSavePhoto                        = API(@"/App/Task_SavePhoto");
Endpoint const EndpointTaskSaveVideo                        = API(@"/App/Task_SaveVideo");
Endpoint const EndpointTaskAssetTaskDetails                 = API(@"/App/Task_AssetTaskDetails");
Endpoint const EndpointCompleteTask                         = API(@"/App/Task_Complete");
Endpoint const EndpointSavePhotoComments                    = API(@"/App/SavePhotoComments");
Endpoint const EndpointTaskCommentsSave                     = API(@"/App/TaskComments_Save");
Endpoint const EndpointTaskCommentsGet                      = API(@"/App/TaskComments_Get");

Endpoint const EndpointPropertyLayoutSync                   = CONNECT_API(@"/app/propertylayout/sync");
Endpoint const EndpointCustomerTasksCatch                   = CONNECT_API(@"/app/task/customer_tasks_catch");
Endpoint const EndpointCustomerTasks                        = CONNECT_API(@"/app/task/customer_tasks");

// Timeout interval for all requests
const static NSTimeInterval kTimeoutInterval = 180;

@implementation IFConnection

+ (NSData *)dataWithRequest: (NSURLRequest *)oRequest {
    __block NSHTTPURLResponse *oResponse = nil;
    __block NSData *oData = nil;

    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    NSURLSessionDataTask *dataTask = [[NSURLSession sharedSession] dataTaskWithRequest: oRequest completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        oData = data;
        oResponse = (NSHTTPURLResponse *)response;
        
        dispatch_semaphore_signal(semaphore);
    }];
    [dataTask resume];
    
    dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
    
    if ([oResponse statusCode] != 200) return nil;
    
    return oData;
}

+ (NSDictionary *)connectionWithRequest: (NSURLRequest *)oRequest {
    NSData *oData = [self dataWithRequest: oRequest];
    if (oData == nil) return nil;
    
    NSDictionary *result = [NSJSONSerialization JSONObjectWithData:oData options:NSJSONReadingMutableContainers error:nil];
    DLog(@"[POST] response: %@ \n%@", oRequest.URL, result);
    return result;
}

+ (NSDictionary *)PostRequest:(Endpoint)endpoint oParams:(NSDictionary *)oParams {
    return [self PostRequestWithURL:[NSURL URLWithString:endpoint] oParams:oParams httpBodyFormat:HTTPBodyFormatFormData]; 
}

+ (NSDictionary *)PostRequest:(Endpoint)endpoint oParams:(NSDictionary *)oParams httpBodyFormat:(HTTPBodyFormat)httpBodyFormat {
    return [self PostRequestWithURL:[NSURL URLWithString:endpoint] oParams:oParams httpBodyFormat:httpBodyFormat];
}

+ (NSDictionary *)PostRequestWithURL:(NSURL *)oURL oParams:(NSDictionary *)oParams httpBodyFormat:(HTTPBodyFormat)httpBodyFormat {
    DLog(@"[POST] request: %@ \n%@", oURL, oParams);
    NSMutableURLRequest *oRequest = [NSMutableURLRequest requestWithURL:oURL];
    [oRequest setHTTPMethod:@"POST"];
    [oRequest setCachePolicy:NSURLRequestReloadIgnoringLocalCacheData];
    [oRequest setHTTPShouldHandleCookies:NO];
    [oRequest setTimeoutInterval:kTimeoutInterval];
    [oRequest setHTTPBody:[IFConnection encodeDictionary:oParams httpBodyFormat:httpBodyFormat]];
    return [self connectionWithRequest: oRequest];
}

+ (NSDictionary *)PostRequestWithFile:(Endpoint)endpoint sPath:(NSString *)sFilePath {
    return [self PostRequestWithFile:endpoint oParams:nil sPath:sFilePath sFileFieldName:@"oFile"];
}

+ (NSDictionary *)PostRequestWithFile:(Endpoint)endpoint oParams:(NSDictionary *)oParams sPath:(NSString *)sFilePath sFileFieldName:(NSString *)sFileFieldName {
    @try {
        if (sFilePath.length > 0) {
            NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:endpoint]];
            request.HTTPMethod = @"POST";
            request.timeoutInterval = kTimeoutInterval;

            NSString *boundary = @"-------------------------236452834657573646374756743664";
            NSString *contentType = [NSString stringWithFormat:@"multipart/form-data; boundary=%@", boundary];
            [request addValue:contentType forHTTPHeaderField:@"Content-Type"];
            NSMutableData *mutableData = [NSMutableData data];
            [mutableData appendData:[[NSString stringWithFormat:@"\r\n--%@\r\n", boundary] dataUsingEncoding:NSUTF8StringEncoding]];
            [mutableData appendData:[[NSString stringWithFormat:@"Content-Disposition: form-data; name=\"%@\";filename=\"%@\" ",
                    sFileFieldName, [sFilePath lastPathComponent]] dataUsingEncoding:NSUTF8StringEncoding]];

            // image/jpeg     text/xml
            [mutableData appendData:[@"Content-Type: application/data \r\n\r\n" dataUsingEncoding:NSUTF8StringEncoding]];
            [mutableData appendData:[NSData dataWithContentsOfFile:sFilePath]];

            if (oParams != nil && oParams.count > 0) {
                for (NSString *key in oParams.allKeys) {
                    [mutableData appendData:[[NSString stringWithFormat:@"\r\n--%@\r\n", boundary] dataUsingEncoding:NSUTF8StringEncoding]];
                    [mutableData appendData:[[NSString stringWithFormat:@"Content-Disposition: form-data; name=\"%@\"\r\n\r\n", key] dataUsingEncoding:NSUTF8StringEncoding]];
                    [mutableData appendData:[oParams[key] dataUsingEncoding:NSUTF8StringEncoding]];
                }
            } else {
                [mutableData appendData:[[NSString stringWithFormat:@"\r\n--%@\r\n", boundary] dataUsingEncoding:NSUTF8StringEncoding]];
                [mutableData appendData:[@"Content-Disposition: form-data; name=\"iCustomerID\"\r\n\r\n" dataUsingEncoding:NSUTF8StringEncoding]];
                [mutableData appendData:[[CommonHelper IFGetPref:@"iCustomerID"] dataUsingEncoding:NSUTF8StringEncoding]];

                [mutableData appendData:[[NSString stringWithFormat:@"\r\n--%@\r\n", boundary] dataUsingEncoding:NSUTF8StringEncoding]];
                [mutableData appendData:[@"Content-Disposition: form-data; name=\"sToken\"\r\n\r\n" dataUsingEncoding:NSUTF8StringEncoding]];
                [mutableData appendData:[[CommonUser currentToken] dataUsingEncoding:NSUTF8StringEncoding]];

                [mutableData appendData:[[NSString stringWithFormat:@"\r\n--%@\r\n", boundary] dataUsingEncoding:NSUTF8StringEncoding]];
                [mutableData appendData:[@"Content-Disposition: form-data; name=\"ver\"\r\n\r\n" dataUsingEncoding:NSUTF8StringEncoding]];
                [mutableData appendData:[sVer dataUsingEncoding:NSUTF8StringEncoding]];

                [mutableData appendData:[[NSString stringWithFormat:@"\r\n--%@\r\n", boundary] dataUsingEncoding:NSUTF8StringEncoding]];
                [mutableData appendData:[@"Content-Disposition: form-data; name=\"sDeviceInfo\"\r\n\r\n" dataUsingEncoding:NSUTF8StringEncoding]];
                [mutableData appendData:[[CommonHelper GetDeviceInfo] dataUsingEncoding:NSUTF8StringEncoding]];
            }

            [mutableData appendData:[[NSString stringWithFormat:@"\r\n--%@--\r\n", boundary] dataUsingEncoding:NSUTF8StringEncoding]];
            request.HTTPBody = mutableData;
            return [self connectionWithRequest: request];
        }
        return nil;
    } @catch (NSException *ex) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex];
        return nil;
    }
}

+ (NSDictionary *)PostRequestWithFile_ExternalInspection:(NSString *)sURL sPath:(NSString *)sFilePath sTokenID:(NSString *)sTokenID sToken:(NSString *)sToken {
    @try {
        //"/Sync/ServerApp"
        if (sFilePath.length > 0) {
            NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:sURL]];
            [request setHTTPMethod:@"POST"];
            NSString *boundary = @"-------------------------236452834657573646374756743664";
            NSString *contentType = [NSString stringWithFormat:@"multipart/form-data; boundary=%@", boundary];
            [request addValue:contentType forHTTPHeaderField:@"Content-Type"];
            NSMutableData *postbody = [NSMutableData data];
            [postbody appendData:[[NSString stringWithFormat:@"\r\n--%@\r\n", boundary] dataUsingEncoding:NSUTF8StringEncoding]];
            [postbody appendData:[[NSString stringWithFormat:@"Content-Disposition: form-data; name=\"oFile\";filename=\"%@\" ", [sFilePath lastPathComponent]] dataUsingEncoding:NSUTF8StringEncoding]];

            // image/jpeg     text/xml
            [postbody appendData:[@"Content-Type: application/data \r\n\r\n" dataUsingEncoding:NSUTF8StringEncoding]];
            [postbody appendData:[NSData dataWithContentsOfFile:sFilePath]];

            [postbody appendData:[[NSString stringWithFormat:@"\r\n--%@\r\n", boundary] dataUsingEncoding:NSUTF8StringEncoding]];
            [postbody appendData:[@"Content-Disposition: form-data; name=\"iTokenID\"\r\n\r\n" dataUsingEncoding:NSUTF8StringEncoding]];
            [postbody appendData:[sTokenID dataUsingEncoding:NSUTF8StringEncoding]];

            [postbody appendData:[[NSString stringWithFormat:@"\r\n--%@\r\n", boundary] dataUsingEncoding:NSUTF8StringEncoding]];
            [postbody appendData:[@"Content-Disposition: form-data; name=\"sToken\"\r\n\r\n" dataUsingEncoding:NSUTF8StringEncoding]];
            [postbody appendData:[sToken dataUsingEncoding:NSUTF8StringEncoding]];

            [postbody appendData:[[NSString stringWithFormat:@"\r\n--%@\r\n", boundary] dataUsingEncoding:NSUTF8StringEncoding]];
            [postbody appendData:[@"Content-Disposition: form-data; name=\"ver\"\r\n\r\n" dataUsingEncoding:NSUTF8StringEncoding]];
            [postbody appendData:[sVer dataUsingEncoding:NSUTF8StringEncoding]];

            NSString *sDeviceInfo = [NSString stringWithFormat:@"%@ - ExternalTokenID: %@", [CommonHelper GetDeviceInfo], sTokenID];
            [postbody appendData:[[NSString stringWithFormat:@"\r\n--%@\r\n", boundary] dataUsingEncoding:NSUTF8StringEncoding]];
            [postbody appendData:[@"Content-Disposition: form-data; name=\"sDeviceInfo\"\r\n\r\n" dataUsingEncoding:NSUTF8StringEncoding]];
            [postbody appendData:[sDeviceInfo dataUsingEncoding:NSUTF8StringEncoding]];

            [postbody appendData:[[NSString stringWithFormat:@"\r\n--%@--\r\n", boundary] dataUsingEncoding:NSUTF8StringEncoding]];

            [request setTimeoutInterval:100];
            [request setHTTPBody:postbody];
            
            return [self connectionWithRequest: request];
        }
        return nil;

    } @catch (NSException *ex) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex];
        return nil;
    }
}

+ (NSData *)DownloadFile_Data:(NSURL *)oURL {
    @try {
        NSMutableURLRequest *oRequest1 = [[NSMutableURLRequest alloc] initWithURL:oURL];
        [oRequest1 setHTTPMethod:@"GET"];
        
        NSData *oData = [self dataWithRequest: oRequest1];
        return oData ?: [self dataWithRequest: oRequest1];
    } @catch (NSException *ex) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex];
        return nil;
    }
}

+ (NSDictionary *)DownloadFile:(NSURL *)oURL {
    @try {
        NSMutableURLRequest *oRequest1 = [[NSMutableURLRequest alloc] initWithURL:oURL];
        [oRequest1 setHTTPMethod:@"GET"];
        oRequest1.timeoutInterval = 480;
        
        NSData *oData = [self dataWithRequest: oRequest1];
        if (oData == nil) oData = [self dataWithRequest: oRequest1];
        
        NSDictionary *dic = (NSMutableDictionary *) [XMLReader dictionaryForXMLString:[[NSString alloc] initWithData:oData encoding:NSUTF8StringEncoding] error:NULL];
        return dic;
    } @catch (NSException *ex) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex];
        return nil;
    }
}

+ (NSData*)encodeDictionary:(NSDictionary*)dictionary httpBodyFormat:(HTTPBodyFormat)httpBodyFormat {
    switch (httpBodyFormat) {
        case HTTPBodyFormatJSON:
            return [NSJSONSerialization dataWithJSONObject:dictionary options:0 error:nil];
        case HTTPBodyFormatFormData:
            NSString *queryString = [CommonHelper getQueryString: dictionary];
            return [queryString dataUsingEncoding:NSUTF8StringEncoding];
    }
}

@end
