//
//  if_asset_content.m
//  SnapInspect3
//
//  Created by <PERSON> on 2/28/17.
//  Copyright © 2017 SnapInspect. All rights reserved.
//

#import "if_Asset_Content.h"
#import "AssetInfoCell.h"
#import "AssetImageViewCell.h"
#import "if_AssetDetail.h"
#import <MessageUI/MFMailComposeViewController.h>
#import "if_Assets_2nd.h"
#import "if_AssetRoute.h"
#import "if_ServerEmail.h"
#import "AssetInfoCellWithoutSchedule.h"
#import "NSArray+HighOrderFunction.h"
#import "CommonRequest.h"
@import Extensions;

#define IMAGE_CELL_IDENTIFIER @"AssetImageViewCell"
#define ASSET_CELL_IDENTIFIER @"AssetInfoCell"
#define INSPECTION_CELL_IDENTIFIER @"InspectionCell"
#define ASSET_CELL_NO_SCHEDULE_IDENTIFIER @"AssetInfoCellWithoutSchedule"

#define UIColorFromRGB(rgbValue) [UIColor colorWithRed:((float)((rgbValue & 0xFF0000) >> 16))/255.0 green:((float)((rgbValue & 0xFF00) >> 8))/255.0 blue:((float)(rgbValue & 0xFF))/255.0 alpha:1.0]

@interface if_Asset_Content ()<CLLocationManagerDelegate, SCCaptureCameraControllerDelegate>
@property (strong, nonatomic) CLLocationManager *locationManager;
@property (strong, nonatomic) NSArray<v_Schedule *> *arrSchedule;
@property (assign, nonatomic) BOOL isLoadingAssetSchedules;
@property (assign, nonatomic) BOOL isFetchedAssetSchedules;
@property (assign, nonatomic) int waitingForUploadFileID;
@end

// Section Identifier
static NSString * const kSectionAsseImage = @"Asset Image";
static NSString * const kSectionAssetInfo = @"Asset Info";
static NSString * const kSectionSchedule = @"Schedule";
static NSString * const kSectionInspectionInProgress = @"Inspection in Progress";
static NSString * const kSectionInspectionCompleted = @"Completed Inspections";
static NSString * const kSectionInspectionUploaded = @"Uploaded Inspections";

@implementation if_Asset_Content {
    NSArray *arrInsType;
    NSMutableArray *collapseSections;
    CGFloat currentLatitude;
    CGFloat currentLongitude;
    O_Asset *oAsset;
    CGFloat iWidth, iHeight;
    PromptView *promtAlertView;
    BOOL hidedPrompt;
    O_File *oPropertyPhoto;
    UIRefreshControl *refreshControl;
    int iChildCount;
    bool bTenantProperty;
   // bool bCompanyReview;
}

@synthesize tableContents,sortedKeys,headerView, iSAssetID, sAddress1, sAddress2, arrCompletedIns, arrCurrentIns, arrUploadedIns;
- (void)viewDidLoad {
    [super viewDidLoad];

    self.arrSchedule = @[];
    self.isFetchedAssetSchedules = NO;

    CGSize oSize = [[UIScreen mainScreen] bounds].size;
    iWidth = oSize.width;
    
    _tableView.delegate = self;
    _tableView.dataSource = self;
    bTenantProperty = false;

    self.tableView.rowHeight = UITableViewAutomaticDimension;
    [self.tableView registerNib:[UINib nibWithNibName:@"InspectionCell" bundle:nil] forCellReuseIdentifier:INSPECTION_CELL_IDENTIFIER];
#ifndef NO_SCHEDULE
        [self.tableView registerNib:[UINib nibWithNibName:@"AssetInfoCellWithoutSchedule" bundle:nil] forCellReuseIdentifier:ASSET_CELL_NO_SCHEDULE_IDENTIFIER];
#else
        [self.tableView registerNib:[UINib nibWithNibName:@"AssetInfoCell" bundle:nil] forCellReuseIdentifier:ASSET_CELL_IDENTIFIER];
#endif
    [self.tableView registerNib:[UINib nibWithNibName:@"AssetImageViewCell" bundle:nil] forCellReuseIdentifier:IMAGE_CELL_IDENTIFIER];

    self.tableView.separatorColor = [UIColor clearColor];
    iHeight = 115;

    UIBarButtonItem *cameraButton = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemCamera target:self action:@selector(addPhoto:)];
    self.navigationItem.rightBarButtonItem = cameraButton;
}

- (void)longPress:(UILongPressGestureRecognizer*)gesture {
    if ( gesture.state == UIGestureRecognizerStateEnded ) {
        [db_Inspection RecoverAllAssetInspection: iSAssetID];
        [self ReloadTable];
    }
}

- (void)refreshTable {
    if (![if_AppDelegate.shared bInternet:false]) {
        [refreshControl endRefreshing];
        return;
    }

    MBProgressHUD *hud = [[MBProgressHUD alloc] si_initWithView:self.view];
    [self.view addSubview:hud];
    hud.labelText = @"Downloading...";

    [hud showAnimated:YES whileExecutingBlock:^{
        [hud setDetailsLabelText:@"Downloading Data..."];
        NSMutableDictionary *params = [@{
                @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
                @"sToken": [CommonUser currentToken],
                @"iSAssetID": [NSString stringWithFormat:@"%ld", iSAssetID]
        } mutableCopy];
        // Load the asset photo if have it
        NSDictionary *oReturn = [IFConnection PostRequest:EndpointDownloadAssetPhoto oParams:params];
        if ([oReturn[@"success"] boolValue]) {
            NSData *oData = [IFConnection DownloadFile_Data:[NSURL URLWithString:[oReturn valueForKey:@"sDownloadURL"]]];
            if (oData != nil) {
                NSString *sPath = [[if_AppDelegate GetGlobalVar] valueForKeyPath:@"sFilePath"];
                NSString *sUniqueFileName = [CommonHelper GetUniqueFileName];
                NSString *sPhotoName = [NSString stringWithFormat:@"f_%@.jpg", sUniqueFileName];
                NSString *sPhotoPath = [sPath stringByAppendingPathComponent:sPhotoName];
                [oData writeToFile:sPhotoPath atomically:NO];
                oPropertyPhoto.sFile = sPhotoName;
                [db_Media UpdateFile:oPropertyPhoto];
                safe_dispatch_main_async(^{
                    [self.tableView reloadData];
                })
            } else {
                safe_dispatch_main_async(^{
                    [self ShowAlert:@"Error" sMessage:@"Error when download photo, please try again."];
                })
            }
        }
        // Load asset schedules
        [self loadAssetSchedules];
    } completionBlock:^{
        [refreshControl endRefreshing];
        [hud removeFromSuperview];
    }];
}

- (void)addPhoto: (id)sender {
    if (![self checkGPSLocationPermission]) return;
    SCNavigationController *nav = [
        [SCNavigationController alloc] initWithDelegate:self
                                              iParamID:(int)iSAssetID
                                            iInsItemID:0
                                                iInsID:0
                                            iSObjectID:oAsset.iSAssetID
                                      cameraSaveOption:CameraSaveOptionFile
                                          cameraOption:CameraOptionSingle
                                            iPhotoSize:kDefaultTakenPhotoSize
    ];
    
    // Show camera interface
    [nav showCameraWithParentController:self];
}

- (void)PostProcessFileID:(int)iFileID iSObjectID:(int)iSObjectID{
   // oAsset.iCustomerID = _iPhotoID;
   // oAsset.sCustom2 = [CommonJson AddJsonKeyValueString:@"FILE" sValue:[NSString stringWithFormat:@"%d", iFileID] sJson:oAsset.sCustom2];
   // [db_Asset UpdateProperty:oAsset];
    [db_Asset MarkFileAttachCMD:iFileID];
    [db_Asset MarkFileAssetPhoto:iFileID iSAssetID:(int) iSAssetID];
    self.waitingForUploadFileID = iFileID;
}

- (void)PostPreviewPhotoID:(int)iPhotoID iInsItemID:(int)iInsItemID iParamID:(int)iParamID {
    [Navigator PresentDisplayFileID:iPhotoID];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];

    // Upload asset photo
    if (self.waitingForUploadFileID > 0) {
        [self uploadAssetPhoto:self.waitingForUploadFileID];
        self.waitingForUploadFileID = -1;
    }

    // Prompt
    hidedPrompt = true;
    if (hidedPrompt)
        return;
    
    promtAlertView = [PromptView shareInstance];
    [promtAlertView setFrame:CGRectMake(0, 0, iWidth, PROMPT_VIEW_HEIGHT)];
    [promtAlertView setMessage:@"Choose your property layout here and select the number of rooms that need inspecting. You can edit these items in the next screen."];
    [promtAlertView.closeButton addTarget:self action:@selector(hidePrompt) forControlEvents:UIControlEventTouchUpInside];
    [self.promptView addSubview:promtAlertView];
    
    [UIView animateWithDuration:0.5 animations:^{
        self.promptViewHeight.constant = PROMPT_VIEW_HEIGHT;
        [self.view layoutIfNeeded];
    } completion:^(BOOL finished) {
    }];

}

- (void)hidePrompt {
    
    [UIView animateWithDuration:0.5 animations:^{
        self.promptViewHeight.constant = 0;
        [self.view layoutIfNeeded];
    } completion:^(BOOL finished) {
        [promtAlertView removeFromSuperview];
        hidedPrompt = YES;
    }];

    
}
- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.navigationItem.title = [NSString stringWithFormat:@"%@, %@", sAddress1, sAddress2];
    collapseSections = [NSMutableArray array];
    [self LoadInspections];

    oAsset = [db_Asset GetPropertyBySAssetID:(int) iSAssetID];
    sAddress1 = [oAsset.sAddress1 length] > 0 ? oAsset.sAddress1 : @"";
    sAddress2 = oAsset.sAddress2;
    iChildCount = [db_Asset ValidateChildAssetExist:(int) iSAssetID];
    NSMutableArray *oResult = [@[sAddress1.copy] mutableCopy];
    self.tableContents = @{
            kSectionAsseImage: @"",
            kSectionAssetInfo: oResult,
            kSectionSchedule: self.arrSchedule,
            kSectionInspectionInProgress: arrCurrentIns,
            kSectionInspectionCompleted: arrCompletedIns,
            kSectionInspectionUploaded: arrUploadedIns
    };
    self.sortedKeys = @[
            kSectionAsseImage,
            kSectionAssetInfo,
            kSectionSchedule,
            kSectionInspectionInProgress,
            kSectionInspectionCompleted,
            kSectionInspectionUploaded
    ];

    [db_Log InsertLog:@"Event" sMessage:[NSString stringWithFormat:@"Exist Address View - %@", sAddress1]];

    [self startUserLocationSearch];

    @try {
        bTenantProperty = [CommonHelper bExternalProperty:oAsset.sCustom2];
        if (bTenantProperty) {
            self.navigationItem.rightBarButtonItem = nil;
        }

        oPropertyPhoto = [db_Asset GetAssetPhoto:(int) iSAssetID];
        if (oPropertyPhoto != nil && oPropertyPhoto.iFileID > 0) {
            refreshControl = [[UIRefreshControl alloc] init];
            refreshControl.backgroundColor = [UIColor lightGrayColor];
            refreshControl.tintColor = [UIColor whiteColor];
            [self.tableView addSubview:refreshControl];
            [refreshControl addTarget:self action:@selector(refreshTable) forControlEvents:UIControlEventValueChanged];
        }
    } @catch (NSException *exxx) {

    }

    // Collapse the schedule section
    if (!self.isFetchedAssetSchedules) {
        [collapseSections addObject:@([self.sortedKeys indexOfObject:kSectionSchedule])];
    }

    [self.tableView reloadData];
}

- (void)LoadInspections{
    arrCurrentIns =[db_Inspection GetInspectionsBySAssetID:iSAssetID bCompleted:false bSynced:false bExcludeChild:false];
    arrCompletedIns = [db_Inspection GetInspectionsBySAssetID:iSAssetID bCompleted:true bSynced:false bExcludeChild:false];
    
    arrUploadedIns = [db_Inspection GetInspectionsBySAssetID:iSAssetID bCompleted:true bSynced:true bExcludeChild:false];
    if (arrCompletedIns == nil || [arrCompletedIns count] == 0){
        O_InspectionTemp *oIns = [[O_InspectionTemp alloc] init];
        oIns.sTitle = @"No Completed Inspections.";
        oIns.bEmpty = true;
        [arrCompletedIns addObject:oIns];
    }
    if (arrCurrentIns == nil || [arrCurrentIns count] == 0){
        O_InspectionTemp *oIns = [[O_InspectionTemp alloc] init];
        oIns.sTitle = @"No Inspection in Progress.";
        oIns.bEmpty = true;
        [arrCurrentIns addObject:oIns];
    }
    if (arrUploadedIns == nil || [arrUploadedIns count] == 0){
        O_InspectionTemp *oIns = [[O_InspectionTemp alloc] init];
        oIns.sTitle = @"No Inspection in Progress.";
        oIns.bEmpty = true;
        [arrUploadedIns addObject:oIns];
    }
}
-(void) willRotateToInterfaceOrientation:(UIInterfaceOrientation)toInterfaceOrientation duration:(NSTimeInterval)duration {
    CGSize oSize = [[UIScreen mainScreen] bounds].size;
    iWidth = oSize.height;
    [_tableView reloadData];
    [promtAlertView setFrame:CGRectMake(0, 0, iWidth, PROMPT_VIEW_HEIGHT)];
    [promtAlertView.closeButton addTarget:self action:@selector(hidePrompt) forControlEvents:UIControlEventTouchUpInside];
}

-(void)uploadAssetPhoto:(int)iFileID {
    MBProgressHUD *hud = [[MBProgressHUD alloc] si_initWithView:self.view];
    [self.view addSubview:hud];
    hud.labelText = @"Uploading ...";
    hud.detailsLabelText = @"Please wait";

    @weakify(self, hud);
    [hud showAnimated:YES whileExecutingBlock:^{
        @strongify(self);
        O_File *oFile = [db_Media GetFile:iFileID];
        if (![CommonRequest uploadFile:oFile]) {
            safe_dispatch_main_async(^{
                [self ShowAlert:@"Error" sMessage:@"Upload unsuccessful, please try again."];
            });
            return;
        }

        NSDictionary *params = @{
                @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
                @"sToken": [CommonUser currentToken],
                @"iSFileID": [NSString stringWithFormat:@"%d", oFile.iSFileID]
        };
        NSDictionary *oReturn = [IFConnection PostRequest:EndpointAttachAssetPhoto oParams:[params mutableCopy]];
        if (oReturn == nil || [[oReturn valueForKey:@"success"] boolValue] == NO) {
            NSDictionary *oReturn1 = [IFConnection PostRequest:EndpointAttachAssetPhoto oParams:[params mutableCopy]];
            if (oReturn1 == nil || [[oReturn1 valueForKey:@"success"] boolValue] == NO) {
                safe_dispatch_main_async(^{
                    [self ShowAlert:@"Error" sMessage:@"Upload unsuccessful, please try again."];
                });
                return;
            }
        }

        // success
        oFile.sCustom1 = [CommonJson RemoveJsonKey:@"AttachCMD" sJson:oFile.sCustom1];
        [db_Media UpdateFile:oFile];
    } completionBlock:^{
        @strongify(self, hud);
        [self->_tableView reloadData];
        [hud removeFromSuperview];
    }];
}

-(void)startUserLocationSearch{
    self.locationManager = [[CLLocationManager alloc]init];
    self.locationManager.delegate = self;
    self.locationManager.distanceFilter = kCLDistanceFilterNone;
    self.locationManager.desiredAccuracy = kCLLocationAccuracyHundredMeters;
    
    if ([self.locationManager respondsToSelector:@selector(requestWhenInUseAuthorization)]) {
        [self.locationManager requestWhenInUseAuthorization];
    }
    [self.locationManager startUpdatingLocation];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}
-(IBAction)Start_Inspection:(id)sender {
    @autoreleasepool {
        // int iChildCount = [db_Asset ValidateChildAssetExist:iSAssetID];
        if (iChildCount > 0) {
            NSString *sInspectBuilding = @"Inspect the Asset";
            NSString *sInspectUnit = @"View Apartments";
            if (oAsset.iSPAssetID > 0) {
                sInspectUnit = @"View Rooms";
            }
            UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Action" message:@"" preferredStyle:UIAlertControllerStyleActionSheet];
            UIAlertAction *oAlertBuilding = [UIAlertAction actionWithTitle:sInspectBuilding style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
                [self Start_Inspection_Step_2:sender];
            }];
            UIAlertAction *oAlertApartment = [UIAlertAction actionWithTitle:sInspectUnit style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
                if_Assets_2nd *oAssets = [self.storyboard instantiateViewControllerWithIdentifier:@"Assets_2nd"];
                oAssets.iPAssetID = (int) iSAssetID;
                oAssets.sTitle = sAddress1;
                [self.navigationController pushViewController:oAssets animated:YES];
            }];
            [alert addAction:oAlertBuilding];
            [alert addAction:oAlertApartment];
            [alert addAction:[UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil]];

            alert.modalPresentationStyle = UIModalPresentationPopover;
            alert.popoverPresentationController.sourceView = sender;
            alert.popoverPresentationController.sourceRect = [sender bounds];
            [self presentViewController:alert animated:YES completion:nil];
        } else {
            // oResult = [NSMutableArray arrayWithObject:sAddress1];
            [self Start_Inspection_Step_2:sender];
        }
    }
}

-(void)Start_Inspection_Step_2:(id)sender{
    if (![self validateNewInspection]) {
        return;
    }
    
    if (arrInsType) arrInsType = nil;
    
    @weakify(self)
    if (bTenantProperty){
        NSString *sFileName = [CommonHelper sRequestInspection_FileName:oAsset.sCustom2];
        arrInsType = [json_Common TT_LoadInsTypes:sFileName];
        [InsTypeSelectionView show:arrInsType didSelectOption:^(O_InsType * _Nonnull oInsType) {
            @strongify(self)
            @try {
                NSArray *arrAssetLayout = [json_Common TT_LoadAssetLayout:sFileName iAssetID:iSAssetID sPTC:oInsType.sPTC];
                if (arrAssetLayout != nil && [arrAssetLayout count] > 0) {
                    [db_Log InsertLog:@"Ins Setup" sMessage:[NSString stringWithFormat:@"Tenant Load Ins From Existing Layout - InsTypeID: %d - InsTypeCount: %d - %@, %@", oInsType.iSInsTypeID, (int) [arrInsType count], sAddress1, oAsset.sAddress2]];
                    int iInsID = [db_SetupInspection AddInspection_JSON_TenantTool:arrAssetLayout iSAssetID:oAsset.iSAssetID sAddress1:oAsset.sAddress1 sAddress2:oAsset.sAddress2 oInsType:oInsType iSScheduleID:0 sCustom2:oAsset.sCustom2 sLat:currentLatitude sLong:currentLongitude sPTC:oInsType.sPTC dtSchedule:@""];
                    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                        [self PushInspection:iInsID];
                    }];
                    
                } else {
                    int iInsID = [CommonSetupIns StartInspectionBranch_JSON_TenantTool:oAsset.iSAssetID sAddress1:oAsset.sAddress1 sAddress2:oAsset.sAddress2 iSScheduleID:0 sPTC:oInsType.sPTC sType:oInsType.sType oInsType:oInsType sCustom2:oAsset.sCustom2 sLat:currentLatitude sLong:currentLongitude dtSchedule:@""];
                    if (iInsID > 0) {
                        [db_Log InsertLog:@"Ins Setup" sMessage:[NSString stringWithFormat:@"Tenant Load Ins By Pass - InsTypeID: %d - InsTypeCount: %d - %@, %@", oInsType.iSInsTypeID, (int) [arrInsType count], sAddress1, oAsset.sAddress2]];
                        [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                            [self PushInspection:iInsID];
                        }];
                    } else {
                        [db_Log InsertLog:@"Ins Setup" sMessage:[NSString stringWithFormat:@"Tenant Setup Layout - InsTypeID: %d - InsTypeCount: %d - %@, %@", oInsType.iSInsTypeID, (int) [arrInsType count], sAddress1, oAsset.sAddress2]];
                        [Navigator PushLayoutSetup:oInsType.iSInsTypeID
                                      iSScheduleID:0
                                         iSAssetID:oAsset.iSAssetID
                                             sType:oInsType.sType
                                              sPTC:oInsType.sPTC
                                         sAddress1:oAsset.sAddress1
                                         sAddress2:oAsset.sAddress2
                                          sCustom1:oAsset.sCustom1
                                        dtSchedule:@""
                             onInspectionDidCreate:nil
                        ];
                    }
                }
            }
            @catch (NSException *exception) {
                [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            }
            
        }];
    }
    else{
        arrInsType = [db_Common GetIntTypes];
        [InsTypeSelectionView show:arrInsType didSelectOption:^(O_InsType * _Nonnull oInsType) {
            @strongify(self)
            @try {
                NSArray *arrIndevice = [db_Inspection GetInspections_InDevice:oAsset.iSAssetID];
                int iExistInsID = 0;
                for (O_Inspection *oIns in arrIndevice) {
                    if (oIns.iSInsTypeID == oInsType.iSInsTypeID) {
                        iExistInsID = oIns.iInsID;
                        break;
                    }
                }
                
                if (iExistInsID > 0) {
                    UIAlertController *alertPrompt = [UIAlertController alertControllerWithTitle:@"Message" message:[NSString stringWithFormat:@"You have an incompleted %@. Choose 'Continue' to work on existing inspection, or 'New' to start new inspection.", oInsType.sInsTitle] preferredStyle:UIAlertControllerStyleActionSheet];
                    UIAlertAction *oAlertOption1 = [UIAlertAction actionWithTitle:@"Continue" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
                        [self PushInspection:iExistInsID];
                        
                    }];
                    UIAlertAction *oAlertOption2 = [UIAlertAction actionWithTitle:@"New" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
                        [self checkIfHasScheduesNeedToStartNewInspectionWithInsType:oInsType sourceView:sender];
                    }];
                    [alertPrompt addAction:oAlertOption1];
                    [alertPrompt addAction:oAlertOption2];
                    
                    UIAlertAction *oClose1 = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil];
                    [alertPrompt addAction:oClose1];
                    
                    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                        if ([if_AppDelegate biPad]) {
                            alertPrompt.modalPresentationStyle = UIModalPresentationPopover;
                            alertPrompt.popoverPresentationController.sourceView = sender;
                            alertPrompt.popoverPresentationController.sourceRect = [sender bounds];
                            [self presentViewController:alertPrompt animated:YES completion:nil];
                        } else {
                            [self presentViewController:alertPrompt animated:YES completion:nil];
                        }
                    }];
                } else {
                    [self checkIfHasScheduesNeedToStartNewInspectionWithInsType:oInsType sourceView:sender];
                }
            }
            @catch (NSException *exception) {
                [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            }
            
        }];
    }
}
// check if any schedules exist for that asset/inspection type/customer combination.
- (void)checkIfHasScheduesNeedToStartNewInspectionWithInsType:(O_InsType *)oInsType sourceView:(id)sourceView {
    NSArray<v_Schedule *> *lsSchedules = [db_Schedule getSchedulesByAssetID:iSAssetID insTypeID:oInsType.iSInsTypeID];
    if (lsSchedules != nil && lsSchedules.count > 0) {
        // if schedule exist, then show alert.
        [self showAlertForExistingSchedules:oInsType sourceView:sourceView];
    } else {
        // if no schedule exist,  then by pass.
        [self startNewInspectionWithRemoteLayoutsIfNeedWithInsType:oInsType];
    }
}

- (void)showAlertForExistingSchedules:(O_InsType *)oInsType sourceView:(id)sourceView {
    [self ShowAlert:@"Message"
           sMessage:@"Oops! It looks like this inspection has already been scheduled. Please go to the 'Schedules' section to begin. If this is a new inspection, you can ignore this message."
       cancelButton:@"Ignore & Continue"
       cancelAction:^(UIAlertAction *action) {
        // go to the logic to start new inspection.
        [self startNewInspectionWithRemoteLayoutsIfNeedWithInsType:oInsType];
    }
         doneButton:@"Go to schedules"
         doneAction:^(UIAlertAction *action) {
        // switches to the schedule tab.
        [self switchToSchedulesTab];
    }];
}

- (void)switchToSchedulesTab {
    if_HomeScreen *homeScreen = [self.navigationController latestViewControllerWithItsClass:if_HomeScreen.class];
    [self.navigationController popToViewController:homeScreen animated:YES];
    [homeScreen switchToNewTab:HomeScreenTabSchedules];
}

- (void)startNewInspectionWithRemoteLayoutsIfNeedWithInsType:(O_InsType *)oInsType {
    void(^processLayoutsAndStartNewInspection)(NSArray<O_Layout *> *layouts) = ^(NSArray<O_Layout *> *layouts) {
        // check if the layout is bypassed
        NSArray<O_Layout *> *lsLayout = [layouts filter:^BOOL(O_Layout *oLayout) {
            return ![CommonInspection bPassLayout:oLayout withInsType:oInsType];
        }];
        
        [Navigator startInspectionWithLayouts:lsLayout
                                      insType:oInsType
                                 iSScheduleID:0
                                    iSAssetID:(int) iSAssetID
                                        sType:oInsType.sType
                                         sPTC:oInsType.sPTC
                                    sAddress1:oAsset.sAddress1
                                    sAddress2:oAsset.sAddress2
                                     sCustom1:oAsset.sCustom1
                            sCustom1_Schedule:nil
                                     latitude:currentLatitude
                                    longitude:currentLongitude
                                   dtSchedule:@""
                        onInspectionDidCreate:nil];
    };
    
    if ([CommonPermission bUseAssetLayoutV2]) {
        NSArray<O_Layout *> *lsLayout = [db_Common getPropertyLayoutsWithSPTC:oInsType.sPTC iPropertyID:iSAssetID];
        if (lsLayout != nil && lsLayout.isNotEmpty) {
            processLayoutsAndStartNewInspection(lsLayout);
        } else {
            [self showLoading: @"Loading..."];
            // Load Asset Layout for the server and start the new inspection based on the layout (if have)
            [CommonRequest getAssetLayoutV3WithISAssetID:iSAssetID
                                             iSInsTypeID:oInsType.iSInsTypeID
                                                    sPTC:oInsType.sPTC
                                              completion:^(NSArray<O_Layout *> *lsLayout, NSString *errorMsg) {
                if (lsLayout == nil || lsLayout.isEmpty) {
                    [self startNewInspectionWithInsType:oInsType];
                } else {
                    processLayoutsAndStartNewInspection(lsLayout);
                }
                [self hidesLoading];
            }];
        }
    } else {
        [self startNewInspectionWithInsType:oInsType];
    }
}

- (void)startNewInspectionWithInsType: (O_InsType *)oInsType {
    NSArray *arrAssetLayout = [db_Common GetAssetLayouts:iSAssetID sPTC:oInsType.sPTC];
    if (arrAssetLayout != nil && [arrAssetLayout count] > 0) {
        [db_Log InsertLog:@"Ins Setup" sMessage:[NSString stringWithFormat:@"Load Ins From Existing Layout - InsTypeID: %d - InsTypeCount: %d - %@, %@", oInsType.iSInsTypeID, (int) [arrInsType count], sAddress1, oAsset.sAddress2]];
        int iInsID = [db_SetupInspection AddInspection:arrAssetLayout iSAssetID:oAsset.iSAssetID sAddress1:oAsset.sAddress1 sAddress2:oAsset.sAddress2 iSInsTypeID:oInsType.iSInsTypeID iSScheduleID:0 sLat:currentLatitude sLong:currentLongitude sCustom1_Schedule:@"" dtSchedule:@""];
        [[NSOperationQueue mainQueue] addOperationWithBlock:^{
            [self PushInspection:iInsID];
        }];

    } else {
        int iInsID = [CommonSetupIns StartInspectionBranch:iSAssetID sAddress1:oAsset.sAddress1 sAddress2:oAsset.sAddress2 iSScheduleID:0 sPTC:oInsType.sPTC sType:oInsType.sType iSInsTypeID:oInsType.iSInsTypeID sLat:currentLatitude sLong:currentLongitude sCustom1_Schedule:@"" dtSchedule:@""];
        if (iInsID > 0) {
            [db_Log InsertLog:@"Ins Setup" sMessage:[NSString stringWithFormat:@"Load Ins By Pass - InsTypeID: %d - InsTypeCount: %d - %@, %@", oInsType.iSInsTypeID, (int) [arrInsType count], sAddress1, oAsset.sAddress2]];
            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                [self PushInspection:iInsID];
            }];
        } else {
            [db_Log InsertLog:@"Ins Setup" sMessage:[NSString stringWithFormat:@"Setup Layout - InsTypeID: %d - InsTypeCount: %d - %@, %@", oInsType.iSInsTypeID, (int) [arrInsType count], sAddress1, oAsset.sAddress2]];

            [Navigator PushLayoutSetup:oInsType.iSInsTypeID
                          iSScheduleID:0
                             iSAssetID:oAsset.iSAssetID
                                 sType:oInsType.sType
                                  sPTC:oInsType.sPTC
                             sAddress1:oAsset.sAddress1
                             sAddress2:oAsset.sAddress2
                              sCustom1:oAsset.sCustom1
                            dtSchedule:@""
                 onInspectionDidCreate:nil
            ];
        }
    }
}

-(void)PushInspection:(int)iInsID{
    [Navigator PushInspection: iInsID];
}
-(void)ReloadTable{
    [self LoadInspections];
    NSMutableArray *oResult = nil;
    iChildCount = [db_Asset ValidateChildAssetExist:(int) iSAssetID];
    oResult = [@[sAddress1] mutableCopy];
    self.tableContents = @{
            kSectionAsseImage: @"",
            kSectionAssetInfo: oResult,
            kSectionSchedule: self.arrSchedule,
            kSectionInspectionInProgress: arrCurrentIns,
            kSectionInspectionCompleted: arrCompletedIns,
            kSectionInspectionUploaded: arrUploadedIns};
    [_tableView reloadData];
}
-(IBAction)btn_RefreshPropertyInspection:(id)sender {
    if (![if_AppDelegate.shared bInternet:false]) {
        // [self ShowAlert:@"Error" sMessage:@"Please connect to Internet to proceed."];
        return;
    }

    MBProgressHUD *hud = [[MBProgressHUD alloc] si_initWithView:self.view];
    [self.view addSubview:hud];
    hud.labelText = @"Connecting to Server";

    [hud showAnimated:YES whileExecutingBlock:^{
        if ([CommonHelper bExternalProperty:oAsset.sCustom2]) {
            NSString *sFileName = [CommonHelper sRequestInspection_FileName:oAsset.sCustom2];
            NSString *sTokenID = [CommonJson GetJsonKeyValue:@"iTokenID" sJson:oAsset.sCustom2];
            NSString *sToken = [CommonJson GetJsonKeyValue:@"sToken" sJson:oAsset.sCustom2];
            NSMutableDictionary *oRefreshToken = [@{
                    @"iTokenID": sTokenID,
                    @"sToken": sToken,
                    @"iAssetID": [NSString stringWithFormat:@"%ld", iSAssetID]
            } mutableCopy];
            [hud setDetailsLabelText:@"Downloading Data..."];
            NSDictionary *oReturn = [IFConnection PostRequest:EndpointGetExternalPropertyReport oParams:oRefreshToken];
            //NSString *cc = @"bb";
            if ([[oReturn valueForKey:@"success"] boolValue]) {
                [db_Inspection DeleteAllAssetInspection:iSAssetID];
                NSArray *arrInsTypeIDsPermission = [json_Common TT_LoadInsTypes_CustomPermission:sFileName];
                for (NSMutableDictionary *oTemp in [oReturn valueForKey:@"lsInspection"]) {
                    int iSInsID = [[oTemp valueForKey:@"iInspectionID"] intValue];
                    // Ignore if the ins type in the permission list
                    if ([arrInsTypeIDsPermission containsObject:@(iSInsID)]) continue;

                    [CommonInspection ReconsolidateInspectionWithServer:iSInsID oDic:oTemp];
                }
                arrUploadedIns = [db_Inspection GetInspectionsBySAssetID:iSAssetID bCompleted:true bSynced:true bExcludeChild:false];
                if (arrUploadedIns == nil || [arrUploadedIns count] == 0) {
                    O_InspectionTemp *oIns = [[O_InspectionTemp alloc] init];
                    oIns.sTitle = @"No Inspection in Progress.";
                    oIns.bEmpty = true;
                    [arrUploadedIns addObject:oIns];
                }
                NSMutableArray *oResult = nil;
                oResult = [@[sAddress1] mutableCopy];
                self.tableContents = @{
                        kSectionAsseImage: @"",
                        kSectionAssetInfo: oResult,
                        kSectionSchedule: self.arrSchedule,
                        kSectionInspectionInProgress: arrCurrentIns,
                        kSectionInspectionCompleted: arrCompletedIns,
                        kSectionInspectionUploaded: arrUploadedIns
                };
                [[NSOperationQueue mainQueue] addOperationWithBlock:^{

                    [_tableView reloadData];
                }];
            }
        } else {
            NSMutableDictionary *oRefreshToken = [@{
                    @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
                    @"sToken": [CommonUser currentToken],
                    @"iObjectID": [NSString stringWithFormat:@"%ld", iSAssetID]
            } mutableCopy];
            [hud setDetailsLabelText:@"Downloading Data..."];
            NSDictionary *oReturn = [IFConnection PostRequest:EndpointSearchInspectionAPI oParams:oRefreshToken];
            //NSString *cc = @"bb";
            if ([[oReturn valueForKey:@"success"] boolValue]) {
                [db_Inspection DeleteAllAssetInspection:iSAssetID];

                NSArray *arrInsTypeIDsInPermission = [CommonInspection GetInsTypeIDs_CustomerPermission];
                for (NSMutableDictionary *oTemp in [oReturn valueForKey:@"lsInspection"]) {
                    int iSInsID = [[oTemp valueForKey:@"iInspectionID"] intValue];
                    int iInsTypeID = [[oTemp valueForKey:@"iInsTypeID"] intValue];

                    // Ignore if the ins type in the permission list
                    if ([arrInsTypeIDsInPermission containsObject:@(iInsTypeID)])
                        continue;

                    [CommonInspection ReconsolidateInspectionWithServer:iSInsID oDic:oTemp];
                }
                arrUploadedIns = [db_Inspection GetInspectionsBySAssetID:iSAssetID bCompleted:true bSynced:true bExcludeChild:false];
                if (arrUploadedIns == nil || [arrUploadedIns count] == 0) {
                    O_InspectionTemp *oIns = [[O_InspectionTemp alloc] init];
                    oIns.sTitle = @"No Inspection in Progress.";
                    oIns.bEmpty = true;
                    [arrUploadedIns addObject:oIns];
                }
                NSMutableArray *oResult = [@[sAddress1] mutableCopy];
                self.tableContents = @{
                        kSectionAsseImage: @"",
                        kSectionAssetInfo: oResult,
                        kSectionSchedule: self.arrSchedule,
                        kSectionInspectionInProgress: arrCurrentIns,
                        kSectionInspectionCompleted: arrCompletedIns,
                        kSectionInspectionUploaded: arrUploadedIns
                };
                [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                    [_tableView reloadData];
                }];
            }
        }
    } completionBlock:^{
        [hud removeFromSuperview];
    }];
}

- (void)expandSection:(UIButton*)sender {
    NSInteger tag = sender.tag;

    // Load the data for the expanded section - schedule
    if (tag == [self.sortedKeys indexOfObject:kSectionSchedule] && !self.isFetchedAssetSchedules) {
        [self loadAssetSchedules];
    }

    // Display or hides the rows for the section
    [_tableView beginUpdates];
    if (![collapseSections containsObject:@(tag)]) {
        [collapseSections addObject:@(tag)];
        [sender setImage:[UIImage imageNamed:@"expand_arrow"] forState:UIControlStateNormal];
        [_tableView reloadSections:[NSIndexSet indexSetWithIndex:tag] withRowAnimation:UITableViewRowAnimationAutomatic];
    } else {
        [collapseSections removeObject:@(tag)];
        [sender setImage:[UIImage imageNamed:@"collapse_arrow"] forState:UIControlStateNormal];
        [_tableView reloadSections:[NSIndexSet indexSetWithIndex:tag] withRowAnimation:UITableViewRowAnimationAutomatic];
    }
    [_tableView endUpdates];
}

- (void)expandSectionByTitle:(UIButton*)sender {
    NSInteger tag = sender.tag;

    // Load the data for the expanded section - schedule
    if (tag == [self.sortedKeys indexOfObject:kSectionSchedule] && !self.isFetchedAssetSchedules) {
        [self loadAssetSchedules];
    }

    [_tableView beginUpdates];
    if (![collapseSections containsObject:@(tag)]) {
        [collapseSections addObject:@(tag)];
        [_tableView reloadSections:[NSIndexSet indexSetWithIndex:tag] withRowAnimation:UITableViewRowAnimationNone];
    }
    
    else {
        [collapseSections removeObject:@(tag)];
        [_tableView reloadSections:[NSIndexSet indexSetWithIndex:tag] withRowAnimation:UITableViewRowAnimationNone];
    }
    [_tableView endUpdates];
}

-(void)onBtnChildAssetClicked:(id)sender {
    if_Assets_2nd *oAssets = [self.storyboard instantiateViewControllerWithIdentifier:@"Assets_2nd"];
    oAssets.iPAssetID = iSAssetID;
    oAssets.sTitle = sAddress1;
    [self.navigationController pushViewController:oAssets animated:YES];
}

#pragma mark - UITableViewDelegate
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return [self.sortedKeys count];
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    CGRect frame = tableView.frame;
    UIView *section_headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, iWidth, frame.size.height)];
    UILabel *title = [[UILabel alloc] initWithFrame:CGRectMake(20, 5, frame.size.width, 30)];
    title.font = [UIFont SanFranciscoText_Medium: 18];
    [title setTextColor:UIColorFromRGB(0x4A4A4A)];
    title.text = self.sortedKeys[section];
    if (section == 5){
        UIButton *addButton = [[UIButton alloc]init];
        [addButton setImage:[UIImage imageNamed:@"sync_inspection"] forState:UIControlStateNormal];
        [addButton setImage:[UIImage imageNamed:@"sync_inspection_highlight"] forState:UIControlStateHighlighted];
        [addButton setFrame:CGRectMake(frame.size.width - 85, 5, 30, 30)];
        [addButton setTintColor:[UIColor blackColor]];
        
        [addButton addTarget:self action:@selector(btn_RefreshPropertyInspection:) forControlEvents:UIControlEventTouchUpInside];
        UILongPressGestureRecognizer *longPress = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(longPress:)];
        [addButton addGestureRecognizer:longPress];
        [section_headerView addSubview:addButton];
    }
    
    UIButton *expandButton = [[UIButton alloc] init];
    if (![collapseSections containsObject:@(section)]) {
        [expandButton setImage:[UIImage imageNamed:@"collapse_arrow"] forState:UIControlStateNormal];
    } else {
        [expandButton setImage:[UIImage imageNamed:@"expand_arrow"] forState:UIControlStateNormal];
    }
    
    [expandButton setFrame:CGRectMake(frame.size.width - 47, 8, 25, 25)];
    expandButton.tag = section;
    [expandButton addTarget:self action:@selector(expandSection:) forControlEvents:UIControlEventTouchUpInside];
    [section_headerView addSubview:expandButton];
    
    [section_headerView setBackgroundColor:[UIColor whiteColor]];
    
    [section_headerView addSubview:title];
    
    UIButton *viewBtn = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, iWidth - 100, 40)];
    [viewBtn addTarget:self action:@selector(expandSectionByTitle:) forControlEvents:UIControlEventTouchUpInside];
    viewBtn.tag = section;

    [section_headerView addSubview:viewBtn];
    
    return section_headerView;

}

- ( CGFloat )tableView:( UITableView *)tableView heightForHeaderInSection:( NSInteger )section {
    if (section == 0 || section == 1)
        return 0;
    return 40 ;
}

- (NSString *)tableView:(UITableView *)tableView  titleForHeaderInSection:(NSInteger)section {
    return self.sortedKeys[section];
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section == 0) {
       // O_Photo *oPhoto = [db_Media GetPhoto:oAsset.iCustomerID];
        if (oPropertyPhoto != nil && oPropertyPhoto.sFile != nil) {
            return 1;
        }
        return 0;
    }

    if (section == 1) return 1;

    if ([collapseSections containsObject:@(section)]) return 0;

    if (section == 2 && self.arrSchedule.isEmpty) return 1;

    NSArray *listData = self.tableContents[self.sortedKeys[section]];
    return [listData count];
}

- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    return 55;
}

- (int)getRowCount:(NSString*)str Font:(UIFont*)font
{
    CGFloat width = [self widthOfString:str withFont:font];
    int row = ceil(width/(iWidth-32));
    return row;
}

- (CGFloat)widthOfString:(NSString *)string withFont:(UIFont *)font {
    NSDictionary *attributes = @{NSFontAttributeName: font};
    return [[[NSAttributedString alloc] initWithString:string attributes:attributes] size].width;
}

-(void)tapDetected:(UITapGestureRecognizer *)gr{
    UIImageView *theTappedImageView = (UIImageView *)gr.view;
    NSInteger tag = theTappedImageView.tag;
    [Navigator PushDisplayImageFileID: (int)tag];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    @try {
        @weakify(self)
        if ([indexPath section] == 0) {
            if ([CommonHelper IF_FileExist:oPropertyPhoto.sFile]) {
                AssetImageViewCell *cell = [tableView dequeueReusableCellWithIdentifier:IMAGE_CELL_IDENTIFIER];
                if (cell == nil) {
                    NSArray *nib = [[NSBundle mainBundle] loadNibNamed:@"AssetImageViewCell" owner:self options:nil];
                    cell = nib.lastObject;
                }
                cell.accessoryType = UITableViewCellAccessoryNone;

                // O_Photo *oPhoto = [db_Media GetPhoto:oAsset.iCustomerID];
                if (oPropertyPhoto != nil && oPropertyPhoto.sFile != nil && [CommonHelper IF_FileExist:oPropertyPhoto.sFile]) {
                    cell.assetImageView.image = [UIImage imageWithContentsOfFile:[CommonHelper IF_FilePath:oPropertyPhoto.sFile]];
                    UITapGestureRecognizer *singleTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapDetected:)];

                    singleTap.numberOfTapsRequired = 1;
                    [cell.assetImageView setTag:oPropertyPhoto.iFileID];
                    [cell.assetImageView setUserInteractionEnabled:YES];
                    [cell.assetImageView addGestureRecognizer:singleTap];
                    cell.assetImageView.layer.cornerRadius = 5.0;
                    // cell.assetImageView.layer.borderColor = [[UIColor lightGrayColor] CGColor];
                    //cell.assetImageView.layer.borderWidth = 2.0;
                    cell.assetImageView.layer.masksToBounds = YES;
                    cell.assetImageView.contentMode = UIViewContentModeScaleAspectFill;
                }
                cell.selectionStyle = UITableViewCellSelectionStyleNone;

                CGRect scrRect = [[UIScreen mainScreen] bounds];
                CGFloat size = scrRect.size.width < scrRect.size.height ? scrRect.size.width : scrRect.size.height;
                CGFloat cHeight = size / 16 * 9 - 15;
                cell.imageViewHeight.constant = cHeight;

                return cell;
            } else {
                UITableViewCell *cell = [tableView
                        dequeueReusableCellWithIdentifier:@"DisplayIdentifier"];
                if (cell == nil) {
                    cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault
                                                  reuseIdentifier:@"DisplayIdentifier"];
                }
                cell.textLabel.text = @"Pull down to view Asset Photo";
                [cell.textLabel setFont:[UIFont italicSystemFontOfSize:14]];
                [cell.textLabel setTextAlignment:NSTextAlignmentCenter];
                [cell.textLabel setTextColor:[[if_AppDelegate GetGlobalVar] valueForKey:@"lbl_Gray"]];
                cell.accessoryType = UITableViewCellAccessoryNone;
                cell.selectionStyle = UITableViewCellSelectionStyleNone;
                [cell setBackgroundColor:[UIColor colorWithRed:224 / 255.0 green:224 / 255.0 blue:224 / 255.0 alpha:.5]];

                return cell;
            }
        }

        if ([indexPath section] == 1) {
            if ([indexPath row] == 0) {
#ifndef NO_SCHEDULE
                static NSString *CellIdentifier = ASSET_CELL_NO_SCHEDULE_IDENTIFIER;

                AssetInfoCellWithoutSchedule *cell = (AssetInfoCellWithoutSchedule *) [tableView dequeueReusableCellWithIdentifier:CellIdentifier];
                if (cell == nil) {
                    NSArray *nib = [[NSBundle mainBundle] loadNibNamed:@"AssetInfoCellWithoutSchedule" owner:self options:nil];
                    cell = nib.lastObject;
                }
#else
                static NSString *CellIdentifier = ASSET_CELL_IDENTIFIER;
                
                AssetInfoCell *cell = (AssetInfoCell *)[tableView dequeueReusableCellWithIdentifier:CellIdentifier];
                if (cell == nil)
                {
                    NSArray *nib = [[NSBundle mainBundle] loadNibNamed:@"AssetInfoCell" owner:self options:nil];
                    cell = nib.lastObject;
                }
#endif
                // Configure if able to new start inspection
                cell.isAvailableNewInspection = [CommonPermission bEnableNewInspection];

                //Display Rooms Or Apartments if OAsset has children
                if (iChildCount > 0 || oAsset.bPush) {
                    if (oAsset.iSPAssetID > 0) {
                        [cell.btnApartments setTitle:@"ROOMS" forState:UIControlStateNormal];
                    } else {
                        [cell.btnApartments setTitle:@"UNITS" forState:UIControlStateNormal];
                    }

                    cell.apartmentsStackView.hidden = NO;
                    cell.btnApartments.onTapped = ^(id  _Nullable sender) {
                        @strongify(self)
                        [self btn_Asset2_2nd:sender];
                    };
                    cell.ivRoomIndicator.onTapped = ^(id  _Nullable sender) {
                        @strongify(self)
                        [self btn_Asset2_2nd:sender];
                    };
                } else {
                    cell.apartmentsStackView.hidden = YES;
                    cell.btnApartments.onTapped = nil;
                    cell.ivRoomIndicator.onTapped = nil;
                }
                
                // Tasks
                cell.tasksStackView.hidden = ![CommonPermission bEnableTasks];
                if (!cell.tasksStackView.isHidden) {
                    [cell.btnAssetTasks setTitle:@"TASKS" forState:UIControlStateNormal];
                    cell.btnAssetTasks.onTapped = ^(id  _Nullable sender) {
                        @strongify(self)
                        [self onTasksButtonTapped:sender];
                    };
                    cell.btnTasksIndicator.onTapped = ^(id  _Nullable sender) {
                        @strongify(self)
                        [self onTasksButtonTapped:sender];
                    };
                } else {
                    cell.btnTasksIndicator.onTapped = nil;
                    cell.btnAssetTasks.onTapped = nil;
                }

                //#ifdef TEST_MODE
                cell.addressLab.text = [NSString stringWithFormat:@"%@, %@", sAddress1, sAddress2];
                //#else
                //            cell.addressLab.text = [NSString stringWithFormat:@"%@, %@", sAddress1, sAddress2];
                //#endif
                NSString *sLastInspection = [CommonJson GetJsonKeyValue:@"SUBLBL" sJson:oAsset.sCustom1];
                NSString *sTitleID = [CommonJson GetJsonKeyValue:@"SUBT" sJson:oAsset.sCustom1];

                if (sLastInspection != nil && sLastInspection.length > 0) {
                    //sLastInspection = [NSString stringWithFormat:@"Last Inspection: %@", sLastInspection];
                    if (sTitleID == nil || sTitleID.length == 0) {
                        cell.lastInspectionLab.text = [NSString stringWithFormat:@"Last Inspection: %@", sLastInspection];
                    } else {
                        cell.lastInspectionLab.text = sLastInspection;

                    }
                } else {
                    if (sTitleID == nil || sTitleID.length == 0) {
                        cell.lastInspectionLab.text = @"Not inspected previously";
                    } else {
                        cell.lastInspectionLab.text = @"";
                    }
                }
                NSString *sInspectorName = [CommonJson GetPropertyManager:oAsset.iCustomerID];
                cell.assignedNameLab.text = [NSString stringWithFormat:@"Manager: %@", sInspectorName];
                // cell.lastInspectionLab.text = ;

                [cell.btnMap addTarget:self action:@selector(showMap:) forControlEvents:UIControlEventTouchUpInside];
                [cell.scheduleBtn addTarget:self action:@selector(startSchedule:) forControlEvents:UIControlEventTouchUpInside];
                [cell.startInspectionBtn addTarget:self action:@selector(startInspection:) forControlEvents:UIControlEventTouchUpInside];
                cell.accessoryType = UITableViewCellAccessoryNone;

                cell.selectionStyle = UITableViewCellSelectionStyleNone;

                return cell;
            } else {
                UITableViewCell *cell = [tableView
                        dequeueReusableCellWithIdentifier:@"DisplayIdentifier"];
                if (cell == nil) {
                    cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault
                                                  reuseIdentifier:@"DisplayIdentifier"];
                }
                NSString *btnTitle = [self.tableContents[self.sortedKeys[[indexPath section]]] objectAtIndex:[indexPath row]];
                CALayer *TopBorder = [CALayer layer];
                TopBorder.frame = CGRectMake(0.0f, 0.0f, [if_AppDelegate GetDeviceWidth], 0.5f);
                TopBorder.backgroundColor = [UIColor lightGrayColor].CGColor;
                [cell.layer addSublayer:TopBorder];

                cell.textLabel.text = btnTitle;
                cell.textLabel.lineBreakMode = NSLineBreakByWordWrapping;
                cell.textLabel.numberOfLines = 0;
                cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
                return cell;
            }
        } else {
            InspectionCell *cell = [tableView dequeueReusableCellWithIdentifier:INSPECTION_CELL_IDENTIFIER forIndexPath:indexPath];
            if (cell == nil) {
                NSArray *nib = [[NSBundle mainBundle] loadNibNamed:@"InspectionCell" owner:self options:nil];
                cell = nib.lastObject;
            }
            // Configure the asset schedule cell...
            cell.accessoryView = nil;
            if (indexPath.section == 2) {
                NSArray<v_Schedule *> *schedules = self.tableContents[self.sortedKeys[indexPath.section]];
                if (schedules.isNotEmpty) {
                    v_Schedule *oSchedule = schedules[indexPath.row];
                    cell.titleLab.textColor = [UIColor color_3E6BFF];
                    [cell.titleLab setTextAndVisibility:oSchedule.sInspectorName];
                    [cell.detailsLab setTextAndVisibility:[NSString stringWithFormat:@"%@ %@", oSchedule.sInsTitle, oSchedule.sFormattedDate]];
                    cell.viewStatus.hidden = NO;
                    cell.topMargin.constant = TOP_MARGIN_SMALL;
                    cell.bottomMargin.constant = TOP_MARGIN_SMALL;
                    cell.accessIndicator.hidden = NO;
                    cell.viewStatus.backgroundColor = PROGRESS_COLOR;
                    cell.accessIndicator.image = [UIImage imageNamed:@"ic_forward"];
                } else {
                    cell.titleLab.textColor = [UIColor colorFromHexString:@"#4A4A4A"];
                    [cell.titleLab setTextAndVisibility: self.isLoadingAssetSchedules ? @"Loading..." : @"No Asset Schedules."];
                    [cell.detailsLab setTextAndVisibility:nil];
                    cell.viewStatus.hidden = YES;
                    cell.topMargin.constant = TOP_MARGIN_LARGE;
                    cell.bottomMargin.constant = TOP_MARGIN_LARGE;
                    cell.accessIndicator.hidden = YES;
                }
            } else {
                // Configure the inspection cell...
                O_InspectionTemp *oInsTemp = self.tableContents[self.sortedKeys[indexPath.section]][indexPath.row];
                [cell.titleLab setTextAndVisibility:[oInsTemp sTitle]];
                cell.titleLab.textColor = [UIColor colorFromHexString:@"#4A4A4A"];
                [cell.detailsLab setTextAndVisibility:(oInsTemp.dtStartDate == nil && oInsTemp.dtEndDate == nil) ?
                        @"" : [NSString stringWithFormat:@"%@  %@ - %@", oInsTemp.sInsTitle,
                                oInsTemp.dtStartDate == nil ? @"?" : oInsTemp.dtStartDate,
                                oInsTemp.dtEndDate == nil ? @"?" : oInsTemp.dtEndDate]];
                cell.topMargin.constant = TOP_MARGIN_SMALL;
                cell.bottomMargin.constant = TOP_MARGIN_SMALL;
                cell.viewStatus.hidden = NO;
                if (oInsTemp.bEmpty) {
                    cell.accessIndicator.hidden = YES;
                    cell.viewStatus.hidden = YES;
                    cell.topMargin.constant = TOP_MARGIN_LARGE;
                    cell.bottomMargin.constant = TOP_MARGIN_LARGE;
                } else if ([indexPath section] == 5) {
                    cell.accessIndicator.hidden = YES;
                    cell.viewStatus.backgroundColor = UPLOADED_COLOR;
                    if ([oInsTemp.sStatus length] > 0) {
                        cell.accessoryView = [[UI_StatusButton alloc]
                                initWithFrame:CGRectMake(0, 0, 40, 40)
                                        sText:oInsTemp.sStatus
                                       oColor:[UIColor colorFromHexString:oInsTemp.sStatus_Color]];
                    } else {
                        cell.accessIndicator.hidden = NO;
                        cell.accessIndicator.image = [UIImage imageNamed:@"ic_check"];
                    }
                } else if ([indexPath section] == 4) {
                    cell.accessIndicator.hidden = YES;
                    cell.viewStatus.backgroundColor = COMPLETED_COLOR;
                    UIButton *oButton = [UIButton buttonWithType:UIButtonTypeCustom];
                    [oButton setFrame:CGRectMake(0, 0, 32, 32)];
                    [oButton setContentEdgeInsets:UIEdgeInsetsMake(4, 4, 4, 4)];
                    [oButton setTag:oInsTemp.iInsID];
                    [oButton setImage:[UIImage imageNamed:@"icon_upload"] forState:UIControlStateNormal];
                    [oButton addTarget:self action:@selector(btn_Upload_1:) forControlEvents:UIControlEventTouchUpInside];
                    cell.accessoryView = oButton;
                } else {
                    cell.viewStatus.backgroundColor = PROGRESS_COLOR;
                    if (oInsTemp.iSInsID > 0) {
                        cell.accessIndicator.hidden = NO;
                        cell.accessIndicator.image = [UIImage imageNamed:@"ic_check"];
                    } else {
                        cell.accessIndicator.hidden = NO;
                        cell.accessIndicator.image = [UIImage imageNamed:@"ic_forward"];
                    }
                }
            }
            return cell;
        }
    }
    @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
    return nil;
}

-(IBAction)btn_Asset2_2nd:(UIButton *)sender{
    if_Assets_2nd *oAssets = [self.storyboard instantiateViewControllerWithIdentifier:@"Assets_2nd"];
    oAssets.iPAssetID = iSAssetID;
    oAssets.sTitle = sAddress1;
    [self.navigationController pushViewController:oAssets animated:YES];
}

-(void)btn_Upload_1:(UIButton *)sender {
    if_HomeScreen *homeScreen = [self.navigationController latestViewControllerWithItsClass:if_HomeScreen.class];
    if (homeScreen != nil) {
        @weakify(self)
        [homeScreen Upload_Action:(int) sender.tag completion:^{
            @strongify(self)
            [self ReloadTable];
        }];
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    @try {
        [tableView deselectRowAtIndexPath:indexPath animated:YES];
        if ([indexPath section] == 1){
            if ([indexPath row] == 0){
                if_AssetDetail *oDetail = [self.storyboard instantiateViewControllerWithIdentifier:@"AssetDetail"];
                oDetail.iSAssetID = iSAssetID;
                [self.navigationController pushViewController:oDetail animated:YES];
            }
        }
        else if ([indexPath section] == 2) {
            v_Schedule *vSchedule = [self.arrSchedule safe_objectAtIndex:indexPath.row];
            if (vSchedule != nil) {
                [Navigator showsActionSheetForSchedule:vSchedule
                                            sourceView:[tableView cellForRowAtIndexPath:indexPath]
                               inspectionActionHandler:nil];
            }
        }
        else if ([indexPath section] == 3){
            O_InspectionTemp *oTemp = arrCurrentIns[[indexPath row]];
            if (oTemp.iInsID > 0 ){
                [self PushInspection:oTemp.iInsID];
            }
        }
        else if ([indexPath section] == 4){
            O_InspectionTemp *oTemp = arrCompletedIns[[indexPath row]];
            if (oTemp.iInsID > 0){
                [self PushInspection:oTemp.iInsID];
            }
        }
        else if ([indexPath section] == 5){
            O_InspectionTemp *oTemp = arrUploadedIns[[indexPath row]];
            if (oTemp.iSInsID > 0){
                [self ViewReport:oTemp.iSInsID sTitle:oTemp.sTitle sInsTitle:oTemp.sInsTitle oRect:[[tableView cellForRowAtIndexPath:indexPath] bounds]  oView:[tableView cellForRowAtIndexPath:indexPath] oInsTemp:oTemp];
                
            }
        }
    }
    @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
}

- (void)showMap:(UIButton*)sender {
    if_AssetRoute *oDetail = [self.storyboard instantiateViewControllerWithIdentifier:@"AssetRoute"];
    oDetail.address = sAddress1;
    oDetail.iSAssetID = iSAssetID;
    [self.navigationController pushViewController:oDetail animated:YES];
}

- (void)startSchedule:(UIButton*)sender {
 
}

- (void)startInspection:(UIButton*)sender {
    [self ReloadTable];
    [self Start_Inspection:sender];
}

-(void)ViewReport:(int)iInsID sTitle:(NSString *)sTitle sInsTitle:(NSString *)sInsTitle oRect:(CGRect)oRect oView:(UIView *)oView
         oInsTemp:(O_InspectionTemp *)oInsTemp{
    if ([self.navigationController.visibleViewController isKindOfClass:[UIAlertController class]]) {
        return;
    }
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:sTitle message:@"" preferredStyle:UIAlertControllerStyleActionSheet];
    
    UIAlertAction *oViewPDF = [UIAlertAction actionWithTitle:@"View PDF" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
         [CommonHelper_SendReport ViewReport_Internal:nil oVC:self iInsID:iInsID sTitle:sTitle sType:@"P"];
    }];
    [alert addAction:oViewPDF];
    

    if (![CommonHelper bExternalProperty:oAsset.sCustom2]){
        UIAlertAction *oSendPDF = [UIAlertAction actionWithTitle:@"Send PDF" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){

            [CommonHelper_SendReport SendReport_Internal:nil oVC:self iInsID:iInsID sTitle:oInsTemp.sTitle sType:@"P" iSAssetID:oAsset.iSAssetID];
            
        }];
        [alert addAction:oSendPDF];
        if ([CommonUI bEnableWord]){
        
        UIAlertAction *oSendWord = [UIAlertAction actionWithTitle:@"Send Word Doc" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
            [CommonHelper_SendReport SendReport_Internal:nil oVC:self iInsID:iInsID sTitle:oInsTemp.sTitle sType:@"D" iSAssetID:oAsset.iSAssetID];
            
            
        }];
        [alert addAction:oSendWord];
        }
        //if ()
        
        UIAlertAction *oComment = [UIAlertAction actionWithTitle:@"Comments" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {

            //push
//            [if_InspectionComment push:self.storyboard fromController:self data:iInsID];
            
            //present
            if (![if_AppDelegate.shared bInternet_WithMessage:@"Please connect to Internet."]){
                
                return;
            }
            [if_InspectionComment present:self.storyboard fromController:self data:iInsID];
        }];
        [alert addAction:oComment];
        
        if (oInsTemp.bReview){
            if (!oInsTemp.bChild){
                
                
                /*UIAlertAction *oCopyInspection= [UIAlertAction actionWithTitle:@"Start Review" style:UIAlertActionStyleDestructive handler:^(UIAlertAction *action){
                    
                    [self CopyInspectionAlert:oView oRect:oRect iInsID:iInsID bMediaTransfer:true];
                    
                }];
                [alert addAction:oCopyInspection]; */
                
                UIAlertAction *oEditInspection= [UIAlertAction actionWithTitle:@"Review" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
                    if (![if_AppDelegate.shared bInternet_WithMessage:@"Please connect to Internet."]){
                        
                        return;
                    }
                   [self EditInspection:oInsTemp];
                    
                }];
                [alert addAction:oEditInspection];
            }
        }
        else if (oInsTemp.bChild){
            
        }
        else{
            if ([CommonPermission bEnableNewInspection] && [CommonPermission bEnableCopyInspection]) {
                UIAlertAction *oCopyInspection_Popup = [UIAlertAction actionWithTitle:@"Copy Inspection" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
                    if (![if_AppDelegate.shared bInternet_WithMessage:@"Please connect to Internet."]){
                        
                        return;
                    }
                    UIAlertController *oCopyInspectionPopupViewController = [UIAlertController alertControllerWithTitle:@"Copy Inspection" message:@"Create a new inspection based on selected inspection with options to include/exclude photos" preferredStyle:UIAlertControllerStyleAlert];
                    
                    UIAlertAction *oCopyInspection_Empty = [UIAlertAction actionWithTitle:@"Copy Inspection" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
                        
                        [self CopyInspectionAlert:oView oRect:oRect iInsID:iInsID bMediaTransfer:false];
                        
                    }];
                    [oCopyInspectionPopupViewController addAction:oCopyInspection_Empty];
                    
                    UIAlertAction *oCopyInspection= [UIAlertAction actionWithTitle:@"Copy Inspection (With Photos)" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
                        
                        [self CopyInspectionAlert:oView oRect:oRect iInsID:iInsID bMediaTransfer:true];
                        
                    }];
                    [oCopyInspectionPopupViewController addAction:oCopyInspection];
                    UIAlertAction* oCopyInspection_Cancel = [UIAlertAction
                                                    actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel
                                                    handler:^(UIAlertAction * action) {}];
                    [oCopyInspectionPopupViewController addAction:oCopyInspection_Cancel];
                    [self presentViewController:oCopyInspectionPopupViewController animated:YES completion:nil];
                    
                }];
                [alert addAction:oCopyInspection_Popup];
            }
            
            if ([CommonPermission bEnableEditInspection]) {
                UIAlertAction *oEditInspection= [UIAlertAction actionWithTitle:@"Edit Inspection" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
                    if (![if_AppDelegate.shared bInternet_WithMessage:@"Please connect to Internet."]){
                        
                        return;
                    }
                    [self EditInspection:oInsTemp];
                    
                }];
                [alert addAction:oEditInspection];
            }
            
        }
        if ([oInsTemp.sChild length] > 0){
            UIAlertAction *oCopyInspection= [UIAlertAction actionWithTitle:@"Inspection History" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
                if (![if_AppDelegate.shared bInternet_WithMessage:@"Please connect to Internet."]){
                    
                    return;
                }
                [self CopyInspectionAlert:oView oRect:oRect iInsID:iInsID bMediaTransfer:true];
                
            }];
            [alert addAction:oCopyInspection];
        }
       
    }
    if ([CommonPermission bEnableEditInspection] && [CommonHelper bStatus]) {
        @weakify(self)
        UIAlertAction *oCopyInspection = [UIAlertAction actionWithTitle:@"Update Status" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
            @strongify(self)
            if (![if_AppDelegate.shared bInternet_WithMessage:@"Please connect to Internet."]) return;

            [CommonUI updateInspectionStatus:oInsTemp.iSInsID
                          fromViewController:self
                                  sourceView:oView
                                  completion:^(NSString *sCustom1, NSString *status, NSString *colorCode) {
                                      O_Inspection *oIns = [db_Inspection GetInspectionBySInsID:(int)oInsTemp.iSInsID];
                                      oIns.sCustom1 = sCustom1;
                                      [db_Inspection UpdateInspectionCustom:oIns];
                                      oInsTemp.sStatus = status;
                                      oInsTemp.sStatus_Color = colorCode;
                                      [self ReloadTable];
                                  }];
        }];
        [alert addAction:oCopyInspection];
    }
    
    
    UIAlertAction *oClose = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil];
    [alert addAction:oClose];
    
    if ([if_AppDelegate biPad]){
        [alert setModalPresentationStyle:UIModalPresentationFullScreen];
        UIPopoverPresentationController *popPresenter = [alert
                                                         popoverPresentationController];
        // popPresenter.sourceView = button;
        popPresenter.sourceView = oView;
        popPresenter.sourceRect = oRect;
        
        [self presentViewController:alert animated:YES completion:nil];
    }
    else{
        [self presentViewController:alert animated:YES completion:nil];
    }
    
}

-(void)EditInspection:(O_InspectionTemp *)oInsTemp {
    @try {
        if (![if_AppDelegate.shared bInternet:false]) return;
        [CommonInspection downloadInspectionAndContinueWithISInsID:(int)oInsTemp.iSInsID bComplete:NO bSynced:NO fromViewController:self completion:nil];
    }
    @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
}

-(void)CopyInspectionAlert:(UIView *)oView oRect:(CGRect) oRect iInsID:(int)iSInsID bMediaTransfer:(bool)bMediaTransfer{
    if (![self validateNewInspection]) {
        return;
    }
    
    O_Inspection *oTemp = [db_Inspection GetInspectionBySInsID:iSInsID];
    
    arrInsType = [db_Common GetIntTypes];
    NSMutableArray *filteredInsTypes = [NSMutableArray array];
    @weakify(self)
    for (O_InsType *oInsType in arrInsType) {
        if ([oInsType.sPTC isEqualToString:oTemp.sPTC] && [oInsType.sType isEqualToString:oTemp.sType]){
            [filteredInsTypes addObject:oInsType];
        }
    }
    
    [InsTypeSelectionView show:filteredInsTypes didSelectOption:^(O_InsType * _Nonnull oInsType) {
        @strongify(self)
        @try {
            if (![if_AppDelegate.shared bInternet:false])  return;
            
            MBProgressHUD *hud = [[MBProgressHUD alloc] si_initWithView:self.view];
            [self.view addSubview:hud];
            hud.labelText = @"Processing...";
            
            [hud showAnimated:YES whileExecutingBlock:^{
                [hud setDetailsLabelText:@"Connecting to Server..."];
                [db_SetupInspection DuplicateInspection:iSInsID
                                               oInsType:oInsType
                                                 bPhoto:bMediaTransfer
                                                   sLat:currentLatitude
                                                  sLong:currentLongitude
                                             completion:^(int iInsID, NSString *sMessage) {
                    @strongify(self)
                    if (iInsID > 0) {
                        [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                            [self PushInspection:iInsID];
                        }];
                    } else if (sMessage != nil) {
                        [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                            [self ShowAlert:@"Error" sMessage:sMessage];
                        }];
                    }
                }];
                
            } completionBlock:^{
                [hud removeFromSuperview];
            }];
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }
        
    }];
}

-(void)SendSeverEmail:(NSString *)to withSubject:(NSString *)subject withBody:(NSString *)body insID:(int)iInsID{
    @try {
        if_ServerEmail *vc = [self.storyboard instantiateViewControllerWithIdentifier:@"if_ServerEmail"];
        /*   NSMutableArray *oArray= [[NSMutableArray alloc] init];
         if ([to containsString:@","]){
         oArray = [NSMutableArray arrayWithArray: [to componentsSeparatedByString:@","]];
         @try{
         [oArray removeObjectIdenticalTo:[NSNull null]];
         }@catch(NSException *ex){
         
         }
         }
         else{
         oArray = [NSMutableArray arrayWithArray: [to componentsSeparatedByString:@";"]];
         @try{
         [oArray removeObjectIdenticalTo:[NSNull null]];
         }@catch(NSException *ex){
         
         }
         }*/
        [vc setToRecipients:to];
        //[mailController setToRecipients:[[NSArray alloc] initWithObjects:@"<EMAIL>", nil] ];
        [vc setSubject:subject];
        [vc setBody:body  isHTML:YES];
        [vc setInsID:iInsID];
        [self.navigationController pushViewController:vc animated:YES];
    }
    @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
    
    
}

-(void)SendEmail:(NSString *)to withSubject:(NSString *)subject withBody:(NSString *)body{
    @try {
        UINavigationBar.appearance.barTintColor =  [UIColor colorWithRed:78/255.0f green:105/255.0f blue:255/255.0f alpha:1];
        MFMailComposeViewController *mailController = [[MFMailComposeViewController alloc] init];
        [mailController setToRecipients:[to componentsSeparatedByString:@";"]];
        [[mailController navigationBar] setTintColor: [UIColor blackColor]];
     //   [mailController navigationBar] setColo
        //[mailController setToRecipients:[[NSArray alloc] initWithObjects:@"<EMAIL>", nil] ];
        [mailController setSubject:subject];
        [mailController setMessageBody:body isHTML:YES];
        
        mailController.mailComposeDelegate = (id)self;

        mailController.navigationBar.titleTextAttributes = [NSDictionary dictionaryWithObject:[UIColor whiteColor] forKey: NSForegroundColorAttributeName];
        // UINavigationController *myNavController = [self navigationController];
        
        if ( mailController != nil ) {
            if ([MFMailComposeViewController canSendMail]){
                //[self ]
                [self.navigationController presentViewController:mailController animated:YES completion:nil];
            }
        }
    }
    @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
    
    
}

-(void)mailComposeController:(MFMailComposeViewController*)controller didFinishWithResult:(MFMailComposeResult)result error:(NSError*)error {
    // [self dismissModalViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}
- (void)pickerView:(UIPickerView *)pickerView didSelectRow: (NSInteger)row inComponent:(NSInteger)component {
}

- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component {
    return [arrInsType count];
}

- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
    return 1;
}

- (NSString *)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component {
    O_InsType *oInsType = [arrInsType objectAtIndex:row];
    return oInsType.sInsTitle;
}

-(void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray<CLLocation *> *)locations{
    
    [self.locationManager stopUpdatingLocation];
    CGFloat usersLatitude = self.locationManager.location.coordinate.latitude;
    CGFloat usersLongidute = self.locationManager.location.coordinate.longitude;
    
    currentLatitude = usersLatitude;
    currentLongitude = usersLongidute;
}

// MARK: - Load schedule
- (void)loadAssetSchedules {
    if (self.isLoadingAssetSchedules) return;
    if (![NetworkConnection isNetworkReachable]) {
        safe_dispatch_main_async(^{
            self.isLoadingAssetSchedules = NO;
            [self ReloadTable];
        })
        return;
    }
    // Load asset schedules
    self.isLoadingAssetSchedules = YES;
    dispatch_background_async((^{
        NSMutableDictionary *params = [@{
            @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
            @"sToken": [CommonUser currentToken],
            @"iAssetID": [NSString stringWithFormat:@"%ld", iSAssetID]
        } mutableCopy];
        NSDictionary *schedulesReturn = [IFConnection PostRequest:EndpointGetAssetSchedule oParams:params];
        if ([schedulesReturn[@"success"] boolValue]) {
            DLog(@"[POST] request: %@", schedulesReturn);
            self.isFetchedAssetSchedules = YES;
            self.arrSchedule = [self parseSchedules:schedulesReturn[@"lsSchedule"]];
        }
        safe_dispatch_main_async(^{
            self.isLoadingAssetSchedules = NO;
            [self ReloadTable];
        })
    }));
}

- (NSArray<v_Schedule *> *)parseSchedules:(NSArray *)schedulesResults {
    if (!schedulesResults || schedulesResults.count == 0) return @[];
    return [schedulesResults map:^id(NSDictionary *scheduleData) {
        return [[v_Schedule alloc] initWithDictionary:scheduleData];
    }];
}

#pragma mark - Tasks

- (void)onTasksButtonTapped: (id)sender {
    // Navigate to asset tasks if this detail is pushed from asset detail
    if ([self.navigationController latestViewControllerWithItsClass:if_AssetTasks.class]) {
        [self.navigationController popToLatestViewControllerKindOf:if_AssetTasks.class animated:YES];
    } else {
        if_AssetTasks *vc = [if_AssetTasks buildWithAssetID:oAsset.iSAssetID];
        [self.navigationController pushViewController:vc animated:YES];
    }
}

@end
