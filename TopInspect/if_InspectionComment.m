//
//  if_InspectionComment.m
//  SnapInspect3
//
//  Created by <PERSON><PERSON><PERSON> on 1/15/18.
//  Copyright © 2018 SnapInspect. All rights reserved.
//

#import "CellCommentsText.h"
#import "CellCommentsPhoto.h"
#import "CommonRequest.h"
#define PLACEFOLDER_TEXT @"Type Reply..."
#define TEXTCELLID @"CellCommentsTextId"
#define PHOTOCELLID @"CellCommentsPhotoId"
#define I_INSID_KEY @"iInsIdForComment"

@interface if_InspectionComment () <UITableViewDelegate, UITableViewDataSource, UITextViewDelegate, UIImagePickerControllerDelegate, UINavigationControllerDelegate>

@end

@implementation if_InspectionComment {
    CGFloat textViewHeightConstraintValue;
    O_Inspection *oIns;
    NSMutableArray *lsComments;
    NSMutableDictionary *mapComments;
    MBProgressHUD *hud;
    CGFloat iWidth;
    NSMutableArray *lsSelectedusers;
    NSArray *keys;
    UIRefreshControl *refreshControl;
    NSArray *users;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    _iSInsID = [CommonHelper getPrefInt:I_INSID_KEY defaultValue:0];
    
   oIns = [db_Inspection GetInspectionBySInsID:_iSInsID];
    //oIns = [db_Inspection GetInspection:iInsID];
    if (oIns != nil){
        self.navigationItem.title = oIns.sTitle;
        self.lblTitle.text = oIns.sInsTitle;
    }

/// Initialize
    lsComments = [[NSMutableArray alloc] init];
    mapComments = [[NSMutableDictionary alloc] init];
    keys = [[NSArray alloc] init];
    
    hud = [[MBProgressHUD alloc] si_initWithView:self.view];
    CGSize oSize = [[UIScreen mainScreen] bounds].size;
    iWidth = oSize.width;
    
/// Comment Text Configure
    self.txtNote.layer.cornerRadius = 4;
    self.txtNote.layer.borderColor = [UIColor colorWithRed:209/255.0 green:209/255.0 blue:209/255.0 alpha:1.0].CGColor;
    self.txtNote.layer.borderWidth = 1;
    self.txtNote.textContainerInset = UIEdgeInsetsMake(10, 0, 6, 0);
    self.txtNote.delegate = self;

    textViewHeightConstraintValue = self.txtNoteHeight.constant;
    [self resetTextViewNote];
    
/// TableView Configure
    self.tvComments.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
    self.tvComments.rowHeight = UITableViewAutomaticDimension;
    self.tvComments.delegate = self;
    self.tvComments.dataSource = self;
    
    [_tvComments registerNib:[UINib nibWithNibName:@"CellCommentsText" bundle:nil] forCellReuseIdentifier:TEXTCELLID];
    [_tvComments registerNib:[UINib nibWithNibName:@"CellCommentsPhoto" bundle:nil] forCellReuseIdentifier:PHOTOCELLID];
    
    refreshControl = [[UIRefreshControl alloc]init];
    refreshControl.backgroundColor = [UIColor lightGrayColor];
    refreshControl.tintColor = [UIColor whiteColor];
    [_tvComments addSubview:refreshControl];
    [refreshControl addTarget:self action:@selector(refreshTable) forControlEvents:UIControlEventValueChanged];
    
    users = [CommonJson GetAllUsers];
    
    [self refreshTable];

}

- (void)dealloc {
    [CommonHelper savePrefInt:I_INSID_KEY value:0];
}

- (void)reloadWithNewInsID {
    oIns = [db_Inspection GetInspectionBySInsID:_iSInsID];
    if (oIns != nil){
        self.navigationItem.title = oIns.sTitle;
        self.lblTitle.text = oIns.sInsTitle;
    }
    
    [self refreshTable];
}

- (void)refreshTable {
    [self getAllInspectionComments];
}

-(void) willRotateToInterfaceOrientation:(UIInterfaceOrientation)toInterfaceOrientation duration:(NSTimeInterval)duration {
    CGSize oSize = [[UIScreen mainScreen] bounds].size;
    iWidth = oSize.height;
    [_tvComments reloadData];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    /// Notification
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardDidShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardDidHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
    
}
- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillHideNotification object:nil];
}

- (void)keyboardDidShow: (NSNotification *) notification {
    CGFloat bottom = self.inputViewBottom.constant;
    DLog(@"%f", bottom);
    CGSize keyboardSize = [[notification userInfo][UIKeyboardFrameEndUserInfoKey] CGRectValue].size;
    if (keyboardSize.height > 0) {
        self.inputViewBottom.constant = keyboardSize.height;
        [UIView animateWithDuration:0.25 animations:^{
            [self.view layoutIfNeeded];
            [self scrollToBottom];
        }];
    }
}

- (void)keyboardDidHide: (NSNotification *) notification{
    self.inputViewBottom.constant = 0;
    [UIView animateWithDuration:0.25 animations:^{
       [self.view layoutIfNeeded];
    }];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (IBAction)sendAction:(id)sender {
    [self.view endEditing:YES];

    NSString *comment = _txtNote.text;
    [self sendInspectionComment:comment type:@"c"];
}

- (IBAction)menuAction:(id)sender {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleActionSheet];

    UIAlertAction *btnCamera = [UIAlertAction actionWithTitle:@"Take a Photo" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        [self openCamera];
    }];
    [alert addAction:btnCamera];
    UIAlertAction *btnGallery = [UIAlertAction actionWithTitle:@"Choose from Library" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        [self openLibrary];
    }];
    [alert addAction:btnGallery];
    UIAlertAction *btnCancel = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:^(UIAlertAction *_Nonnull action) {

    }];
    [alert addAction:btnCancel];

    alert.modalPresentationStyle = UIModalPresentationFullScreen;
    alert.popoverPresentationController.sourceView = sender;
    alert.popoverPresentationController.sourceRect = [sender bounds];
    [self presentViewController:alert animated:YES completion:nil];
}

- (IBAction)backAction:(id)sender {
    [self.navigationController popViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}


#pragma Mark -- Photo Attached
- (void) openCamera {
    if (![UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
        [self showAlertDialog:@"Error" Message:@"Device has no camera" withCompletionHandler:nil];
    } else {
        UIImagePickerController *picker = [[UIImagePickerController alloc] init];
        picker.delegate = self;
        picker.allowsEditing = NO;
        picker.sourceType = UIImagePickerControllerSourceTypeCamera;

        [self presentViewController:picker animated:YES completion:NULL];
    }
}

- (void)openLibrary {
    UIImagePickerController *picker = [[UIImagePickerController alloc] init];
    picker.delegate = self;
    picker.allowsEditing = NO;
    picker.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
    
    [self presentViewController:picker animated:YES completion:NULL];
}

#pragma Mark -- UIImagePickerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary *)info {    
    UIImage *chosenImage = info[UIImagePickerControllerOriginalImage];
    UIImage *resizeImg = [self imageWithImage:chosenImage convertToSize:900];
    
    [picker dismissViewControllerAnimated:YES completion:NULL];
    
    NSString *sPath = [[if_AppDelegate GetGlobalVar] valueForKeyPath:@"sFilePath"];
    NSString *sUniqueFileName = [CommonHelper GetUniqueFileName];
    NSString *sPhotoName = [NSString stringWithFormat:@"f_%@.jpg", sUniqueFileName];
    NSString *sPhotoPath = [sPath stringByAppendingPathComponent: sPhotoName];
    [UIImageJPEGRepresentation(resizeImg, 0.5)  writeToFile:sPhotoPath atomically:YES];
    
    O_File *oFile = [[O_File alloc] init];
    oFile.iSObjectID = oIns.iSAssetID;
    oFile.iSFileID = 0;
    oFile.iFileID = 0;
    oFile.sFile = sPhotoName;
    oFile.sLat = @"";
    oFile.sLong = @"";
    oFile.sComments = @"";
    oFile.bUploaded = 0;
    oFile.bDeleted = 0;
    oFile.dtDateTime = [CommonHelper GetDateString:[NSDate new]];
    oFile.iSize = 0;
    oFile.sCustom1 = @"";
    oFile.sCustom2 = @"";
    
    [self uploadCommentMedia:oFile];
}

- (UIImage *)imageWithImage:(UIImage *)image convertToSize:(CGFloat)fitLength {
    CGFloat imageWidth = image.size.width;
    CGFloat imageHeight = image.size.height;
    CGFloat width, height;
    if (imageWidth > imageHeight) {
        width = fitLength;
        height = width * imageHeight / imageWidth;
    } else {
        height = fitLength;
        width = height * imageWidth/imageHeight;
    }
    
    CGSize size = CGSizeMake(width, height);
    
    UIGraphicsBeginImageContext(size);
    [image drawInRect:CGRectMake(0, 0, size.width, size.height)];
    UIImage *destImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return destImage;
}

- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker {
    [picker dismissViewControllerAnimated:YES completion:NULL];
}


- (CGFloat) measureTextHeight:(NSString *)text forTextView:(UITextView *)textView {
    UIEdgeInsets contentInset = textView.contentInset;
    UIEdgeInsets textContainerInsets = textView.textContainerInset;
    CGFloat widthPadding = contentInset.left + contentInset.right + textContainerInsets.left + textContainerInsets.right + textView.textContainer.lineFragmentPadding * 2;
    
    CGSize maximumSize = CGSizeMake(textView.contentSize.width - widthPadding, CGFLOAT_MAX);
    
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc]init];
    paragraphStyle.lineBreakMode = textView.textContainer.lineBreakMode;
    NSDictionary *attributes = @{NSFontAttributeName: textView.font,
                                 NSParagraphStyleAttributeName: paragraphStyle};
    CGRect newSize = [text boundingRectWithSize:maximumSize
                                             options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading
                                          attributes:attributes
                                             context:nil];
    return ceil(newSize.size.height);
}

- (void) setTextViewHeight:(UITextView *)textView height:(CGFloat)height {
    if (_txtNoteHeight.constant != height) {
        if (height <= _tvComments.bounds.size.height / 3) {
            _txtNoteHeight.constant = height;
        }
        textView.contentSize = CGSizeMake(textView.bounds.size.width, height);
    }
}

- (void) resetTextViewNote {
    _txtNote.text = @"";
    [self setTextViewHeight:_txtNote height:textViewHeightConstraintValue];
    
    _txtNote.text = PLACEFOLDER_TEXT;
    _txtNote.textColor = [UIColor lightGrayColor];
    
    _txtNote.selectedTextRange = [_txtNote textRangeFromPosition:_txtNote.beginningOfDocument toPosition:_txtNote.beginningOfDocument];
    
    [self setButton:_btnSend enabled:NO];
}

- (void)setButton:(UIButton *) button enabled:(BOOL) enabled {
    button.enabled = enabled;
    button.alpha = enabled ? 1.0 : 0.5;
}

#pragma Mark -- UITableViewDelegate
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return keys.count;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
//    CGRect frame = tableView.frame;
    
    UILabel *title = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, iWidth, 40)];
    [title setTextColor:[UIColor colorWithRed:124/255.0 green:149/255.0 blue:163/255.0 alpha:1.0]];
    title.font = [UIFont boldSystemFontOfSize:15];
    title.textAlignment = NSTextAlignmentCenter;
    
    title.text = keys[section];
    
    return title;
}

- ( CGFloat )tableView:( UITableView *)tableView heightForHeaderInSection:( NSInteger )section {
    return 40;
}

- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section {
    return keys[section];
}

- (NSInteger)tableView:(UITableView *)table
 numberOfRowsInSection:(NSInteger)section {
    NSString *key = keys[section];
    NSMutableArray *arry = [mapComments valueForKey:key];
    return arry.count;
}

- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 30;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSString *key = keys[indexPath.section];
    NSMutableArray *array = [mapComments valueForKey:key];

    O_Comment *oComment = array[indexPath.row];
    O_Comment *oBeforeComment = nil;
    if (indexPath.row > 0) {
        oBeforeComment = array[indexPath.row - 1];
    }
    BOOL isHeadShowed = true;
    if (oComment != nil && oBeforeComment != nil) {
        isHeadShowed = oComment.iCustomerID != oBeforeComment.iCustomerID;
    }

    if ([oComment.sType.lowercaseString isEqualToString:@"c"] || [oComment.sType.lowercaseString isEqualToString:@"a"]) {
        CellCommentsText *cell = (CellCommentsText *) [tableView dequeueReusableCellWithIdentifier:TEXTCELLID];
        if (cell == nil) {
            NSArray *nib = [[NSBundle mainBundle] loadNibNamed:@"CellCommentsText" owner:self options:nil];
            cell = nib[0];
        }

        [cell displayTextItem:oComment showHead:isHeadShowed];
        return cell;
    } else {
        CellCommentsPhoto *cell = (CellCommentsPhoto *) [tableView dequeueReusableCellWithIdentifier:PHOTOCELLID];
        if (cell == nil) {
            NSArray *nib = [[NSBundle mainBundle] loadNibNamed:@"CellCommentsPhoto" owner:self options:nil];
            cell = nib[0];
        }

        [cell displayPhotoItem:oComment showHead:isHeadShowed];

        if (oComment.cPhoto != nil) {
            cell.ivPhoto.image = oComment.cPhoto;
        } else {
            cell.loadIndicator.hidden = false;
            [cell.loadIndicator startAnimating];
            dispatch_async(dispatch_get_global_queue(0, 0), ^{
                if (oComment.sDescription != nil) {
                    NSURL *sURL = [[NSURL alloc] init];
                    NSString *url;
                    NSDictionary *oReturn = [CommonRequest SI_GetFile_DownloadURL:(int) [oComment.sDescription intValue]];
                    @try {
                        url = [oReturn valueForKey:@"sDownloadURL"];
                        sURL = [NSURL URLWithString:[oReturn valueForKey:@"sDownloadURL"]];
                    } @catch (NSException *eee) {

                    }

                    ImageLoader *loader = [ImageLoader sharedLoader];
                    dispatch_async(dispatch_get_main_queue(), ^{
                        cell.ivPhoto.image = [UIImage imageNamed:@"Image_PlaceHolder_Full.png"];

                        if (url != nil && ![url isEqualToString:@""]) {
                            [loader imageForUrlWithUrlString:url completionHandler:^(UIImage *image, NSString *url) {
                                cell.loadIndicator.hidden = true;
                                [cell.loadIndicator stopAnimating];

                                if (image != nil) {
                                    oComment.cPhoto = image;
                                    cell.ivPhoto.image = image;
                                }
                            }];
                        } else {
                            cell.loadIndicator.hidden = true;
                            [cell.loadIndicator stopAnimating];
                        }
                    });
                    /*
                    
                    NSData * data = [[NSData alloc] initWithContentsOfURL: sURL];
                    NSString *sPath = [[if_AppDelegate GetGlobalVar] valueForKeyPath:@"sFilePath"];
                    NSString *sPhotoPath = [sPath stringByAppendingPathComponent: [[oReturn objectForKey:@"oFile"] valueForKey:@"sFileName"] ];
                    [data writeToFile:sPhotoPath atomically:NO];
                    
                    
                    if ( data == nil ) { //No Existed
                        dispatch_async(dispatch_get_main_queue(), ^{
                            CellCommentsPhoto *pCell = (id)[_tvComments cellForRowAtIndexPath:indexPath];
                            pCell.loadIndicator.hidden = true;
                            [pCell.loadIndicator stopAnimating];
                            pCell.ivPhoto.image = [UIImage imageNamed:@"Image_PlaceHolder_Full.png"];
                        });
                        return;
                    }
                    dispatch_async(dispatch_get_main_queue(), ^{
                        CellCommentsPhoto *pCell = (id)[_tvComments cellForRowAtIndexPath:indexPath];
                        pCell.loadIndicator.hidden = true;
                        [pCell.loadIndicator stopAnimating];
                        if (pCell) {
                            pCell.ivPhoto.image = [UIImage imageWithData: data];
                            oComment.cPhoto = [UIImage imageWithData: data];
                        }
                    });
                     */
                }
            });
        }
        return cell;
    }

    return nil;
}

#pragma mark -- UITextViewDelegate
- (BOOL) textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text {
    NSString *changedText = [textView.text stringByReplacingCharactersInRange:range withString:text];
    CGFloat textHeight = [self measureTextHeight:changedText forTextView:textView];
    
    //calulate vertical insets
    UIEdgeInsets contentInset = textView.contentInset;
    UIEdgeInsets textContainerInsets = textView.textContainerInset;
    
    CGFloat heightPadding = contentInset.top + contentInset.bottom + textContainerInsets.top + textContainerInsets.bottom;
    
    CGFloat finalHeight = MAX(textViewHeightConstraintValue, textHeight + heightPadding);
    
    [self setTextViewHeight:textView height:finalHeight];
    
    if ([changedText isEqualToString:@""]) {
        [self resetTextViewNote];
        
        return NO;
    } else if ([textView.textColor isEqual:[UIColor lightGrayColor]] && ![text isEqualToString:@""]) {
        textView.text = nil;
        textView.textColor = [UIColor blackColor];
    }

   return YES;
}

- (void) textViewDidChange:(UITextView *)textView {
    NSString *trimmedString = [textView.text stringByTrimmingCharactersInSet:
                                               [NSCharacterSet whitespaceCharacterSet]];
    [self setButton:_btnSend enabled:![trimmedString isEqualToString:@""]];
}

- (void) textViewDidChangeSelection:(UITextView *)textView {
    if (self.view.window != nil) {
        if ([textView.textColor isEqual:[UIColor lightGrayColor]]) {
            textView.selectedTextRange = [textView textRangeFromPosition:textView.beginningOfDocument toPosition:textView.beginningOfDocument];
        }
    }
    
    [self findUsers];
}

- (void) reloadTableView {
    [self resetTextViewNote];

    [mapComments removeAllObjects];
    
    for (O_Comment *comm in lsComments) {
        NSString *key = comm.sDate;
        if ([NSString isNullOrEmpty:key]) continue;

        NSMutableArray *comments = [mapComments valueForKey:key];
        if (nil == comments) {
            comments = [@[comm] mutableCopy];
            [mapComments setValue:comments forKey:key];
        } else {
            [comments addObject:comm];
        }
    }

    keys = [self sortKeysByTime:mapComments.allKeys];
    [_tvComments reloadData];

    double delayInSeconds = 0.5;
    dispatch_time_t popTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayInSeconds * NSEC_PER_SEC));
    dispatch_after(popTime, dispatch_get_main_queue(), ^(void){
        [self scrollToBottom];
    });
 
}

- (void)scrollToBottom {
    if (keys.count > 0) {
        NSMutableArray *arry = [mapComments valueForKey:keys[keys.count - 1]];
        NSIndexPath* ipath = [NSIndexPath indexPathForRow: arry.count - 1 inSection: keys.count - 1];
        [_tvComments scrollToRowAtIndexPath: ipath atScrollPosition: UITableViewScrollPositionBottom animated: YES];
    }
}

- (NSArray *)sortKeysByTime:(NSArray *)keys {
    NSArray *sortedKeys = [keys sortedArrayUsingComparator:^NSComparisonResult(id a, id b) {
        NSDate *d1 = [CommonHelper GetDateFromString:@"EEE d/M/yyyy" dateString:(NSString *)a];
        NSDate *d2 = [CommonHelper GetDateFromString:@"EEE d/M/yyyy" dateString:(NSString *)b];
        
        return [d1 compare:d2];
    }];
    
    return sortedKeys;
}


- (void)updatePopView: (NSMutableArray *) users {
    CGFloat btnHeight = 33;
    CGFloat padding = 2;
    CGFloat topMargin = 4;
    _popViewHeight.constant = (btnHeight + padding) * users.count + topMargin + 2;
    for (UIView *v in _popView.subviews) {
        [v removeFromSuperview];
    }
    
    lsSelectedusers = users;
    
    for (int i = 0; i < users.count; i++) {
        NSDictionary *dic = users[i];
        UIButton *btn = [[UIButton alloc] initWithFrame:CGRectMake(0, topMargin + (btnHeight + padding) * i, _popView.bounds.size.width, btnHeight)];
        [btn setTitle: [dic valueForKey:@"Name"] forState:UIControlStateNormal];
        [btn setTitleColor:[UIColor blueColor] forState:UIControlStateNormal];
        btn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
        btn.contentEdgeInsets = UIEdgeInsetsMake(0, 10, 0, 0);
        btn.titleLabel.font = [UIFont systemFontOfSize:12];
        btn.tag = i;
        [btn addTarget:self action:@selector(pickUserAction:) forControlEvents:UIControlEventTouchUpInside];
        [_popView addSubview:btn];
    }
}

- (void)pickUserAction:(UIButton *)btn {
    NSInteger idx = btn.tag;

    if (lsSelectedusers == nil || lsSelectedusers.count <= idx) {
        return;
    }
    
    NSDictionary *selectedDic = lsSelectedusers[idx];
  //  DLog(@"sfsf  %@", selectedDic);
    
    NSString *name = [selectedDic valueForKey:@"Name"];
    name = [name stringByReplacingOccurrencesOfString:@" " withString:@""];
    
    NSString *originStr = _txtNote.text;
    NSArray *strs = [originStr componentsSeparatedByString:@"@"];
    if (strs.count > 1) {
        NSString *lst = strs.lastObject;
        originStr = [originStr stringByReplacingOccurrencesOfString:lst withString:@""];
    }
    
    _txtNote.text  = [NSString stringWithFormat:@"%@%@", originStr, name];
    [self resetPopView];
    
//    [self sendInspectionCommentOnlyUser:selectedDic];
}

#pragma Mark -- Find Users
- (void)findUsers {
    if ([_txtNote.text rangeOfString:@"@"].location == NSNotFound) {
        [self resetPopView];
        return;
    }
    
    NSArray *names = [_txtNote.text componentsSeparatedByString:@"@"];
    NSString *searchUserKeyword = [names.lastObject lowercaseString];
    
    if (searchUserKeyword == nil || [searchUserKeyword isEqualToString:@""]) {
        [self resetPopView];
        return;
    }
    
    NSMutableArray *results = [[NSMutableArray alloc] init];
    
    if (users == nil) {
        return;
    }
    
    for (NSDictionary *oo in users){
        if ([[[oo valueForKey:@"Name"] lowercaseString] rangeOfString:searchUserKeyword].location != NSNotFound) {
            [results addObject:oo];
        }
    }
    
    if (results.count > 0) {
        [self updatePopView:results];
    } else {
        [self resetPopView];
    }
}

- (void)resetPopView {
    lsSelectedusers = nil;
    _popViewHeight.constant = 0;
    for (UIView *v in _popView.subviews) {
        [v removeFromSuperview];
    }
}

#pragma Mark -- Parse Comments
- (void) processResult: (NSDictionary *)oResult {
   // DLog(@"result %@", oResult);
    [lsComments removeAllObjects];
    if (oIns == nil || oIns.iSAssetID == 0){
        NSDictionary *oDic = oResult[@"oInspection"];
        oIns = [[O_Inspection alloc] init];
        oIns.iSAssetID = [[oDic valueForKey:@"iPropertyID"] intValue];
        oIns.sInsTitle = [oDic valueForKey:@"sInsTitle"];
        oIns.sTitle = [oDic valueForKey:@"sTitle"];
        self.navigationItem.title = oIns.sTitle;
        self.lblTitle.text = oIns.sInsTitle;
    }

    NSArray *dicArray = oResult[@"lsComments"];
    for (NSDictionary *oDic in dicArray) {
        O_Comment *oComment = [self parseCommentDic:oDic];
        if (oComment != nil) {
            [lsComments addObject:oComment];
        }
    }
    
    [self reloadTableView];
}

- (void) addComment: (NSDictionary *)oResult {
    NSDictionary *oDic = oResult[@"oComment"];
    if (oDic != nil) {
        O_Comment *oComment = [self parseCommentDic:oDic];
        if (oComment != nil) {
            [lsComments addObject:oComment];
        }
    }
    
    [self reloadTableView];
}

- (O_Comment *) parseCommentDic: (NSDictionary *) oDic {
    O_Comment *oComment = nil;
  //  DLog(@"sdfsd  %@", oDic);
    if (oDic[@"iCommentID"]) {
        oComment = [[O_Comment alloc] init];
        oComment.iCommentID = [oDic[@"iCommentID"] intValue];
    } else {
        return nil;
    }
    
    if (oDic[@"iCompanyID"]) {
        oComment.iCompanyID = [oDic[@"iCompanyID"] intValue];
    } else {
        oComment.iCompanyID = 0;
    }
    
    if (oDic[@"iCustomerID"]) {
        oComment.iCustomerID = [oDic[@"iCustomerID"] intValue];
        oComment.sName = [CommonComments GetPropertyManager:oComment.iCustomerID users:users];
        oComment.color = [CommonComments GetColor:oComment.iCustomerID users:users];
    } else {
        oComment.iCustomerID = 0;
    }
    
    if (oDic[@"iInspectionID"]) {
        oComment.iInspectionID = [oDic[@"iInspectionID"] longValue];
    } else {
        oComment.iInspectionID = 0;
    }
    
    if (oDic[@"iTaskID"]) {
        oComment.iTaskID = oDic[@"iTaskID"];
    } else {
        oComment.iTaskID = @"";
    }
    
    if (oDic[@"sCustom1"]) {
        oComment.sCustom1 = oDic[@"sCustom1"];
    } else {
        oComment.sCustom1 = @"";
    }
    
    if (oDic[@"sDesp"] && oDic[@"sDesp"] != [NSNull null] ) {
        oComment.sDescription = oDic[@"sDesp"];
    } else {
        oComment.sDescription = @"";
    }
    
    if (oDic[@"dtDateTime"]) {
        NSDate *date = [CommonHelper GetDateFromString:@"MMM dd, yyyy HH:mm" dateString:oDic[@"dtDateTime"]];
        
        oComment.date = date;
        oComment.sDate = [CommonHelper GetDateStrFromDate:@"EEE d/M/yyyy" date:date];
        oComment.sTime = [CommonHelper GetDateStrFromDate:@"h:mm a" date:date];
    } else {
        oComment.sDate = @"";
        oComment.sTime = @"";
    }
    
    if (oDic[@"sType"]) {
        oComment.sType = oDic[@"sType"];
        if (oComment.sType && [oComment.sType caseInsensitiveCompare:@"F"] == NSOrderedSame){
            O_File *oFile = [db_Media GetFileByServerID:[oComment.sDescription intValue]];
            if (oFile && [CommonHelper IF_FileExist:oFile.sFile]){
                if ([[[oFile.sFile pathExtension] lowercaseString] isEqualToString:@"png"] ||
                    [[[oFile.sFile pathExtension] lowercaseString] isEqualToString:@"jpg"]||
                    [[[oFile.sFile pathExtension] lowercaseString] isEqualToString:@"jpeg"])
                    oComment.cPhoto =  [[UIImage alloc] initWithContentsOfFile:[CommonHelper IF_FilePath: oFile.sFile]];
            }
        }
    } else {
        oComment.sType = @"";
    }
    
    if (oDic[@"bDeleted"]) {
        oComment.bDeleted = [oDic[@"bDeleted"] boolValue];
    } else {
        oComment.bDeleted = NO;
    }
    
    return oComment;
}

#pragma Mark -- http
- (void) getAllInspectionComments {
    NSMutableDictionary *oReturn = [@{
            @"iInspectionID": [NSString stringWithFormat:@"%d", _iSInsID],
            @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
            @"sToken": [CommonUser currentToken]
    } mutableCopy];
    
    [self.view addSubview:hud];
    hud.labelText = @"Processing. Please wait ...";
  
    [hud showAnimated:YES whileExecutingBlock:^{
        NSDictionary *oResult = [IFConnection PostRequest:EndpointGetInspectionComments oParams:oReturn];
        if (oResult != nil && [[oResult valueForKey:@"success"] boolValue]){
             [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                 [self processResult:oResult];
             }];
        } else {
            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                [self showAlertDialog:@"Error" Message:@"Please make sure you are connected to Internet." withCompletionHandler:nil];
            }];
        }
        
    } completionBlock:^{
        [refreshControl endRefreshing];
        [hud removeFromSuperview];
    }];

}

- (void) sendInspectionCommentOnlyUser: (NSDictionary *)userDic {
    NSMutableDictionary *oReturn = [[NSMutableDictionary alloc] init];
    NSString *userName = [userDic valueForKey:@"Name"];
    oReturn = [@{
            @"iInspectionID": [NSString stringWithFormat:@"%d", _iSInsID],
            @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
            @"sToken": [CommonUser currentToken],
            @"sName": userName,
            @"sType": @"a",
            @"sSource": @"o"
        } mutableCopy];
    [self.view addSubview:hud];
    hud.labelText = @"Processing. Please wait ...";
    
    [hud showAnimated:YES whileExecutingBlock:^{
        NSDictionary *oResult = [IFConnection PostRequest:EndpointAddInspectionComments oParams:oReturn];
        if (oResult != nil && [[oResult valueForKey:@"success"] boolValue]){
            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                [self addComment:oResult];
            }];
        }
        else{
            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                [self showAlertDialog:@"Error" Message:@"Please make sure you are connected to Internet." withCompletionHandler:nil];
            }];
        }
        
    } completionBlock:^{
        [hud removeFromSuperview];
    }];
    
}
- (void) sendInspectionComment: (NSString *)sComments type:(NSString *)sType {
    NSMutableDictionary *oReturn = [[NSMutableDictionary alloc] init];
    oReturn = [@{
            @"iInspectionID": [NSString stringWithFormat:@"%d", _iSInsID],
            @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
            @"sToken": [CommonUser currentToken],
            @"sComments": sComments,
            @"sType": sType,
            @"sSource": @"o"
        } mutableCopy];
    [self.view addSubview:hud];
    hud.labelText = @"Processing. Please wait ...";
    
    [hud showAnimated:YES whileExecutingBlock:^{
        NSDictionary *oResult = [IFConnection PostRequest:EndpointAddInspectionComments oParams:oReturn];
        if (oResult != nil && [[oResult valueForKey:@"success"] boolValue]){
            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                [self addComment:oResult];
            }];
        }
        else{
            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                [self showAlertDialog:@"Error" Message:@"Please make sure you are connected to Internet." withCompletionHandler:nil];
            }];
        }
        
    } completionBlock:^{
        [hud removeFromSuperview];
    }];
    
}

- (void) uploadCommentMedia:(O_File *)oFile {
    [self.view addSubview:hud];
    hud.labelText = @"Connecting to Server";
    
    [hud showAnimated:YES whileExecutingBlock:^{
        hud.labelText = @"Uploading Data";
        [self uploadMedia:oFile withCompletionHandler:^(NSString* url) {
            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                if (nil != url) {
                    [self sendInspectionComment:url type:@"f"];
                } else {
                    [self showAlertDialog:@"Error" Message:@"Please make sure you are connected to Internet." withCompletionHandler:nil];
                }
            }];
        }];
    } completionBlock:^{
        [hud removeFromSuperview];
    }];
}

- (void) uploadMedia:(O_File *)oFile withCompletionHandler:(void (^)(NSString *))callback {
    if ([CommonHelper IF_FileExist:oFile.sFile]){
        NSDictionary *oReturn = [CommonRequest SI_UploadFile:oFile];
        NSString *sUploadURL = @"";
        if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
            sUploadURL = [oReturn valueForKey:@"sURL"];
            oFile.iSFileID = [[oReturn[@"oFile"] valueForKey:@"iFileID"]intValue];
        }
        else{
            oReturn = [CommonRequest SI_UploadFile:oFile];
            if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                sUploadURL = [oReturn valueForKey:@"sURL"];
                oFile.iSFileID = [[oReturn[@"oFile"] valueForKey:@"iFileID"]intValue];
            }
            else{
                [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                    [self showAlertDialog:@"Error" Message:@"Uploading Failed!" withCompletionHandler:nil];
                }];
                
                callback(nil);
                return;
            }
        }
        if (sUploadURL != nil && [sUploadURL length] > 10){
            if (![CommonS3 UploadFileToS3:oFile.sFile sURL:sUploadURL]){
                if (![CommonS3 UploadFileToS3:oFile.sFile sURL:sUploadURL]){
                    callback(nil);
                    return;
                }
                else{
                    oReturn = [CommonRequest SI_UploadFile_ConfirmFile:oFile];
                    if (oReturn == nil || [[oReturn valueForKey:@"success"] boolValue] == false){
                        oReturn = [CommonRequest SI_UploadFile_ConfirmFile:oFile];
                        if (oReturn == nil || [[oReturn valueForKey:@"success"] boolValue] == false){
                            callback(nil);
                            return;
                        }
                    }
                    
                    callback([NSString stringWithFormat:@"%d", oFile.iSFileID]);
                    return;
                }
            }
            else{
                oReturn = [CommonRequest SI_UploadFile_ConfirmFile:oFile];
                if (oReturn == nil || [[oReturn valueForKey:@"success"] boolValue] == false){
                    oReturn = [CommonRequest SI_UploadFile_ConfirmFile:oFile];
                    if (oReturn == nil || [[oReturn valueForKey:@"success"] boolValue] == false){
                        callback(nil);
                        return;
                    }
                }
                
                callback([NSString stringWithFormat:@"%d", oFile.iSFileID]);
                return;
            }
        }
    }
    callback(nil);
}

+ (void) push:(UIStoryboard *)storyboard fromController: (UIViewController *)fromCV data: (int) iInsId {
    if (fromCV.navigationController != nil) {
        [CommonHelper savePrefInt:I_INSID_KEY value:iInsId];
        UIViewController *con = [storyboard instantiateViewControllerWithIdentifier:@"if_InspectionComment"];
        [fromCV.navigationController pushViewController:con animated:YES];
    }
}

//RootInspectionComment
+ (void) present:(UIStoryboard *)storyboard fromController: (UIViewController *)fromCV  data: (int) iInsId {
    [CommonHelper savePrefInt:I_INSID_KEY value:iInsId];
    
    UINavigationController *nav = [storyboard instantiateViewControllerWithIdentifier:@"RootInspectionComment"];
    [fromCV presentViewController:nav animated:YES completion:nil];
}
@end
