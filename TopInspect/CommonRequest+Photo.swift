//
//  CommonRequest+Photo.swift
//  SnapInspect3
//
//  Created by <PERSON> on 10/15/24.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

import Foundation

extension CommonRequest {
    // Get the photo thumb url
    static func getPhotoThumbURL(fileID: FileID) -> URL? {
        guard let photo = db_Media.getPhoto(fileID: fileID) else {
            return nil
        }
        
        // Check if the photo exists locally, if so, return the local URL
        if photo.iPhotoID > 0,
           let sThumbPath = photo.sThumb, !sThumbPath.isEmpty,
           let rootFileURL = CommonHelper.getRootFileURL()
        {
            return rootFileURL.appendingPathComponent(sThumbPath)
        }

        // Otherwise, get the photo url from api endpoint
        let params = [
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken,
            "iPhotoID": "\(photo.iSPhotoID)"
        ]

        guard let result = IFConnection.postRequest(.getPhotoThumb, oParams: params),
              let success = result["success"] as? Bool, success,
              let sURL = result["sURL"] as? String
        else { return nil }

        return URL(string: sURL)
    }
}

// async-await wrapper
extension CommonRequest {
    static func getPhotoThumbURL(fileID: FileID) async -> URL? {
        await Task.detached(priority: .userInitiated) {
            CommonRequest.getPhotoThumbURL(fileID: fileID)
        }.value
    }

    static func downloadPhoto(fileID: FileID, taskID: Int) async {
        await Task.detached(priority: .userInitiated) {
            guard fileID.isRemote, let photo = db_Media.getPhotoOrCreate(byServerID: Int32(fileID.rowID)) else {
                return
            }
            if CommonMedia.downloadPhoto(fromServer: photo) {
                photo.sField3 = .init(taskID)
                db_Media.update(photo)
            }
        }.value
    }
}
