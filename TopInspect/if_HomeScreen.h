//
//  if_HomeScreen.h
//  InspectionFolio
//
//  Created by <PERSON> on 17/01/14.
//  Copyright (c) 2014 Cloudya. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "if_Assets.h"

// https://my.snapinspect.com/external/schedule?code=aVNjaGVkdWxlSUQ9MTIz
extern NSString * const kExternalScheduleCode;
extern NSString * const kNotificationExternalScheduleOpened;

extern NSString * const kExternalAssetID;
extern NSString * const kNotificationAssetDetailOpened;
extern NSString * const kNotificationCustomInfoUpdated;
extern NSString * const kNotificationSyncCompleted;

// tab bar items
typedef NS_ENUM(NSUInteger, HomeScreenTab) {
    HomeScreenTabAssets,
    HomeScreenTabInspections,
    HomeScreenTabSchedules,
    HomeScreenTabProjects,
    HomeScreenTabTasks
};

@interface if_HomeScreen : UITabBarController <if_HomeScreenDelegate>

-(void)GoToSetting;
-(void)Upload_Action:(int)iInsID completion: (nullable void (^)())completion;
-(void)ExternalRecover;
-(void)Sync;
-(void)btn_AddProperty;
-(void)btn_Action:(nullable id)sender;

- (void)switchToNewTab:(HomeScreenTab)tab;
@end
