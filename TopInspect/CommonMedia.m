//
//  CommonMedia.m
//  SnapInspect3
//
//  Created by <PERSON> on 22/11/17.
//  Copyright © 2017 SnapInspect. All rights reserved.
//

#import "NSArray+HighOrderFunction.h"
#import <AWSCore/AWSCore.h>
#import <AWSS3/AWSS3.h>
@import AppFeatures;

@implementation CommonMedia

+ (UIImage *)processImage_Thumb:(UIImage *)image { //process captured image, crop, resize and rotate
    UIImage *smallImage = [self imageWithImage:image scaledToWidth:80.0f]; //UIGraphicsGetImageFromCurrentImageContext();
    CGRect cropRect = CGRectMake(0, 0, 80, 80);
    CGImageRef imageRef = CGImageCreateWithImageInRect([smallImage CGImage], cropRect);
    UIImage *croppedImage = [UIImage imageWithCGImage:imageRef];
    CGImageRelease(imageRef);
    return croppedImage;
}

+ (UIImage *)imageWithImage:(UIImage *)sourceImage scaledToWidth:(float)i_width {
    CGFloat oldWidth = sourceImage.size.width;
    CGFloat scaleFactor = i_width / oldWidth;

    CGFloat newHeight = sourceImage.size.height * scaleFactor;
    CGFloat newWidth = oldWidth * scaleFactor;

    UIGraphicsBeginImageContext(CGSizeMake(newWidth, newHeight));
    [sourceImage drawInRect:CGRectMake(0, 0, newWidth, newHeight)];
    UIImage *newImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return newImage;
}

+ (BOOL)DownloadPhotoFromServer:(O_Photo *)oPhoto {
    if (![CommonHelper IF_FileExist:oPhoto.sFile]) {
        NSDictionary *oVideoToken = @{
            @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
            @"sToken": [CommonUser currentToken],
            @"iPhotoID": [NSString stringWithFormat:@"%d", oPhoto.iSPhotoID]
        };

        NSDictionary *oReturn = [IFConnection PostRequest:EndpointGetPhoto oParams:oVideoToken];
        if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]) {
            NSData *oData = [IFConnection DownloadFile_Data:[NSURL URLWithString:[oReturn valueForKey:@"sURL"]]];
            if (oData != nil) {
                NSString *sPath = [[if_AppDelegate GetGlobalVar] valueForKeyPath:@"sFilePath"];
                NSString *sUniqueFileName = [CommonHelper GetUniqueFileName];
                NSString *sPhotoName = [NSString stringWithFormat:@"p_%@.jpg", sUniqueFileName];
                NSString *sThumbName = [NSString stringWithFormat:@"pt_%@.jpg", sUniqueFileName];
                NSString *sPhotoPath = [sPath stringByAppendingPathComponent:sPhotoName];
                NSString *sThumbPath = [sPath stringByAppendingPathComponent:sThumbName];
                [oData writeToFile:sPhotoPath atomically:NO];
                //[UIImageJPEGRepresentation(croppedImage, 0.8)  writeToFile:sPhotoPath atomically:YES];
                UIImage *oTempImage = [CommonHelper processImage_Thumb:[UIImage imageWithData:oData]];
                [UIImageJPEGRepresentation(oTempImage, 0.8) writeToFile:sThumbPath atomically:YES];

                oPhoto.sFile = sPhotoName;
                oPhoto.sThumb = sThumbName;
                oPhoto.sComments = [oReturn valueForKey:@"sComments"];
                int iPhotoID = [db_Media UpdatePhoto:oPhoto];
                return true;
            } else {
                oData = [IFConnection DownloadFile_Data:[NSURL URLWithString:[oReturn valueForKey:@"sURL"]]];
                if (oData != nil) {
                    NSString *sPath = [[if_AppDelegate GetGlobalVar] valueForKeyPath:@"sFilePath"];
                    NSString *sUniqueFileName = [CommonHelper GetUniqueFileName];
                    NSString *sPhotoName = [NSString stringWithFormat:@"p_%@.jpg", sUniqueFileName];
                    NSString *sThumbName = [NSString stringWithFormat:@"pt_%@.jpg", sUniqueFileName];
                    NSString *sPhotoPath = [sPath stringByAppendingPathComponent:sPhotoName];
                    NSString *sThumbPath = [sPath stringByAppendingPathComponent:sThumbName];
                    [oData writeToFile:sPhotoPath atomically:NO];
                    //[UIImageJPEGRepresentation(croppedImage, 0.8)  writeToFile:sPhotoPath atomically:YES];
                    UIImage *oTempImage = [CommonHelper processImage_Thumb:[UIImage imageWithData:oData]];
                    [UIImageJPEGRepresentation(oTempImage, 0.8) writeToFile:sThumbPath atomically:YES];

                    oPhoto.sFile = sPhotoName;
                    oPhoto.sThumb = sThumbName;
                    oPhoto.sComments = [oReturn valueForKey:@"sComments"];
                    int iPhotoID = [db_Media UpdatePhoto:oPhoto];
                    return true;
                } else {
                    return false;
                }
            }
        }
        return false;
    } else {
        return true;
    }

}

+ (void)downloadFloorPlanImages:(NSArray <O_FloorPlan *>*)floorPlans completion:(void (^)(void))completion {
    UIViewController *topMost = UIApplication.sharedApplication.topMost;
    NSUInteger count = floorPlans.count;
    dispatch_background_async(^{
        [floorPlans enumerateObjectsUsingBlock:^(O_FloorPlan *plan, NSUInteger idx, BOOL *stop) {
            safe_dispatch_main_async((^{
                [topMost showLoading:@"Connecting to server"
                    detailMessage:[NSString stringWithFormat:@"Downloading blueprint image %d/%d", (int) idx + 1, (int) count]];
            }))
            [self downloadImagesForFloorPlan:plan];
        }];
        safe_dispatch_main_async(^{
            [topMost hidesLoading];
            if (completion) completion();
        })
    })
}

+ (void)downloadImagesForFloorPlan:(O_FloorPlan *)plan {
    @try {
        NSDictionary *params = @{
            @"iFloorPlanID": [@(plan.iSFloorPlanID) stringValue],
            @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
            @"sToken": [CommonUser currentToken]
        };
        NSDictionary *resp = [IFConnection PostRequest:EndpointGetFloorPlan oParams:params];
        if (resp != nil && [resp[@"success"] boolValue]) {
            O_FloorPlan *oFloorPlan = [[O_FloorPlan alloc] initWithDictionary:resp[@"oFloorPlan"]];
            oFloorPlan.iFloorPlanID = plan.iFloorPlanID;
            if (![NSString isNullOrEmpty:oFloorPlan.sPlanPath] && ![CommonHelper IF_FilePathExist:oFloorPlan.sPlanFile.path]) {
                // download the base image for blueprint, not need to do that again if had downloaded
                NSData *imageData = [NSData dataWithContentsOfURL:[NSURL URLWithString:oFloorPlan.sPlanPath]];
                [imageData writeToFile:oFloorPlan.sPlanFile.path atomically:YES];
            }
            oFloorPlan.sPlanPath = oFloorPlan.sPlanFileName;

            // if the image path is empty, copy the base image as the thumbnail image
            if ([NSString isNullOrEmpty:oFloorPlan.sImagePath]) {
                [NSFileManager.defaultManager copyItemAtURL:oFloorPlan.sPlanFile toURL:oFloorPlan.sImageFile error:NULL];
            } else {
                // download the thumbnail image for blueprint
                NSData *imageData = [NSData dataWithContentsOfURL:[NSURL URLWithString:oFloorPlan.sImagePath]];
                [imageData writeToFile:oFloorPlan.sImageFile.path atomically:YES];
            }
            oFloorPlan.sImagePath = oFloorPlan.sImageFileName;
            [db_FloorPlan saveWithFloorPlans:@[oFloorPlan]];
        }
    } @catch (NSException *exception) {
        Log_Exception(exception)
    }
}

+ (int)saveImageAsPhoto:(UIImage *)image withInsItemID:(int)iInsItemID withInsID:(int)iInsID {
    UIImage *oTempImage = [CommonMedia processImage_Thumb:image];
    return [self saveImageDataAsPhoto:UIImageJPEGRepresentation(image, 1.0)
                        thumbnailData:UIImageJPEGRepresentation(oTempImage, 1.0)
                        withInsItemID:iInsItemID withInsID:iInsID];
}

+ (int)saveImageDataAsPhoto:(NSData *)imageData
              thumbnailData:(NSData *)thumbnailData
              withInsItemID:(int)iInsItemID withInsID:(int)iInsID {
    NSString *sPath = [[if_AppDelegate GetGlobalVar] valueForKeyPath:@"sFilePath"];
    NSString *sUniqueFileName = [CommonHelper GetUniqueFileName];
    NSString *sPhotoName = [NSString stringWithFormat:@"p_%@.jpg", sUniqueFileName];
    NSString *sThumbName = [NSString stringWithFormat:@"pt_%@.jpg", sUniqueFileName];
    NSString *sPhotoPath = [sPath stringByAppendingPathComponent:sPhotoName];
    NSString *sThumbPath = [sPath stringByAppendingPathComponent:sThumbName];
    [imageData writeToFile:sPhotoPath atomically:YES];
    [thumbnailData writeToFile:sThumbPath atomically:YES];
    O_Photo *oPhoto = [[O_Photo alloc] init];
    oPhoto.sFile = sPhotoName;
    oPhoto.sThumb = sThumbName;
    NSString *sComments = [CommonJson AddJsonKeyValueString:Constants.kJsonKeyFloorPlan sValue:@"1" sJson:oPhoto.sComments];
    oPhoto.sComments = [CommonJson simplifyJsonString:sComments];
    oPhoto.bDeleted = false;
    oPhoto.dtDateTime = [CommonHelper GetDateString:[NSDate new]];
    oPhoto.iSize = (int) [CommonHelper GetFileSize:sPhotoPath];
    oPhoto.iInsItemID = iInsItemID;
    oPhoto.iInsID = iInsID;
    int iPhotoID = [db_Media UpdatePhoto:oPhoto];
    oPhoto.iPhotoID = iPhotoID;
    [db_Media UpdatePhoto:oPhoto];
    return iPhotoID;
}

+ (BOOL)UploadPhotos:(NSMutableArray *)arrPhotos 
            sAdditionalInfo:(NSString *)sAdditionalInfo
                  progress:(void (^)(NSUInteger current, NSUInteger total))progress {
    @autoreleasepool {
        @try {
            
            //NSString *sMessage;
            int iCount = 1;
            for (O_Photo *oPhoto in arrPhotos){
                @autoreleasepool {
                    if (!oPhoto.bUploaded && !oPhoto.bDeleted){
                        if ([CommonHelper IF_FileExist:oPhoto.sFile]){
                            if (progress) progress(iCount, arrPhotos.count);
                            NSMutableDictionary *oDic = [NSMutableDictionary dictionary];
                            oDic[@"iCustomerID"] = [CommonHelper IFGetPref:@"iCustomerID"];
                            oDic[@"sToken"] = [CommonUser currentToken];
                            oDic[@"dtCreated"] = oPhoto.dtDateTime;
                            oDic[@"sPhotoComment"] = oPhoto.sComments;
                            oDic[@"iSize"] = [NSString stringWithFormat:@"%d", oPhoto.iSize];
                            oDic[@"sGeo"] = oPhoto.sGeo ?: @"";
                            NSDictionary *oReturn = [IFConnection PostRequest: EndpointUploadPhoto oParams:oDic];
                            NSString *sUploadURL = @"";
                            if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                                sUploadURL = [oReturn valueForKey:@"sURL"];
                                oPhoto.iSPhotoID = [[oReturn valueForKey:@"iSPhotoID"] intValue];
                                [db_Media UpdatePhoto:oPhoto];
                            }
                            else{
                                NSDictionary *oReturn1 = [IFConnection PostRequest: EndpointUploadPhoto
                                                                           oParams:[@{
                                                                            @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
                                                                            @"sToken": [CommonUser currentToken],
                                                                            @"dtCreated": oPhoto.dtDateTime,
                                                                            @"sPhotoComment": oPhoto.sComments,
                                                                            @"iSize": @(oPhoto.iSize),
                                                                            @"sGeo": oPhoto.sGeo ?: @""
                                                                           } mutableCopy]];
                                if (oReturn1 != nil && [[oReturn1 valueForKey:@"success"] boolValue]){
                                    sUploadURL = [oReturn1 valueForKey:@"sURL"];
                                    oPhoto.iSPhotoID = [[oReturn valueForKey:@"iSPhotoID"] intValue];
                                    [db_Media UpdatePhoto:oPhoto];
                                }
                                else{
                                    //bSyncSuccess = false;
                                    //sMessage = [NSString stringWithFormat:@"%@ %@", sMessage, @"106"];
                                    return false;
                                }
                            }
                            if (sUploadURL != nil && [sUploadURL length] > 10){
                                if (![self UploadFileToS3:oPhoto.sFile sURL:sUploadURL]){
                                    if (![self UploadFileToS3:oPhoto.sFile sURL:sUploadURL]){
                                        //bSyncSuccess = false;
                                        //sMessage = [NSString stringWithFormat:@"%@ %@", sMessage, @"107"];
                                        return false;
                                    }
                                    else{
                                        
                                        
                                        NSDictionary *oReturn = [IFConnection PostRequest:EndpointUploadPhotoSuccess oParams:[@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"iSPhotoID": [NSString stringWithFormat:@"%d", oPhoto.iSPhotoID]} mutableCopy]];
                                        if (oReturn == nil || [[oReturn valueForKey:@"success"] boolValue] == NO){
                                            NSDictionary *oReturn1 = [IFConnection PostRequest:EndpointUploadPhotoSuccess oParams:[@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"iSPhotoID": [NSString stringWithFormat:@"%d", oPhoto.iSPhotoID]} mutableCopy]];
                                            if (oReturn1 == nil || [[oReturn1 valueForKey:@"success"] boolValue] == NO){
                                                return false;
                                            }
                                        }
                                        
                                        oPhoto.bUploaded = true;
                                        [db_Media UpdatePhoto:oPhoto];
                                        // [CommonHelper DeletePhotoFilesIfNeed:oPhoto];
                                    }
                                }
                                else{
                                    
                                    NSDictionary *oReturn = [IFConnection PostRequest:EndpointUploadPhotoSuccess oParams:[@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"iSPhotoID": [NSString stringWithFormat:@"%d", oPhoto.iSPhotoID]} mutableCopy]];
                                    if (oReturn == nil || [[oReturn valueForKey:@"success"] boolValue] == NO){
                                        NSDictionary *oReturn1 = [IFConnection PostRequest:EndpointUploadPhotoSuccess oParams:[@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"iSPhotoID": [NSString stringWithFormat:@"%d", oPhoto.iSPhotoID]} mutableCopy]];
                                        if (oReturn1 == nil || [[oReturn1 valueForKey:@"success"] boolValue] == NO){
                                            return false;
                                        }
                                    }
                                    
                                    oPhoto.bUploaded = true;
                                    [db_Media UpdatePhoto:oPhoto];
                                    // [CommonHelper DeletePhotoFilesIfNeed:oPhoto];
                                }
                            }
                            else{
                                //sMessage = [NSString stringWithFormat:@"%@ %@", sMessage, @"105"];
                                return false;
                            }
                        }
                    } else if (oPhoto.hasUploadedWhenEditingIns) {
                        [self savePhotoComment:oPhoto];
                    }
                    iCount = iCount + 1;
                }
            }
            [CommonAnalytics trackEvent:@"iOS Upload Inspection Photos" meta:@{
                @"Title": [NSString stringWithFormat:@"%@", sAdditionalInfo],
                @"Photos": [NSString stringWithFormat:@"%d out of %ld", iCount, (arrPhotos == nil ? 0 :arrPhotos.count)]
            }];
            [db_Log InsertLog:@"Upload" sMessage:[NSString stringWithFormat:@"%d Photos Uploaded - %@", iCount, sAdditionalInfo]];
            return true;
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return false;;
        }
        
    }
}

+ (BOOL)UploadPhotos_ExternalInspection:(NSMutableArray *)arrPhotos 
                        sAdditionalInfo:(NSString *)sAdditionalInfo
                              progress:(void (^)(NSUInteger current, NSUInteger total))progress 
                              sTokenID:(NSString *)sTokenID 
                              sToken:(NSString *)sToken
{
    @autoreleasepool {
        @try {

            //NSString *sMessage;
            int iCount = 1;
            for (O_Photo *oPhoto in arrPhotos){
                if ((!oPhoto.bUploaded) && (!oPhoto.bDeleted)){
                    if ([CommonHelper IF_FileExist:oPhoto.sFile]){
                        if (progress) progress(iCount, arrPhotos.count);
                        NSMutableDictionary *oDic = [@{
                            @"iTokenID": sTokenID,
                            @"sToken": sToken,
                            @"dtCreated": oPhoto.dtDateTime,
                            @"sPhotoComment": oPhoto.sComments,
                            @"iSize": [NSString stringWithFormat:@"%d", oPhoto.iSize],
                            @"sGeo": oPhoto.sGeo ?: @""
                        } mutableCopy];

                        NSDictionary *oReturn = [IFConnection PostRequest:EndpointUploadPhotoExternalInspection oParams:oDic];
                        NSString *sUploadURL = @"";
                        if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                            sUploadURL = [oReturn valueForKey:@"sURL"];
                            oPhoto.iSPhotoID = [[oReturn valueForKey:@"iSPhotoID"] intValue];
                            [db_Media UpdatePhoto:oPhoto];
                        }
                        else{
                            NSDictionary *oReturn1 = [IFConnection PostRequest: EndpointUploadPhotoExternalInspection
                                                                       oParams:[@{
                                                                           @"iTokenID": sTokenID,
                                                                           @"sToken": sToken,
                                                                           @"dtCreated": oPhoto.dtDateTime,
                                                                           @"sPhotoComment": oPhoto.sComments,
                                                                           @"iSize": @(oPhoto.iSize),
                                                                           @"sGeo": oPhoto.sGeo ?: @""
                                                                       } mutableCopy]];
                            if (oReturn1 != nil && [[oReturn1 valueForKey:@"success"] boolValue]){
                                sUploadURL = [oReturn1 valueForKey:@"sURL"];
                                oPhoto.iSPhotoID = [[oReturn valueForKey:@"iSPhotoID"] intValue];
                                [db_Media UpdatePhoto:oPhoto];
                            }
                            else{
                                //bSyncSuccess = false;
                                //sMessage = [NSString stringWithFormat:@"%@ %@", sMessage, @"106"];
                                return false;
                            }
                        }
                        if (sUploadURL != nil && [sUploadURL length] > 10){
                            if (![self UploadFileToS3:oPhoto.sFile sURL:sUploadURL]){
                                if (![self UploadFileToS3:oPhoto.sFile sURL:sUploadURL]){
                                    //bSyncSuccess = false;
                                    //sMessage = [NSString stringWithFormat:@"%@ %@", sMessage, @"107"];
                                    return false;
                                }
                                else{
                                    NSDictionary *oReturn = [IFConnection PostRequest: EndpointUploadPhotoSuccessExternalInspection oParams: [@{@"iTokenID": sTokenID, @"sToken": sToken, @"iSPhotoID": [NSString stringWithFormat:@"%d", oPhoto.iSPhotoID]} mutableCopy]];
                                    if (oReturn == nil || [[oReturn valueForKey:@"success"] boolValue] == NO){
                                        NSDictionary *oReturn1 = [IFConnection PostRequest: EndpointUploadPhotoSuccessExternalInspection oParams:[@{@"iTokenID": sTokenID, @"sToken": sToken, @"iSPhotoID": [NSString stringWithFormat:@"%d", oPhoto.iSPhotoID]} mutableCopy]];
                                        if (oReturn1 == nil || [[oReturn1 valueForKey:@"success"] boolValue] == NO){
                                            return false;
                                        }
                                    }

                                    oPhoto.bUploaded = true;
                                    [db_Media UpdatePhoto:oPhoto];
                                    // [CommonHelper DeletePhotoFilesIfNeed:oPhoto];
                                }
                            }
                            else{

                                NSDictionary *oReturn = [IFConnection PostRequest: EndpointUploadPhotoSuccessExternalInspection oParams:[@{@"iTokenID": sTokenID, @"sToken": sToken, @"iSPhotoID": [NSString stringWithFormat:@"%d", oPhoto.iSPhotoID]} mutableCopy]];
                                if (oReturn == nil || [[oReturn valueForKey:@"success"] boolValue] == NO){
                                    NSDictionary *oReturn1 = [IFConnection PostRequest: EndpointUploadPhotoSuccessExternalInspection oParams:[@{@"iTokenID": sTokenID, @"sToken": sToken, @"iSPhotoID": [NSString stringWithFormat:@"%d", oPhoto.iSPhotoID]} mutableCopy]];
                                    if (oReturn1 == nil || [[oReturn1 valueForKey:@"success"] boolValue] == NO){
                                        return false;
                                    }
                                }

                                oPhoto.bUploaded = true;
                                [db_Media UpdatePhoto:oPhoto];
                                // [CommonHelper DeletePhotoFilesIfNeed:oPhoto];
                            }
                        }
                        else{
                            //sMessage = [NSString stringWithFormat:@"%@ %@", sMessage, @"105"];
                            return false;
                        }
                    }
                }
                iCount = iCount + 1;
            }
            [CommonAnalytics trackEvent:@"iOS Upload Inspection Photos - External Inspection" meta:@{
                @"Title": [NSString stringWithFormat:@"%@", sAdditionalInfo],
                @"Photos": [NSString stringWithFormat:@"%d out of %ld", iCount, (arrPhotos == nil ? 0 :arrPhotos.count)]
            }];
            [db_Log InsertLog:@"Upload" sMessage:[NSString stringWithFormat:@"%d Photos Uploaded - %@", iCount, sAdditionalInfo]];
            return true;
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return false;;
        }

    }
}

+ (BOOL)UploadVideos_ExternalInspection:(NSMutableArray *)arrVideos
                            sAdditionalInfo:(NSString *)sAdditionalInfo
                            progress:(void (^)(NSUInteger current, NSUInteger total))progress
                            uploadProgress:(void (^)(int current, int64_t totalBytesSent, int64_t totalBytesExpectedToSend))uploadProgress
                            sTokenID:(NSString *)sTokenID
                            sToken:(NSString *)sToken {
    @autoreleasepool {
        @try {
            int i=1;
            for (O_Video *oVideo in arrVideos){
                if ((!oVideo.bUploaded) && [CommonHelper IF_FileExist:oVideo.sFile]){
                    NSMutableDictionary *oVideoToken = [@{
                        @"iTokenID": sTokenID,
                        @"sToken": sToken,
                        @"iVideoSize": [NSString stringWithFormat:@"%ld", oVideo.iSize],
                        @"iWidth": [NSString stringWithFormat:@"%d", oVideo.iWidth],
                        @"iHeight": [NSString stringWithFormat:@"%d", oVideo.iHeight],
                        @"sGeo": oVideo.sGeo ?: @""
                    } mutableCopy];
                    if (progress) progress(i, arrVideos.count);
                    NSDictionary *oReturn = [IFConnection PostRequest: EndpointGetVideoTokenExternalInspection oParams:oVideoToken];
                    @try{
                        if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                            if (![self UploadVideoToS3:oVideo i:i oReturn:oReturn uploadProgress:uploadProgress]){
                                if (![self UploadVideoToS3:oVideo i:i oReturn:oReturn uploadProgress:uploadProgress]){
                                    return false;
                                }
                            }
                        }
                        else{
                            oReturn = [IFConnection PostRequest:EndpointGetVideoTokenExternalInspection oParams:oVideoToken];
                            if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                                if (![self UploadVideoToS3:oVideo i:i oReturn:oReturn uploadProgress:uploadProgress]){
                                    if (![self UploadVideoToS3:oVideo i:i oReturn:oReturn uploadProgress:uploadProgress]){
                                        return false;
                                    }
                                }

                            }
                            else{
                                return false;
                            }
                        }
                        int iSVideoID = [[oReturn valueForKey:@"iVideoID"] intValue];
                        if (iSVideoID > 0){
                            [oVideoToken removeObjectForKey:@"iVideoSize"];
                            oVideoToken[@"iVideoID"] = [NSString stringWithFormat:@"%d", iSVideoID];
                            NSDictionary *oFinalReturn = [IFConnection PostRequest: EndpointUploadVideoSuccessExternalInspection oParams:oVideoToken];
                            if (oFinalReturn != nil && [[oFinalReturn valueForKey:@"success"]boolValue]){
                                oVideo.iSVideoID = iSVideoID;
                                oVideo.bUploaded = true;
                                oVideo.bProcessed = true;
                                oVideo.bGetURL = true;
                                [db_Media UpdateVideo:oVideo];
                                // [CommonHelper DeleteVideoFilesIfNeed:oVideo];
                            }
                            else{
                                oFinalReturn = [IFConnection PostRequest: EndpointUploadVideoSuccessExternalInspection oParams:oVideoToken];
                                if (oFinalReturn != nil && [[oFinalReturn valueForKey:@"success"]boolValue]){
                                    oVideo.iSVideoID = iSVideoID;
                                    oVideo.bUploaded = true;
                                    oVideo.bProcessed = true;
                                    oVideo.bGetURL = true;
                                    [db_Media UpdateVideo:oVideo];
                                    // [CommonHelper DeleteVideoFilesIfNeed:oVideo];
                                }
                                else{
                                    return false;
                                }
                            }

                        }
                        else{
                            return false;
                        }
                    }@catch(NSException *ex){
                        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@ - Inner", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex];
                        return false;
                    }


                }
                i++;
            }
            if (arrVideos != nil && arrVideos.count > 0){
                [CommonAnalytics trackEvent:@"iOS Upload Inspection Videos External Inspection" meta:@{
                    @"Title": [NSString stringWithFormat:@"%@", sAdditionalInfo],
                    @"Videos": [NSString stringWithFormat:@"%d out of %ld", i, (arrVideos == nil ? 0 :arrVideos.count)]
                }];
            }
            [db_Log InsertLog:@"Upload" sMessage:[NSString stringWithFormat:@"%d Videos Uploaded - External - %@", i, sAdditionalInfo]];
            return true;
        }
        @catch(NSException *ex1){
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex1];
            return false;
        }
        return true;
    }
}

+ (BOOL)UploadVideos:(NSMutableArray *)arrVideos 
     sAdditionalInfo:(NSString *)sAdditionalInfo
            progress:(void (^)(NSUInteger current, NSUInteger total))progress
            uploadProgress:(void (^)(int current, int64_t totalBytesSent, int64_t totalBytesExpectedToSend))uploadProgress {
    @autoreleasepool {
        @try {
            int i=1;
            for (O_Video *oVideo in arrVideos){
                if ((!oVideo.bUploaded) && [CommonHelper IF_FileExist:oVideo.sFile]){
                    NSMutableDictionary *oVideoToken = [@{
                            @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
                            @"sToken": [CommonUser currentToken],
                            @"iVideoSize": [NSString stringWithFormat:@"%ld", oVideo.iSize],
                            @"iWidth": [NSString stringWithFormat:@"%d", oVideo.iWidth],
                            @"iHeight": [NSString stringWithFormat:@"%d", oVideo.iHeight],
                            @"sGeo": oVideo.sGeo ?: @""
                    } mutableCopy];
                    
                    if (progress) progress(i, arrVideos.count);
                    NSDictionary *oReturn = [IFConnection PostRequest:EndpointGetVideoToken oParams:oVideoToken];
                    @try{
                        if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                            if (![self UploadVideoToS3:oVideo i:i oReturn:oReturn uploadProgress:uploadProgress]){
                                if (![self UploadVideoToS3:oVideo i:i oReturn:oReturn uploadProgress:uploadProgress]){
                                    return false;
                                }
                            }
                        }
                        else{
                            oReturn = [IFConnection PostRequest:EndpointGetVideoToken oParams:oVideoToken];
                            if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                                if (![self UploadVideoToS3:oVideo i:i oReturn:oReturn uploadProgress:uploadProgress]){
                                    if (![self UploadVideoToS3:oVideo i:i oReturn:oReturn uploadProgress:uploadProgress]){
                                        return false;
                                    }
                                }

                            }
                            else{
                                return false;
                            }
                        }
                        int iSVideoID = [[oReturn valueForKey:@"iVideoID"] intValue];
                        if (iSVideoID > 0){
                            [oVideoToken removeObjectForKey:@"iVideoSize"];
                            oVideoToken[@"iVideoID"] = [NSString stringWithFormat:@"%d", iSVideoID];
                            NSDictionary *oFinalReturn = [IFConnection PostRequest: EndpointUploadVideoSuccess oParams:oVideoToken];
                            if (oFinalReturn != nil && [[oFinalReturn valueForKey:@"success"]boolValue]){
                                oVideo.iSVideoID = iSVideoID;
                                oVideo.bUploaded = true;
                                oVideo.bProcessed = true;
                                oVideo.bGetURL = true;
                                [db_Media UpdateVideo:oVideo];
                                // [CommonHelper DeleteVideoFilesIfNeed:oVideo];
                            }
                            else{
                                oFinalReturn = [IFConnection PostRequest:EndpointUploadVideoSuccess oParams:oVideoToken];
                                if (oFinalReturn != nil && [[oFinalReturn valueForKey:@"success"]boolValue]){
                                    oVideo.iSVideoID = iSVideoID;
                                    oVideo.bUploaded = true;
                                    oVideo.bProcessed = true;
                                    oVideo.bGetURL = true;
                                    [db_Media UpdateVideo:oVideo];
                                    // [CommonHelper DeleteVideoFilesIfNeed:oVideo];
                                }
                                else{
                                    return false;
                                }
                            }

                        }
                        else{
                            return false;
                        }
                    }@catch(NSException *ex){
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@ - Inner", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex];
                        return false;
                    }


                }
                 i++;
            }
            if (arrVideos != nil && arrVideos.count > 0){
                [CommonAnalytics trackEvent:@"iOS Upload Inspection Videos" meta:@{
                    @"Title": [NSString stringWithFormat:@"%@", sAdditionalInfo],
                    @"Videos": [NSString stringWithFormat:@"%d out of %ld", i, (arrVideos == nil ? 0 :arrVideos.count)]
                }];
            }
            [db_Log InsertLog:@"Upload" sMessage:[NSString stringWithFormat:@"%d Videos Uploaded - %@", i, sAdditionalInfo]];
            return true;
        }
        @catch(NSException *ex1){
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex1];
            return false;
        }
        return true;
   }
}

+ (BOOL)UploadVideoToS3:(O_Video *)oVideo i:(int)i oReturn:(NSDictionary *)oReturn 
        uploadProgress:(void (^)(int current, int64_t totalBytesSent, int64_t totalBytesExpectedToSend))uploadProgress {
    @autoreleasepool {
        @try {
            NSString *sAccessKey = [oReturn valueForKey:@"sAccessKey"];
            NSString *sSecretKey = [oReturn valueForKey:@"sSecretKey"];
            NSString *sToken = [oReturn valueForKey:@"sToken"];
            NSString *sVideoURL = [oReturn valueForKey:@"sVideoURL"];
            NSString *sThumbURL = [ oReturn valueForKey:@"sThumbURL"];
            NSString *sBucket = [oReturn valueForKey:@"sBucket"];
            
            if (![self UploadVideoToS3Action:oVideo i:i sAccessKey:sAccessKey sSecretKey:sSecretKey sToken:sToken sS3VideoURL:sVideoURL sThumbURL:sThumbURL sBucket:sBucket uploadProgress:uploadProgress]){
                if (![self UploadVideoToS3Action:oVideo i:i sAccessKey:sAccessKey sSecretKey:sSecretKey sToken:sToken sS3VideoURL:sVideoURL sThumbURL:sThumbURL sBucket:sBucket uploadProgress:uploadProgress]){
                    return false;
                }
            }
            oVideo.sSFile = sVideoURL;
            oVideo.sSThumb = sThumbURL;
            [db_Media UpdateVideo:oVideo];
            return true;
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return false;
        }
    }
}

+ (BOOL)UploadVideoToS3Action:(O_Video *)oVideo i:(int)i sAccessKey:(NSString *)sAccessKey sSecretKey:(NSString *)sSecretKey
                      sToken:(NSString *)sToken sS3VideoURL:(NSString *)sS3VideoURL sThumbURL:(NSString *)sThumbURL
                     sBucket:(NSString *)sBucket uploadProgress:(void (^)(int current, int64_t totalBytesSent, int64_t totalBytesExpectedToSend))uploadProgress {
    @try {


       //  AWSStaticCredentialsProvider *credentials = [[AWSStaticCredentialsProvider alloc] initWithAccessKey:@"********************"
                             //                                           secretKey:@"pnI4RSFEvBEB4DzHLYXwbdHaLQ/VkN5SEjC80HZI"];
        AWSBasicSessionCredentialsProvider *credentials = [[AWSBasicSessionCredentialsProvider alloc] initWithAccessKey:sAccessKey secretKey:sSecretKey sessionToken:sToken];
       // AWSSTSCredential *credentials = [[AWSSTSCredential alloc] initWithAccessKey:sAccessKey secretKey:sSecretKey sessionKey:sToken];
        AWSServiceConfiguration *configuration = [[AWSServiceConfiguration alloc] initWithRegion:AWSRegionUSWest2 credentialsProvider:credentials];
        [AWSServiceManager defaultServiceManager].defaultServiceConfiguration = configuration;
        [AWSS3 registerS3WithConfiguration:configuration forKey:sThumbURL];


        AWSS3 *s3 = [AWSS3 S3ForKey:sThumbURL];

       // AWSS3PutObjectRequest *oRequest= [[AWSS3PutObjectRequest alloc] initWithKey:sThumbURL inBucket:sBucketName];
        AWSS3PutObjectRequest *oRequest = [[AWSS3PutObjectRequest alloc] init];
        [oRequest setKey:sThumbURL];
        [oRequest setBucket:sBucket];
        [oRequest setACL:AWSS3ObjectCannedACLPublicRead];
        NSString *sFilePath = [CommonHelper IF_FilePath:oVideo.sThumb];
        [oRequest setBody:[NSURL fileURLWithPath:sFilePath]];
//        [oRequest setFilename:[CommonHelper IF_FilePath:oVideo.sThumb]];
        [oRequest setContentLength:[NSNumber numberWithUnsignedLongLong:[[[NSFileManager defaultManager] attributesOfItemAtPath:sFilePath error:nil][NSFileSize] longLongValue]]];
        [oRequest setContentType:@"image/jpeg"];
        [[[[s3 putObject:oRequest] continueWithBlock:^id(AWSTask *task) {
            if (task.error){

                [s3 putObject:oRequest];
            }
            return nil;

        }] continueWithBlock:^id(AWSTask *task) {
            if (task.error){
//                NSDictionary *data = [NSDictionary dictionaryWithObjects:[NSArray arrayWithObjects:[NSString stringWithFormat:@"%@ || %@ || %@", @"SyncViewController.SendThumbNail.UploadFail", sFilePath, sThumbURL] , nil]
//                                                                 forKeys:[NSArray arrayWithObjects:@"Code_Position", nil]];
                //        BUGSENSE_LOG(nil, data);
            }
            return nil;
        }] waitUntilFinished];
        [AWSS3 removeS3ForKey:sThumbURL];
        //Upload Thumb End Here


      //  [AWSServiceManager defaultServiceManager].defaultServiceConfiguration = configuration;
        [AWSS3TransferManager registerS3TransferManagerWithConfiguration:configuration forKey:sS3VideoURL];
        AWSS3TransferManager *oTransfer = [AWSS3TransferManager S3TransferManagerForKey:sS3VideoURL];
        AWSS3TransferManagerUploadRequest *oTransferRequest = [AWSS3TransferManagerUploadRequest new];
        [oTransferRequest setKey:sS3VideoURL];
        [oTransferRequest setBucket:sBucket];
        [oTransferRequest setACL:AWSS3ObjectCannedACLPublicRead];
        sFilePath =  [CommonHelper IF_FilePath:oVideo.sFile];
        [oTransferRequest setBody:[NSURL fileURLWithPath: sFilePath]];
        [oTransferRequest setContentLength:[NSNumber numberWithUnsignedLongLong:[[[NSFileManager defaultManager] attributesOfItemAtPath:sFilePath error:nil][NSFileSize] longLongValue]]];
        [oTransferRequest setContentType:@"video/mp4"];
        oTransferRequest.uploadProgress = ^(int64_t bytesSent, int64_t totalBytesSent, int64_t totalBytesExpectedToSend) {
            if (uploadProgress) uploadProgress(i, totalBytesSent, totalBytesExpectedToSend);
        };
        [[[[oTransfer upload:oTransferRequest] continueWithBlock:^id(AWSTask *task) {
            if (task.error) {
                return [oTransfer upload:oTransferRequest];
            }

            return nil;

        }] continueWithBlock:^id(AWSTask *task) {

            return nil;
        }] waitUntilFinished];
        [AWSS3TransferManager removeS3TransferManagerForKey:sS3VideoURL];
        return true;
    }
    @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        return false;
    }


    return TRUE;
}

+ (BOOL)UploadFileToS3 :(NSString *)sFileName sURL:(NSString *)sURL {
    @autoreleasepool {
        @try {
            NSString *sPath = [CommonHelper IF_FilePath:sFileName];
            NSError *error = nil;
            NSDictionary *attributes = [[NSFileManager defaultManager]
                    attributesOfItemAtPath:sPath error:&error];
            if (!error) {
                NSMutableURLRequest *request = [[NSMutableURLRequest alloc] initWithURL:[NSURL URLWithString:sURL]];
                //NSString *sFilePath = sFilePath;
                NSInputStream *stream = [[NSInputStream alloc] initWithFileAtPath:sPath];
                [request setHTTPMethod:@"PUT"];
                [request setHTTPBodyStream:stream];
                NSNumber *size = attributes[NSFileSize];
                [request setValue:@"application/octet-stream" forHTTPHeaderField:@"Content-Type"];
                [request setValue:[[size stringValue] copy] forHTTPHeaderField:@"Content-Length"];
                [request setTimeoutInterval:120];

                NSURLResponse *oWebResponse;

                [NSURLConnection sendSynchronousRequest:request returningResponse:&oWebResponse error:nil];

                int code = (int) [(NSHTTPURLResponse *) oWebResponse statusCode];
                return !(oWebResponse == nil || code != 200);

            }
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }

    }
    return false;
}

+ (void)savePhotoComment: (O_Photo *)oPhoto {
    NSString *bUpdatePhotoComment = [CommonJson GetJsonKeyValue:PrefsKeys.kUpdatePhotoComment sJson:oPhoto.sField1];
    if ([NSString isNullOrEmpty:bUpdatePhotoComment] || [bUpdatePhotoComment integerValue] != 1) return;

    NSDictionary *oReturn = [IFConnection PostRequest:EndpointSavePhotoComment oParams:[@{
        @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
        @"sToken": [CommonUser currentToken],
        @"iPhotoID": [NSString stringWithFormat:@"%d", oPhoto.iSPhotoID],
        @"sComment": oPhoto.sComments
    } mutableCopy]];

    if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]) {
        oPhoto.sField1 = [CommonJson RemoveJsonKey:PrefsKeys.kUpdatePhotoComment sJson:oPhoto.sField1];
        [db_Media UpdatePhoto:oPhoto];
    }
}

@end
