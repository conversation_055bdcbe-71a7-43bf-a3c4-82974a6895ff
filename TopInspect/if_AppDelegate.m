//////
//  if_AppDelegate.m
//  InspectionFolio
//
//  Created by <PERSON> on 15/01/14.
//  Copyright (c) 2014 Cloudya. All rights reserved.
//

//#import "SALQuickTutorialViewController.h"
#import "Reachability.h"
#include <Foundation/Foundation.h>
//#import <AWSRuntime/AWSRuntime.h>
#import "if_ViewController.h"
#import "DB_Upgrade.h"
#import "if_NewSchedule.h"
#import "if_InspectionComment.h"
//#import <Analytics/SEGAnalytics.h>
#import <Firebase.h>
//#import <AWS/AWSS3.h>
//#import <AWSCore/AWSCore.h>
#import <AWSSimpleDB/AWSSimpleDB.h>
#import <GoogleSignIn/GIDSignIn.h>
#import <MSAL/MSALPublicClientApplication.h>
#import <Intercom/Intercom.h>
@import Extensions;

NSString *const DownloadKioskDataCompletedNotification = @"DownloadKioskDataCompleted";

@implementation if_AppDelegate

bool biPad, bLog, bTips;

//bool bisSyncing;
//NSString *sMessage;
NSMutableDictionary *oGlobalVar;

- (EKEventStore *)eventStore {
    if (!_eventStore) {
        _eventStore = [[EKEventStore alloc] init];
    }
   // else{
   //     bCreate = false;
   // }


    return _eventStore;
}
/*-(void)CreateCalendar{
    bool bCreate = true;
    if ([CommonHelper IFGetPref:@"CalendarID"] == nil || [[CommonHelper IFGetPref:@"CalendarID"] length] == 0){
        NSArray *calendars = [_eventStore calendarsForEntityType:EKEntityTypeEvent];
        for (EKCalendar *thisCalendar in calendars) {
            if (thisCalendar.title != nil && [thisCalendar.title isEqualToString:@"SnapInspect"]){
                bCreate = false;
                [CommonHelper IFSavePref:@"CalendarID" sValue:thisCalendar.calendarIdentifier]  ;
                break;
            }
        }
    }
    if (bCreate){
        //EKEventStore *eventStore = [[EKEventStore alloc] init];
        EKCalendar *calendar = [EKCalendar calendarWithEventStore:_eventStore];
        calendar.title = @"SnapInspect";
        
        // Iterate over all sources in the event store and look for the local source
        EKSource *theSource = nil;
         for (EKSource *source in _eventStore.sources) {
         if (source.sourceType == EKSourceTypeCalDAV) {
         theSource = source;
         break;
         }
         }
         
         if (theSource) {
         calendar.source = theSource;
         } else {
         DLog(@"Error: Local source not available");
         //return;
         }
        for (EKSource *source in self.eventStore.sources)
        {
            if (source.sourceType == EKSourceTypeCalDAV &&
                [source.title isEqualToString:@"iCloud"]) //Couldn't find better way, if there is, then tell me too. :)
            {
                theSource = source;
                break;
            }
        }
        
        if (theSource == nil)
        {
            for (EKSource *source in self.eventStore.sources)
            {
                if (source.sourceType == EKSourceTypeLocal)
                {
                    theSource = source;
                    break;
                }
            }
        }
        NSError *oError = nil;
        BOOL result = [_eventStore saveCalendar:calendar commit:YES error:&oError];
        if (result) {
            // DLog(@"Saved calendar to event store.")
            // NSString *bb =calendar.calendarIdentifier;
            //   NSString *cc = calendar.title;
            [CommonHelper IFSavePref:@"CalendarID" sValue:calendar.calendarIdentifier]  ;
            // self.calendarIdentifier = ;
        } else {
            // DLog(@"Error saving calendar: %@.", error);
        }
    }
}*/

+ (instancetype)shared {
    return (if_AppDelegate *)UIApplication.sharedApplication.delegate;
}

- (void)updateAuthorizationStatusToAccessEventStore {
    // 2
    EKAuthorizationStatus authorizationStatus = [EKEventStore authorizationStatusForEntityType:EKEntityTypeEvent];
    
    switch (authorizationStatus) {
            // 3
        case EKAuthorizationStatusDenied:
        case EKAuthorizationStatusRestricted: {
            [self ShowAlert:@"Message" sMessage:@"Please go to Setting -> Privacy -> Calendars to enable Permission for SnapInspect 3."];


             [CommonHelper SetEventEnabled:false];
           // [self.tableView reloadData];
            break;
        }
            
            // 4
        case EKAuthorizationStatusAuthorized:{
            [CommonHelper SetEventEnabled:true];
         //   [self CreateCalendar];
            NSArray *arrSchedule = [db_Schedule GetScheduled];
            NSDateFormatter *dateFormat = [[NSDateFormatter alloc] init];
            [dateFormat setDateFormat:@"MMM dd, yyyy HH:mm"];
            for (O_Schedule *oSchedule in arrSchedule){
                [db_Schedule AddEventBySScheduleID:oSchedule.iSScheduleID];
            }
            break;
        }
            // 5
        case EKAuthorizationStatusNotDetermined: {
            
            [self.eventStore requestAccessToEntityType:EKEntityTypeEvent
                                            completion:^(BOOL granted, NSError *error) {
                                                [CommonHelper SetEventEnabled:true];
                                               // [self CreateCalendar];
                                                NSArray *arrSchedule = [db_Schedule GetScheduled];
                                                NSDateFormatter *dateFormat = [[NSDateFormatter alloc] init];
                                                [dateFormat setDateFormat:@"MMM dd, yyyy HH:mm"];
                                                for (O_Schedule *oSchedule in arrSchedule){
                                                    [db_Schedule AddEventBySScheduleID:oSchedule.iSScheduleID];
                                                    
                                                }
                                                
                                            }];
            break;
        }
    }
}
-(void)RemoveCalendarEvent:(NSString *)sCalendarID{
    @try{
        EKEvent* eventToRemove = [[self eventStore] eventWithIdentifier:sCalendarID];
        if (eventToRemove) {
            NSError* error = nil;
            [[self eventStore]  removeEvent:eventToRemove span:EKSpanThisEvent commit:YES error:&error];
        }
    }@catch(NSException *ex){
        
    }

}
-(void)RemoveCalendar{
    NSArray *calendars = [_eventStore calendarsForEntityType:EKEntityTypeEvent];
    for (EKCalendar *thisCalendar in calendars) {
        if (thisCalendar.title != nil && [thisCalendar.title isEqualToString:@"SnapInspect"]){
            [_eventStore removeCalendar:thisCalendar commit:YES error:nil];
            break;
        }
    }
    [CommonHelper IFSavePref:@"CalendarID" sValue:@""];

}
-(NSString *)AddCalendarEvent:(NSString *)sTitle dtEventDate:(NSDate *)dtEventDate sNote:(NSString *)sNote {
    
    @try{
        EKEvent *event = [EKEvent eventWithEventStore:[self eventStore]];
        //EKCalendar *calendar = [[self eventStore] calendarWithIdentifier:[CommonHelper IFGetPref:@"CalendarID"]];
       // NSString *Sss = calendar.title;
        //event.calendar = calendar;
        event.title = [NSString stringWithFormat:@"SnapInspect - %@", sTitle];
        event.startDate = dtEventDate; //today
        event.endDate = [dtEventDate dateByAddingTimeInterval:60*60];  //set 1 hour meeting
        event.calendar = [[self eventStore] defaultCalendarForNewEvents];
        
        event.notes =sNote;
        
        //event.hasAlarms = true;
        //event.al
        NSError *err = nil;
        
        [[self eventStore] saveEvent:event span:EKSpanThisEvent commit:YES error:&err];
        return event.eventIdentifier;
        
    }@catch(NSException *ex){
        
    }
    return nil;
    
}
+(void)ShowTutorial:(NSString *)sTitle sMessage:(NSString *)sMessage{
    
   /* if (bTips){
        if ([CommonHelper IFGetPref:[NSString stringWithFormat:@"tips-%@", NSLocalizedString(sTitle, nil)]] == nil){
            if (quickTutorialViewController != nil){
                quickTutorialViewController = nil;
            }
             quickTutorialViewController = [[SALQuickTutorialViewController alloc] initWithKey:@"MyUniqueKey" title:NSLocalizedString(sTitle, nil) message:NSLocalizedString(sMessage, nil) image:nil];
            
            quickTutorialViewController.dismissesWithButton = YES;
            [quickTutorialViewController show];
            if ([(NSLocalizedString(sTitle, nil)) isEqualToString:@"Inspection Tips 2"]){
                bTips = false;
                [CommonHelper IFSavePref:@"bTutorial" sValue:@"false"];
                
            }
        }
    }*/
}
+(void)SetTips:(bool)_bTips{
    bTips = _bTips;
}
- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
    _callCenter = [[CTCallCenter alloc] init];
    _callCenter.callEventHandler = ^(CTCall* call) {
        // anounce that we've had a state change in our call center
        NSDictionary *dict = @{@"callState": call.callState};
        [[NSNotificationCenter defaultCenter] postNotificationName:@"CTCallStateDidChange" object:nil userInfo:dict];
    };

    // Setup the dependency libraries
    [CommonSetups configure];

    //Set NewUIType as default
   // if ([CommonHelper IFGetPref:bNewInspectionUI] == nil){
    //    [CommonHelper SetNewInspectionUI:YES];
   // }
        
   // [[Mint sharedInstance] initAndStartSession:@"8e35cb3b"];
   // [[Mint sharedInstance] setUserIdentifier:[CommonHelper IFGetPref:@"sEmail"]];
   //O_Asset *oAsset = [[O_Asset alloc] init];
    self.enhancedImages = [[NSMutableArray alloc] init];
    @try {
         NSString *sFilePath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES).firstObject;
        oGlobalVar = [[NSMutableDictionary alloc] init];
        [oGlobalVar setValue:sFilePath forKey:@"sFilePath"];
        NSString *sDBPath = [sFilePath stringByAppendingPathComponent:@"IF_Data.db"];
        [oGlobalVar setValue:sDBPath forKey:@"sDBPath"];
        if (![CommonHelper IF_FileExist:@"IF_Data.db"]){
            NSString *sDBResource = [[[NSBundle mainBundle] resourcePath] stringByAppendingPathComponent:@"IF_Data.db"];
            NSFileManager *oManager = [NSFileManager defaultManager];
          //  bool bExist = [oManager fileExistsAtPath:sDBResource];
            [oManager copyItemAtPath:sDBResource toPath:sDBPath error:nil];
            
        }
        [oGlobalVar setValue:[UIColor colorWithRed:219.0/255.0 green:54.0/255 blue:51.0/255.0 alpha:1] forKey:@"btn_Red"];
         [oGlobalVar setValue:[UIColor colorWithRed:144.0/255.0 green:188.0/255.0 blue:36.0/255.0 alpha:1] forKey:@"btn_Green"];
         [oGlobalVar setValue:[UIColor colorWithRed:222.0/255.0 green:222.0/255.0 blue:222.0/255.0 alpha:1] forKey:@"btn_Grey"];
         [oGlobalVar setValue:[UIColor colorWithRed:168/255.0 green:168/255.0 blue:168/255.0 alpha:1] forKey:@"lbl_Gray"];
        //bisSyncing = false;
        //if ([CommonHelper IFGetPref:@"iLayoutVerID"] == nil){
        //    [CommonHelper IFSavePref:@"iLayoutVerID" sValue:@"0"];
       // }
       
        if ([CommonHelper IFGetPref:@"iQPVerID"] == nil){
            [CommonHelper IFSavePref:@"iQPVerID" sValue:@"0"];
        }
        if ([CommonHelper IFGetPref:@"bTutorial"] == nil){
            [CommonHelper IFSavePref:@"bTutorial" sValue:@"true"];
        }
        bTips =[[CommonHelper IFGetPref:@"bTutorial"] boolValue];
        NSString *sCurrentAppVer = [CommonHelper IFGetPref:@"sCurrentAppVer"];

        if (sCurrentAppVer == nil || (![Constants.sAppVer isEqualToString:sCurrentAppVer])){
            [DB_Upgrade DB_Upgrade_1_0];
            [DB_Upgrade DB_Upgrade_1_1];
            [DB_Upgrade DB_Upgrade_1_5];
            
            [DB_Upgrade DB_Upgrade_1_6];
            [DB_Upgrade DB_Upgrade_1_7];
            [DB_Upgrade DB_Upgrade_1_8];
            [DB_Upgrade DB_Upgrade_1_9];
            [DB_Upgrade DB_Upgrade_2_0];
            [DB_Upgrade DB_Upgrade_2_1];
            [DB_Upgrade DB_Upgrade_2_2];
            [DB_Upgrade DB_Upgrade_2_3];
            [DB_Upgrade DB_Upgrade_2_4];
            [DB_Upgrade DB_Upgrade_2_5];
            [DB_Upgrade DB_Upgrade_2_6];
            [DB_Upgrade DB_Upgrade_2_7];

            [CommonHelper IFSavePref:@"sCurrentAppVer" sValue:Constants.sAppVer];
        }

        [DB_Upgrade DB_CreateInspectionView];
        [DB_Upgrade DB_CreateScheduleView];
        if ([CommonHelper IFGetPref:@"iCustomerID"] != nil){
           // NSOperationQueue *queue;
          //  [self UploadToSimpleDB];
        }
        biPad = [UIDevice.currentDevice userInterfaceIdiom] == UIUserInterfaceIdiomPad;
        //isKiosk = false;
       // kioskPath = @"";
        //bLog = [CommonHelper IFGetPref:@"bLog"] == nil ? false : [[CommonHelper IFGetPref:@"bLog"] boolValue];
        bLog = true;
        [[UIApplication sharedApplication] setIdleTimerDisabled:YES];

        [if_AppDelegate logUser];
        @try{
            NSString *sKey = [CommonHelper IFGetPref:@"sIntercomHash"];
            if (sKey != nil  && sKey.length > 0){
                dispatch_background_async(^{
                    [Intercom setApiKey:@"ios_sdk-2d4658d140897a8a9ad6e432821f09e1ca78c1b4" forAppId:@"y508wqoz"];
                    [Intercom setUserHash: sKey];
                    [CommonHelper IntercomLogin];
                });
            }
        }@catch(NSException *ex){
          //  NSString *cc = @"bb";
        }
        self.window.tintAdjustmentMode = UIViewTintAdjustmentModeNormal;
        
        UNUserNotificationCenter *notificationCenter = [UNUserNotificationCenter currentNotificationCenter];
        notificationCenter.delegate = self;
        
        return YES;
    }
    @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }

    return YES;
}

+(int) GetDeviceWidth{
    int iWidth = 0;
   // UIInterfaceOrientation interfaceOrientation = [UIApplication sharedApplication].statusBarOrientation;
    CGSize oSize = [[UIScreen mainScreen] bounds].size;
  //  if(UIInterfaceOrientationIsPortrait(interfaceOrientation)){
  //      iWidth = oSize.width;
  //  }else if(UIInterfaceOrientationIsLandscape(interfaceOrientation)){
        iWidth = oSize.width;
   // }
    return iWidth;
}

+(int) GetDeviceHeight{
    int iWidth = 0;
    // UIInterfaceOrientation interfaceOrientation = [UIApplication sharedApplication].statusBarOrientation;
    CGSize oSize = [[UIScreen mainScreen] bounds].size;
    //  if(UIInterfaceOrientationIsPortrait(interfaceOrientation)){
    //      iWidth = oSize.width;
    //  }else if(UIInterfaceOrientationIsLandscape(interfaceOrientation)){
    iWidth = oSize.height;
    // }
    return iWidth;
}
+(SI_Orientation)GetDeviceOrientation{
    CGRect scrRect = [[UIScreen mainScreen]bounds];
    if (scrRect.size.width > scrRect.size.height) { //portrait
        return SI_Orientation_Portrait;
    } else { //landscape
        return SI_Orientation_LandScape;
    }
}
+(void)logUser {
    NSString *sEmail = [CommonHelper IFGetPref:@"sEmail"];
    if (sEmail != nil && [sEmail length] > 0) {
        @try {
            NSString *userID = [CommonHelper IFGetPref:@"iCustomerID"];
            NSString *userName = [NSString stringWithFormat:@"%@ %@", [CommonHelper IFGetPref:@"sFirstName"], [CommonHelper IFGetPref:@"sLastName"]];

            [FIRCrashlytics.crashlytics setUserID: userID];
            [FIRCrashlytics.crashlytics setCustomValue: sEmail forKey: @"Email"];
            [FIRCrashlytics.crashlytics setCustomValue: userName forKey: @"UserName"];

            [FIRAnalytics setUserID: userID];
            [FIRAnalytics setUserPropertyString:sEmail forName:@"Email"];
            [FIRAnalytics setUserPropertyString:userName forName:@"UserName"];
            
            [DatadogHelper setWithUserID: userID name: userName email: sEmail];
        } @catch (NSException *ex) {

        }
    }
}

/*+(void)setsMessage:(NSString *)_sMessage{
    sMessage = _sMessage;
}
+(NSString *)getsMessage{
    return sMessage;
}*/
+(NSMutableDictionary *)GetGlobalVar{
    return oGlobalVar;
}
//+(void)setisSyncing:(bool)_bissyncing{
//    bisSyncing = _bissyncing;
//}
//+(bool)bisSyncing{
//    return bisSyncing;
//}

+ (BOOL)biPad {
    return biPad;
}

/*+(NSString *)sKioskPath{
    @try {
        return  kioskPath ;
    }
    @catch (NSException *ex) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex];
        return @"";
    }
}
+(void)SetKioskMode:(bool)bValue sKioskPath:(NSString *)sKioskPath{
    isKiosk = bValue;
    kioskPath = sKioskPath;
}
+(bool)bKioskMode{
    @try {
        return  isKiosk ;
    }
    @catch (NSException *ex) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex];
        return false;
    }
}*/

+ (BOOL)bLog {
    return bLog;
}

- (void)applicationWillResignActive:(UIApplication *)application
{

   
    // Sent when the application is about to move from active to inactive state. This can occur for certain types of temporary interruptions (such as an incoming phone call or SMS message) or when the user quits the application and it begins the transition to the background state.
    // Use this method to pause ongoing tasks, disable timers, and throttle down OpenGL ES frame rates. Games should use this method to pause the game.
}
- (void)applicationDidEnterBackground:(UIApplication *)application {
    UIViewController *vc = UIApplication.sharedApplication.topMost;
    if ([vc isKindOfClass:[ShootVideoViewController class]]) {
        [CommonAnalytics trackEvent:@"Visit Video Area - Enter background" meta:nil];
        __block UIBackgroundTaskIdentifier bgTask = [application beginBackgroundTaskWithExpirationHandler:^{
            [application endBackgroundTask:bgTask];
            bgTask = UIBackgroundTaskInvalid;
        }];
        [((ShootVideoViewController *) vc) finishBtTap];
        [application endBackgroundTask:bgTask];
        bgTask = UIBackgroundTaskInvalid;
    }
    /*UIViewController *vc = [self visibleViewController:[UIApplication sharedApplication].keyWindow.rootViewController];
    
    if ([vc isKindOfClass:[ShootVideoViewController class]]){
        
        bgTask = [[UIApplication  sharedApplication] beginBackgroundTaskWithExpirationHandler:^{
            [((ShootVideoViewController *)vc) finishBtTap];
            
        }];

        
    }*/
    // Use this method to release shared resources, save user data, invalidate timers, and store enough application state information to restore your application to its current state in case it is terminated later.
    // If your application supports background execution, this method is called instead of applicationWillTerminate: when the user quits.
}

- (void)applicationWillEnterForeground:(UIApplication *)application {
    // Called as part of the transition from the background to the inactive state; here you can undo many of the changes made on entering the background.
}

- (void)applicationDidBecomeActive:(UIApplication *)application {
    if ([CommonHelper IFGetPref:@"iCustomerID"] != nil) {
        [CommonHelper requestAuthorizationNotification];
    }
}

- (void)scheduleLocalNotification:(NSString*)title body:(NSString*)body userInfo:(NSDictionary *)userInfo {
    UNMutableNotificationContent *content = [UNMutableNotificationContent new];
    content.title = title;
    content.body = body;
    content.userInfo = userInfo;
    content.sound = [UNNotificationSound defaultSound];

    UNTimeIntervalNotificationTrigger *trigger = [UNTimeIntervalNotificationTrigger triggerWithTimeInterval:1
                                                                                                    repeats:NO];

    NSString *identifier = @"PushNotification";
    UNNotificationRequest *request = [UNNotificationRequest requestWithIdentifier:identifier
                                                                          content:content trigger:trigger];

    [UNUserNotificationCenter.currentNotificationCenter addNotificationRequest:request withCompletionHandler:^(NSError * _Nullable error) {
        if (error != nil) {
            DLog(@"Something went wrong: %@",error);
        }
    }];
}

- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler {
    if (application.isActive) {
        id alert = userInfo[@"aps"][@"alert"];
        NSString *title = @"";
        NSString *body = @"";
        if (alert != nil) {
            if ([alert isKindOfClass:[NSDictionary class]]) {
                title = alert[@"title"];
                body = alert[@"body"];
            } else if ([alert isKindOfClass:[NSString class]]) {
                body = alert;
            }
        
            @weakify(self)
            [NotificationBannerHelper showBannerWithTitle: title body: body onTap: ^{
                @strongify(self)
                [self parseNotificationUserInfo: userInfo];
            }];
        }
    } else {
        [self parseNotificationUserInfo:userInfo];
    }
    
    completionHandler(UIBackgroundFetchResultNewData);
}

- (NSString *)stringFromDeviceToken:(NSData *)deviceToken {
    NSUInteger length = deviceToken.length;
    if (length == 0) {
        return nil;
    }

    const unsigned char *buffer = (const unsigned char *)deviceToken.bytes;
    NSMutableString *hexString  = [NSMutableString stringWithCapacity:(length * 2)];
    for (int i = 0; i < length; ++i) {
        [hexString appendFormat:@"%02x", buffer[i]];
    }
    return [hexString copy];
}

- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken {
    if ([self bInternet:true]) {
        NSString *myDeviceToken = [self stringFromDeviceToken:deviceToken];
        myDeviceToken = [myDeviceToken stringByReplacingOccurrencesOfString:@" " withString:@""];
        @try {
            dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^ {
                @try {
                    NSString *sTestToken = [CommonHelper IFGetPref:@"sPushToken"];
                    NSString *sToken = [CommonUser currentToken];
                    int iTestTokenID = [[CommonHelper IFGetPref:@"iPushTokenID"] intValue];
                    NSString *iCustomerID = [CommonHelper IFGetPref:@"iCustomerID"];
                    if (![NSString isNullOrEmpty: myDeviceToken] && ![myDeviceToken isEqualToString:sTestToken]) {
                        if (iCustomerID != nil && ![NSString isNullOrEmpty: sToken]) {
                            NSMutableDictionary *oVideoToken = [@{
                                @"iCustomerID": iCustomerID, @"sToken": sToken, @"sType": ifPushTokenType,
                                @"sDeviceToken": myDeviceToken, @"iDeviceTokenID": [NSString stringWithFormat:@"%d", iTestTokenID]
                            } mutableCopy];

                            NSDictionary *oReturn = [IFConnection PostRequest:EndpointUpdateToken oParams:oVideoToken];
                            if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]) {
                                [CommonHelper IFSavePref:@"sPushToken" sValue:myDeviceToken];
                                iTestTokenID = [[oReturn[@"oPushToken"] valueForKey:@"iPushTokenID"] intValue];
                                [CommonHelper IFSavePref:@"iPushTokenID" sValue:[NSString stringWithFormat:@"%d", iTestTokenID]];
                            }
                        }
                    }
                } @catch (NSException *eeee) {

                }
            });

        } @catch (NSException *ex) {
            //  NSString *cc = @"bb";
        }

        //    [self ShowAlert:@"Message" sMessage:myDeviceToken];
        @try {
            [Intercom setDeviceToken:deviceToken failure:nil];
        } @catch (NSException *ex) {

        }
    }
}

- (void)applicationWillTerminate:(UIApplication *)application {
    // Called when the application is about to terminate. Save data if appropriate. See also applicationDidEnterBackground:.
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler {
    if (![userActivity.activityType isEqualToString:NSUserActivityTypeBrowsingWeb]) return NO;
    if (!userActivity.webpageURL) return NO;
    
    NSURL *webUrl = userActivity.webpageURL;
    NSString *sPath = [webUrl.path lowercaseString];
    if (sPath == nil || sPath.length == 0 || [sPath isEqualToString:@"/"]) return YES;

    NSDictionary *queryParams = [CommonHelper GetQueryParameter:webUrl];
    //Path "/external/request" is no longer in use. the new URL is webUrl
    if ([sPath containsString:@"/external/request"]) {
        NSString *serverURL = [NSString stringWithFormat:@"%@%@", ifServerURL, @"/SyncExternal/TokenExchange"];
        NSDictionary *oResponse = [IFConnection PostRequest:serverURL oParams:queryParams.mutableCopy];
        if (oResponse != nil && oResponse[@"success"] && oResponse[@"iRequestID"]) {
            NSString *sRequestID = oResponse[@"iRequestID"];
            NSString *sRequestCode = oResponse[@"sRequestCode"];
            NSString *sEmail = [CommonHelper IFGetPref:@"sEmail"];
            if (![NSString isNullOrEmpty:sEmail]) {
                [CommonHelper SetKioskMode:true];
            }

            NSString *sURL = [NSString
                    stringWithFormat:@"%@/SyncExternal/GetRequestExternalAccess?iRequestID=%@&sRequestCode=%@",
                            ifServerURL, sRequestID, sRequestCode];
            [self DownloadKioskData:sURL];
        } else {
            [self ShowAlert:@"Message" sMessage:oResponse[@"message"]];
        }
        return YES;
    } else if ([sPath containsString:@"/request"]) {
        if (![CommonHelper IFGetPref: @"sEmail"]) [CommonHelper SetKioskMode:true];
        NSString *sURL = [[webUrl absoluteString] stringByReplacingOccurrencesOfString:@"/Request" withString:@"/GetRequest"];
        [self DownloadKioskData:sURL];
        return YES;
    } else if ([sPath containsString: @"/external/schedule"]) { // Schedule
        [CommonHelper IFSavePref:kExternalScheduleCode sValue: queryParams[@"code"]];
        [NSNotificationCenter.defaultCenter postNotificationName: kNotificationExternalScheduleOpened object: nil];
        return YES;
    } else if ([sPath containsString:@"applink/assets/"]) { // Asset
        [CommonHelper IFSavePref:kExternalAssetID sValue: webUrl.lastPathComponent];
        [NSNotificationCenter.defaultCenter postNotificationName: kNotificationAssetDetailOpened object: nil];
        return YES;
    }

    // We couldn't handle the URL, open it in browser instead
    [[UIApplication sharedApplication] openURL:webUrl  options:@{} completionHandler:nil];
    return NO;
}

- (BOOL)bInternet:(bool)bSilent {
    return [self bInternet_WithMessage: @"Please connect to Internet to proceed." bSlient: bSilent];
}

- (BOOL)bInternet_WithMessage:(NSString *)sMessage {
    return [self bInternet_WithMessage: sMessage bSlient: NO];
}

- (BOOL)bInternet_WithMessage:(NSString *)sMessage bSlient: (BOOL)bSilent {
    NetworkStatus oStatus;
    @try {
        Reachability *reachability = [Reachability reachabilityForInternetConnection];
        oStatus = [reachability currentReachabilityStatus];
    } @catch(NSException *ex) {
        Log_Exception(ex);
    }

    BOOL connect = oStatus != NotReachable;
    if (!connect && !bSilent) {
        safe_dispatch_main_async(^{
            [self ShowAlert:@"Error" sMessage:sMessage];
        });
    }
    return connect;
}

- (void)GoToHomeIfKiosk {
    UIViewController *topMost = UIApplication.sharedApplication.topMost;
    if ([CommonHelper bKioskMode] && [topMost isKindOfClass:[if_ViewController class]]) {
        if_ViewController *homeVc = (if_ViewController *) topMost;
        [homeVc GoToHome];
    }
}

- (void)DownloadKioskData:(NSString *)urlStr {
    UIViewController *topMost = UIApplication.sharedApplication.topMost;
    
    MBProgressHUD *hud = [[MBProgressHUD alloc] si_initWithView:topMost.view];
    [topMost.view addSubview:hud];
    
    hud.labelText = @"Connecting to Server";
    [hud showAnimated:YES whileExecutingBlock:^{
        DLog(@"Downloading Started");
        [hud setDetailsLabelText:@"Downloading..."];
        NSData *urlData = [IFConnection DownloadFile_Data:[NSURL URLWithString:urlStr]];
        if (urlData) {
            NSDictionary *dictContent = [NSJSONSerialization JSONObjectWithData:urlData options:kNilOptions error:nil];
            //DLog(@"dictContent  %@", dictContent);
            if (dictContent) {
                [hud setDetailsLabelText:@"Processing..."];
                bool bSuccess = [CommonHelper GetBoolFromDictionary:dictContent sKey:@"success"];

                if (bSuccess) {
                    NSDictionary *oToken = dictContent[@"oToken"];
                    int iTokenID = [CommonHelper GetIntFromDictionary:oToken sKey:@"iExternalTokenID"];
                    NSString *sToken = [CommonHelper GetStringFromDictionary:oToken sKey:@"sExternalToken"];

                    NSString *sExternalRequestType = [CommonHelper GetStringFromDictionary:oToken sKey:@"sType"];
                    NSString *sRequesterEmail = [CommonHelper GetStringFromDictionary:oToken sKey:@"sEmail"];
                    NSString *sRequesterName = [CommonHelper GetStringFromDictionary:oToken sKey:@"sName"];
                    
                    if ([sExternalRequestType isEqualToString:@"Property"]) {
                        NSArray *arrProperties = dictContent[@"lsProperty"];
                        if (arrProperties != nil && arrProperties.count > 0) {
                            NSString *sPath = [[if_AppDelegate GetGlobalVar] valueForKeyPath:@"sFilePath"];
                            NSString *sFileName = [NSString stringWithFormat:@"%d_%@.json", iTokenID, [CommonHelper GetUniqueFileName]];
                            [urlData writeToFile:[sPath stringByAppendingPathComponent:sFileName] atomically:YES];

                            for (NSDictionary *oObj in arrProperties) {
                                @try {
                                    int iSAssetID = [CommonHelper GetIntFromDictionary:oObj sKey:@"iPropertyID"];
                                    O_Asset *oAsset = [db_Asset GetPropertyBySAssetID:iSAssetID];
                                    if (oAsset == nil || oAsset.iAssetID == 0) {
                                        oAsset = [[O_Asset alloc] init];
                                        oAsset.iAssetID = 0;
                                    } else if (![CommonHelper bExternalInspection:oAsset.sCustom2]) {
                                        break;
                                    }
                                    oAsset.iSAssetID = iSAssetID;
                                    @try {
                                        oAsset.iSPAssetID = [CommonHelper GetIntFromDictionary:oObj sKey:@"iSPropertyID"];
                                    } @catch (NSException *ex) {
                                        oAsset.iSPAssetID = 0;
                                    }
                                    oAsset.sAddress1 = [CommonHelper GetStringFromDictionary:oObj sKey:@"sAddress1"];
                                    oAsset.sAddress2 = [CommonHelper GetStringFromDictionary:oObj sKey:@"sAddress2"];
                                    oAsset.iCustomerID = [CommonHelper GetIntFromDictionary:oObj sKey:@"iCustomerID"];
                                    oAsset.sKey = [CommonHelper GetStringFromDictionary:oObj sKey:@"sKey"];
                                    oAsset.sAlarm = [CommonHelper GetStringFromDictionary:oObj sKey:@"sAlarm"];
                                    oAsset.dtInsDue = [CommonHelper GetStringFromDictionary:oObj sKey:@"dtDue"];
                                    oAsset.sFilter = [CommonHelper FilterString:oAsset.sAddress1 sAddress2:oAsset.sAddress2];;
                                    oAsset.iPLVerID = [CommonHelper GetIntFromDictionary:oObj sKey:@"iPLVerID"];
                                    //  oAsset.sCustom1 = [CommonHelper GetStringFromDictionary:oObj sKey:@"sCustom1"];
                                    //oAsset.sCustom2 = [CommonHelper GetStringFromDictionary:oObj sKey:@"sCustom2"];
                                    NSMutableDictionary *oCustom2 = [[NSMutableDictionary alloc] init];
                                    [oCustom2 setValue:[NSString stringWithFormat:@"%d", iTokenID] forKey:@"iTokenID"];

                                    [oCustom2 setValue:sFileName forKey:@"RIP"];
                                    //[oCustom1 setValue:[NSString stringWithFormat:@"%d", iRequestID] forKey:@"iRequestID"];
                                    [oCustom2 setValue:sToken forKey:@"sToken"];

                                    oAsset.sCustom2 = [CommonHelper DictionaryToJson:oCustom2];

                                    [db_Asset UpdateProperty:oAsset];
                                } @catch (NSException *eee) {
                                    [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"eee %@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:eee];
                                }

                            }
                            [CommonHelper SetTenantToolMode:true];
                            
                            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                                [self GoToHomeIfKiosk];
                                if ([UIApplication.sharedApplication.topMost isKindOfClass: if_Assets.class]) {
                                    if_Assets *selected = (if_Assets *)UIApplication.sharedApplication.topMost;
                                    [selected ReloadTable:true];
                                }
                            }];

                        } else {
                            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                                [self ShowAlert:@"Message"
                                       sMessage:@"Invalid asset share. Please contact the request sender for more details."];
                            }];
                        }

                    } else if ([sExternalRequestType isEqualToString:@"Schedule"]) {

                        NSDictionary *oDic = dictContent[@"oSchedule"];
                        int iSScheduleID = [CommonHelper GetIntFromDictionary:oDic sKey:@"iScheduleID"];

                        O_Schedule *oTemp = [db_Schedule GetScheduledBySScheduleID:iSScheduleID];
                        if (oTemp == nil || oTemp.iScheduleID == 0) {
                            // NSString *sRequestCode = [CommonHelper GetStringFromDictionary:oToken sKey:@"sExternalToken"];

                            NSString *sPath = [[if_AppDelegate GetGlobalVar] valueForKeyPath:@"sFilePath"];
                            NSString *sFileName = [NSString stringWithFormat:@"%d_%@.json", iTokenID, [CommonHelper GetUniqueFileName]];
                            [urlData writeToFile:[sPath stringByAppendingPathComponent:sFileName] atomically:YES];


                            O_Schedule *oSchedule = [[O_Schedule alloc] init];
                            oSchedule.iSScheduleID = iSScheduleID;


                            oSchedule.iSAssetID = [CommonHelper GetIntFromDictionary:oDic sKey:@"iSAssetID"];

                            oSchedule.iSInsTypeID = [CommonHelper GetIntFromDictionary:oDic sKey:@"iInsTypeID"];

                            oSchedule.sAddress1 = [CommonHelper GetStringFromDictionary:oDic sKey:@"sAddress1"];
                            oSchedule.sAddress2 = [CommonHelper GetStringFromDictionary:oDic sKey:@"sAddress2"];
                            oSchedule.sInsTitle = [CommonHelper GetStringFromDictionary:oDic sKey:@"sInsTitle"];

                            oSchedule.sType = [CommonHelper GetStringFromDictionary:oDic sKey:@"sType"];
                            oSchedule.sPTC = [CommonHelper GetStringFromDictionary:oDic sKey:@"sPTC"];

                            oSchedule.dtDateTime = [CommonHelper GetStringFromDictionary:oDic sKey:@"dtSchedule"];
                            NSMutableDictionary *oCustom1 = [[NSMutableDictionary alloc] init];
                            [oCustom1 setValue:[NSString stringWithFormat:@"%d", iTokenID] forKey:@"iTokenID"];

                            [oCustom1 setValue:sFileName forKey:@"RIP"];
                            //[oCustom1 setValue:[NSString stringWithFormat:@"%d", iRequestID] forKey:@"iRequestID"];
                            [oCustom1 setValue:sToken forKey:@"sToken"];
                            [oCustom1 setValue:sRequesterEmail forKey:@"REmail"];
                            [oCustom1 setValue:sRequesterName forKey:@"RName"];
                            [oCustom1 setValue:[CommonHelper GetStringFromDictionary:oDic sKey:@"sAdditionalInfo"] forKey:@"sAddInfo"];

                            oSchedule.sCustom1 = [CommonHelper DictionaryToJson:oCustom1];

                            [db_Schedule SaveSchedule:oSchedule];
                            [CommonHelper SetRequestInspectionMode:true];
                            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                                [self GoToHomeIfKiosk];
                                if ([UIApplication.sharedApplication.topMost isKindOfClass: if_NewSchedule.class]) {
                                    if_NewSchedule *scheduleVc = (if_NewSchedule *)UIApplication.sharedApplication.topMost;
                                    [scheduleVc reloadSchedules];
                                }
                            }];
                        } else {
                            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                                [self ShowAlert:@"Message" sMessage:@"Please click on Schedule tab to start inspection."];
                            }];
                        }
                    }

                } else {
                    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                        [self ShowAlert:@"Message" sMessage:[CommonHelper GetStringFromDictionary:dictContent sKey:@"message"]];
                    }];
                }

            } else {
                [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                    [self ShowAlert:@"Error" sMessage:@"Invalid Data 2"];
                }];

            }
        } else {
            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                [self ShowAlert:@"Error" sMessage:@"Invalid Data 1"];
            }];
        }

    } completionBlock:^{
        [hud removeFromSuperview];
        [NSNotificationCenter.defaultCenter postNotificationName:DownloadKioskDataCompletedNotification object:nil];
    }];
}

- (void)ShowAlert:(NSString *)sTitle sMessage:(NSString *)sMessage {
    [UIApplication.sharedApplication.topMost ShowAlert:sTitle sMessage:sMessage];
}

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options {
    return [GIDSignIn.sharedInstance handleURL:url]
            || [MSALPublicClientApplication handleMSALResponse:url sourceApplication:options[UIApplicationOpenURLOptionsSourceApplicationKey]]
            || [self handleOpenURL:url];
}

- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url sourceApplication:(NSString *)sourceApplication annotation:(id)annotation {
    return [self handleOpenURL:url];
}

- (BOOL)handleOpenURL:(NSURL *)url {
    //http://mytest.snapinspect.com/external/RequestInspection?iRequestID=5168&sRequestCode=123123
    
   // if ([[url path] isEqualToString:@"/external/requestinspection"]) {
   //     [CommonHelper IFSavePref:@"bKiosk" sValue:[NSString stringWithFormat:@"%d", 1]];
   // } else {  //QR Image
    @try{
        NSData * imageData = [[NSData alloc] initWithContentsOfURL: url];
        UIImage *image = [UIImage imageWithData: imageData];
        
        
        CIContext *context = [CIContext contextWithOptions:nil];
        CIDetector *detector = [CIDetector detectorOfType:CIDetectorTypeQRCode context:context options:@{CIDetectorAccuracy:CIDetectorAccuracyHigh}];
        CIImage *imageQRCode = [CIImage imageWithCGImage:image.CGImage];
        NSArray *features = [detector featuresInImage:imageQRCode];
        CIQRCodeFeature *feature = [features firstObject];
        
        NSString *result = feature.messageString;
        NSString *sTokenFull = [result stringByReplacingOccurrencesOfString:@"SI_ExternalIns://" withString:@""];
        if ([sTokenFull rangeOfString:@"_"].location != NSNotFound){
            NSArray *oArray = [sTokenFull componentsSeparatedByString:@"_"];
            NSString *sFullURL = [ifServerURL stringByAppendingFormat:@"/SyncExternal/GetRequestExternalAccess?iRequestID=%@&sRequestCode=%@", oArray[0], oArray[1]];
            NSString *sEmail = [CommonHelper IFGetPref:@"sEmail"];
            if (!sEmail){
                [CommonHelper SetKioskMode:true];
            }
            [self DownloadKioskData:sFullURL];
        } else {
            [self ShowAlert:@"Error" sMessage:@"Invalid."];
        }

    } @catch(NSException *ex) {
        [self ShowAlert:@"Error" sMessage:@"Invalid."];
    }

        /*DLog(@"ddd  %@", result);
        
        UIAlertController * alert = [UIAlertController
                                     alertControllerWithTitle:@"QR Code"
                                     message:result
                                     preferredStyle:UIAlertControllerStyleAlert];
        
        
        
        UIAlertAction* yesButton = [UIAlertAction
                                    actionWithTitle:@"Ok"
                                    style:UIAlertActionStyleDefault
                                    handler:^(UIAlertAction * action) {
                                        //Handle your yes please button action here
                                    }];
        
        [alert addAction:yesButton];
        
        [self.window.rootViewController presentViewController:alert animated:YES completion:nil];*/
    //}
    
    return YES;
}

#pragma mark -- UNUserNotificationCenterDelegate

- (void)userNotificationCenter:(UNUserNotificationCenter *)center willPresentNotification:(UNNotification *)notification withCompletionHandler:(void (^)(UNNotificationPresentationOptions))completionHandler {
    UNNotificationPresentationOptions options = UNNotificationPresentationOptionAlert + UNNotificationPresentationOptionSound;
    completionHandler(options);
}

- (void)userNotificationCenter:(UNUserNotificationCenter *)center didReceiveNotificationResponse:(UNNotificationResponse *)response withCompletionHandler:(void (^)())completionHandler {
    NSDictionary *infos = response.notification.request.content.userInfo;
    [self parseNotificationUserInfo: infos];
    completionHandler();
}

- (void)parseNotificationUserInfo: (NSDictionary *)infos {
    @try {
        NSDictionary *oDic = infos[@"aps"];
        if (oDic == nil) return;
        if ([[oDic valueForKey:@"bServerSync"] boolValue]) {
            [CommonHelper IFSavePref:@"bServerSync" sValue:@"1"];
        } else {
            NSString *sAction = [oDic valueForKey:@"si_action"];
            if (sAction == nil || !sAction.length) return;
            O_InboxAction *action = [[O_InboxAction alloc] init];
            action.type = [O_InboxAction getInboxType:sAction];
            action.param = [CommonHelper GetStringFromDictionary:oDic sKey:@"si_param"];

            UIViewController *topMost = [UIViewController topMostOf: self.window.rootViewController];
            switch (action.type) {
                case InboxTypeViewTaskComments:
                    [self parseTaskNotification:action params:action.param];
                    break;
                default:
                    if (![topMost isKindOfClass:if_Inbox.class]) {
                        UINavigationController *inboxRootNav = [UIStoryboard.main identifier: @"RootInboxController"];
                        if_Inbox *inbox = inboxRootNav.rootViewController;
                        inbox.defaultAction = action;
                        [topMost presentViewController:inboxRootNav animated:YES completion:nil];
                    } else {
                        if_Inbox *inbox = (if_Inbox *)topMost;
                        [inbox parseInboxAction:action];
                    }
            }
        }
    } @catch (NSException *ex) {
        Log_Exception(ex)
    }
}

@end
