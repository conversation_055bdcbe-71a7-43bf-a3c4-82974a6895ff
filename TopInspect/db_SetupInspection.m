//
//  db_SetupInspection.m
//  InspectionFolio
//
//  Created by <PERSON> on 31/01/14.
//  Copyright (c) 2014 Cloudya. All rights reserved.
//

#import "NSArray+HighOrderFunction.h"

@implementation db_SetupInspection
+(O_Layout *)SearchLayoutByID:(NSArray *)arrLayouts iSLayoutID:(int)iSLayoutID{
    @autoreleasepool {
        @try {
            for (O_Layout *oLayout in arrLayouts){
                
                
                if (oLayout.iSLayoutID == iSLayoutID){
                    return oLayout;
                }
            }
            return nil;
        }
        @catch (NSException *ex) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex];
            return nil;
        }
        
    }
}
/*+(int)AddInspectionByPass:(int)iSAssetID sAddress1:(NSString *)sAddress1 sAddress2:(NSString *)sAddress2 iSInsTypeID:(int)iSInsTypeID iSScheduleID:(int)iSScheduleID{
    @autoreleasepool {
        @try {
            NSArray *arrInsType  = [db_Common GetIntTypes];
            O_InsType *oInsType = [[O_InsType alloc] init];
            for (O_InsType *oTemp in arrInsType){
                if (oTemp.iSInsTypeID == iSInsTypeID){
                    oInsType = oTemp;
                    break;
                }
            }
            int iInspectionID = [db_SetupInspection InsertInspection:iSInsTypeID iSAssetID:iSAssetID sPTC:oInsType.sPTC sType:oInsType.sType sInsTitle:oInsType.sInsTitle sTitle:[NSString stringWithFormat:@"%@, %@", sAddress1, sAddress2] sAddress1:sAddress1 sAddress2:sAddress2 iSScheduleID:iSScheduleID ];
            if (iInspectionID > 0){
                NSArray *arr1stLayout = [db_Common GetLayouts:0 sPTC:oInsType.sPTC];
                int iCounter1 = 1;
                for (O_Layout *o1stLayout in arr1stLayout){
                    if (o1stLayout.bAutoAdd){
                        if ([oInsType.sType isEqualToString:@"S"] && (![o1stLayout.sQType isEqualToString:@"V"]) && (![o1stLayout.sQType isEqualToString:@"S"])){
                            [db_InsItem InsertInsItem:0 iInsID:iInspectionID iSLayoutID:o1stLayout.iSLayoutID sName:o1stLayout.sName sValue1:@"" sValue2:@"" sValue3:@"" sValue4:@"" sValue5:@"" sValue6:@"" sQType:o1stLayout.sQType sConfig1:o1stLayout.sSV1Config sConfig2:o1stLayout.sSV2Config sConfig3:o1stLayout.sSV3Config sConfig4:o1stLayout.sSV4Config sConfig5:o1stLayout.sSV5Config sConfig6:o1stLayout.sSV6Config sConfig:o1stLayout.sSConfig iSort:iCounter1];
                            iCounter1 ++;
                        }
                        else{
                            int iPInsItemID = [db_InsItem InsertInsItem:0 iInsID:iInspectionID iSLayoutID:o1stLayout.iSLayoutID sName:o1stLayout.sName sValue1:@"" sValue2:@"" sValue3:@"" sValue4:@"" sValue5:@"" sValue6:@"" sQType:o1stLayout.sQType sConfig1:o1stLayout.sFV1Config sConfig2:o1stLayout.sFV2Config sConfig3:o1stLayout.sFV3Config sConfig4:o1stLayout.sFV4Config sConfig5:o1stLayout.sFV5Config sConfig6:o1stLayout.sFV6Config sConfig:o1stLayout.sFConfig iSort:iCounter1];
                            iCounter1 ++;
                            // wrong   should use child id to get
                            NSArray *arr2ndLayout = [db_Common GetLayouts:o1stLayout.iSLayoutID sPTC:oInsType.sPTC];
                            int iCounter2 = 1;
                            for (O_Layout *o2ndLayout in arr2ndLayout){
                                @autoreleasepool {
                                    [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:o2ndLayout.iSLayoutID sName:o2ndLayout.sName sValue1:@"" sValue2:@"" sValue3:@"" sValue4:@"" sValue5:@"" sValue6:@"" sQType:o2ndLayout.sQType sConfig1:o2ndLayout.sFV1Config sConfig2:o2ndLayout.sFV2Config sConfig3:o2ndLayout.sFV3Config sConfig4:o2ndLayout.sFV4Config sConfig5:o2ndLayout.sFV5Config sConfig6:o2ndLayout.sFV6Config sConfig:o2ndLayout.sFConfig iSort:iCounter2];
                                    iCounter2++;
                                }
                            }
                        }
                    }
                }
            }
        }
        @catch (NSException *exception) {
            NSDictionary *data = [NSDictionary dictionaryWithObjects:[NSArray arrayWithObjects:@"db_SetupInspection.AddInspectionByPass", nil]
                                                             forKeys:[NSArray arrayWithObjects:@"Per", nil]];
            MintLogException(exception, data);
            return 0;
        }
        
    }
}*/
+(O_InsType *)GetInsType_SInsTypeID:(int) iSInsTypeID{
    @try{
        NSArray *arrInsType  = [db_Common GetIntTypes];
        O_InsType *oInsType = [[O_InsType alloc] init];
        for (O_InsType *oTemp in arrInsType){
            if (oTemp.iSInsTypeID == iSInsTypeID){
                oInsType = oTemp;
                break;
            }
        }
        return oInsType;
    }@catch(NSException *ex){
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex];
    }
    return nil;
}
+ (int) AddInspection:(NSArray *) arrAssetLayout
            iSAssetID:(int) iSAssetID
            sAddress1:(NSString *) sAddress1
            sAddress2:(NSString *) sAddress2
          iSInsTypeID:(int) iSInsTypeID
         iSScheduleID:(int) iSScheduleID
                 sLat:(CGFloat) sLat
                sLong:(CGFloat) sLong
    sCustom1_Schedule:(NSString *) sCustom1_Schedule 
           dtSchedule:(NSString *) dtSchedule {
    @autoreleasepool {
        @try {
            O_InsType *oInsType = [db_SetupInspection GetInsType_SInsTypeID:iSInsTypeID];
            if (oInsType == nil || oInsType.iInsTypeID  == 0){
                return 0;
            }
            int iInspectionID = [db_SetupInspection InsertInspection:iSInsTypeID iSAssetID:iSAssetID sPTC:oInsType.sPTC sType:oInsType.sType sInsTitle:oInsType.sInsTitle sTitle:[NSString stringWithFormat:@"%@, %@", sAddress1, sAddress2] sAddress1:sAddress1 sAddress2:sAddress2 iSScheduleID:iSScheduleID sLat:sLat sLong:sLong  sCustom1_Schedule:sCustom1_Schedule dtSchedule:dtSchedule];
            if (iInspectionID > 0){
                int iCounter1 = 1;
                // check if pass the layout
                NSArray *arr1stLayout = [[db_Common GetLayouts:0 sPTC:oInsType.sPTC] filter:^BOOL(O_Layout *oLayout) {
                    return ![CommonInspection bPassLayout:oLayout withInsType:oInsType];
                }];
                for (O_AssetLayout *oAssetLayout in arrAssetLayout){
                    @autoreleasepool {
                        O_Layout *o1stLayout = [db_SetupInspection SearchLayoutByID:arr1stLayout iSLayoutID:oAssetLayout.iSLayoutID];
                        // check if pass the layout
                        if ([CommonInspection bPassLayout:o1stLayout withInsType:oInsType]) continue;
                        if (o1stLayout != nil){
                            if ([oInsType.sType isEqualToString:@"S"]
                                    && (![o1stLayout.sQType isEqualToString:@"V"])
                                    && (![o1stLayout.sQType isEqualToString:@"S"])){
                                
                                                     
                                [db_InsItem InsertInsItem:0 iInsID:iInspectionID iSLayoutID:o1stLayout.iSLayoutID sName:oAssetLayout.sName sValue1:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV1Config] sValue2:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV2Config] sValue3:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV3Config] sValue4:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV4Config] sValue5:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV5Config] sValue6:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV6Config] sQType:o1stLayout.sQType sConfig1:o1stLayout.sSV1Config sConfig2:o1stLayout.sSV2Config sConfig3:o1stLayout.sSV3Config sConfig4:o1stLayout.sSV4Config sConfig5:o1stLayout.sSV5Config sConfig6:o1stLayout.sSV6Config sConfig:o1stLayout.sSConfig iSort:iCounter1 iSAssetLayoutID:oAssetLayout.iSAssetLayoutID sNameChanged:@""];
                                iCounter1 ++;
                                if (o1stLayout.sSConfig != nil && [CommonJson GetJsonKeyValue:@"ADD" sJson:o1stLayout.sSConfig] != nil){
                                    //o1stLayout.sSConfig = [o1stLayout.sSConfig stringByReplacingOccurrencesOfString:@"ADD" withString:@""];
                                    o1stLayout.sSConfig = [CommonJson RemoveJsonKey:@"ADD" sJson:o1stLayout.sSConfig];
                                }
                            }else{
                                NSString *sValue1 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV1Config];
                                NSString *sValue2 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV2Config];
                                NSString *sValue3 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV3Config];
                                NSString *sValue4 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV4Config];
                                NSString *sValue5 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV5Config];
                                NSString *sValue6 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV6Config];
                                int iPInsItemID = [db_InsItem InsertInsItem:0 iInsID:iInspectionID iSLayoutID:o1stLayout.iSLayoutID sName:oAssetLayout.sName sValue1:sValue1 sValue2:sValue2 sValue3:sValue3 sValue4:sValue4 sValue5:sValue5 sValue6:sValue6 sQType:o1stLayout.sQType sConfig1:o1stLayout.sFV1Config sConfig2:o1stLayout.sFV2Config sConfig3:o1stLayout.sFV3Config sConfig4:o1stLayout.sFV4Config sConfig5:o1stLayout.sFV5Config sConfig6:o1stLayout.sFV6Config sConfig:o1stLayout.sFConfig iSort:iCounter1 iSAssetLayoutID:oAssetLayout.iSAssetLayoutID sNameChanged:@""];
                                iCounter1 ++;
                                // wrong   should use child id to get
                                NSArray *arr2ndLayout;
                                // o1stLayout has ADD attribute, arr2ndLayout is retrieved from table ai_Layout
                                // If there is "_ProLayout_AreaOnly" in the sCompanyCustom1,
                                // then it will load the child layout area from the table ai_Layout
                                if ([CommonJson GetJsonKeyValue:@"ADD" sJson:o1stLayout.sFConfig] != nil
                                        || [NSString isNullOrEmpty:oAssetLayout.sChildID]
                                        || [oAssetLayout.sChildID isEqualToString:@"[]"]
                                        || [CommonPermission bProLayoutAreaOnly]) {
                                   arr2ndLayout = [db_Common GetLayouts:o1stLayout.iSLayoutID sPTC:oInsType.sPTC];
                                } else {
                                    arr2ndLayout = [db_Common GetLayoutsByIDString:oAssetLayout.sChildID iSPLayoutID:o1stLayout.iSLayoutID sPTC:o1stLayout.sPTC sQType:o1stLayout.sQType];
                                }
                                
                                // sort by iOrder
                                arr2ndLayout = [arr2ndLayout sortedArrayUsingComparator:^NSComparisonResult(O_Layout *layout1, O_Layout *layout2) {
                                    return [@(layout1.iOrder) compare:@(layout2.iOrder)];
                                }];
                                
                                int iCounter2 = 1;
                                for (O_Layout *o2ndLayout in arr2ndLayout){
                                    @autoreleasepool {
                                        if ([CommonInspection bPassLayout:o2ndLayout withInsType:oInsType]) continue;
                                        if ([o2ndLayout.sQType isEqualToString:@"P"]){
                                            [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:o2ndLayout.iSLayoutID sName:o2ndLayout.sName sValue1:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV1Config] sValue2:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV2Config] sValue3:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV3Config] sValue4:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV4Config] sValue5:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV5Config] sValue6:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV6Config] sQType:o2ndLayout.sQType sConfig1:o2ndLayout.sFV1Config sConfig2:o2ndLayout.sFV2Config sConfig3:o2ndLayout.sFV3Config sConfig4:o2ndLayout.sFV4Config sConfig5:o2ndLayout.sFV5Config sConfig6:o2ndLayout.sFV6Config sConfig:o2ndLayout.sFConfig iSort:iCounter2 iSAssetLayoutID:0 sNameChanged:o2ndLayout.bAutoAdd ? @"c" : @""];
                                        }
                                        else{
                                            [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:o2ndLayout.iSLayoutID sName:o2ndLayout.sName sValue1:sValue1 sValue2:sValue2 sValue3:sValue3 sValue4:sValue4 sValue5:sValue5 sValue6:sValue6 sQType:o2ndLayout.sQType sConfig1:o2ndLayout.sFV1Config sConfig2:o2ndLayout.sFV2Config sConfig3:o2ndLayout.sFV3Config sConfig4:o2ndLayout.sFV4Config sConfig5:o2ndLayout.sFV5Config sConfig6:o2ndLayout.sFV6Config sConfig:o2ndLayout.sFConfig iSort:iCounter2 iSAssetLayoutID:0 sNameChanged:o2ndLayout.bAutoAdd ?@"c"  : @""];
                                            
                                        }
                                        iCounter2++;
                                    }
                                }
                               /* NSArray *arrAddtionItems = [oAssetLayout.sMoreItems componentsSeparatedByString:@"[|]"];
                                for (NSString *sTempName in arrAddtionItems){
                                    @autoreleasepool {
                                        if (sTempName != nil && [sTempName length] > 0){
                                            [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:0 sName:sTempName sValue1:sValue1 sValue2:sValue2 sValue3:sValue3 sValue4:sValue4 sValue5:sValue5 sValue6:sValue5 sQType:o1stLayout.sQType sConfig1:@"" sConfig2:@"" sConfig3:@"" sConfig4:@"" sConfig5:@"" sConfig6:@"" sConfig:@"" iSort:iCounter2 iSAssetLayoutID:0];
                                        iCounter2++;
                                        }
                                    }
                                }*/

                                
                                if ([oInsType.sType isEqualToString:@"F"] && o1stLayout.sFConfig != nil && [CommonJson GetJsonKeyValue:@"ADD" sJson:o1stLayout.sFConfig] != nil){
                                    o1stLayout.sFConfig =  [CommonJson RemoveJsonKey:@"ADD" sJson:o1stLayout.sFConfig];
                                }
                                else if ([oInsType.sType isEqualToString:@"S"] && o1stLayout.sSConfig != nil && [CommonJson GetJsonKeyValue:@"ADD" sJson:o1stLayout.sSConfig] != nil){
                                    o1stLayout.sSConfig =  [CommonJson RemoveJsonKey:@"ADD" sJson:o1stLayout.sSConfig];
                                }

                            }
                        }
                    }
                }
                for (O_Layout *o1stLayout in arr1stLayout){
                    NSString *sConfig = [oInsType.sType isEqualToString:@"F"] ? (o1stLayout.sFConfig == nil ? @"" : (o1stLayout.sFConfig)) : (o1stLayout.sSConfig == nil ? @"" : (o1stLayout.sSConfig));
                    if ([CommonJson GetJsonKeyValue:@"ADD" sJson:sConfig] != nil){
                        if ([oInsType.sType isEqualToString:@"S"] && (![o1stLayout.sQType isEqualToString:@"V"]) && (![o1stLayout.sQType isEqualToString:@"S"])){
                            [db_InsItem InsertInsItem:0 iInsID:iInspectionID iSLayoutID:o1stLayout.iSLayoutID sName:o1stLayout.sName sValue1:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV1Config] sValue2:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV2Config] sValue3:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV3Config] sValue4:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV4Config] sValue5:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV5Config] sValue6:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV6Config] sQType:o1stLayout.sQType sConfig1:o1stLayout.sSV1Config sConfig2:o1stLayout.sSV2Config sConfig3:o1stLayout.sSV3Config sConfig4:o1stLayout.sSV4Config sConfig5:o1stLayout.sSV5Config sConfig6:o1stLayout.sSV6Config sConfig:o1stLayout.sSConfig iSort:iCounter1 iSAssetLayoutID:0 sNameChanged:@""];
                            iCounter1 ++;
                        }
                        else{
                            NSString *sValue1 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV1Config];
                            NSString *sValue2 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV2Config];
                            NSString *sValue3 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV3Config];
                            NSString *sValue4 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV4Config];
                            NSString *sValue5 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV5Config];
                            NSString *sValue6 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV6Config];
                            int iPInsItemID = [db_InsItem InsertInsItem:0 iInsID:iInspectionID iSLayoutID:o1stLayout.iSLayoutID sName:o1stLayout.sName sValue1:sValue1 sValue2:sValue2 sValue3:sValue3 sValue4:sValue4 sValue5:sValue5 sValue6:sValue6 sQType:o1stLayout.sQType sConfig1:o1stLayout.sFV1Config sConfig2:o1stLayout.sFV2Config sConfig3:o1stLayout.sFV3Config sConfig4:o1stLayout.sFV4Config sConfig5:o1stLayout.sFV5Config sConfig6:o1stLayout.sFV6Config sConfig:o1stLayout.sFConfig iSort:iCounter1 iSAssetLayoutID:0 sNameChanged:@""];
                            iCounter1 ++;
                            // wrong   should use child id to get
                            NSArray *arr2ndLayout = [db_Common GetLayouts:o1stLayout.iSLayoutID sPTC:oInsType.sPTC];
                            // sort by iOrder
                            arr2ndLayout = [arr2ndLayout sortedArrayUsingComparator:^NSComparisonResult(O_Layout *layout1, O_Layout *layout2) {
                                return [@(layout1.iOrder) compare:@(layout2.iOrder)];
                            }];
                            
                            int iCounter2 = 1;
                            for (O_Layout *o2ndLayout in arr2ndLayout){
                                @autoreleasepool {
                                    if ([CommonInspection bPassLayout:o2ndLayout withInsType:oInsType]) continue;
                                    if ([o2ndLayout.sQType isEqualToString:@"P"]){
                                          [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:o2ndLayout.iSLayoutID sName:o2ndLayout.sName sValue1:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV1Config] sValue2:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV2Config] sValue3:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV3Config] sValue4:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV4Config] sValue5:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV5Config] sValue6:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV6Config] sQType:o2ndLayout.sQType sConfig1:o2ndLayout.sFV1Config sConfig2:o2ndLayout.sFV2Config sConfig3:o2ndLayout.sFV3Config sConfig4:o2ndLayout.sFV4Config sConfig5:o2ndLayout.sFV5Config sConfig6:o2ndLayout.sFV6Config sConfig:o2ndLayout.sFConfig iSort:iCounter2 iSAssetLayoutID:0 sNameChanged:@""];
                                    }
                                    else{
                                        [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:o2ndLayout.iSLayoutID sName:o2ndLayout.sName sValue1:sValue1 sValue2:sValue2 sValue3:sValue3 sValue4:sValue4 sValue5:sValue5 sValue6:sValue6 sQType:o2ndLayout.sQType sConfig1:o2ndLayout.sFV1Config sConfig2:o2ndLayout.sFV2Config sConfig3:o2ndLayout.sFV3Config sConfig4:o2ndLayout.sFV4Config sConfig5:o2ndLayout.sFV5Config sConfig6:o2ndLayout.sFV6Config sConfig:o2ndLayout.sFConfig iSort:iCounter2 iSAssetLayoutID:0 sNameChanged:@""];
                                    }
                                    iCounter2++;
                                }
                            }
                        }
                    }
                }
            }
            return iInspectionID;
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return 0;
        }

    }

}

+ (int) AddInspection_JSON_TenantTool:(NSArray *) arrAssetLayout
                            iSAssetID:(int) iSAssetID
                            sAddress1:(NSString *) sAddress1
                            sAddress2:(NSString *) sAddress2
                             oInsType:(O_InsType *) oInsType
                         iSScheduleID:(int) iSScheduleID
                             sCustom2:(NSString *) sCustom2
                                 sLat:(CGFloat) sLat
                                sLong:(CGFloat) sLong
                                 sPTC:(NSString *) sPTC
                           dtSchedule:(NSString *) dtSchedule {
    @autoreleasepool {
        @try {
            NSString *sFileName = [CommonHelper sRequestInspection_FileName:sCustom2];

            if (oInsType == nil || oInsType.iInsTypeID  == 0){
                return 0;
            }
            int iInspectionID = [db_SetupInspection InsertInspection:oInsType.iSInsTypeID iSAssetID:iSAssetID sPTC:oInsType.sPTC sType:oInsType.sType sInsTitle:oInsType.sInsTitle sTitle:[NSString stringWithFormat:@"%@, %@", sAddress1, sAddress2] sAddress1:sAddress1 sAddress2:sAddress2 iSScheduleID:iSScheduleID sLat:sLat sLong:sLong sCustom1_Schedule:@"" dtSchedule:dtSchedule] ;
            O_Inspection *oIns = [db_Inspection GetInspectionByInsID:iInspectionID];
            oIns.sCustom2 = sCustom2;
            [db_Inspection UpdateInspectionCustom:oIns];
            if (iInspectionID > 0){
                int iCounter1 = 1;
                // check if pass the layout
                NSArray *arr1stLayout = [[json_Common LoadLayout:0 sFileName:sFileName sPTC:sPTC] filter:^BOOL(O_Layout *oLayout) {
                    return ![CommonInspection bPassLayout:oLayout withInsType:oInsType];
                }];
                for (O_AssetLayout *oAssetLayout in arrAssetLayout){
                    @autoreleasepool {
                        O_Layout *o1stLayout = [db_SetupInspection SearchLayoutByID:arr1stLayout iSLayoutID:oAssetLayout.iSLayoutID];
                        // check if pass the layout
                        if ([CommonInspection bPassLayout:o1stLayout withInsType:oInsType]) continue;
                        if (o1stLayout != nil){
                            if ([oInsType.sType isEqualToString:@"S"] && (![o1stLayout.sQType isEqualToString:@"V"]) && (![o1stLayout.sQType isEqualToString:@"S"])){
                                
                                
                                [db_InsItem InsertInsItem:0 iInsID:iInspectionID iSLayoutID:o1stLayout.iSLayoutID sName:oAssetLayout.sName sValue1:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV1Config] sValue2:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV2Config] sValue3:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV3Config] sValue4:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV4Config] sValue5:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV5Config] sValue6:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV6Config] sQType:o1stLayout.sQType sConfig1:o1stLayout.sSV1Config sConfig2:o1stLayout.sSV2Config sConfig3:o1stLayout.sSV3Config sConfig4:o1stLayout.sSV4Config sConfig5:o1stLayout.sSV5Config sConfig6:o1stLayout.sSV6Config sConfig:o1stLayout.sSConfig iSort:iCounter1 iSAssetLayoutID:oAssetLayout.iSAssetLayoutID sNameChanged:@""];
                                iCounter1 ++;
                                if (o1stLayout.sSConfig != nil && [CommonJson GetJsonKeyValue:@"ADD" sJson:o1stLayout.sSConfig] != nil){
                                    //o1stLayout.sSConfig = [o1stLayout.sSConfig stringByReplacingOccurrencesOfString:@"ADD" withString:@""];
                                    o1stLayout.sSConfig = [CommonJson RemoveJsonKey:@"ADD" sJson:o1stLayout.sSConfig];
                                }
                            }else{
                                NSString *sValue1 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV1Config];
                                NSString *sValue2 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV2Config];
                                NSString *sValue3 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV3Config];
                                NSString *sValue4 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV4Config];
                                NSString *sValue5 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV5Config];
                                NSString *sValue6 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV6Config];
                                int iPInsItemID = [db_InsItem InsertInsItem:0 iInsID:iInspectionID iSLayoutID:o1stLayout.iSLayoutID sName:oAssetLayout.sName sValue1:sValue1 sValue2:sValue2 sValue3:sValue3 sValue4:sValue4 sValue5:sValue5 sValue6:sValue6 sQType:o1stLayout.sQType sConfig1:o1stLayout.sFV1Config sConfig2:o1stLayout.sFV2Config sConfig3:o1stLayout.sFV3Config sConfig4:o1stLayout.sFV4Config sConfig5:o1stLayout.sFV5Config sConfig6:o1stLayout.sFV6Config sConfig:o1stLayout.sFConfig iSort:iCounter1 iSAssetLayoutID:oAssetLayout.iSAssetLayoutID sNameChanged:@""];
                                iCounter1 ++;
                                // wrong   should use child id to get
                                // NSArray *arr2ndLayout = [db_Common GetLayoutsByIDString:oAssetLayout.sChildID iSPLayoutID:o1stLayout.iSLayoutID sPTC:o1stLayout.sPTC sQType:o1stLayout.sQType];
                                NSArray *arr2ndLayout = [json_Common LoadLayoutByIDString:o1stLayout.iSLayoutID sChildID:oAssetLayout.sChildID sFileName:sFileName sParentQType:o1stLayout.sQType];
                                // sort by "iOrder"
                                arr2ndLayout = [arr2ndLayout sortedArrayUsingComparator:^NSComparisonResult(O_Layout *layout1, O_Layout *layout2) {
                                    return [@(layout1.iOrder) compare:@(layout2.iOrder)];
                                }];
                                
                                int iCounter2 = 1;
                                for (O_Layout *o2ndLayout in arr2ndLayout){
                                    @autoreleasepool {
                                        if ([CommonInspection bPassLayout:o2ndLayout withInsType:oInsType]) continue;
                                        if ([o2ndLayout.sQType isEqualToString:@"P"]){
                                            [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:o2ndLayout.iSLayoutID sName:o2ndLayout.sName sValue1:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV1Config] sValue2:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV2Config] sValue3:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV3Config] sValue4:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV4Config] sValue5:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV5Config] sValue6:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV6Config] sQType:o2ndLayout.sQType sConfig1:o2ndLayout.sFV1Config sConfig2:o2ndLayout.sFV2Config sConfig3:o2ndLayout.sFV3Config sConfig4:o2ndLayout.sFV4Config sConfig5:o2ndLayout.sFV5Config sConfig6:o2ndLayout.sFV6Config sConfig:o2ndLayout.sFConfig iSort:iCounter2 iSAssetLayoutID:0 sNameChanged:o2ndLayout.bAutoAdd ? @"c" : @""];
                                        }
                                        else{
                                            [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:o2ndLayout.iSLayoutID sName:o2ndLayout.sName sValue1:sValue1 sValue2:sValue2 sValue3:sValue3 sValue4:sValue4 sValue5:sValue5 sValue6:sValue6 sQType:o2ndLayout.sQType sConfig1:o2ndLayout.sFV1Config sConfig2:o2ndLayout.sFV2Config sConfig3:o2ndLayout.sFV3Config sConfig4:o2ndLayout.sFV4Config sConfig5:o2ndLayout.sFV5Config sConfig6:o2ndLayout.sFV6Config sConfig:o2ndLayout.sFConfig iSort:iCounter2 iSAssetLayoutID:0 sNameChanged:o2ndLayout.bAutoAdd ?@"c"  : @""];
                                            
                                        }
                                        iCounter2++;
                                    }
                                }
                                /* NSArray *arrAddtionItems = [oAssetLayout.sMoreItems componentsSeparatedByString:@"[|]"];
                                 for (NSString *sTempName in arrAddtionItems){
                                 @autoreleasepool {
                                 if (sTempName != nil && [sTempName length] > 0){
                                 [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:0 sName:sTempName sValue1:sValue1 sValue2:sValue2 sValue3:sValue3 sValue4:sValue4 sValue5:sValue5 sValue6:sValue5 sQType:o1stLayout.sQType sConfig1:@"" sConfig2:@"" sConfig3:@"" sConfig4:@"" sConfig5:@"" sConfig6:@"" sConfig:@"" iSort:iCounter2 iSAssetLayoutID:0];
                                 iCounter2++;
                                 }
                                 }
                                 }*/
                                
                                
                                if ([oInsType.sType isEqualToString:@"F"] && o1stLayout.sFConfig != nil && [CommonJson GetJsonKeyValue:@"ADD" sJson:o1stLayout.sFConfig] != nil){
                                    o1stLayout.sFConfig =  [CommonJson RemoveJsonKey:@"ADD" sJson:o1stLayout.sFConfig];
                                }
                                else if ([oInsType.sType isEqualToString:@"S"] && o1stLayout.sSConfig != nil && [CommonJson GetJsonKeyValue:@"ADD" sJson:o1stLayout.sSConfig] != nil){
                                    o1stLayout.sSConfig =  [CommonJson RemoveJsonKey:@"ADD" sJson:o1stLayout.sSConfig];
                                }
                                
                            }
                        }
                    }
                }
                for (O_Layout *o1stLayout in arr1stLayout){
                    NSString *sConfig = [oInsType.sType isEqualToString:@"F"] ? (o1stLayout.sFConfig == nil ? @"" : (o1stLayout.sFConfig)) : (o1stLayout.sSConfig == nil ? @"" : (o1stLayout.sSConfig));
                    if ([CommonJson GetJsonKeyValue:@"ADD" sJson:sConfig] != nil){
                        if ([oInsType.sType isEqualToString:@"S"] && (![o1stLayout.sQType isEqualToString:@"V"]) && (![o1stLayout.sQType isEqualToString:@"S"])){
                            [db_InsItem InsertInsItem:0 iInsID:iInspectionID iSLayoutID:o1stLayout.iSLayoutID sName:o1stLayout.sName sValue1:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV1Config] sValue2:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV2Config] sValue3:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV3Config] sValue4:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV4Config] sValue5:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV5Config] sValue6:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV6Config] sQType:o1stLayout.sQType sConfig1:o1stLayout.sSV1Config sConfig2:o1stLayout.sSV2Config sConfig3:o1stLayout.sSV3Config sConfig4:o1stLayout.sSV4Config sConfig5:o1stLayout.sSV5Config sConfig6:o1stLayout.sSV6Config sConfig:o1stLayout.sSConfig iSort:iCounter1 iSAssetLayoutID:0 sNameChanged:@""];
                            iCounter1 ++;
                        }
                        else{
                            NSString *sValue1 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV1Config];
                            NSString *sValue2 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV2Config];
                            NSString *sValue3 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV3Config];
                            NSString *sValue4 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV4Config];
                            NSString *sValue5 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV5Config];
                            NSString *sValue6 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV6Config];
                            int iPInsItemID = [db_InsItem InsertInsItem:0 iInsID:iInspectionID iSLayoutID:o1stLayout.iSLayoutID sName:o1stLayout.sName sValue1:sValue1 sValue2:sValue2 sValue3:sValue3 sValue4:sValue4 sValue5:sValue5 sValue6:sValue6 sQType:o1stLayout.sQType sConfig1:o1stLayout.sFV1Config sConfig2:o1stLayout.sFV2Config sConfig3:o1stLayout.sFV3Config sConfig4:o1stLayout.sFV4Config sConfig5:o1stLayout.sFV5Config sConfig6:o1stLayout.sFV6Config sConfig:o1stLayout.sFConfig iSort:iCounter1 iSAssetLayoutID:0 sNameChanged:@""];
                            iCounter1 ++;
                            // wrong   should use child id to get
                            //NSArray *arr2ndLayout = [db_Common GetLayouts:o1stLayout.iSLayoutID sPTC:oInsType.sPTC];
                            NSArray *arr2ndLayout = [json_Common LoadLayout:o1stLayout.iSLayoutID sFileName:sFileName sPTC:sPTC];
                            // sort by "iOrder"
                            arr2ndLayout = [arr2ndLayout sortedArrayUsingComparator:^NSComparisonResult(O_Layout *layout1, O_Layout *layout2) {
                                return [@(layout1.iOrder) compare:@(layout2.iOrder)];
                            }];
                            
                            int iCounter2 = 1;
                            for (O_Layout *o2ndLayout in arr2ndLayout){
                                @autoreleasepool {
                                    if ([CommonInspection bPassLayout:o2ndLayout withInsType:oInsType]) continue;
                                    if ([o2ndLayout.sQType isEqualToString:@"P"]){
                                        [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:o2ndLayout.iSLayoutID sName:o2ndLayout.sName sValue1:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV1Config] sValue2:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV2Config] sValue3:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV3Config] sValue4:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV4Config] sValue5:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV5Config] sValue6:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV6Config] sQType:o2ndLayout.sQType sConfig1:o2ndLayout.sFV1Config sConfig2:o2ndLayout.sFV2Config sConfig3:o2ndLayout.sFV3Config sConfig4:o2ndLayout.sFV4Config sConfig5:o2ndLayout.sFV5Config sConfig6:o2ndLayout.sFV6Config sConfig:o2ndLayout.sFConfig iSort:iCounter2 iSAssetLayoutID:0 sNameChanged:@""];
                                    }
                                    else{
                                        [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:o2ndLayout.iSLayoutID sName:o2ndLayout.sName sValue1:sValue1 sValue2:sValue2 sValue3:sValue3 sValue4:sValue4 sValue5:sValue5 sValue6:sValue6 sQType:o2ndLayout.sQType sConfig1:o2ndLayout.sFV1Config sConfig2:o2ndLayout.sFV2Config sConfig3:o2ndLayout.sFV3Config sConfig4:o2ndLayout.sFV4Config sConfig5:o2ndLayout.sFV5Config sConfig6:o2ndLayout.sFV6Config sConfig:o2ndLayout.sFConfig iSort:iCounter2 iSAssetLayoutID:0 sNameChanged:@""];
                                    }
                                    iCounter2++;
                                }
                            }
                        }
                    }
                }
            }
            return iInspectionID;
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return 0;
        }
        
    }
    
}

+ (int) AddInspection_JSON:(NSArray *) arrAssetLayout
                 iSAssetID:(int) iSAssetID
                 sAddress1:(NSString *) sAddress1
                 sAddress2:(NSString *) sAddress2
               iSInsTypeID:(int) iSInsTypeID
              iSScheduleID:(int) iSScheduleID
                  sCustom1:(NSString *) sCustom1
                      sLat:(CGFloat) sLat
                     sLong:(CGFloat) sLong
         sCustom1_Schedule:(NSString *) sCustom1_Schedule
                dtSchedule:(NSString *) dtSchedule {
    @autoreleasepool {
        @try {
            NSString *sFileName = [CommonHelper sRequestInspection_FileName:sCustom1];
            O_InsType *oInsType = [json_Common LoadInsType:sFileName];
            NSString *sPTC = oInsType.sPTC;
            if (oInsType == nil || oInsType.iInsTypeID  == 0){
                return 0;
            }
            int iInspectionID = [db_SetupInspection InsertInspection:iSInsTypeID iSAssetID:iSAssetID sPTC:oInsType.sPTC sType:oInsType.sType sInsTitle:oInsType.sInsTitle sTitle:[NSString stringWithFormat:@"%@, %@", sAddress1, sAddress2] sAddress1:sAddress1 sAddress2:sAddress2 iSScheduleID:iSScheduleID sLat:sLat sLong:sLong sCustom1_Schedule:sCustom1_Schedule dtSchedule:dtSchedule];
            O_Inspection *oIns = [db_Inspection GetInspectionByInsID:iInspectionID];
            oIns.sCustom2 = sCustom1;
            [db_Inspection UpdateInspectionCustom:oIns];
            if (iInspectionID > 0){
                int iCounter1 = 1;
                // check if pass the layout
                NSArray *arr1stLayout = [[json_Common LoadLayout:0 sFileName:sFileName sPTC:sPTC] filter:^BOOL(O_Layout *oLayout) {
                    return ![CommonInspection bPassLayout:oLayout withInsType:oInsType];
                }];

                for (O_AssetLayout *oAssetLayout in arrAssetLayout){
                    @autoreleasepool {
                        O_Layout *o1stLayout = [db_SetupInspection SearchLayoutByID:arr1stLayout iSLayoutID:oAssetLayout.iSLayoutID];
                        // check if pass the layout
                        if ([CommonInspection bPassLayout:o1stLayout withInsType:oInsType]) continue;
                        
                        if (o1stLayout != nil){
                            if ([oInsType.sType isEqualToString:@"S"] && (![o1stLayout.sQType isEqualToString:@"V"]) && (![o1stLayout.sQType isEqualToString:@"S"])){
                                
                                
                                [db_InsItem InsertInsItem:0 iInsID:iInspectionID iSLayoutID:o1stLayout.iSLayoutID sName:oAssetLayout.sName sValue1:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV1Config] sValue2:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV2Config] sValue3:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV3Config] sValue4:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV4Config] sValue5:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV5Config] sValue6:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV6Config] sQType:o1stLayout.sQType sConfig1:o1stLayout.sSV1Config sConfig2:o1stLayout.sSV2Config sConfig3:o1stLayout.sSV3Config sConfig4:o1stLayout.sSV4Config sConfig5:o1stLayout.sSV5Config sConfig6:o1stLayout.sSV6Config sConfig:o1stLayout.sSConfig iSort:iCounter1 iSAssetLayoutID:oAssetLayout.iSAssetLayoutID sNameChanged:@""];
                                iCounter1 ++;
                                if (o1stLayout.sSConfig != nil && [CommonJson GetJsonKeyValue:@"ADD" sJson:o1stLayout.sSConfig] != nil){
                                    //o1stLayout.sSConfig = [o1stLayout.sSConfig stringByReplacingOccurrencesOfString:@"ADD" withString:@""];
                                    o1stLayout.sSConfig = [CommonJson RemoveJsonKey:@"ADD" sJson:o1stLayout.sSConfig];
                                }
                            }else{
                                NSString *sValue1 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV1Config];
                                NSString *sValue2 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV2Config];
                                NSString *sValue3 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV3Config];
                                NSString *sValue4 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV4Config];
                                NSString *sValue5 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV5Config];
                                NSString *sValue6 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV6Config];
                                int iPInsItemID = [db_InsItem InsertInsItem:0 iInsID:iInspectionID iSLayoutID:o1stLayout.iSLayoutID sName:oAssetLayout.sName sValue1:sValue1 sValue2:sValue2 sValue3:sValue3 sValue4:sValue4 sValue5:sValue5 sValue6:sValue6 sQType:o1stLayout.sQType sConfig1:o1stLayout.sFV1Config sConfig2:o1stLayout.sFV2Config sConfig3:o1stLayout.sFV3Config sConfig4:o1stLayout.sFV4Config sConfig5:o1stLayout.sFV5Config sConfig6:o1stLayout.sFV6Config sConfig:o1stLayout.sFConfig iSort:iCounter1 iSAssetLayoutID:oAssetLayout.iSAssetLayoutID sNameChanged:@""];
                                iCounter1 ++;

                                // For request inspection, load the child layout items from the xml file directly
                                NSArray *arr2ndLayout = [json_Common LoadLayout:o1stLayout.iSLayoutID sFileName:sFileName sPTC:o1stLayout.sPTC];
                                // sort by "iOrder"
                                arr2ndLayout = [arr2ndLayout sortedArrayUsingComparator:^NSComparisonResult(O_Layout *layout1, O_Layout *layout2) {
                                    return [@(layout1.iOrder) compare:@(layout2.iOrder)];
                                }];
                                
                                int iCounter2 = 1;
                                for (O_Layout *o2ndLayout in arr2ndLayout){
                                    @autoreleasepool {
                                        if ([CommonInspection bPassLayout:o2ndLayout withInsType:oInsType]) continue;
                                        if ([o2ndLayout.sQType isEqualToString:@"P"]){
                                            [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:o2ndLayout.iSLayoutID sName:o2ndLayout.sName sValue1:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV1Config] sValue2:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV2Config] sValue3:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV3Config] sValue4:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV4Config] sValue5:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV5Config] sValue6:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV6Config] sQType:o2ndLayout.sQType sConfig1:o2ndLayout.sFV1Config sConfig2:o2ndLayout.sFV2Config sConfig3:o2ndLayout.sFV3Config sConfig4:o2ndLayout.sFV4Config sConfig5:o2ndLayout.sFV5Config sConfig6:o2ndLayout.sFV6Config sConfig:o2ndLayout.sFConfig iSort:iCounter2 iSAssetLayoutID:0 sNameChanged:o2ndLayout.bAutoAdd ? @"c" : @""];
                                        }
                                        else{
                                            [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:o2ndLayout.iSLayoutID sName:o2ndLayout.sName sValue1:sValue1 sValue2:sValue2 sValue3:sValue3 sValue4:sValue4 sValue5:sValue5 sValue6:sValue6 sQType:o2ndLayout.sQType sConfig1:o2ndLayout.sFV1Config sConfig2:o2ndLayout.sFV2Config sConfig3:o2ndLayout.sFV3Config sConfig4:o2ndLayout.sFV4Config sConfig5:o2ndLayout.sFV5Config sConfig6:o2ndLayout.sFV6Config sConfig:o2ndLayout.sFConfig iSort:iCounter2 iSAssetLayoutID:0 sNameChanged:o2ndLayout.bAutoAdd ?@"c"  : @""];
                                            
                                        }
                                        iCounter2++;
                                    }
                                }
                                /* NSArray *arrAddtionItems = [oAssetLayout.sMoreItems componentsSeparatedByString:@"[|]"];
                                 for (NSString *sTempName in arrAddtionItems){
                                 @autoreleasepool {
                                 if (sTempName != nil && [sTempName length] > 0){
                                 [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:0 sName:sTempName sValue1:sValue1 sValue2:sValue2 sValue3:sValue3 sValue4:sValue4 sValue5:sValue5 sValue6:sValue5 sQType:o1stLayout.sQType sConfig1:@"" sConfig2:@"" sConfig3:@"" sConfig4:@"" sConfig5:@"" sConfig6:@"" sConfig:@"" iSort:iCounter2 iSAssetLayoutID:0];
                                 iCounter2++;
                                 }
                                 }
                                 }*/
                                
                                
                                if ([oInsType.sType isEqualToString:@"F"] && o1stLayout.sFConfig != nil && [CommonJson GetJsonKeyValue:@"ADD" sJson:o1stLayout.sFConfig] != nil){
                                    o1stLayout.sFConfig =  [CommonJson RemoveJsonKey:@"ADD" sJson:o1stLayout.sFConfig];
                                }
                                else if ([oInsType.sType isEqualToString:@"S"] && o1stLayout.sSConfig != nil && [CommonJson GetJsonKeyValue:@"ADD" sJson:o1stLayout.sSConfig] != nil){
                                    o1stLayout.sSConfig =  [CommonJson RemoveJsonKey:@"ADD" sJson:o1stLayout.sSConfig];
                                }
                                
                            }
                        }
                    }
                }
                for (O_Layout *o1stLayout in arr1stLayout){
                    NSString *sConfig = [oInsType.sType isEqualToString:@"F"] ? (o1stLayout.sFConfig == nil ? @"" : (o1stLayout.sFConfig)) : (o1stLayout.sSConfig == nil ? @"" : (o1stLayout.sSConfig));
                    if ([CommonJson GetJsonKeyValue:@"ADD" sJson:sConfig] != nil){
                        if ([oInsType.sType isEqualToString:@"S"] && (![o1stLayout.sQType isEqualToString:@"V"]) && (![o1stLayout.sQType isEqualToString:@"S"])){
                            [db_InsItem InsertInsItem:0 iInsID:iInspectionID iSLayoutID:o1stLayout.iSLayoutID sName:o1stLayout.sName sValue1:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV1Config] sValue2:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV2Config] sValue3:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV3Config] sValue4:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV4Config] sValue5:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV5Config] sValue6:[CommonHelper GetDefaultCheckboxValue:o1stLayout.sSV6Config] sQType:o1stLayout.sQType sConfig1:o1stLayout.sSV1Config sConfig2:o1stLayout.sSV2Config sConfig3:o1stLayout.sSV3Config sConfig4:o1stLayout.sSV4Config sConfig5:o1stLayout.sSV5Config sConfig6:o1stLayout.sSV6Config sConfig:o1stLayout.sSConfig iSort:iCounter1 iSAssetLayoutID:0 sNameChanged:@""];
                            iCounter1 ++;
                        }
                        else{
                            NSString *sValue1 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV1Config];
                            NSString *sValue2 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV2Config];
                            NSString *sValue3 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV3Config];
                            NSString *sValue4 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV4Config];
                            NSString *sValue5 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV5Config];
                            NSString *sValue6 = [CommonHelper GetDefaultCheckboxValue:o1stLayout.sFV6Config];
                            int iPInsItemID = [db_InsItem InsertInsItem:0 iInsID:iInspectionID iSLayoutID:o1stLayout.iSLayoutID sName:o1stLayout.sName sValue1:sValue1 sValue2:sValue2 sValue3:sValue3 sValue4:sValue4 sValue5:sValue5 sValue6:sValue6 sQType:o1stLayout.sQType sConfig1:o1stLayout.sFV1Config sConfig2:o1stLayout.sFV2Config sConfig3:o1stLayout.sFV3Config sConfig4:o1stLayout.sFV4Config sConfig5:o1stLayout.sFV5Config sConfig6:o1stLayout.sFV6Config sConfig:o1stLayout.sFConfig iSort:iCounter1 iSAssetLayoutID:0 sNameChanged:@""];
                            iCounter1 ++;
                            // wrong   should use child id to get
                            //NSArray *arr2ndLayout = [db_Common GetLayouts:o1stLayout.iSLayoutID sPTC:oInsType.sPTC];
                            NSArray *arr2ndLayout = [json_Common LoadLayout:o1stLayout.iSLayoutID sFileName:sFileName sPTC:sPTC];
                            // sort by "iOrder"
                            arr2ndLayout = [arr2ndLayout sortedArrayUsingComparator:^NSComparisonResult(O_Layout *layout1, O_Layout *layout2) {
                                return [@(layout1.iOrder) compare:@(layout2.iOrder)];
                            }];
                            
                            int iCounter2 = 1;
                            for (O_Layout *o2ndLayout in arr2ndLayout){
                                @autoreleasepool {
                                    if ([CommonInspection bPassLayout:o2ndLayout withInsType:oInsType]) continue;
                                    if ([o2ndLayout.sQType isEqualToString:@"P"]){
                                        [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:o2ndLayout.iSLayoutID sName:o2ndLayout.sName sValue1:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV1Config] sValue2:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV2Config] sValue3:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV3Config] sValue4:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV4Config] sValue5:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV5Config] sValue6:[CommonHelper GetDefaultCheckboxValue:o2ndLayout.sFV6Config] sQType:o2ndLayout.sQType sConfig1:o2ndLayout.sFV1Config sConfig2:o2ndLayout.sFV2Config sConfig3:o2ndLayout.sFV3Config sConfig4:o2ndLayout.sFV4Config sConfig5:o2ndLayout.sFV5Config sConfig6:o2ndLayout.sFV6Config sConfig:o2ndLayout.sFConfig iSort:iCounter2 iSAssetLayoutID:0 sNameChanged:@""];
                                    }
                                    else{
                                        [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:o2ndLayout.iSLayoutID sName:o2ndLayout.sName sValue1:sValue1 sValue2:sValue2 sValue3:sValue3 sValue4:sValue4 sValue5:sValue5 sValue6:sValue6 sQType:o2ndLayout.sQType sConfig1:o2ndLayout.sFV1Config sConfig2:o2ndLayout.sFV2Config sConfig3:o2ndLayout.sFV3Config sConfig4:o2ndLayout.sFV4Config sConfig5:o2ndLayout.sFV5Config sConfig6:o2ndLayout.sFV6Config sConfig:o2ndLayout.sFConfig iSort:iCounter2 iSAssetLayoutID:0 sNameChanged:@""];
                                    }
                                    iCounter2++;
                                }
                            }
                        }
                    }
                }
            }
            return iInspectionID;
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return 0;
        }
        
    }
    
}

+ (int) InsertInspection:(int) iSInsTypeID
               iSAssetID:(int) iSAssetID
                    sPTC:(NSString *) sPTC
                   sType:(NSString *) sType
               sInsTitle:(NSString *) sInsTitle
                  sTitle:(NSString *) sTitle
               sAddress1:(NSString *) sAddress1
               sAddress2:(NSString *) sAddress2
            iSScheduleID:(int) iSScheduleID
                    sLat:(CGFloat) sLat
                   sLong:(CGFloat) sLong
       sCustom1_Schedule:(NSString *) sCustom1_Schedule
              dtSchedule:(NSString *) dtSchedule {
    @autoreleasepool {
        @try{
            // Add current date for the value of the attribute `dtSchedule`
            NSString *sCustom1 = iSScheduleID > 0 ?
                [CommonJson AddJsonKeyValueString:Constants.kDateSchedule
                                           sValue:dtSchedule
                                            sJson:@""] : @"";
            
            NSString *sQuery =  [NSString stringWithFormat:@"INSERT INTO ai_Inspection (iSInsTypeID, iSAssetID, sPTC, sType, sInsTitle, sTitle, sAddress1, sAddress2, dtStartDate, iSScheduleID, sLat, sLong, sCustom1) values (%d, %d, '%@', '%@', '%@', '%@', '%@', '%@', '%@', %d, '%@', '%@', '%@')", iSInsTypeID, iSAssetID, sPTC, sType, [CommonHelper EscapeString: sInsTitle], [CommonHelper EscapeString: sTitle], [CommonHelper EscapeString: sAddress1],[CommonHelper EscapeString:  sAddress2], [CommonHelper GetDateString:[NSDate date]], iSScheduleID, [[NSNumber numberWithFloat:sLat] stringValue], [[NSNumber numberWithFloat:sLong] stringValue], sCustom1];
            
            if (Constants.bDBQueue){
                context *appContext = [context sharedInstance];
                __block int lInspectionID = 0;
                [appContext.queue inDatabase:^(FMDatabase *oDB) {
                    [oDB executeUpdate:sQuery];
                    lInspectionID = (int)[oDB lastInsertRowId];
                }];
                
                if (iSScheduleID > 0){
                    [db_Schedule UpdateScheduleCustomInfo:iSScheduleID sCustom1:[CommonJson AddJsonKeyValueString:@"iInsID" sValue:[NSString stringWithFormat:@"%d", lInspectionID] sJson:sCustom1_Schedule] sCustom2:@""];
                }
                
                return lInspectionID;
            }
            else{
                
                FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
                if ([oDB open]){
                    [oDB executeUpdate:sQuery];
                    int lInspectionID = (int)[oDB lastInsertRowId];
                    if (iSScheduleID > 0){
                        [db_Schedule UpdateScheduleCustomInfo:iSScheduleID sCustom1:[CommonJson AddJsonKeyValueString:@"iInsID" sValue:[NSString stringWithFormat:@"%d", lInspectionID] sJson:sCustom1_Schedule] sCustom2:@""];
                    }
                    [oDB close];
                    return lInspectionID;
                }
                [oDB close];
                return 0;
            }
        }@catch (NSException *ex) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex];
            return 0;
            
        }
    }
}
//Updatedddddd
+(NSMutableArray *)SearchAssetlayout:(int)iSAssetID sPTC:(NSString *)sPTC{
    @autoreleasepool {
        @try {
            if (Constants.bDBQueue){
                __block NSMutableArray *arReturn = [[NSMutableArray alloc] init];
                context *appContext = [context sharedInstance];
                
                [appContext.queue inDatabase:^(FMDatabase *oDB) {
                    int lCount = [oDB intForQuery:[NSString stringWithFormat:@"select count(*) from ai_AssetLayout where iSLayoutID not in (select iSLayoutID FROM ai_Layout where bDeleted = 0 and iSPLayoutID = 0 and sPTC = '%@') and iSAssetID = %d", sPTC, iSAssetID]];
                    if (lCount == 0){
                        NSString *sQuery = [NSString stringWithFormat:@"SELECT * FROM ai_AssetLayout WHERE iSAssetID = %d AND iSLayoutID in (SELECT iSLayoutID FROM ai_Layout WHERE sPTC = '%@' and bDeleted = 0 and iSPLayoutID = 0) order by iSort", iSAssetID, sPTC];
                        FMResultSet *oReturn = [oDB executeQuery:sQuery];
                        
                        
                        while ([oReturn next]) {
                            O_AssetLayout *oAL = [[O_AssetLayout alloc] initWithResultSet:oReturn];
                            [arReturn addObject:oAL];
                        }
                        [oReturn close];
                    }
                    else{
                        [db_SetupInspection DeleteAssetLayoutByPTCSAssetID:iSAssetID sPTC:sPTC];
                    }
                    
                }];
               return arReturn;

            }
            else{
                FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
                if ([oDB open]){
                    int lCount = [oDB intForQuery:[NSString stringWithFormat:@"select count(*) from ai_AssetLayout where iSLayoutID not in (select iSLayoutID FROM ai_Layout where bDeleted = 0 and iSPLayoutID = 0 and sPTC = '%@') and iSAssetID = %d", sPTC, iSAssetID]];
                    NSMutableArray *oArray = [[NSMutableArray alloc] init];
                    if (lCount == 0){
                        NSString *sQuery = [NSString stringWithFormat:@"SELECT * FROM ai_AssetLayout WHERE iSAssetID = %d AND iSLayoutID in (SELECT iSLayoutID FROM ai_Layout WHERE sPTC = '%@' and bDeleted = 0 and iSPLayoutID = 0) order by iSort", iSAssetID, sPTC];
                        FMResultSet *oReturn = [oDB executeQuery:sQuery];
                        
                        
                        while ([oReturn next]) {
                            O_AssetLayout *oAL = [[O_AssetLayout alloc] initWithResultSet:oReturn];
                            [oArray addObject:oAL];
                        }
                        [oDB close];
                    }
                    else{
                        [db_SetupInspection DeleteAssetLayoutByPTCSAssetID:iSAssetID sPTC:sPTC];
                    }
                    [oDB close];
                    return oArray;
                }
                [oDB close];
                return nil;
            }
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }
    }
}
+(void)DeleteAssetLayoutByPTCSAssetID:(int)iSAssetID sPTC:(NSString *)sPTC{
    @autoreleasepool {
        @try {
            if (Constants.bDBQueue){
                context *appContext = [context sharedInstance];
                __block int iFileID = 0;
                [appContext.queue inDatabase:^(FMDatabase *oDB) {
                    NSString *sQuery = [NSString stringWithFormat:@"delete from ai_AssetLayout where iSAssetID=%d and iSLayoutID in (select iSLayoutID from ai_layout WHERE sPTC= '%@' and iSPLayoutID = 0)", iSAssetID, sPTC];
                    [oDB executeUpdate:sQuery];

                    
                }];
            }
            else{
            FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
            if ([oDB open]){
                NSString *sQuery =[NSString stringWithFormat:@"delete from ai_AssetLayout where iSAssetID=%d and iSLayoutID in (select iSLayoutID from ai_layout WHERE sPTC= '%@' and iSPLayoutID = 0)", iSAssetID, sPTC];
                [oDB executeUpdate:sQuery];
                
            }
            [oDB close];
            }
        }
        @catch (NSException *exception) {
                        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }
    }
}
+(void)LoadInspectionDefaultOrder:(int)iInsID{
    @try{
        O_Inspection *oIns = [db_Inspection GetInspectionByInsID:iInsID];
        NSArray *arrLayout = [db_Common GetLayouts:0 sPTC:oIns.sPTC];
        NSMutableArray *arrInsItem = [db_Inspection GetInsItems:0 iInsID:iInsID];
        int iCount = 1;
        for (O_Layout *oLayout in arrLayout){
            for (O_InsItem *oInsItem in arrInsItem){
                if (oLayout.iSLayoutID == oInsItem.iSLayoutID){
                    oInsItem.iSort = iCount;
                    [db_Inspection UpdateOrder:oInsItem];
                    iCount++;
                }
            }
        }
    }@catch(NSException *ex){
        
    }
}
+(void)LoadInspectionDefaultOrder_JSON:(int)iInsID sFileName:(NSString *)sFileName sPTC:(NSString *)sPTC{
    @try{        
        //O_Inspection *oIns = [db_Inspection GetInspection:iInsID];
        NSArray *arrLayout = [json_Common LoadLayout:0 sFileName:sFileName sPTC:sPTC];
        NSMutableArray *arrInsItem = [db_Inspection GetInsItems:0 iInsID:iInsID];
        int iCount = 1;
        for (O_Layout *oLayout in arrLayout){
            for (O_InsItem *oInsItem in arrInsItem){
                if (oLayout.iSLayoutID == oInsItem.iSLayoutID){
                    oInsItem.iSort = iCount;
                    [db_Inspection UpdateOrder:oInsItem];
                    iCount++;
                }
            }
        }
    }@catch(NSException *ex){
        
    }
}
/*+(bool)TestByPassLayout:(NSString *)sPTC{
    @autoreleasepool {
        
        @try{
            FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getRootFilePath]];
            if ([oDB open]){
                
                NSString *sQuery = [NSString stringWithFormat:@"SELECT COUNT(*) FROM ai_Layout WHERE sPTC='%@' AND bDeleted = 0 and sConfig LIKE '%%ADD%%'", sPTC];
                int iCount = [oDB intForQuery:sQuery];
                [oDB close];
                return (iCount == 0);
            }
            [oDB close];
            return false;
        }@catch (NSException *ex) {
            NSDictionary *data = [NSDictionary dictionaryWithObjects:[NSArray arrayWithObjects:@"db_Common.TestByPassLayout",  nil]
                                                             forKeys:[NSArray arrayWithObjects:@"per",  nil]];
            MintLogException(ex, data);
            return false;
        }
    }
}*/

+ (void)DuplicateInspection:(int)iSInsID 
                   oInsType:(O_InsType *)oInsType
                     bPhoto:(bool)bMediaTransfer
                       sLat:(CGFloat)sLat 
                      sLong:(CGFloat)sLong 
                 completion:(void (^)(int, NSString *))completion {
    //bool bMediaTransfer = true;
    NSMutableDictionary *oVideoToken = [@{
            @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
            @"sToken": [CommonUser currentToken],
            @"iInspectionID": [NSString stringWithFormat:@"%d", iSInsID],
            @"sLocalDate":[CommonHelper GetDateStrFromDate: @"yyyy-MM-dd HH:mm"
                                                      date: [NSDate new]],
            @"iCopyToInsTypeID":[NSString stringWithFormat:@"%d", oInsType.iSInsTypeID],
            @"bMediaTransfer": bMediaTransfer ? @"true" : @"false"} mutableCopy];
    int iInspectionID = 0;
    NSString *sMessage = nil;
    NSDictionary *oReturn = [IFConnection PostRequest:EndpointMirrorInspectionAPI oParams:oVideoToken];
    if (oReturn != nil) {
        if ([[oReturn valueForKey:@"success"] boolValue]) {
            NSMutableDictionary *oDic = oReturn[@"oInspection"];
            iInspectionID = [db_SetupInspection InsertInspection:oInsType.iSInsTypeID iSAssetID:[[oDic valueForKey:@"iPropertyID"] intValue] sPTC:oInsType.sPTC sType:oInsType.sType sInsTitle:oInsType.sInsTitle sTitle:[oDic valueForKey:@"sTitle"] sAddress1:[oDic valueForKey:@"sAddress1"] sAddress2:[oDic valueForKey:@"sAddress2"] iSScheduleID:0 sLat:sLat sLong:sLong sCustom1_Schedule:@"" dtSchedule:@""];
            O_Inspection *oCustom2_TempIns = [db_Inspection GetInspectionByInsID:iInspectionID];

            NSString *sCustom2 = oCustom2_TempIns.sCustom2;
            sCustom2 = [CommonJson AddJsonKeyValueString:@"_iCopyInsID" sValue:[NSString stringWithFormat:@"%d", iSInsID] sJson:sCustom2];
            sCustom2 = [CommonJson AddJsonKeyValueString:@"bMediaTransfer" sValue:@(bMediaTransfer).stringValue sJson:sCustom2];
            oCustom2_TempIns.sCustom2 = sCustom2;

            bool bReview = false;
            @try {
                NSString *sReview = oDic[@"sCustom2"][@"_bReview"];
                if (sReview != nil && [sReview isEqualToString:@"1"]) {
                    bReview = true;
                    oCustom2_TempIns.sCustom2 = [CommonJson AddJsonKeyValueString:@"_bReview" sValue:@"1" sJson:oCustom2_TempIns.sCustom2];
                }
            } @catch (NSException *abcd) {

            }

            [db_Inspection UpdateInspectionCustom:oCustom2_TempIns];
            NSArray *arrInsItem = [oReturn valueForKey:@"lsInsItem"];
            NSMutableArray *arrParent = [[NSMutableArray alloc] init];
            for (NSMutableDictionary *oDic in arrInsItem) {
                if ([[oDic valueForKey:@"iPInsItemID"] intValue] == 0) {
                    [arrParent addObject:[oDic copy]];
                }
            }

            for (NSDictionary *oTempParent in arrParent) {

                int iPInsItemID = [db_InsItem InsertInsItem:0 iInsID:iInspectionID iSLayoutID:[CommonHelper GetIntFromDictionary:oTempParent sKey:@"iLayoutID"] sName:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sName"] sValue1:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sValue1"] sValue2:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sValue2"] sValue3:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sValue3"] sValue4:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sValue4"] sValue5:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sValue5"] sValue6:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sValue6"] sQType:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sQType"] sConfig1:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sConfig1"] sConfig2:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sConfig2"] sConfig3:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sConfig3"] sConfig4:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sConfig4"] sConfig5:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sConfig5"] sConfig6:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sConfig6"] sConfig:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sConfig"] iSort:[CommonHelper GetIntFromDictionary:oTempParent sKey:@"iOrder"] iSAssetLayoutID:0 sNameChanged:@""];
                if (bReview) {
                    O_InsItem *oTempPInsItem = [db_InsItem GetInsItem:iPInsItemID];
                    oTempPInsItem.sCustom1 = [CommonJson AddJsonKeyValueString:@"_iCopyInsItemID" sValue:[NSString stringWithFormat:@"%@", [oTempParent valueForKey:@"iInsItemID"]] sJson:oTempPInsItem.sCustom1];
                    [db_InsItem UpdateInsItemCustom:oTempPInsItem];

                }

                //DLog([NSString stringWithFormat:@"L1 %@", [CommonHelper GetStringFromDictionary:oTempParent sKey:@"sName"]]);
                int iSPInsItemID = [[oTempParent valueForKey:@"iInsItemID"] intValue];
                for (int i = 0; i < [arrInsItem count]; i++) {
                    NSDictionary *oTempChild = arrInsItem[i];
                    //         NSString *sTitle = [oTempChild valueForKey:@"sName"];
                    int iSInsItemID = [[oTempChild valueForKey:@"iPInsItemID"] intValue];
                    if (iSInsItemID == iSPInsItemID) {
                        //DLog([NSString stringWithFormat:@"L2 %@", [CommonHelper GetStringFromDictionary:oTempChild sKey:@"sName"]]);

                        int iCInsItemID = [db_InsItem InsertInsItem:iPInsItemID iInsID:iInspectionID iSLayoutID:[CommonHelper GetIntFromDictionary:oTempChild sKey:@"iLayoutID"] sName:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sName"] sValue1:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sValue1"] sValue2:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sValue2"] sValue3:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sValue3"] sValue4:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sValue4"] sValue5:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sValue5"] sValue6:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sValue6"] sQType:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sQType"] sConfig1:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sConfig1"] sConfig2:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sConfig2"] sConfig3:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sConfig3"] sConfig4:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sConfig4"] sConfig5:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sConfig5"] sConfig6:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sConfig6"] sConfig:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sConfig"] iSort:[CommonHelper GetIntFromDictionary:oTempChild sKey:@"iOrder"] iSAssetLayoutID:0 sNameChanged:@""];

                        O_InsItem *oTempCInsItem = [db_InsItem GetInsItem:iCInsItemID];
                        if (bReview) {
                            oTempCInsItem.sCustom1 = [CommonJson AddJsonKeyValueString:@"_iCopyInsItemID" sValue:[NSString stringWithFormat:@"%@", [oTempChild valueForKey:@"iInsItemID"]] sJson:oTempCInsItem.sCustom1];
                        }
                        oTempCInsItem.sCustom2 = [CommonJson sArrayToJsonString:oTempChild[@"sCustom2"]];
                        [db_InsItem UpdateInsItemCustom:oTempCInsItem];

                    }
                }


            }
            if (bMediaTransfer) {
                NSArray *arrPhoto = [oReturn valueForKey:@"lsPhoto"];

                for (NSDictionary *oTemp in arrPhoto) {
                    O_Photo *oPhoto = [[O_Photo alloc] init];
                    oPhoto.iInsID = iInspectionID;
                    oPhoto.iInsItemID = 0;
                    oPhoto.sThumb = @"";
                    oPhoto.sFile = @"";
                    oPhoto.sComments = [CommonHelper GetStringFromDictionary:oTemp sKey:@"sPhotoComment"];
                    oPhoto.bDeleted = false;
                    oPhoto.dtDateTime = [CommonHelper GetStringFromDictionary:oTemp sKey:@"dtCreate"];
                    oPhoto.iSPhotoID = [CommonHelper GetIntFromDictionary:oTemp sKey:@"iPhotoID"];
                    oPhoto.iSize = [CommonHelper GetIntFromDictionary:oTemp sKey:@"iSize"];
                    oPhoto.bUploaded = true;
                    int iPhotoID = [db_Media UpdatePhoto:oPhoto];
                    oPhoto.iPhotoID = iPhotoID;
                    [db_Media UpdatePhoto:oPhoto];
                }
                [db_SetupInspection DuplicateInspection_ProcessPhoto:iInspectionID];
            }
        } else {
            sMessage = [oReturn valueForKey:@"message"];
            if ([NSString isNullOrEmpty:sMessage]) {
                sMessage = @"The inspection can not be copied.";
            }
        }
    }
    completion(iInspectionID, sMessage);
}

+(void)DuplicateInspection_ProcessVideo:(int)iInsID{
    NSMutableArray *lsParent = [db_Inspection GetInsItems:0 iInsID:iInsID];
    for (O_InsItem *oInsItem in lsParent){
        if ([oInsItem.sQType isEqualToString:@"V"]){
            NSMutableArray *lsChild = [db_Inspection GetInsItems:oInsItem.iInsItemID iInsID:iInsID];
            for (O_InsItem *oChild in lsChild){
                @try{
                    
                    O_Video *oVideo = [db_Media GetVideoByServerID:[oChild.sValue1 intValue] iInsID:iInsID];
                    oVideo.iInsItemID = oChild.iInsItemID;
     
                    int iVideoID = [db_Media UpdateVideo_InsItemID: oVideo];
                    if (iVideoID > 0){
                        oChild.sValue1 = [NSString stringWithFormat:@"%d", iVideoID];
                    }
                    else{
                        oChild.sValue1 = @"";
                    }
                    [db_InsItem UpdateInsItemValue:oChild];
                }@catch(NSException *eee){
                    oChild.sValue1 = @"";
                }
            }
        }
    }
}
+(void)DuplicateInspection_ProcessPhoto:(int)iInsID{
    NSMutableArray *lsParent = [db_Inspection GetInsItems:0 iInsID:iInsID];
    for (O_InsItem *oInsItem in lsParent){
        NSString *sPConfig1 = oInsItem.sConfig1;
        NSString *sPConfig2 = oInsItem.sConfig2;
        NSString *sPConfig3 = oInsItem.sConfig3;
        NSString *sPConfig4 = oInsItem.sConfig4;
        NSString *sPConfig5 = oInsItem.sConfig5;
        NSString *sPConfig6 = oInsItem.sConfig6;
        [db_SetupInspection ProcessInsItem_Photos:oInsItem  sConfig1:sPConfig1 sConfig2:sPConfig2 sConfig3:sPConfig3 sConfig4:sPConfig4 sConfig5:sPConfig5 sConfig6:sPConfig6];
        NSMutableArray *lsChild = [db_Inspection GetInsItems:oInsItem.iInsItemID iInsID:iInsID];
        for (O_InsItem *oChild in lsChild){
            [db_SetupInspection ProcessInsItem_Photos:oChild
                sConfig1:[oChild.sQType isEqualToString:@"P"] ? oChild.sConfig1 : sPConfig1
                sConfig2:[oChild.sQType isEqualToString:@"P"] ? oChild.sConfig2 : sPConfig2
                sConfig3:[oChild.sQType isEqualToString:@"P"] ? oChild.sConfig3 : sPConfig3
                sConfig4:[oChild.sQType isEqualToString:@"P"] ? oChild.sConfig4 : sPConfig4
                sConfig5:[oChild.sQType isEqualToString:@"P"] ? oChild.sConfig5 : sPConfig5
                sConfig6: [oChild.sQType isEqualToString:@"P"] ? oChild.sConfig6 : sPConfig6];
        }
    }
}
+(void)ProcessInsItem_Photos:(O_InsItem *)oInsItem sConfig1:(NSString *)sConfig1 sConfig2:(NSString *)sConfig2 sConfig3:(NSString *)sConfig3 sConfig4:(NSString *)sConfig4 sConfig5:(NSString *)sConfig5 sConfig6:(NSString *)sConfig6{
    if ([oInsItem.sQType isEqualToString:@"S"]){
        oInsItem.sValue1 = [db_SetupInspection GetPhotoSequenceID:oInsItem.sValue1 iInsItemID:oInsItem.iInsItemID iInsID:oInsItem.iInsID];
        [db_InsItem UpdateInsItemValue:oInsItem];
    }
    else{
    if ([db_SetupInspection TestIsPhotoType:sConfig1]){
        oInsItem.sValue1 = [db_SetupInspection GetPhotoSequenceID:oInsItem.sValue1 iInsItemID:oInsItem.iInsItemID iInsID:oInsItem.iInsID];
        [db_InsItem UpdateInsItemValue:oInsItem];
    }
    if ([db_SetupInspection TestIsPhotoType:sConfig2]){
        oInsItem.sValue2 = [db_SetupInspection GetPhotoSequenceID:oInsItem.sValue2 iInsItemID:oInsItem.iInsItemID iInsID:oInsItem.iInsID];
        [db_InsItem UpdateInsItemValue:oInsItem];
    }
    if ([db_SetupInspection TestIsPhotoType:sConfig3]){
        oInsItem.sValue3 = [db_SetupInspection GetPhotoSequenceID:oInsItem.sValue3 iInsItemID:oInsItem.iInsItemID iInsID:oInsItem.iInsID];
        [db_InsItem UpdateInsItemValue:oInsItem];
    }
    if ([db_SetupInspection TestIsPhotoType:sConfig4]){
        oInsItem.sValue4 = [db_SetupInspection GetPhotoSequenceID:oInsItem.sValue4 iInsItemID:oInsItem.iInsItemID iInsID:oInsItem.iInsID];
        [db_InsItem UpdateInsItemValue:oInsItem];
    }
    if ([db_SetupInspection TestIsPhotoType:sConfig5]){
        oInsItem.sValue5 = [db_SetupInspection GetPhotoSequenceID:oInsItem.sValue5 iInsItemID:oInsItem.iInsItemID iInsID:oInsItem.iInsID];
        [db_InsItem UpdateInsItemValue:oInsItem];
    }
    if ([db_SetupInspection TestIsPhotoType:sConfig6]){
        oInsItem.sValue6 = [db_SetupInspection GetPhotoSequenceID:oInsItem.sValue6 iInsItemID:oInsItem.iInsItemID iInsID:oInsItem.iInsID];
        [db_InsItem UpdateInsItemValue:oInsItem];
    }
    }
}
+(NSString *)GetPhotoSequenceID:(NSString *)sValue iInsItemID:(int)iInsItemID iInsID:(int)iInsID{
    @try{
       // NSMutableArray *oArray = [[NSMutableArray alloc] init];
        NSArray *arrValue = (sValue == nil) ? [[NSArray alloc] init] : [[sValue stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]] componentsSeparatedByString:@","];
        NSMutableArray *oFinal = [[NSMutableArray alloc] init];
        for (NSString *cc in arrValue){
            @try{
                if ([cc intValue] > 0){
                    O_Photo *BBB = [db_Media GetPhotoByServerID:[cc intValue] iInsID:iInsID];
                    BBB.iInsItemID = iInsItemID;
                    [db_Media UpdatePhoto_InsItemID:BBB];
                    NSString *dd =[NSString stringWithFormat:@"%d", BBB.iPhotoID];
                    [oFinal addObject:dd];
                //    NSString *cc = @"bb";
                }
            }@catch(NSException *eex){
                
            }

        }
        return [oFinal componentsJoinedByString:@","];
    }@catch(NSException *ex){
        
    }
    return @"";
}

+(bool)TestIsPhotoType:(NSString *)sConfig{
    SI_Control_Type controlType = [O_Control getControlTypeWithConfig: sConfig];
    return controlType & (SI_Control_PTO | SI_Control_SCAN);
}

+ (int)editInspectionWithServerInsID:(int)iSInsID {
    NSMutableDictionary *oVideoToken = [@{
            @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
            @"sToken": [CommonUser currentToken],
            @"iInspectionID": [NSString stringWithFormat:@"%d", iSInsID]
    } mutableCopy];
    int iInsID = 0;
    NSDictionary *oReturn = [IFConnection PostRequest:EndpointEditInspectionAPI oParams:oVideoToken];
    if (oReturn != nil){
        if ([[oReturn valueForKey:@"success"] boolValue]){
            // Delete the previous inspection (unassign the iSInsID)
            [db_Inspection deleteInspectionWithSInsID:iSInsID];
            
            // Update the local inspection or create one if it doesn't exist
            [CommonInspection ReconsolidateInspectionWithServer:iSInsID oDic:oReturn[@"oInspection"]];
            O_Inspection *oIns = [db_Inspection GetInspectionBySInsID:iSInsID];
            iInsID = oIns.iInsID;

            // Update the local inspection items
            NSArray *arrInsItem = [oReturn valueForKey:@"lsInsItem"];
            NSMutableArray *arrParent = [[NSMutableArray alloc] init];
            for (NSMutableDictionary *oDic in arrInsItem){
                if ([[oDic valueForKey:@"iPInsItemID"] intValue] == 0){
                    [arrParent addObject:[oDic copy]];
                }
            }
            for (NSDictionary *oTempParent in arrParent){
                NSString *sParentCustom1 = [CommonJson AddJsonKeyValueString:@"_iInsItemID"
                        sValue:[NSString stringWithFormat:@"%@", [oTempParent valueForKey:@"iInsItemID"]] sJson:@""];
                int iPInsItemID = [db_InsItem InsertInsItem_WithCustom:0
                        iInsID:iInsID iSLayoutID:[CommonHelper GetIntFromDictionary:oTempParent sKey:@"iLayoutID"]
                         sName:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sName"]
                       sValue1:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sValue1"]
                       sValue2:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sValue2"]
                       sValue3:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sValue3"]
                       sValue4:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sValue4"]
                       sValue5:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sValue5"]
                       sValue6:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sValue6"]
                        sQType:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sQType"]
                      sConfig1:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sConfig1"]
                      sConfig2:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sConfig2"]
                      sConfig3:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sConfig3"]
                      sConfig4:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sConfig4"]
                      sConfig5:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sConfig5"]
                      sConfig6:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sConfig6"]
                       sConfig:[CommonHelper GetStringFromDictionary:oTempParent sKey:@"sConfig"]
                         iSort:[CommonHelper GetIntFromDictionary:oTempParent sKey:@"iOrder"]
               iSAssetLayoutID:0 sNameChanged:@"" sCustom1:sParentCustom1 sCustom2:@""];
                int iSPInsItemID =[[oTempParent valueForKey:@"iInsItemID"] intValue];
                for (int i=0; i< [arrInsItem count]; i++){
                    NSDictionary *oTempChild = [arrInsItem safe_objectAtIndex:i];
                    //         NSString *sTitle = [oTempChild valueForKey:@"sName"];
                    int iSInsItemID = [[oTempChild valueForKey:@"iPInsItemID"] intValue];
                    if (iSInsItemID == iSPInsItemID ){
                        NSString *sChildCustom1 = [CommonJson AddJsonKeyValueString:@"_iInsItemID" sValue:[NSString stringWithFormat:@"%@", [oTempChild valueForKey:@"iInsItemID"]] sJson:@""];
                        
                        int iCInsItemID = [db_InsItem InsertInsItem_WithCustom:iPInsItemID iInsID:iInsID iSLayoutID:[CommonHelper GetIntFromDictionary:oTempChild sKey:@"iLayoutID"] sName:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sName"] sValue1:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sValue1"]  sValue2:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sValue2"]  sValue3:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sValue3"]  sValue4:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sValue4"]  sValue5:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sValue5"]  sValue6:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sValue6"] sQType:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sQType"] sConfig1:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sConfig1"]  sConfig2:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sConfig2"]  sConfig3:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sConfig3"]  sConfig4:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sConfig4"]  sConfig5:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sConfig5"]  sConfig6:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sConfig6"]  sConfig:[CommonHelper GetStringFromDictionary:oTempChild sKey:@"sConfig"]  iSort:[CommonHelper GetIntFromDictionary:oTempChild sKey:@"iOrder"]  iSAssetLayoutID:0 sNameChanged:@"" sCustom1:sChildCustom1 sCustom2:@""];

                        O_InsItem *oTempCInsItem = [db_InsItem GetInsItem:iCInsItemID];
                        oTempCInsItem.sCustom2 = [CommonJson sArrayToJsonString:oTempChild[@"sCustom2"]];
                        [db_InsItem UpdateInsItemCustom:oTempCInsItem];
                    }
                }
            }
            NSArray *arrPhoto = [oReturn valueForKey:@"lsPhoto"];
            
            for (NSDictionary *oTemp in arrPhoto){
                O_Photo *oPhoto = [[O_Photo alloc] init];
                oPhoto.iInsID = iInsID;
                oPhoto.iInsItemID = 0;
                oPhoto.sThumb = @"";
                oPhoto.sFile = @"";
                oPhoto.sComments = [CommonHelper GetStringFromDictionary:oTemp sKey:@"sPhotoComment"];
                oPhoto.bDeleted = false;
                oPhoto.dtDateTime = [CommonHelper GetStringFromDictionary:oTemp sKey:@"dtCreate"];
                oPhoto.iSPhotoID = [CommonHelper GetIntFromDictionary:oTemp sKey:@"iPhotoID"];
                oPhoto.iSize = [CommonHelper GetIntFromDictionary:oTemp sKey:@"iSize"];
                oPhoto.bUploaded = true;
                int iPhotoID = [db_Media UpdatePhoto:oPhoto];
                oPhoto.iPhotoID = iPhotoID;
                [db_Media UpdatePhoto:oPhoto];
            }
            [db_SetupInspection DuplicateInspection_ProcessPhoto:iInsID];
            //Need to complete how to process photo etc.
            NSArray *arrVideo = [oReturn valueForKey:@"lsVideo"];
            
            for (NSDictionary *oVideoTemp in arrVideo){
                O_Video *oVideo = [[O_Video alloc] init];
                oVideo.iInsID = iInsID;
                oVideo.iSVideoID = [CommonHelper GetIntFromDictionary:oVideoTemp sKey:@"iVideoID"];
                oVideo.iInsItemID = 0;
                oVideo.sThumb = @"";
                oVideo.sFile = @"";
                oVideo.sSThumb = [CommonHelper GetStringFromDictionary:oVideoTemp sKey:@"sThumbURL"];;
                oVideo.sSFile = [CommonHelper GetStringFromDictionary:oVideoTemp sKey:@"sVideoURL"];
                oVideo.bGetURL = true;
                oVideo.bUploaded = true;
                oVideo.bProcessed = true;
                oVideo.iSize = [CommonHelper GetIntFromDictionary:oVideoTemp sKey:@"iVideoSize"];
                [db_Media UpdateVideo:oVideo];
                [db_Media UpdateVideo:oVideo];

            }
            [db_SetupInspection DuplicateInspection_ProcessVideo:iInsID];
        }
    }
    return iInsID;
}
@end
