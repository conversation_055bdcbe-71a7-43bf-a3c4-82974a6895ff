//
//  IFSync.m
//  InspectionFolio
//
//  Created by <PERSON> on 16/01/14.
//  Copyright (c) 2014 Cloudya. All rights reserved.
//

#import "IFSync.h"
#import "db_Sync.h"
#import "O_AssetSync.h"
#import "db_Common.h"
#import "db_Asset.h"
#import "db_Delete.h"

@implementation IFSync
-(NSString *)GetMessage{
    return sMessage;
}
-(bool)LoginAPI:(NSString *)sEmail sPassword:(NSString *)sPassword{
    NSDictionary *oReturn = [IFConnection PostRequest:EndpointLoginAPI
                                              oParams:[@{
            @"UserName": sEmail, @"Password": sPassword, @"sDeviceInfo": @"iPhone"
    } mutableCopy]];
    if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
        NSString *sCustomerID = [oReturn valueForKey:@"iCustomerID"];
        NSString *sToken = [oReturn valueForKey:@"sToken"];
        [CommonHelper IFSavePref:@"sEmail" sValue:sEmail];
        [CommonHelper IFSavePref:@"sPassword" sValue:sPassword];
        [CommonHelper IFSavePref:@"iCustomerID" sValue:sCustomerID];
        [CommonHelper IFSavePref:@"sFirstName" sValue:[oReturn valueForKey:@"sFirstName"]];
        [CommonHelper IFSavePref:@"sLastName" sValue:[oReturn valueForKey:@"sLastName"]];
        return true;
    }
    else if (oReturn != nil && (![[oReturn valueForKey:@"bSuccess"] boolValue])){
         sMessage = [oReturn valueForKey:@"sMessage"];
    }else{
         sMessage = @"There is a problem with internet connection, Please try again.";
    }
    return false;
}

-(NSDictionary *)SyncAction{

    NSArray *oArray = [db_Sync GetPropertyListConcat];
    NSString *sFileName = [[[if_AppDelegate GetGlobalVar] valueForKey:@"sFilePath"] stringByAppendingPathComponent:@"SS1.dat"];
    
    NSFileManager *fileManager = [NSFileManager defaultManager];
    if(![fileManager fileExistsAtPath:sFileName])
    {
        [fileManager createFileAtPath:sFileName contents:nil attributes:nil];
    }
    else{
        [fileManager removeItemAtPath:sFileName error:nil];
        [fileManager createFileAtPath:sFileName contents:nil attributes:nil];
    }
    
    NSFileHandle *oFile = [NSFileHandle fileHandleForWritingAtPath:sFileName];
    [oFile seekToEndOfFile];

    NSArray *arrCheckList = [db_Common GetCheckList];
    for (O_CheckList *oCheckList in arrCheckList){
        int iSCheckListID = oCheckList.iSCheckListID;
        uint8_t iLayoutVerID = oCheckList.iLayoutVerID;
        [oFile writeData:[NSData dataWithBytes:&iSCheckListID length:sizeof(iSCheckListID)]];
        [oFile writeData:[NSData dataWithBytes:&iLayoutVerID length:sizeof(iLayoutVerID)]];
        
    }
    int iTest = 0;
    [oFile writeData:[NSData dataWithBytes:&iTest length:sizeof(iTest)]];
    uint8_t iQPVerID = (uint8_t)[[CommonHelper IFGetPref:@"iQPVerID"] intValue];
    [oFile writeData:[NSData dataWithBytes:&iQPVerID length:sizeof(iQPVerID)]];
    for (O_AssetSync *oNum in oArray){
        int iSAssetID = oNum.iSAssetID;
        uint8_t iPLVerID = (uint8_t)oNum.iPLVerID;
       // int iLength1 =sizeof(iSAssetID);
       // int iLength = sizeof(iPLVerID);
        [oFile writeData:[NSData dataWithBytes:&iSAssetID length:sizeof(iSAssetID)]];
        [oFile writeData:[NSData dataWithBytes:&iPLVerID length:sizeof(iPLVerID)]];
    }
    [oFile closeFile];

    NSDictionary *oReturn = [IFConnection PostRequestWithFile:EndpointServerApp sPath:sFileName];
    return oReturn;
}
-(void)DownloadProcessConfig:(NSDictionary *)oDic{
    @try {
        [CommonHelper IFSavePref:@"iFileCustomerID" sValue:[oDic valueForKey:@"CustomerID"]];
        [CommonHelper IFSavePref:@"iCompanyID" sValue:[oDic valueForKey:@"CompanyID"] ];
        [CommonHelper IFSavePref:@"bNotification" sValue:[oDic valueForKey:@"sNotification"]];
        
        [CommonHelper IFSavePref:@"iCountryID" sValue:[oDic valueForKey:@"iCountryID"] ];
        [CommonHelper IFSavePref:@"iIndustryID" sValue:[oDic valueForKey:@"iIndustryID"]];
        [CommonHelper IFSavePref:@"sFileEmail" sValue:[[oDic valueForKey:@"Email"] valueForKey:@"text"]];
        [CommonHelper IFSavePref:@"sFirstName" sValue:[[oDic valueForKey:@"FirstName"] valueForKey:@"text"]];
        [CommonHelper IFSavePref:@"sLastName" sValue:[[oDic valueForKey:@"LastName"] valueForKey:@"text"]];
        [CommonHelper IFSavePref:@"sCompanyName" sValue:[[oDic valueForKey:@"CompanyName"] valueForKey:@"text"]];
        [CommonHelper IFSavePref:@"iQPVerID" sValue:[[oDic valueForKey:@"iQPVerID"] valueForKey:@"text"]];
        NSString *sNewCategory =[[oDic valueForKey:@"sCategory"] valueForKey:@"text"];
        if (sNewCategory != nil && [sNewCategory length] > 0 ){
              NSMutableArray *oNewArray  = [NSJSONSerialization JSONObjectWithData:[sNewCategory dataUsingEncoding:NSUTF8StringEncoding] options:NSJSONReadingMutableContainers error:nil];
            
            
            for (NSDictionary *oDic in oNewArray){
                [db_Category ProcessNoticeCategory:[[O_NoticeCategory alloc] initWithDictionary:oDic]];
            }
        }
    }
    @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }

}

-(void)ProcessLayouts:(NSDictionary *)oDic{
    @try {
        NSArray *arLayout = [oDic valueForKey:@"LOT"];
        NSMutableArray *oTempContainer = [[NSMutableArray alloc] init];
    
        for (int i=0; i< arLayout.count; i++){
            @autoreleasepool {
                NSDictionary *oLayout =  [arLayout objectAtIndex:i];
                NSString *sPTC = [oLayout valueForKey:@"PTC"];
                if ([oTempContainer indexOfObject:sPTC] == NSNotFound){
                    [oTempContainer addObject:sPTC];
                    [db_Sync DeleteLayouts:sPTC];
                }
                
                [db_Sync InsertLayout:[[oLayout valueForKey:@"LD"] intValue] iSPLayoutID:[[oLayout valueForKey:@"PLD"] intValue] sPTC:[oLayout valueForKey:@"PTC"] sQType:[oLayout valueForKey:@"QT"] sName:[[oLayout valueForKey:@"LON"] valueForKey:@"text"] sFV1Config:[[oLayout valueForKey:@"FV1"] valueForKey:@"text"] sFV2Config:[[oLayout valueForKey:@"FV2"] valueForKey:@"text"] sFV3Config:[[oLayout valueForKey:@"FV3"] valueForKey:@"text"] sFV4Config:[[oLayout valueForKey:@"FV4"] valueForKey:@"text"] sFV5Config:[[oLayout valueForKey:@"FV5"] valueForKey:@"text"] sFV6Config:[[oLayout valueForKey:@"FV6"] valueForKey:@"text"] sSV1Config:[[oLayout valueForKey:@"SV1"] valueForKey:@"text"] sSV2Config:[[oLayout valueForKey:@"SV2"] valueForKey:@"text"] sSV3Config:[[oLayout valueForKey:@"SV3"] valueForKey:@"text"] sSV4Config:[[oLayout valueForKey:@"SV4"] valueForKey:@"text"] sSV5Config:[[oLayout valueForKey:@"SV5"] valueForKey:@"text"] sSV6Config:[[oLayout valueForKey:@"SV6"] valueForKey:@"text"] sFConfig:[[oLayout valueForKey:@"FV"] valueForKey:@"text"] sSConfig:[[oLayout valueForKey:@"SV"] valueForKey:@"text"] bAutoAdd:[[oLayout valueForKey:@"AD"] boolValue] iMark:[[oLayout valueForKey:@"V"] intValue] iOrder:[[oLayout valueForKey:@"S"] intValue]];
            }
        }
    }
    @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }

}
-(void)ProcessInsTypes:(NSDictionary *)oDic{
    @try {
        NSArray *arLayout = [oDic valueForKey:@"INT"];
        [db_Sync DeleteInsTypes];
        for (int i=0; i< arLayout.count; i++){
            NSDictionary *oLayout =  [arLayout objectAtIndex:i];
            @autoreleasepool {
                [db_Sync InsertInsType:[oLayout valueForKey:@"ITPTC"] sInsTitle:[[oLayout valueForKey:@"Title"] valueForKey:@"text" ] sType:[oLayout valueForKey:@"ITT"] iSInsTypeID:[[oLayout valueForKey:@"ITID"] intValue]];
            }
        }
    }
    @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
    
}
-(void)ProcessCheckList:(NSDictionary *)oDic{
    @try {
        NSMutableArray *arLayout = [[NSMutableArray alloc] init];
        id oLayoutTemp = [oDic valueForKey:@"CLST"];
        if ([oLayoutTemp isKindOfClass:[NSArray class]]){
            arLayout = oLayoutTemp;
        }
        else{
            [arLayout addObject:oLayoutTemp];
        }
        [db_Delete DeleteCheckList];
        for (int i=0; i< arLayout.count; i++){
            NSDictionary *oLayout =  [arLayout objectAtIndex:i];
            @autoreleasepool {
                [db_Common InsertCheckList:[[oLayout valueForKey:@"CLID"] intValue] sTitle:[[oLayout valueForKey:@"CLC"] valueForKey:@"text"] sPTC:[[oLayout valueForKey:@"CLPTC"] valueForKey:@"text" ] iLayoutVerID:[[oLayout valueForKey:@"CLVer"] intValue]];
                 
                 
            }
        }
    }
    @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
}
-(void)ProcessProperties:(NSDictionary *)oDic{
    @autoreleasepool {
        @try {
            NSMutableArray *arWords = [[NSMutableArray alloc] init];
            id oWordTemp = [oDic valueForKey:@"OP"];
            if ([oWordTemp isKindOfClass:[NSArray class]]){
                arWords = oWordTemp;
            }
            else if (oWordTemp == nil){
                return;
            }
            else{
                [arWords addObject:oWordTemp];
            }
            
            for (int i=0; i< arWords.count; i++){
                NSDictionary *oLayout =  [arWords objectAtIndex:i];
                O_Asset *oAsset = [db_Asset GetPropertyBySAssetID:[[oLayout valueForKey:@"PID"] intValue]];
                if ([[oLayout valueForKey:@"BD"] boolValue]){
                    [db_Delete DeleteProperty:[[oLayout valueForKey:@"PID"] intValue]];
                }
                else{
                    if (oAsset == nil || oAsset.iAssetID == 0){
                        oAsset = [[O_Asset  alloc] init];
                        oAsset.iAssetID = 0;
                    }
                    else{
                        [db_Delete DeleteProperty_3Table:oAsset.iSAssetID ];
                    }
                    oAsset.iSAssetID = [[oLayout valueForKey:@"PID"] intValue];
                    oAsset.iSPAssetID = [oLayout objectForKey:@"PPID"] ? 0 : [[oLayout valueForKey:@"PPID"] intValue];
                    oAsset.sAddress1 = [[oLayout valueForKey:@"A1"] valueForKey:@"text"];
                    oAsset.sAddress2 = [[oLayout valueForKey:@"A2"] valueForKey:@"text"];
                    oAsset.iCustomerID = [[oLayout valueForKey:@"CID"] intValue];
                    oAsset.sKey = [[oLayout valueForKey:@"SK"] valueForKey:@"text"];
                    oAsset.sAlarm = [[oLayout valueForKey:@"SA"] valueForKey:@"text"];
                    oAsset.dtInsDue = [[oLayout valueForKey:@"ID"] valueForKey:@"text"];
                    oAsset.sFilter = [CommonHelper FilterString:oAsset.sAddress1 sAddress2:oAsset.sAddress2];
                    oAsset.iPLVerID = [[oLayout valueForKey:@"PLVER"] intValue];
                    [db_Asset UpdateProperty:oAsset];
                    
                }

            }
            //[self ProcessContact:oDic];
            //Process Contact
            //Process Property Layout
            //Process InsAlert;
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }
    }
}
-(void)ProcessPropertyLayout:(NSDictionary *)oDic{
    @autoreleasepool {
        @try {
            NSMutableArray *arWords = [[NSMutableArray alloc] init];
            id oWordTemp = [oDic valueForKey:@"AL"];
            if ([oWordTemp isKindOfClass:[NSArray class]]){
                arWords = oWordTemp;
            }
            else if (oWordTemp == nil){
                return;
            }
            else{
                [arWords addObject:oWordTemp];
            }
            for (int i=0; i< arWords.count; i++){
                NSDictionary *oCon =  [arWords objectAtIndex:i];
                O_Contact *oContact = [[O_Contact alloc] init];
                oContact.iSAssetID = [[oCon valueForKey:@"iSAssetID"] intValue];
                oContact.iSContactID = [[oCon valueForKey:@"iSContactID"] intValue];
                oContact.sFirstName = [[oCon valueForKey:@"FN"] valueForKey:@"text"];
                oContact.sLastName = [[oCon valueForKey:@"LN"] valueForKey:@"text"];
                oContact.sPhone = [[oCon valueForKey:@"SP"] valueForKey:@"text"];
                oContact.sMobile = [[oCon valueForKey:@"SM"] valueForKey:@"text"];
                oContact.sEmail = [[oCon valueForKey:@"SE"] valueForKey:@"text"];
                oContact.sTag = [[oCon valueForKey:@"TAG"] valueForKey:@"text"];
                [db_Asset UpdateContact:oContact];
                
            }
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }
    }
}
-(void)ProcessInsAlerts:(NSDictionary *)oDic{
    
}
-(void)ProcessContact:(NSDictionary *)oDic{
    @autoreleasepool {
        @try {
            NSMutableArray *arWords = [[NSMutableArray alloc] init];
            id oWordTemp = [oDic valueForKey:@"Contact"];
            if ([oWordTemp isKindOfClass:[NSArray class]]){
                arWords = oWordTemp;
            }
            else if (oWordTemp == nil){
                return;
            }
            else{
                [arWords addObject:oWordTemp];
            }
            for (int i=0; i< arWords.count; i++){
                NSDictionary *oCon =  [arWords objectAtIndex:i];
                O_Contact *oContact = [[O_Contact alloc] init];
                oContact.iSAssetID = [[oCon valueForKey:@"IA"] intValue];
                oContact.iSContactID = [[oCon valueForKey:@"IC"] intValue];
                oContact.sFirstName = [[oCon valueForKey:@"FN"] valueForKey:@"text"];
                oContact.sLastName = [[oCon valueForKey:@"LN"] valueForKey:@"text"];
                oContact.sPhone = [[oCon valueForKey:@"SP"] valueForKey:@"text"];
                oContact.sMobile = [[oCon valueForKey:@"SM"] valueForKey:@"text"];
                oContact.sEmail = [[oCon valueForKey:@"SE"] valueForKey:@"text"];
                oContact.sTag = [[oCon valueForKey:@"ST"] valueForKey:@"text"];
                [db_Asset UpdateContact:oContact];
                
            }
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }
    }
}
-(void)ProcessSchedules:(NSDictionary *)oDic{
    @autoreleasepool {
        @try {
            [db_Delete DeleteSchedules];
            NSMutableArray *arWords = [[NSMutableArray alloc] init];
            id oWordTemp = [oDic valueForKey:@"SCH"];
            if ([oWordTemp isKindOfClass:[NSArray class]]){
                arWords = oWordTemp;
            }
            else if (oWordTemp == nil){
                return;
            }
            else{
                [arWords addObject:oWordTemp];
            }
            for (int i=0; i< arWords.count; i++){
                NSDictionary *oLayout =  [arWords objectAtIndex:i];
                @autoreleasepool {
                    O_Schedule *oSchedule = [[O_Schedule alloc] init];
                    oSchedule.iSScheduleID = [[oLayout valueForKey:@"SID"] intValue];
                    oSchedule.iSAssetID = [[oLayout valueForKey:@"IA"] intValue];
                    oSchedule.iSInsTypeID =[[oLayout valueForKey:@"IT"] intValue];
                    oSchedule.sAddress1 =  [CommonHelper EscapeString:[[oLayout valueForKey:@"SA1"] valueForKey:@"text"]];
                    oSchedule.sAddress2 = [CommonHelper EscapeString: [[oLayout valueForKey:@"SA2"] valueForKey:@"text"]];
                    oSchedule.sInsTitle = [CommonHelper EscapeString: [[oLayout valueForKey:@"INST"] valueForKey:@"text"]];
                    oSchedule.sCustom1 = [CommonJson AddJsonKeyValueString:@"sAddInfo" sValue:[[oLayout valueForKey:@"AD"] valueForKey:@"text"] sJson:oSchedule.sCustom1];
                    oSchedule.sType = [oLayout valueForKey:@"ST"];
                    oSchedule.sPTC = [oLayout valueForKey:@"sPTC"];
                    oSchedule.dtDateTime = [[oLayout valueForKey:@"sDT"] valueForKey:@"text"];
                    [db_Schedule SaveSchedule:oSchedule];
                    
                }
            }
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }
    }
}
-(void)ProcessQuickPhrase:(NSDictionary *)oDic{
    @autoreleasepool {
        @try {
            NSMutableArray *arWords = [[NSMutableArray alloc] init];
            id oWordTemp = [oDic valueForKey:@"QP"];
            if ([oWordTemp isKindOfClass:[NSArray class]]){
                arWords = oWordTemp;
            }
            else if (oWordTemp == nil){
                return;
            }
            else{
                [arWords addObject:oWordTemp];
            }
           // [db_Delete DeleteQuickPhrase];
            for (int i=0; i< arWords.count; i++){
              //  NSDictionary *oLayout =  [arWords objectAtIndex:i];
                @autoreleasepool {
              //      [db_Common InsertQuickPhrase:[[oLayout valueForKey:@"QPC"] valueForKey:@"text"]];
                    
                    
                }
            }
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }
    }

}
@end
