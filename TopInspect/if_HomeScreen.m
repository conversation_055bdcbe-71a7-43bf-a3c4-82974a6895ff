//
//  if_HomeScreen.m
//  InspectionFolio
//
//  Created by <PERSON> on 17/01/14.
//  Copyright (c) 2014 Cloudya. All rights reserved.
//

#import "if_Settings.h"
#import "db_Upload.h"
#import "if_Inspection.h"
#import "if_NewSchedule.h"
#import "IFSync_New.h"
#import "db_Delete.h"
#import "if_Asset_Content.h"
#import "NSArray+HighOrderFunction.h"
#import <AWSCore/AWSCore.h>
#import <AWSS3/AWSS3.h>
@import Extensions;
@import AppFeatures;

NSString *const kExternalScheduleCode               = @"external_schedule_code";
NSString *const kNotificationExternalScheduleOpened = @"external_schedule_opened";
NSString *const kExternalAssetID                    = @"external_asset_id";
NSString *const kNotificationAssetDetailOpened      = @"asset_detail_opened";
NSString *const kNotificationCustomInfoUpdated      = @"custom_info_updated";
NSString *const kNotificationSyncCompleted          = @"sync_completed";
NSString *const kRotationAnimation                  = @"rotationAnimation";

@interface if_HomeScreen ()
@property (strong, nonatomic) NSArray *validViewControllers;
@property (strong, nonatomic) NSString *sUploadErrorApppendMessage;
@property (strong, nonatomic) MBProgressHUD *hud;
@end

@implementation if_HomeScreen{
    UIButton *ivSync;
    UIButton *inspectSync;
    UIButton *scheduleSync;
    NSTimer *timer;
    int rotate;
    bool bSyncInAction;
    bool bInitialSync;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [CommonAnalytics trackEvent:@"iOS Load Home" meta:nil];
    [self.navigationController setNavigationBarHidden:YES];
    self.navigationItem.hidesBackButton = YES;

    self.hud = [[MBProgressHUD alloc] si_initWithView:self.navigationController.view];
    bSyncInAction = false;
    bInitialSync = false;

    if ([CommonHelper bKioskMode] && [CommonHelper bRequestInspectionMode]) {
        if_RequestInspection *requestIns = [self viewControllerWithClass:if_RequestInspection.class];
        requestIns.oHomeScreenDelegate = self;
        self.validViewControllers = @[requestIns];
    } else {
        NSArray *homeDelegateClasses = @[
            if_Assets.class, 
            if_Inspection.class,
            if_NewSchedule.class, 
            if_MyTasks.class
        ];
        [homeDelegateClasses forEach:^(id cls) {
            UIViewController *vc = [self viewControllerWithClass:cls];
            if ([vc respondsToSelector: @selector(setOHomeScreenDelegate:)]) {
                [vc performSelector: @selector(setOHomeScreenDelegate:) withObject: self];
            }
        }];

        // Assets tab
        if_Assets *assets = [self viewControllerWithClass:if_Assets.class];
        if (assets) ivSync = assets.btnSync;

        // Inspections tab
        if_Inspection *inspection = [self viewControllerWithClass:if_Inspection.class];
        if (inspection) inspectSync = inspection.btnSync;

        // Schedule tab
        if_NewSchedule *newSchedule = [self viewControllerWithClass:if_NewSchedule.class];
        if (newSchedule) scheduleSync = newSchedule.btn_Sync;

        // Project tab
        if_Projects *projects = [self viewControllerWithClass:if_Projects.class];
        projects.homeDelegate = self;

        // Tasks tab    
        if_MyTasks *tasks = [self viewControllerWithClass:if_MyTasks.class];
        tasks.homeDelegate = self;

        self.validViewControllers = @[assets, inspection, newSchedule, projects, tasks];
    }

    [self setupNotificationObservers];
}

- (void)setupNotificationObservers {
    NSNotificationCenter *center = [NSNotificationCenter defaultCenter];
    [center addObserver:self selector:@selector(displayExternalScheduleIfNeeded) name:kNotificationExternalScheduleOpened object:nil];
    [center addObserver:self selector:@selector(displayAssetDetailIfNeed) name:kNotificationAssetDetailOpened object:nil];
}

- (void)setViewControllers:(NSArray<__kindof UIViewController *> *)viewControllers {
    super.viewControllers = viewControllers;
    if (![CommonHelper bKioskMode]) {
        [self displayExternalScheduleIfNeeded];
        [self displayAssetDetailIfNeed];
    }
}

- (void)updateTabs {
    BOOL hideAssetTab = [CommonJson isUserConfigEnabled: UserConfigKeyAssetsHide];
    BOOL enableProjectsTab = [CommonPermission bEnableProject];
    BOOL enableTasksTab = [CommonPermission bEnableMyTasks];
    self.viewControllers = [self.validViewControllers filter: ^BOOL(UIViewController *vc) {
        if ([vc isKindOfClass:if_Assets.class]) return !hideAssetTab;
        else if ([vc isKindOfClass:if_Projects.class]) return enableProjectsTab;
        else if ([vc isKindOfClass:if_MyTasks.class]) return enableTasksTab;
        else return YES;
    }];
}

- (nullable id)viewControllerWithClass: (Class)vcClass {
    return [self.viewControllers find:^BOOL(id v) {
        return [v isKindOfClass: vcClass];
    }];
}

- (NSInteger)viewControllerIndexWithClass: (Class)vcClass {
    return [self.viewControllers firstIndex:^BOOL(id v) {
        return [v isKindOfClass: vcClass];
    }];
}

-(void)btn_Inbox {
    UINavigationController *nav = [self.storyboard instantiateViewControllerWithIdentifier:@"RootInboxController"];
    [self.navigationController presentViewController:nav animated:YES completion:nil];
}

- (void)syncButonAction:(id)sender {
    [self Sync];
}

- (void)btn_Action:(id)sender {
    UIAlertController *oAlert = [UIAlertController alertControllerWithTitle:@"Action" message:nil preferredStyle:UIAlertControllerStyleActionSheet];

    UIAlertAction *oAlertInbox = [UIAlertAction actionWithTitle:@"Inbox" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        @try {
            [self btn_Inbox];
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }

    }];
    [oAlert addAction:oAlertInbox];

    UIAlertAction *oAlertSync = [UIAlertAction actionWithTitle:@"Sync" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        @try {
            [self Sync];
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }

    }];
    [oAlert addAction:oAlertSync];
    @weakify(self)
    UIAlertAction *oAlertUploadAll = [UIAlertAction actionWithTitle:@"Upload Inspections" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        @try {
            [self Upload_Action:0 completion:^{
                @strongify(self)
                NSInteger idx = [self viewControllerIndexWithClass:if_Inspection.class];
                if (idx != NSNotFound) {
                    [self switchToNewTab:HomeScreenTabInspections];
                    if_Inspection *ins = self.selectedViewController;
                    [ins selectUploadedSegment];
                }
            }];
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }

    }];
    [oAlert addAction:oAlertUploadAll];
    /*
    UIAlertAction *oAlertProperty = [UIAlertAction actionWithTitle:@"New Asset" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
        @try {
            [self btn_AddProperty];

        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }

    }];
    [oAlert addAction:oAlertProperty];
*/

    if ([self.navigationController.visibleViewController isKindOfClass:[UITabBarController class]]) {
        UITabBarController *oController = (UITabBarController *) self.navigationController.visibleViewController;
        if ([oController.selectedViewController isKindOfClass:[if_Assets class]]) {
            UIAlertAction *oAlertSort = [UIAlertAction actionWithTitle:@"Sort Asset" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
                @try {
                    [(if_Assets *) oController.selectedViewController btn_SortOrder:sender];
                }
                @catch (NSException *exception) {
                    [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
                }
            }];
            [oAlert addAction:oAlertSort];
        } else if ([oController.selectedViewController isKindOfClass:[if_Inspection class]]) {
            UIAlertAction *oAlertIns = [UIAlertAction actionWithTitle:@"New Inspection" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
                @try {
                    [(if_Inspection *) oController.selectedViewController addInspectAction:nil];
                }
                @catch (NSException *exception) {
                    [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
                }

            }];
            [oAlert addAction:oAlertIns];
        }
    }

    UIAlertAction *oClose = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil];
    [oAlert addAction:oClose];

    oAlert.modalPresentationStyle = UIModalPresentationFullScreen;
    oAlert.popoverPresentationController.sourceView = sender;
    oAlert.popoverPresentationController.sourceRect = [sender bounds];
    [self presentViewController:oAlert animated:YES completion:nil];
}

- (void)btn_AddProperty {
    [Navigator PushNewAsset];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self.navigationController setNavigationBarHidden:NO];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [CommonAnalytics trackEvent:@"iOS Visit Home" meta:nil];
    [self.navigationController setNavigationBarHidden:YES];
    self.navigationItem.title = @"";
    [self updateTabs];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    if (![self bTenantTool]) {
        if ([CommonHelper IFGetPref_Bool:PrefsKeys.bSync]) {
            [self Sync];
            [CommonHelper IFSavePref:PrefsKeys.bSync sValue:@"0"];
        } else if ([CommonHelper IFGetPref_Bool:@"bServerSync"]) {
            [self Sync];
            [CommonHelper IFSavePref:@"bServerSync" sValue:@"0"];
        } else {
            NSString *sSyncDate = [CommonHelper IFGetPref:PrefsKeys.sSyncDate];
            NSDate *oSyncDate = [[NSDateFormatter iso8601] dateFromString:sSyncDate];
            if (oSyncDate != nil) {
                // Sync while opening the app if it is more than 1 day since last sync
                if ([oSyncDate daysBetweenDate:[NSDate date]] >= 1) {
                    [self Sync];
                }
            } else {
                // Sync if the last sync date is not set
                [self Sync];
            }
        }
    }
}

- (void)Sync {
    NSString *sSyncDate = [CommonHelper IFGetPref:PrefsKeys.sSyncDate];
    if (sSyncDate == nil || [sSyncDate isEqualToString:@""] || [sSyncDate hasPrefix:@"1980-1-1"]){
        bInitialSync = true;
    }
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    [self SyncAction_New];
}

- (void)GoToSetting {
    if_Settings *oSetting = [self.storyboard instantiateViewControllerWithIdentifier:@"Settings"];
    oSetting.homeScreenDelegate = self;
    [self.navigationController pushViewController:oSetting animated:YES];
}

-(void)SyncDelegate {
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    [self SyncAction_New];
}

- (void)CompleteSync:(bool)bSuccess sMessage:(NSString *)sMessage {
    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
        [self EndRefreshTable];
        if (bSuccess) {
            if ([self.navigationController.visibleViewController isKindOfClass:[UITabBarController class]]) {
                UITabBarController *oController = (UITabBarController *) self.navigationController.visibleViewController;
                if ([oController.selectedViewController isKindOfClass:[if_Assets class]]) {
                    if_Assets *assets = oController.selectedViewController;
                    [assets ReloadTable:true];
                } else if ([oController.selectedViewController isKindOfClass:[if_NewSchedule class]]) {
                    [((if_NewSchedule *) oController.selectedViewController) reloadSchedules];
                }
            }
            [self ShowAlert:@"Success" sMessage:@"Sync Success"];
        } else {
            [self ShowAlert:@"Error" sMessage:sMessage];
        }
    }];
}

-(void)UploadAssetFiles{

     dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
         NSMutableArray *arrFiles = [db_Media ReadyToUploadFile];
          if (arrFiles != nil && [arrFiles count] > 0){
             [self UploadFiles:arrFiles];
             NSMutableArray *arrFiles_Attach = [db_Media ReadyToAttachPropertyPhoto];
             [self AttachAssetPhotos:arrFiles_Attach];
          }
     });
}
-(void)Upload_Action:(int)iInsID completion:(nullable void (^)())completion {
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    if (![if_AppDelegate.shared bInternet_WithMessage:@"Please connect to Internet to upload inspection."]){
        [self EndRefreshTable];
        return;
    }

    NSArray *inspections = [db_Upload ReadyToUploadInspection:iInsID];
    NSMutableArray *arrInspection = [inspections filter:^BOOL(O_Inspection *ins) {
        NSArray *arrItems1st = [db_Inspection GetItems: 0 iInsID: ins.iInsID];
        return ![arrItems1st contains:^BOOL(O_InsItem *item) {
            return item.hasCompulsoryItems;
        }];
    }].mutableCopy;

    // Mark all the areas can be validate its compulsority
    [inspections forEach:^(O_Inspection *ins) {
        NSArray *allInsItems = [db_Inspection GetItems: 0 iInsID: ins.iInsID];
        [allInsItems forEach:^(O_Item *insItem) {
            [db_InsItem markedAsNeedValidate: insItem];
        }];
    }];

    if (arrInspection.count == 0 && inspections.count > 0) {
        [self ShowAlert: inspections.count > 1 ? @"Uncompleted Inspections" : @"Uncompleted Inspection"
               sMessage: inspections.count > 1 ? @"The inspections can not be uploaded until all compulsory questions are answered. Please follow ! mark to complete inspections." : @"The inspection can not be uploaded until all compulsory questions are answered. Please follow ! mark to complete inspection."];
        return;
    }

    //[Intercom logEventWithName:@"iOS Upload Inspections"];
    [self UploadAssetFiles];

    [CommonAnalytics trackEvent:@"iOS Upload Inspections" meta:nil];
    if (arrInspection != nil){ //&& [arrInspection count] > 0){
        self.sUploadErrorApppendMessage = @"";
        [CommonAnalytics trackEvent:@"iOS Upload Inspections" meta:@{
            @"Total": [NSString stringWithFormat:@"%ld", (unsigned long)arrInspection.count]
        }];
        [db_Log InsertLog:@"Upload" sMessage:[NSString stringWithFormat:@"Upload Inspection - %d inspections attached", (int)[arrInspection count] ]];

        [self.navigationController.view addSubview:self.hud];
        self.hud.labelText = @"Connecting to Server";

        @weakify(self)
        [self.hud showAnimated:YES whileExecutingBlock:^{
            @strongify(self)
            bool bSuccess = true;


            int i=0;


                for (O_Inspection *oInspection in arrInspection){
                    if (![self Upload_Inspection:oInspection]){
                        [CommonAnalytics trackEvent:@"iOS Upload Inspection Fail" meta:@{
                            @"Title": [NSString stringWithFormat:@"%@", oInspection.sTitle]
                        }];
                        bSuccess = false;
                        [db_Log InsertLog:@"Upload" sMessage:[NSString stringWithFormat:@"Failed Upload Inspection %@ for %@ @ %@", oInspection.sInsTitle, oInspection.sAddress1, oInspection.dtStartDate]];
                       // [db_Log InsertLog:@"Upload Video" sMessage:[NSString stringWithFormat:@"Fail %@", [oCurrentUploadedVideo ToLogString]]];
                    }
                    else{
                        [CommonAnalytics trackEvent:@"iOS Upload Inspection Success" meta:@{
                            @"Title": [NSString stringWithFormat:@"%@", oInspection.sTitle]
                        }];
                        if (oInspection.iSScheduleID > 0){
                            [db_Delete DeleteEvent:oInspection.iSScheduleID];
                            [db_Delete DeleteSchedule:oInspection.iSScheduleID];
                        }
                        [db_Log InsertLog:@"Upload" sMessage:[NSString stringWithFormat:@"Successful Upload Inspection %@ for %@ @ %@", oInspection.sInsTitle, oInspection.sAddress1,  oInspection.dtStartDate]];
                        [db_Delete DeletePhoto_ByInsID:oInspection.iInsID];
                        [db_Delete DeleteVideo_ByInsID:oInspection.iInsID];

                       // [db_Log InsertLog:@"Upload Inspection" sMessage:[NSString stringWithFormat:@"Success %@", [oCurrentUploadedVideo ToLogString]]];
                        i = i+ 1;
                    }

                }
            if (bSuccess){
                [CommonAnalytics trackEvent:@"iOS Upload Inspection All Success" meta:@{
                    @"Total": [NSString stringWithFormat:@"%ld", arrInspection.count]
                }];
                [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                    [self EndRefreshTable];
                    if ([CommonHelper bKioskMode]){
                        [self ShowAlert:@"Message" sMessage:@"The inspection has been uploaded. Thank you!"];
                    }
                    else{

                    [self ShowAlert:@"Success" sMessage:[NSString stringWithFormat:@"%d inspections have been uploaded to cloud.", i]];
                    }
                    [db_Log InsertLog:@"Upload" sMessage:[NSString stringWithFormat:@"%d (%d) inspections have been uploaded to cloud.", i, arrInspection == nil ? 0 : (int)arrInspection.count]];
                    if ([self.navigationController.visibleViewController isKindOfClass:[UITabBarController class]]){
                        UITabBarController *oController = (UITabBarController *)self.navigationController.visibleViewController;
                        if ([oController.selectedViewController isKindOfClass:[if_Assets class]]){
                            [((if_Assets *)oController.selectedViewController) ReloadTable:true];
                        }
                        else if ([oController.selectedViewController isKindOfClass:[if_NewSchedule class]]){
                            [((if_NewSchedule *)oController.selectedViewController) reloadSchedules];
                        }
                        else if ([oController.selectedViewController isKindOfClass:[if_Inspection class]]){
                            [((if_Inspection *)oController.selectedViewController) ReloadTable:true];
                        }
                    }
                    else if ([self.navigationController.visibleViewController isKindOfClass:[if_Asset_Content class]]){
                        [((if_Asset_Content *)self.navigationController.visibleViewController) ReloadTable];
                    }
                }];
            }
            else{
                [CommonAnalytics trackEvent:@"iOS Upload Inspection All Fail" meta:@{
                    @"Total": [NSString stringWithFormat:@"%ld", arrInspection.count],
                    @"Fail Total": [NSString stringWithFormat:@"%d", i],
                    @"Message":self.sUploadErrorApppendMessage
                }];
                [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                    [db_Log InsertLog:@"Upload" sMessage:[NSString stringWithFormat:@"Failed Upload Message Displayed - %@", [NSString stringWithFormat:@"%d of %d inspections have been uploaded to cloud successfully.", i, (int)[arrInspection count]]]];
                    [self ShowAlert:@"Fail" sMessage:[NSString stringWithFormat:@"%d of %d inspections have been uploaded to cloud successfully. %@", i, (int)[arrInspection count], ([self.sUploadErrorApppendMessage isEqualToString:@""] ? @"Please Upload Again." : self.sUploadErrorApppendMessage)]];
                }];
            }

        } completionBlock:^{
            if (completion) completion();
        }];

    }
    else{
        [db_Log InsertLog:@"Upload" sMessage:@"No inspections marked as completed."];
        [self ShowAlert:@"Info" sMessage:@"No Inspections have been marked as completed. Please click into the inspection, click on the top right corner 'Complete' button to mark the inspection as 'Completed'. Only inspections in 'Completed Inspection' section will be uploaded to cloud."];
    }
}

-(bool)UploadData_ExternalInspection:(XMLWriter *)xmlWriter oIns:(O_Inspection *)oIns sTokenID:(NSString *)sTokenID sToken:(NSString *)sToken{
    @autoreleasepool {
        @try{
            NSString* xml = [xmlWriter toString];
            NSString *sFileName = [NSString stringWithFormat:@"insdata_%d.xml", oIns.iInsID];
            NSString *storePath = [[[if_AppDelegate GetGlobalVar] valueForKey:@"sFilePath" ] stringByAppendingPathComponent:sFileName];
            NSError     *error;
            [xml writeToFile:storePath atomically:TRUE encoding:NSUTF8StringEncoding error:&error];
            NSDictionary *oReturn = [IFConnection PostRequestWithFile_ExternalInspection:[NSString stringWithFormat:@"%@%@", ifServerURL, @"/SyncExternal/AppServer_ExternalInspection"] sPath:storePath sTokenID:sTokenID sToken:sToken];
            if ( oReturn == nil){
                oReturn = [IFConnection PostRequestWithFile_ExternalInspection:[NSString stringWithFormat:@"%@%@", ifServerURL, @"/SyncExternal/AppServer_ExternalInspection"] sPath:storePath sTokenID:sTokenID sToken:sToken];
                if (oReturn == nil){
                    return false;
                }
            }
            if ([[oReturn valueForKey:@"success"] boolValue]) {
                oIns.iSInsID = [[oReturn valueForKey:@"iInsID"] intValue];
                oIns.bSynced = YES;

                [db_Inspection UpdateInspection:oIns];
                [CommonHelper DeleteFile:sFileName];
            } else {
                //  NSString *sErrorMessage= [ oReturn valueForKey:@"message"];
                //  if ([sErrorMessage rangeOfString:@"expire"].location == NSNotFound){
                self.sUploadErrorApppendMessage = [oReturn valueForKey:@"message"];
                //  }
                return false;
            }
            return true;
        }@catch(NSException *ex){
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex];
            return false;
        }
    }

}

-(bool)Upload_Inspection:(O_Inspection *)oIns{
    
    @autoreleasepool {
        @try {
            void (^progressPhotos)(NSUInteger current, NSUInteger total) = ^(NSUInteger current, NSUInteger total) {
                [self.hud setDetailsLabelText:[NSString stringWithFormat:@"Photos %ld of %ld", current, total ]];
            };
            void (^progressVideos)(NSUInteger current, NSUInteger total) = ^(NSUInteger current, NSUInteger total) {
                [self.hud setDetailsLabelText:[NSString stringWithFormat:@"Videos %ld of %ld", current, total]];
            };
            
            void (^uploadProgressVideos)(int current, int64_t totalBytesSent, int64_t totalBytesExpectedToSend) = ^(int current, int64_t totalBytesSent, int64_t totalBytesExpectedToSend) {
                [self.hud setDetailsLabelText:[NSString stringWithFormat:@"Upload Video %d - %d%%", current, (int)(totalBytesSent * 100.0 / totalBytesExpectedToSend)]];
            };
            
            if ([CommonHelper bExternalInspection:oIns.sCustom2]){
                //////Additional logic to upload for requested inspection
                NSMutableArray *arrPhotos = [db_Media SearchPhotos:0 iInsItemID:0 iInsID:oIns.iInsID];
                NSArray *arrVideos = [db_Media SearchAllVideos:0 iInsID:oIns.iInsID];
                [self.hud setLabelText:[NSString stringWithFormat:@"Uploading - %@", oIns.sAddress1]];
                NSString *sTokenID = [CommonJson GetJsonKeyValue:@"iTokenID" sJson:oIns.sCustom2];
                NSString *sToken = [CommonJson GetJsonKeyValue:@"sToken" sJson:oIns.sCustom2];
                
                if ([CommonMedia UploadPhotos_ExternalInspection:arrPhotos sAdditionalInfo:oIns.sAddress1 progress:progressPhotos sTokenID:sTokenID sToken:sToken]
                    && [CommonMedia UploadVideos_ExternalInspection:arrVideos sAdditionalInfo:oIns.sAddress1 progress:progressVideos uploadProgress:uploadProgressVideos sTokenID:sTokenID sToken:sToken]){
                    
                    [self.hud setDetailsLabelText:@"Inspection Details"];
                    NSMutableArray *arrPhotosSync = [db_Media SearchPhotosSync:oIns.iInsID];
                    NSMutableArray *arrVideosSync = [db_Media SearchVideosSync:oIns.iInsID];
                    //  NSMutableArray *arrNotifications = [db_Media SearchNotificationSync:oIns.iInsID];
                    XMLWriter *oWriter = [db_Upload GetUploadInsXML:oIns arrPhotos:arrPhotosSync arrVideos:arrVideosSync];
                    
                    if (oWriter != nil && oWriter){
                        //return [self :oWriter oIns:(O_Inspection *)oIns];
                        return [self UploadData_ExternalInspection:oWriter oIns:oIns sTokenID:sTokenID sToken:sToken];
                        // return true;
                    }
                    
                }
                return false;
            }
            else{
                
                NSMutableArray *arrPhotos = [db_Media SearchPhotos:0 iInsItemID:0 iInsID:oIns.iInsID];
                NSArray *arrVideos = [db_Media SearchAllVideos:0 iInsID:oIns.iInsID];
                [self.hud setLabelText:[NSString stringWithFormat:@"Uploading - %@", oIns.sAddress1]];
                
                if ([CommonMedia UploadPhotos:arrPhotos sAdditionalInfo:oIns.sAddress1 progress:progressPhotos] &&
                    [CommonMedia UploadVideos:arrVideos sAdditionalInfo:oIns.sAddress1 progress:progressVideos uploadProgress:uploadProgressVideos]) {
                    
                    [self.hud setDetailsLabelText:@"Inspection Details"];
                    NSMutableArray *arrPhotosSync = [db_Media SearchPhotosSync:oIns.iInsID];
                    NSMutableArray *arrVideosSync = [db_Media SearchVideosSync:oIns.iInsID];
                    //  NSMutableArray *arrNotifications = [db_Media SearchNotificationSync:oIns.iInsID];
                    XMLWriter *oWriter = [db_Upload GetUploadInsXML:oIns arrPhotos:arrPhotosSync arrVideos:arrVideosSync];
                    
                    if (oWriter != nil && oWriter){
                        return [self UploadData:oWriter oIns:(O_Inspection *)oIns];
                        // return true;
                    }
                    
                }
                return false;
            }
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return false;
        }
        
    }
}

-(bool)UploadData:(XMLWriter *)xmlWriter oIns:(O_Inspection *)oIns{
    @autoreleasepool {
        @try{
            NSString* xml = [xmlWriter toString];
            NSString *sFileName = [NSString stringWithFormat:@"insdata_%d.xml", oIns.iInsID];
            NSString *storePath = [[[if_AppDelegate GetGlobalVar] valueForKey:@"sFilePath" ] stringByAppendingPathComponent:sFileName];
            NSError     *error;
            [xml writeToFile:storePath atomically:TRUE encoding:NSUTF8StringEncoding error:&error];
            NSDictionary *oReturn = [IFConnection PostRequestWithFile:EndpointAppServer sPath:storePath];
            if ( oReturn == nil){
                oReturn = [IFConnection PostRequestWithFile:EndpointAppServer sPath:storePath];
                if (oReturn == nil){
                    return false;
                }
            }
            bool bSuccess = false;
            @try {
                bSuccess = [[oReturn valueForKey:@"success"] boolValue];
            } @catch (NSException *eeeee) {

            }
            if (bSuccess) {
                oIns.iSInsID = [[oReturn valueForKey:@"iInsID"] intValue];
                oIns.bSynced = YES;

                [db_Inspection UpdateInspection:oIns];
                [db_Inspection BulkClearInsItem:oIns];

                [CommonInspection ReconsolidateInspectionWithServer:oIns.iSInsID oDic: oReturn[@"oInspection"]];

                [CommonHelper DeleteFile:sFileName];

                // Marked need to force sync projects if there is inspection from project inspection is uploaded
                if (oIns.iProjectAssetInsTypeID > 0) {
                    [CommonHelper IFSavePref:PrefsKeys.kForceSyncProjects sValue: @"1"];
                }
            } else {
                //NSString *sErrorMessage= ;
                //if ([sErrorMessage rangeOfString:@"expire"].location == NSNotFound){
                self.sUploadErrorApppendMessage = [oReturn valueForKey:@"message"];
                // }
                return false;
            }
            return true;
        }@catch(NSException *ex){
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex];
            return false;
        }
    }

}

-(void)RecoverFileToS3:(AWSS3TransferManager *)oTransfer sFileURL:(NSString *)sFileURL sServerURL:(NSString *)sServerURL i:(int)i sBucket:(NSString *)sBucket{
    @try {
        @autoreleasepool {

            AWSS3TransferManagerUploadRequest *oTransferRequest = [AWSS3TransferManagerUploadRequest new];
            [oTransferRequest setKey:sServerURL];
            [oTransferRequest setBucket:sBucket];
            [oTransferRequest setACL:AWSS3ObjectCannedACLPublicRead];
            NSString *sFilePath =  [CommonHelper IF_FilePath:sFileURL];
            [oTransferRequest setBody:[NSURL fileURLWithPath: sFilePath]];
            [oTransferRequest setContentLength:[NSNumber numberWithUnsignedLongLong:[[[NSFileManager defaultManager] attributesOfItemAtPath:sFilePath error:nil][NSFileSize] longLongValue]]];
            oTransferRequest.uploadProgress = ^(int64_t bytesSent, int64_t totalBytesSent, int64_t totalBytesExpectedToSend) {

                [self.hud setDetailsLabelText:[NSString stringWithFormat:@"Submit File %d - %d%%", i, (int)(totalBytesSent * 100.0 / totalBytesExpectedToSend)]];


            };
            [[[[oTransfer upload:oTransferRequest] continueWithBlock:^id(AWSTask *task) {
                if (task.error) {
                    return [oTransfer upload:oTransferRequest];
                }

                return nil;

            }] continueWithBlock:^id(AWSTask *task) {

                return nil;
            }] waitUntilFinished];
            [AWSS3TransferManager removeS3TransferManagerForKey:sServerURL];

        }
    }
    @catch ( NSException *exception ) {

           [db_Log InsertLog:@"Recover File" sMessage:[NSString stringWithFormat:@"Fail %@ %@", sFileURL, sServerURL]];
    }
}

-(bool)AttachAssetPhotos:(NSMutableArray *)arrFiles{
    @autoreleasepool {
        @try {
          //  int iCount = 1;
            for (O_File *oFile in arrFiles){
                if ([CommonHelper IF_FileExist:oFile.sFile]){

                    NSDictionary *oReturn = [IFConnection PostRequest:EndpointAttachAssetPhoto oParams:[@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"iSFileID": [NSString stringWithFormat:@"%d", oFile.iSFileID]} mutableCopy]];
                    if (oReturn == nil || [[oReturn valueForKey:@"success"] boolValue] == NO){
                        NSDictionary *oReturn1 = [IFConnection PostRequest: EndpointAttachAssetPhoto oParams:[@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"iSFileID": [NSString stringWithFormat:@"%d", oFile.iSFileID]} mutableCopy]];
                        if (oReturn1 == nil || [[oReturn1 valueForKey:@"success"] boolValue] == NO){
                            continue;
                        }
                    }
                    oFile.sCustom1 = [CommonJson RemoveJsonKey:@"AttachCMD" sJson:oFile.sCustom1];
                    [db_Media UpdateFile:oFile];
                }
            }
            return true;
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return false;;
        }

    }

}
-(bool)UploadFiles:(NSMutableArray *)arrFiles{
    @autoreleasepool {
        @try {

            //NSString *sMessage;
            int iCount = 1;
            for (O_File *oFile in arrFiles){
                //if ((!oPhoto.) && (!oPhoto.bDeleted)){
                    if ([CommonHelper IF_FileExist:oFile.sFile]){
                        bool bUploadFileSuccess = false;
                        [self.hud setDetailsLabelText:[NSString stringWithFormat:@"Asset File %d of %d", iCount, (int)(arrFiles.count) ]];
                        NSMutableDictionary *oDic = [@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"dtDateTaken": oFile.dtDateTime, @"iPropertyID": [NSString stringWithFormat:@"%d", oFile.iSObjectID], @"sClientFileName": oFile.sFile, @"iSize": [NSString stringWithFormat:@"%d", oFile.iSize]} mutableCopy];

                        NSDictionary *oReturn = [IFConnection PostRequest:EndpointUploadAssetFile oParams:oDic];
                        NSString *sUploadURL = @"";
                        if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                            sUploadURL = [oReturn valueForKey:@"sURL"];
                            oFile.iSFileID = [[oReturn[@"oFile"] valueForKey:@"iFileID"]intValue];
                            [db_Media UpdateFile:oFile];
                        }
                        else{
                            NSDictionary *oReturn1 = [IFConnection PostRequest:EndpointUploadAssetFile oParams:[@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"dtDateTaken": oFile.dtDateTime, @"iPropertyID": [NSString stringWithFormat:@"%d", oFile.iSObjectID], @"sClientFileName": oFile.sFile, @"iSize": [NSString stringWithFormat:@"%d", oFile.iSize]} mutableCopy]];

                            if (oReturn1 != nil && [[oReturn1 valueForKey:@"success"] boolValue]){
                                sUploadURL = [oReturn1 valueForKey:@"sURL"];

                                oFile.iSFileID = [[[oReturn objectForKey:@"oFile"] valueForKey:@"iFileID"]intValue];
                                [db_Media UpdateFile:oFile];
                            }
                            else{
                                //bSyncSuccess = false;
                                //sMessage = [NSString stringWithFormat:@"%@ %@", sMessage, @"106"];
                                return false;
                            }
                        }
                        if (sUploadURL != nil && [sUploadURL length] > 10){
                            if (![CommonMedia UploadFileToS3:oFile.sFile sURL:sUploadURL]){
                                if (![CommonMedia UploadFileToS3:oFile.sFile sURL:sUploadURL]){
                                    //bSyncSuccess = false;
                                    //sMessage = [NSString stringWithFormat:@"%@ %@", sMessage, @"107"];
                                    return false;
                                }
                                else{
                                    NSDictionary *oReturn = [IFConnection PostRequest:EndpointUploadAssetFileSuccess oParams:[@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"iSFileID": [NSString stringWithFormat:@"%d", oFile.iSFileID]} mutableCopy]];
                                    if (oReturn == nil || [[oReturn valueForKey:@"success"] boolValue] == NO){
                                        NSDictionary *oReturn1 = [IFConnection PostRequest:EndpointUploadAssetFileSuccess oParams:[@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"iSFileID": [NSString stringWithFormat:@"%d", oFile.iSFileID]} mutableCopy]];
                                        if (oReturn1 == nil || [[oReturn1 valueForKey:@"success"] boolValue] == NO){
                                            return false;
                                        }
                                    }


                                    oFile.bUploaded = true;
                                    //oFile.iSFileID =
                                    [db_Media UpdateFile:oFile];
                                }
                            }
                            else{
                                NSDictionary *oReturn = [IFConnection PostRequest:EndpointUploadAssetFileSuccess oParams:[@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"iSFileID": [NSString stringWithFormat:@"%d", oFile.iSFileID]} mutableCopy]];
                                if (oReturn == nil || [[oReturn valueForKey:@"success"] boolValue] == NO){
                                    NSDictionary *oReturn1 = [IFConnection PostRequest:EndpointUploadAssetFileSuccess oParams:[@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"iSFileID": [NSString stringWithFormat:@"%d", oFile.iSFileID]} mutableCopy]];
                                    if (oReturn1 == nil || [[oReturn1 valueForKey:@"success"] boolValue] == NO){
                                        return false;
                                    }
                                }


                                oFile.bUploaded = true;
                                [db_Media UpdateFile:oFile];
                            }

                        }
                        else{
                            //sMessage = [NSString stringWithFormat:@"%@ %@", sMessage, @"105"];
                            return false;
                        }
                    }
              //  }
                iCount = iCount + 1;
            }
         //   [CommonIntercom trackEvent:@"iOS Upload PropertyFile" meta:@{
          //                                                                       @"Title": [NSString stringWithFormat:@"%@", sAddress1],
            //                                                                     @"Photos": [NSString stringWithFormat:@"%d out of %ld", iCount, arrPhotos == nil ? 0 :arrPhotos.count]}];
         //   [db_Log InsertLog:@"Upload" sMessage:[NSString stringWithFormat:@"%d Photos Uploaded - %@", iCount, sAddress1]];
            return true;
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return false;;
        }

    }

}

- (void)animateSyncButton: (BOOL)isStart {
    if_Assets *assets = [self viewControllerWithClass:if_Assets.class];
    if (assets) ivSync = assets.btnSync;

    // Inspections tab
    if_Inspection *inspection = [self viewControllerWithClass:if_Inspection.class];
    if (inspection) inspectSync = inspection.btnSync;

    // Schedule tab
    if_NewSchedule *newSchedule = [self viewControllerWithClass:if_NewSchedule.class];
    if (newSchedule) scheduleSync = newSchedule.btn_Sync;
    
    if (isStart) {
        CABasicAnimation* rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
        rotationAnimation.toValue = @(-M_PI * 2.0);
        rotationAnimation.duration = 1.0;
        rotationAnimation.cumulative = YES;
        rotationAnimation.repeatCount = HUGE_VALF;
        [ivSync.layer addAnimation:rotationAnimation forKey:kRotationAnimation];
        [inspectSync.layer addAnimation:rotationAnimation forKey:kRotationAnimation];
        [scheduleSync.layer addAnimation:rotationAnimation forKey:kRotationAnimation];
    } else {
        [ivSync.layer removeAnimationForKey:kRotationAnimation];
        [inspectSync.layer removeAnimationForKey:kRotationAnimation];
        [scheduleSync.layer removeAnimationForKey:kRotationAnimation];
    }
}

-(void)SyncAction_New{
    @autoreleasepool {
        @try {
            if (![if_AppDelegate.shared bInternet_WithMessage:@"Please connect to Internet to sync."]){
                [self EndRefreshTable];
                return;
            }


            if (bSyncInAction) {
                //[self ShowAlert:@"Message" sMessage:@"Syncing now. Please wait..."];
               //  [self EndRefreshTable];
                return;
            }
            bSyncInAction = true;
            [self animateSyncButton:YES];
            if (bInitialSync) {
                [self.navigationController.view addSubview:self.hud];
            }
            self.hud.labelText = @"Connecting to Server";
            self.hud.detailsLabelText = @"";

            @weakify(self)
            [self.hud showAnimated:YES whileExecutingBlock:^{
                @strongify(self)
                NSMutableArray *arrFiles = [db_Media ReadyToUploadFile];
                if (arrFiles != nil && [arrFiles count] > 0){
                    [self UploadFiles:arrFiles];
                    NSMutableArray *arrFiles_Attach = [db_Media ReadyToAttachPropertyPhoto];
                    [self AttachAssetPhotos:arrFiles_Attach];
                }


                IFSync_New *oSync = [[IFSync_New alloc] init];
                NSDictionary *oReturn = [oSync SyncAction];
                self.hud.labelText = @"Download Data";
                self.hud.detailsLabelText = @"Connecting ...";
                NSString *sUrl = [oReturn valueForKey:@"sURL"];
                //[self ChangePrompt:@"Downloading Data"];
                if ([[oReturn valueForKey:@"success"] boolValue] && ![sUrl isEqual:[NSNull null]]){
                    self.hud.detailsLabelText = @"Downloading ...";
                    [CommonAnalytics trackEvent:@"iOS Sync GetURL" meta:@{
                        @"sURL": [oReturn valueForKey:@"sURL"] ?: @""
                    }];
                    NSDictionary *oXML = [IFConnection DownloadFile:[NSURL URLWithString:[oReturn valueForKey:@"sURL"]]];
                   // DLog(@"oXML  %@", oXML);
                    if (oXML != nil && oXML.allKeys.count > 0){
                        [CommonAnalytics trackEvent:@"iOS Sync Download Success" meta:nil];
                        [db_Log InsertLog:@"Sync" sMessage:[NSString stringWithFormat:@"DownloadXML - %@", [oReturn valueForKey:@"sFileName"]]];
                        //[self ChangePrompt:];
                      //  DLog(@"xml  %@", oXML);
                        self.hud.labelText = @"Process Data";
                        // NSDictionary *oTEmp =
                        [oSync DownloadProcessConfig:[[oXML valueForKey:@"DATA"] valueForKey:@"Config"]];
                        self.hud.detailsLabelText = @"Processing ...";
                        int iCheckListCount = [oSync ProcessCheckList:[[oXML valueForKey:@"DATA"] valueForKey:@"CheckList"]];

                        int iLayoutCount = [oSync ProcessLayouts:[[oXML valueForKey:@"DATA"] valueForKey:@"Layouts" ]];
                        int iInsTypeCount = [oSync ProcessInsTypes:[[oXML valueForKey:@"DATA"] valueForKey:@"InsTypes" ]];
                        int iPropertyCount = [oSync ProcessProperties:[[oXML valueForKey:@"DATA"] valueForKey:@"Properties"] oProgress:self.hud];
                        int iContactsCount = [oSync ProcessContact:[[oXML valueForKey:@"DATA"] valueForKey:@"Contacts"]];
                        int iInsAlertCount = [oSync ProcessInsAlerts:[[oXML valueForKey:@"DATA"] valueForKey:@"InsAlerts"]];
                        int iAssetLayoutCount = [oSync ProcessPropertyLayout:[[oXML valueForKey:@"DATA"] valueForKey:@"AssetLayouts"] oProgress:self.hud];
                        int iQuickPhraseCount = [oSync ProcessQuickPhrase:[[oXML valueForKey:@"DATA"] valueForKey:@"QuickPhrases"]];

                        int iScheduleCount = [oSync ProcessSchedules:[[oXML valueForKey:@"DATA"] valueForKey:@"Schedules"]];

                        [oSync ProcessCustomer:[[oXML valueForKey:@"DATA"] valueForKey:@"Users"]];
                        [db_Log InsertLog:@"Sync" sMessage:[NSString stringWithFormat:@"Status - Layout:%d, InsType:%d, Property:%d, Contact:%d, InsAlert:%d, AssetLayout:%d, QP:%d, CheckList:%d, Schedule:%d", iLayoutCount, iInsTypeCount, iPropertyCount, iContactsCount, iInsAlertCount, iAssetLayoutCount, iQuickPhraseCount, iCheckListCount, iScheduleCount]];

                        [self syncSmartCommentsWithHud:self.hud];
                        [self syncInsItemProductsWithHud:self.hud];
                        [self syncPropertyLayoutsWithHud:self.hud];
                        
                        [db_Log InsertLog:@"Sync" sMessage:@"Sync Success."];
                    }
                    else{
                        [db_Log InsertLog:@"Sync" sMessage:@"Empty XML"];
                        [self CompleteSync:false sMessage:@"Error Code 2, Please try again."];
                    }

                }
                else{
                    [db_Log InsertLog:@"Sync" sMessage:@"DownloadXML Fail"];
                    [self CompleteSync:false sMessage:@"Error Code 3, Please try again <NAME_EMAIL> for help."];
                }
                //return;

            } completionBlock:^{
                @strongify(self)
                // won't crash when self is nil because in Objective-C, sending a message to nil is a no-op - it silently does nothing and returns zero/nil.
                [self animateSyncButton:NO];
                // but accessing instance variables with the arrow operator (self->bSyncInAction),
                // which causes a crash when dereferencing a nil pointer.
                // So we need to check if self is nil before accessing instance variables.
                if (self) {
                    self->bSyncInAction = false;
                    self->bInitialSync = false;
                }

                // Notify sync completed
                [NSNotificationCenter.defaultCenter postNotificationName:kNotificationSyncCompleted object:nil];

                // End refresh control and reload data
                if ([self.navigationController.visibleViewController isKindOfClass:[UITabBarController class]]){
                    [self EndRefreshTable];
                }

                [self updateTabs];
            }];
            //return;

        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }

    }
}
-(void)EndRefreshTable{
    @try{
        NSArray *arrControllers = self.navigationController.viewControllers;
        for (UIViewController *ooo in arrControllers){
            if ([ooo isKindOfClass:[if_HomeScreen class]]){
                UITabBarController *oController = (UITabBarController *)ooo;
                NSArray * controllerArray = [oController viewControllers];
                for (UIViewController *oView in controllerArray){
                    if ([oView isKindOfClass:[if_Assets class]]){
                        if_Assets *ifAssets = (if_Assets *)oView;
                        [ifAssets EndRefreshTable];
                        [ifAssets ReloadTable:true];
                    } else if ([oView isKindOfClass:if_NewSchedule.class]) {
                        if_NewSchedule *schedule = (if_NewSchedule *)oView;
                        [schedule reloadSchedules];
                    }
                }
            }
        }
    } @catch(NSException *eeee) {

    }
}
-(void)ExternalRecover{
    @try{

        [self.navigationController.view addSubview:self.hud];
        self.hud.labelText = @"External Recover...";

        @weakify(self)
        [self.hud showAnimated:YES whileExecutingBlock:^{
            @weakify(self)
            NSMutableDictionary *oVideoToken_Temp = [@{@"sID": @"iXjuZDXCzk7Y4qFl0EVRxXrM0EPJCbrMPDfpu9xNIMbQFl3OZ3PGam5V"} mutableCopy];
            [self.hud setDetailsLabelText:@"Prepare Recover File"];
            NSDictionary *oReturn = [IFConnection PostRequest: EndpointGetFolderToken oParams:oVideoToken_Temp];
            if (oReturn != nil){
                NSString *sKey = oReturn[@"sAccessKey"];
                NSString *sSecret = oReturn[@"sSecretKey"];
                NSString *sToken = oReturn[@"sToken"];;
                NSString *sServerFolder = oReturn[@"sFolder"];;
                NSString *sFilePath = [[if_AppDelegate GetGlobalVar] valueForKey:@"sFilePath"];
                NSString *sBucket = oReturn[@"sBucket"];;

                NSString* file = @"IF_Data.db";
                [self.hud setDetailsLabelText:file];
                //AWSSTSCredential *credentials = [[AWSSTSCredential alloc] initWithAccessKey:sKey secretKey:sSecret sessionKey:sToken];
                AWSBasicSessionCredentialsProvider *credentials = [[AWSBasicSessionCredentialsProvider alloc] initWithAccessKey:sKey secretKey:sSecret sessionToken:sToken];
                AWSServiceConfiguration *configuration = [[AWSServiceConfiguration alloc] initWithRegion:AWSRegionUSWest2 credentialsProvider:credentials];
                [AWSServiceManager defaultServiceManager].defaultServiceConfiguration = configuration;
                [AWSS3TransferManager registerS3TransferManagerWithConfiguration:configuration forKey:@"recovery"];
                AWSS3TransferManager *oTransfer = [AWSS3TransferManager S3TransferManagerForKey:@"recovery"];
                int i=0;
                [self RecoverFileToS3:oTransfer sFileURL:[CommonHelper IF_FilePath:file] sServerURL:[NSString stringWithFormat:@"%@/database.db", sServerFolder] i:i sBucket:sBucket];
                NSDirectoryEnumerator* enumerator = [[NSFileManager defaultManager] enumeratorAtPath:sFilePath];

                while (file = [enumerator nextObject])
                {
                    // check if it's a directory

                    BOOL isDirectory = NO;
                    [[NSFileManager defaultManager] fileExistsAtPath: [NSString stringWithFormat:@"%@/%@",sFilePath,file]
                                                         isDirectory: &isDirectory];
                    if (!isDirectory)
                    {
                        @autoreleasepool {
                            i++;
                            [self RecoverFileToS3:oTransfer sFileURL:[CommonHelper IF_FilePath:file] sServerURL:[NSString stringWithFormat:@"%@/%@", sServerFolder, file] i:i sBucket:sBucket];
                        }

                    }
                }
                [AWSS3TransferManager removeS3TransferManagerForKey:@"recovery"];
                [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                   [self ShowAlert:@"Success" sMessage:@"Please contact your administrator. Thank you!"];
                }];

            }
            else{
                [self CompleteSync:false sMessage:@"Auth Fail, Please try again."];
                return;
            }


        } completionBlock:^{

        }];


    }@catch(NSException *ex){

    }

}

- (void)forceUploadLocalDataWithEndpoint: (Endpoint _Nonnull)endpoint params: (NSDictionary *_Nullable)params {
    [self.navigationController.view addSubview: self.hud];
    self.hud.labelText = @"Connecting to Server";

    @weakify(self)
    [self.hud showAnimated: YES whileExecutingBlock:^{
        @strongify(self)
        @try {
            self.hud.labelText = @"Please keep in foreground until finish.";
            [self.hud setDetailsLabelText:@"Prepare Recover File"];

            NSString *sFilePath = [[if_AppDelegate GetGlobalVar] valueForKey:@"sFilePath"];
            NSArray *fileNames = [NSFileManager.defaultManager contentsOfDirectoryAtPath: sFilePath error: NULL];
            NSMutableDictionary *groupedByExtension = [NSMutableDictionary dictionary];
            [fileNames forEach:^(NSString *name) {
                if (![groupedByExtension.allKeys containsObject: name.pathExtension]) {
                    groupedByExtension[name.pathExtension] = @0;
                }
                NSNumber *count = groupedByExtension[name.pathExtension];
                groupedByExtension[name.pathExtension] = @(count.integerValue + 1);
            }];

            NSString *content = [[groupedByExtension.allKeys compactMap:^NSString* (NSString *extension) {
                if (extension.isEmpty) {
                    return nil;
                }
                return [NSString stringWithFormat:@".%@ %ld files need to submit", extension, [groupedByExtension[extension] longValue]];
            }] componentsJoinedByString: @"\n"];
            [CommonHelper writeToTextFile: @"summary" content: content];

            /// prefs
            NSMutableDictionary *defaults = [NSUserDefaults.standardUserDefaults dictionaryRepresentation].mutableCopy;
            NSArray *keys = defaults.allKeys;
            for (NSString *key in keys) {
                id value = defaults[key];
                if ([value isKindOfClass:NSData.class]) {
                    [defaults removeObjectForKey: key];
                } else if ([value isKindOfClass: NSDate.class]) {
                    defaults[key] = [(NSDate *)value toStringWithFormat: @"yyyyMMdd'T'HHmmss"];
                }
            }

            [CommonHelper writeToTextFile: @"prefs" content: defaults.toJson];

            NSDictionary *oReturn = [IFConnection PostRequest: endpoint oParams:params.mutableCopy];
            if (oReturn != nil){
                NSString *sKey = oReturn[@"sAccessKey"];
                NSString *sSecret = oReturn[@"sSecretKey"];
                NSString *sToken = oReturn[@"sToken"];;
                NSString *sServerFolder = oReturn[@"sFolder"];;
                NSString *sBucket = oReturn[@"sBucket"];;

                NSString* file = @"IF_Data.db";
                [self.hud setDetailsLabelText:file];
                 AWSBasicSessionCredentialsProvider *credentials = [[AWSBasicSessionCredentialsProvider alloc] initWithAccessKey:sKey secretKey:sSecret sessionToken:sToken];
                // AWSSTSCredential *credentials = [[AWSSTSCredential alloc] initWithAccessKey:sAccessKey secretKey:sSecretKey sessionKey:sToken];
                 AWSServiceConfiguration *configuration = [[AWSServiceConfiguration alloc] initWithRegion:AWSRegionUSWest2 credentialsProvider:credentials];
                [AWSServiceManager defaultServiceManager].defaultServiceConfiguration = configuration;
                [AWSS3TransferManager registerS3TransferManagerWithConfiguration:configuration forKey:@"recovery"];
                AWSS3TransferManager *oTransfer = [AWSS3TransferManager S3TransferManagerForKey:@"recovery"];
                int i=0;
                [self RecoverFileToS3:oTransfer sFileURL:[CommonHelper IF_FilePath:file] sServerURL:[NSString stringWithFormat:@"%@/database.db", sServerFolder] i:i sBucket:sBucket];
                NSDirectoryEnumerator* enumerator = [[NSFileManager defaultManager] enumeratorAtPath:sFilePath];

                while (file = [enumerator nextObject])
                {
                    // check if it's a directory

                    BOOL isDirectory = NO;
                    [[NSFileManager defaultManager] fileExistsAtPath: [NSString stringWithFormat:@"%@/%@",sFilePath,file]
                                                         isDirectory: &isDirectory];
                    if (!isDirectory)
                    {
                        @autoreleasepool {
                            i++;
                            [self RecoverFileToS3:oTransfer sFileURL:[CommonHelper IF_FilePath:file] sServerURL:[NSString stringWithFormat:@"%@/%@", sServerFolder, file] i:i sBucket:sBucket];
                        }

                    }
                }
                [AWSS3TransferManager removeS3TransferManagerForKey:@"recovery"];
                NSMutableDictionary *params = [@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"sSubject": [NSString stringWithFormat:@"New Data Submit From %@", [CommonHelper IFGetPref:@"sEmail"]], @"sMessage": [NSString stringWithFormat:@"Folder Name %@   UserName: %@", sServerFolder, [CommonHelper IFGetPref:@"sEmail"]]} mutableCopy];
                [self.hud setDetailsLabelText:@"Nofity Support"];
                NSDictionary *oReturn = [IFConnection PostRequest: EndpointSendEmail oParams:params];
                NSString *cc = @"bb";
            }
            else{
                [self CompleteSync:false sMessage:@"Auth Submit Data Fail, Please try again."];
                return;
            }

        }
        @catch (NSException *exception) {
            [db_Log InsertLog:@"Sync" sMessage:@"Recover Fail"];
            [self CompleteSync:true sMessage:@"Error Code 1, Please try again."];
            return;
        }
    } completionBlock:^{
        [db_Log InsertLog:@"Sync" sMessage:@"Submit - Upload File Success"];

        [self CompleteSync:true sMessage:@"Sync Success. File Submitted"];
    }];
}

- (void)displayExternalScheduleIfNeeded {
    @try {
        if ([[CommonHelper IFGetPref:@"iCustomerID"] intValue] <= 0) {
            [self ShowAlert:@"Message" sMessage:@"Please login to start the scheduled inspection. "];
            return;
        }

        NSString *codeValue = [CommonHelper IFGetPref: kExternalScheduleCode];
        NSDictionary *queryParams = [[codeValue base64URLDecoded] queryDictionary];
        NSString *scheduleId = queryParams[@"iScheduleID"];

        // If there is no scheduleId, no need to display the external schedule
        if ([NSString isNullOrEmpty:scheduleId]) return;

        // If the `home screen` is not at the top most front,
        // then we need to pop to it and also dismiss the presented view controllers.
        [self.navigationController popToViewController: self animated: YES];
        [self.navigationController dismissViewControllerAnimated:YES completion: nil];
        [self dismissViewControllerAnimated: YES completion: nil];

        [self switchToNewTab:HomeScreenTabSchedules];
        if_NewSchedule *scheduleVC = self.selectedViewController;
        if (scheduleVC != nil) {
            [scheduleVC loadExternalSchedule: scheduleId.intValue];
        }

        // reset the code value
        [CommonHelper IFSavePref: kExternalScheduleCode sValue: nil];
    }@catch(NSException *ex){}

}

- (void)displayAssetDetailIfNeed {
    
    @try{
        NSString *assetId = [CommonHelper IFGetPref: kExternalAssetID];
        NSInteger iSAssetID = [assetId integerValue];
        // If there is no valid assetId, no need to display the asset detail
        if ([NSString isNullOrEmpty:assetId] || iSAssetID == 0) return;

        // Open the asset detail screen
        [Navigator pushAssetDetail: iSAssetID animated: YES];
        // reset the asset id
        [CommonHelper IFSavePref: kExternalAssetID sValue: nil];
    }@catch(NSException *ex){}

}

- (BOOL)bTenantTool {
    
    @try{
        return [CommonHelper bKioskMode] ||
                [CommonHelper bRequestInspectionMode] ||
                [CommonHelper bTenantToolMode];
    }@catch(NSException *ex){}
    return false;

}

- (void)switchToNewTab:(HomeScreenTab)tab {
    // Find the index of the tab we want to switch to
    NSInteger tabIndex = NSNotFound;
    
    switch (tab) {
        case HomeScreenTabAssets:
            tabIndex = [self viewControllerIndexWithClass:if_Assets.class];
            break;
        case HomeScreenTabInspections:
            tabIndex = [self viewControllerIndexWithClass:if_Inspection.class];
            break;
        case HomeScreenTabSchedules:
            tabIndex = [self viewControllerIndexWithClass:if_NewSchedule.class];
            break;
        case HomeScreenTabProjects:
            tabIndex = [self viewControllerIndexWithClass:if_Projects.class];
            break;
        case HomeScreenTabTasks:
            tabIndex = [self viewControllerIndexWithClass:if_MyTasks.class];
            break;
    }
    
    if (tabIndex != NSNotFound) {
        self.selectedIndex = (NSUInteger)tabIndex;
    }
}

@end
