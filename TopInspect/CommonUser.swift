//
// Created by <PERSON> on 2022/8/29.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

import Foundation
import AppFeatures

class CommonUser: NSObject {
    @objc static func isLogin(_ user: O_User) -> Bool {
        user.userID == iCustomerID && iCustomerID > 0
    }

    @objc static var iCustomerID: Int {
        guard let iCustomerID = CommonHelper.ifGetPref(PrefsKeys.iCustomerID) else { return 0 }
        return Int(iCustomerID) ?? 0
    }
    
    @objc static var currentToken: String? {
        environment.keychainService.getToken(forCustomerID: iCustomerID)
    }
    
    @objc static var currentUserName: String {
        [CommonHelper.ifGetPref(PrefsKeys.sFirstName), CommonHelper.ifGetPref(PrefsKeys.sLastName)]
            .compactMap { $0 }
            .joined(separator: " ")
    }
}
