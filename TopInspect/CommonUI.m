//
//  CommonUI.m
//  SnapInspect3
//
//  Created by <PERSON> on 13/05/16.
//  Copyright © 2016 SnapInspect. All rights reserved.
//

#import "NSArray+HighOrderFunction.h"
@import AppFeatures;

@implementation CommonUI
+(NSString *)GetCompanyName{
    @try{
        return [CommonHelper IFGetPref:@"sCompanyName"];
    }@catch(NSException *ex){
        
    }
    return @"";
}
+(bool)bEnableWord{
    @try{
        return [[CommonHelper IFGetPref:PrefsKeys.bEnableWord] isEqualToString:@"1"];
    }@catch(NSException *ex){
        
    }
    return false;
}
+(int)ProcessPromptLabel:(UILabel *)txtLabel sPromptText:(NSString *)sPromptText iWidth:(int)iWidth iCurrentPosition:(int)iCurrentPosition{
    int iPromptHeight = [CommonHelper GetLabelHeight_Prompt:sPromptText iWidth:iWidth - 40];
    [txtLabel setFont:[UIFont italicSystemFontOfSize:13]];
    [txtLabel setTextColor:[UIColor grayColor]];
    //UILabel *oLabel  =[[UILabel alloc] initWithFrame:CGRectMake(40, 10, iWidth - 80, iLabelHeight+5)];
    [txtLabel setFrame:CGRectMake(20, iCurrentPosition + 10, iWidth - 40, iPromptHeight)];
    txtLabel.textAlignment = NSTextAlignmentCenter;
    txtLabel.numberOfLines = 0;
    txtLabel.text = sPromptText;
    //txtLabel.backgroundColor = [UIColor redColor];
    return iPromptHeight;
}

+(void)setPromptLabelWithText:(UILabel *)txtLabel sPromptText:(NSString *)sPromptText iWidth:(int)iWidth iCurrentPosition:(int)iCurrentPosition {
    [CommonUI setPromptLabel:txtLabel];
    int iPromptHeight = [CommonHelper GetLabelHeight_Prompt:sPromptText iWidth:iWidth - 40];
    [txtLabel setFrame:CGRectMake(20, iCurrentPosition + 10, iWidth - 40, iPromptHeight)];
    txtLabel.text = sPromptText;
}

+ (void)setPromptLabel:(UILabel* ) txtLabel {
    [txtLabel setFont:[UIFont systemFontOfSize:12]];
    [txtLabel setTextColor:[UIColor colorWithRed:74.0f/255 green:74.0f/255 blue:74.0f/255 alpha:1.0f]];
    txtLabel.textAlignment = NSTextAlignmentLeft;
    txtLabel.numberOfLines = 0;
}

+(NSString *)GetStatusCode:(NSString *)sStatus_Temp{
    if (sStatus_Temp != nil && [sStatus_Temp length] > 0){
        if ([sStatus_Temp length] == 1){
            return [sStatus_Temp substringToIndex:1];
        }
        else if ([sStatus_Temp rangeOfString:@" "].location != NSNotFound){
            NSRange rangeOfSpace = [sStatus_Temp rangeOfString:@" "];
            NSString *sSecond = @"";
            @try{
                sSecond = [sStatus_Temp substringFromIndex:rangeOfSpace.location + 1];
                sSecond = [sSecond substringToIndex:1];
            }@catch(NSException *eeee){
                
            }
            
            return [NSString stringWithFormat:@"%@%@", [sStatus_Temp substringToIndex:1], sSecond];
        }
        else{
            return  [sStatus_Temp substringToIndex:2];
        }
    }
    return @"";

}
+ (NSString *)GetRatingLabelText:(NSString *)sTitle {
    NSString *title = @"";
    @try {
        title = [[[sTitle stringByTrimmingCharactersInSet:
                [NSCharacterSet whitespaceAndNewlineCharacterSet]] substringToIndex:1] uppercaseString];
        return title;
    } @catch (NSException *ex) {
        Log_Exception(ex)
    }
    return @"";
}

+(int)ProcessLabel:(UILabel *)txtLabel sText:(NSString *)sText iWidth:(int)iWidth sCustom1:(NSString *)sCustom1 iCurrentPosition:(int)iCurrentPosition{
    int iLabelHeight =[CommonHelper GetLabelHeight:sText iWidth:iWidth - 40];
    
    [txtLabel setFrame: CGRectMake(20,iCurrentPosition+ 10, iWidth - 40, iLabelHeight+20)];
    txtLabel.textAlignment = NSTextAlignmentCenter;
    txtLabel.numberOfLines = 0;
   // [txtLabel setBackgroundColor:[UIColor redColor]];
   // txtLabel.font = [UIFont systemFontOfSize:15];
    txtLabel.font = [UIFont boldSystemFontOfSize:15];
    txtLabel.text = sText;
    //txtLabel.backgroundColor = [UIColor blueColor];
    NSString *sTemp1 = [CommonJson GetJsonKeyValue:@"notice" sJson:sCustom1];
    if (sTemp1 != nil && [sTemp1 isEqualToString:@"1"]){
        [txtLabel setTextColor:[UIColor redColor]];
    }
    return iLabelHeight + 15;
}

+ (void)insertTextView: (UITextView *)oTextView withSelectedText:(NSString *)text {
    if ([text length] == 0) return;
    
    UITextPosition *start = oTextView.beginningOfDocument, *end = oTextView.endOfDocument;
    UITextRange *selectedRange = oTextView.selectedTextRange;
    UITextRange *prefixRange = [oTextView textRangeFromPosition: start toPosition: selectedRange.start];
    UITextRange *suffixRange = [oTextView textRangeFromPosition: selectedRange.end toPosition: end];

    NSString *prefix = [oTextView textInRange: prefixRange];
    if (prefix.length > 0 && ![prefix hasSuffix: @" "]) {
        text = [@" " stringByAppendingString: text];
    }
    NSString *suffix = [oTextView textInRange: suffixRange];
    if (suffix.length > 0 && [suffix hasPrefix: @" "]) {
        NSString *suffixRemovedFirst = [suffix substringFromIndex:1];
        [oTextView replaceRange: suffixRange withText: suffixRemovedFirst];
        oTextView.selectedTextRange = selectedRange;
    }

    text = [text stringByAppendingString: @" "];
    [oTextView insertText: text];
}

+ (void)showsCommentsLibrary:(UITextView *)oTextView
                   withInsId:(int)iInsID
                   insItemId:(int)iInsItemID
                  pInsItemId:(int)pInsItemID
                  completion:(void (^)(NSString *sComments))completion {

    UIView *accessoryView = [[UIView alloc] initWithFrame:UIScreen.mainScreen.bounds];
    accessoryView.height = 43.0;
    accessoryView.backgroundColor = [UIColor colorWithHex:0x477BFF];

    UIButton *btnCommentsLibrary = [UIButton buttonWithType:UIButtonTypeCustom];
    [btnCommentsLibrary setTitle:@"Comments Library" forState:UIControlStateNormal];
    [btnCommentsLibrary setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    btnCommentsLibrary.titleLabel.font = [UIFont SanFranciscoText_Semibold:14.0];

    @weakify(oTextView)
    btnCommentsLibrary.onTapped = ^(id sender) {
        [Navigator PushCommentLibraryWithInsId:iInsID
                                     insItemId:iInsItemID
                                    pInsItemId:pInsItemID
                              selectedComments:^(NSString *sComments) {
                                  @strongify(oTextView)
                                  [CommonUI insertTextView:oTextView withSelectedText:sComments];
                                  if (completion) completion(oTextView.text);
                              }];
    };

    [accessoryView addSubview:btnCommentsLibrary];
    [btnCommentsLibrary mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(accessoryView);
        make.right.equalTo(accessoryView).offset(-20.0);
    }];

    // Code reader
    UIButton *btnCodeReader = [UIButton buttonWithType:UIButtonTypeCustom];
    UIImage *qrCodeImage = [UIImage imageNamed:@"icon_toolbar_qrcode" withTintColor:UIColor.whiteColor];
    [btnCodeReader setImage:qrCodeImage forState:UIControlStateNormal];

    btnCodeReader.onTapped = ^(id sender) {
        [CommonUI presentCodeReaderFromViewController:UIViewController.topMost
                                  metadataObjectTypes:@[
                                          AVMetadataObjectTypeEAN13Code,
                                          AVMetadataObjectTypeEAN8Code,
                                          AVMetadataObjectTypeUPCECode,
                                          AVMetadataObjectTypeCode39Code,
                                          AVMetadataObjectTypeCode39Mod43Code,
                                          AVMetadataObjectTypeCode93Code,
                                          AVMetadataObjectTypeCode128Code,
                                          AVMetadataObjectTypePDF417Code,
                                          AVMetadataObjectTypeQRCode
                                  ]
                                           completion:^(NSString *sCode) {
                                               @strongify(oTextView)
                                               [CommonUI insertTextView:oTextView withSelectedText:sCode];
                                               if (completion) completion(oTextView.text);
                                           }];
    };

    [accessoryView addSubview:btnCodeReader];
    [btnCodeReader mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(accessoryView);
        make.left.equalTo(accessoryView).offset(20.0);
    }];

    oTextView.inputAccessoryView = accessoryView;
}

+ (UIColor *)avatarColorWithUserID:(int)userID {
    NSArray *availableHexColors = @[
        @0x31D264, @0xD50000, @0xF44336, @0xFF6D00, @0xFB8C00, @0xFF5151,
        @0xFFC107, @0xFFD600, @0xC6FF00, @0x76FF03, @0x00C853, @0x388E3C,
        @0x00B0FF, @0x0091EA, @0x303F9F, @0xBA68C8, @0xAB47BC, @0x6A1B9A,
        @0xD500F9, @0xAA00FF,
    ];
    
    NSInteger idx = [[CommonJson GetUsers] firstIndex:^BOOL(O_User *user) {
        return user.userID == userID;
    }];
    
    NSInteger index = idx % availableHexColors.count;
    NSInteger hex = [[availableHexColors safe_objectAtIndex: index] integerValue];
    return hex > 0 ? [UIColor colorWithHex: hex] : availableHexColors.firstObject;
}

+ (UIImage *)imageForCommentType: (CommentBoxType)type {
    NSString *imageName = nil;
    switch (type) {
        case CommentBoxTypeDate:
            imageName = @"comment_box_date";
            break;
        case CommentBoxTypeNumber:
            imageName = @"comment_box_number";
            break;
        case CommentBoxTypeText:
            imageName = @"comment_box_text";
            break;
        case CommentBoxTypeList:
            imageName = @"comment_box_list";
            break;
        case CommentBoxTypeCost:
            imageName = @"comment_box_cost";
            break;
    }
    return [imageName length] > 0 ? [UIImage imageNamed: imageName] : nil;
}

+ (void)setupAppearance {

    if (@available(iOS 13.0, *)) {
        UITabBarAppearance *tabBarAppearance = [[UITabBarAppearance alloc] init];
        [tabBarAppearance configureWithOpaqueBackground];
        tabBarAppearance.backgroundColor = UIColor.whiteColor;
        UITabBar.appearance.standardAppearance = tabBarAppearance;
        if (@available(iOS 15.0, *)) {
            UITabBar.appearance.scrollEdgeAppearance = tabBarAppearance;
        }
    }
    
    // Setup navigation bar title text color
    [self configureNavigationBarAppearance:UIColor.color_navigationBar
                                 textColor:UIColor.whiteColor];
}

+ (void)configureNavigationBarAppearance:(UIColor *)backgroundColor
                               textColor:(UIColor *)textColor {
    UINavigationBarAppearance *navBarAppearance = [[UINavigationBarAppearance alloc] init];
    [navBarAppearance configureWithOpaqueBackground];
    navBarAppearance.backgroundColor = backgroundColor;
    navBarAppearance.titleTextAttributes = @{
        NSForegroundColorAttributeName: textColor
    };
    
    UIBarButtonItemAppearance *barButtonItemAppearance = [[UIBarButtonItemAppearance alloc] init];
    NSDictionary<NSAttributedStringKey, id> *titleTextAttributes = @{
        NSForegroundColorAttributeName: textColor
    };
    barButtonItemAppearance.normal.titleTextAttributes = titleTextAttributes;
    barButtonItemAppearance.highlighted.titleTextAttributes = titleTextAttributes;
    
    navBarAppearance.buttonAppearance = barButtonItemAppearance;
    navBarAppearance.doneButtonAppearance = barButtonItemAppearance;
    navBarAppearance.backButtonAppearance = barButtonItemAppearance;
    
    UINavigationBar.appearance.tintColor = textColor;
    UINavigationBar.appearance.standardAppearance = navBarAppearance;
    UINavigationBar.appearance.scrollEdgeAppearance = navBarAppearance;
    
    // Setup navigation bar title text color
    UINavigationBar.appearance.titleTextAttributes = @{
        NSForegroundColorAttributeName: textColor
    };
}

+ (void)setNavigationBarAppearanceShadowHidden:(BOOL)hidden {
    UINavigationBarAppearance *appearance = UINavigationBar.appearance.standardAppearance;
    appearance.shadowImage = hidden ? [UIImage new] : nil;
    UINavigationBar.appearance.standardAppearance = appearance;
    UINavigationBar.appearance.scrollEdgeAppearance = appearance;
    UINavigationBar.appearance.compactAppearance = appearance;
}

+ (void)hideKeyboard {
    [[UIApplication sharedApplication] sendAction: @selector(resignFirstResponder) to: nil from: nil forEvent: nil];
}

+ (UIColor *)projectProgressColorWithCompleted: (NSInteger)completed total: (NSInteger)total {
    if (total <= 0) return [UIColor colorFromHexString:@"#22BC90"];
    if (completed == total) {
        return [UIColor colorFromHexString:@"#22BC90"];
    } else if (completed > 0) {
        return [UIColor colorFromHexString:@"#FFAA00"];
    } else {
        return [UIColor colorFromHexString:@"#FF5151"];
    }
}

+ (BOOL)deviceOrientationIsLandscapeLeft {
    return UIDevice.currentDevice.orientation == UIDeviceOrientationLandscapeLeft;
}

+ (BOOL)deviceOrientationIsLandscapeRight {
    return UIDevice.currentDevice.orientation == UIDeviceOrientationLandscapeRight;
}

+ (BOOL)deviceOrientationIsLandscape {
    return [self deviceOrientationIsLandscapeLeft] || [self deviceOrientationIsLandscapeRight];
}

+ (void)updateInspectionStatus:(NSInteger)iSInsID
            fromViewController:(UIViewController *)viewController
                    sourceView:(UIView *)sourceView
                    completion:(void (^ __nullable)(NSString *sCustom1, NSString *sStatus, NSString *sColorCode))completion {
    NSArray *arrStatus = [CommonHelper GetStatusArray:0];
    NSArray *arrStatusName = [arrStatus map:^NSString *(NSDictionary *status) {
        return status[@"sName"];
    }];
    @weakify(viewController, self)
    [viewController
            ShowOptions:arrStatusName
               selected:^(NSString *option, NSInteger index) {
                   @strongify(viewController, self)
                   NSDictionary *oTemp = [arrStatus safe_objectAtIndex:index];
                   NSString *sStatus = [oTemp valueForKey:@"sName"];
                   NSString *sColorCode = [oTemp valueForKey:@"sColorCode"];
                   [self requestUpdateInspectionStatus:iSInsID
                                    fromViewController:viewController
                                               sStatus:sStatus
                                            sColorCode:sColorCode
                                            completion:completion];
               }
                  title:@"Update Status"
               sMessage:nil
             sourceView:sourceView
           cancelButton:@"Cancel"];
}

+ (void)requestUpdateInspectionStatus:(NSInteger)iSInsID
                   fromViewController:(UIViewController *)viewController
                            sStatus:(NSString *)sStatus
                         sColorCode:(NSString *)sColorCode
                          completion:(void (^ __nullable)(NSString *sCustom1, NSString *sStatus, NSString *sColorCode))completion {
    [viewController showLoading:@"Processing..."];
    dispatch_background_async((^{
        NSMutableDictionary *oRefreshToken = [@{
                @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
                @"sToken": [CommonUser currentToken],
                @"iInspectionID": [NSString stringWithFormat:@"%ld", (long) iSInsID],
                @"sStatus": sStatus,
                @"sColorCode": sColorCode} mutableCopy];
        NSDictionary *oReturn = [[IFConnection
                PostRequest:EndpointUpdateInspectionStatusAPI oParams:oRefreshToken] mutableCopy];
        safe_dispatch_main_async(^{
            [viewController hidesLoading];
            if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]) {
                NSString *sCustom1 = [CommonHelper DictionaryToJson:[oReturn[@"oInspection"] objectForKey:@"sCustom1"]];
                if (completion) completion(sCustom1, sStatus, sColorCode);
            } else if (oReturn != nil && (![[oReturn valueForKey:@"success"] boolValue])) {
                [viewController ShowAlert:@"Error" sMessage:[oReturn valueForKey:@"message"]];
            }
        });
    }));
}

+ (void)presentCodeReaderFromViewController:(UIViewController *)viewController
                        metadataObjectTypes:(NSArray *)metadataObjectTypes
                                 completion:(void (^)(NSString *sCode))completion {
    if ([QRCodeReader supportsMetadataObjectTypes:metadataObjectTypes]) {
        QRCodeReader *reader = [QRCodeReader readerWithMetadataObjectTypes:metadataObjectTypes];
        QRCodeReaderViewController *vc = [QRCodeReaderViewController readerWithCancelButtonTitle:@"Cancel"
                codeReader:reader startScanningAtLoad:YES showSwitchCameraButton:YES showTorchButton:YES];
        vc.modalPresentationStyle = UIModalPresentationFormSheet;
        @weakify(viewController, reader)
        [vc setCompletionWithBlock:^(NSString *resultAsString) {
            @strongify(viewController, reader)
            [reader stopScanning];
            [viewController dismissViewControllerAnimated:YES completion:^{
                if (completion) completion(resultAsString);
            }];
        }];
        [viewController presentViewController:vc animated:YES completion:NULL];
    } else {
        [viewController ShowAlert:@"Error" sMessage:@"Reader not supported by the current device. Please enable camera permission."];
    }
}

@end
