//
//  if_Projects.swift
//  SnapInspect3
//
//  Created by <PERSON> on 2022/7/13.
//  Copyright © 2022 SnapInspect. All rights reserved.
//

import UIKit
import UIScrollView_InfiniteScroll
import Extensions
import AppFeatures

protocol SyncProjectBehavior {
    var refreshControl: UIRefreshControl { get }
    var keywords: String { get }
    func loadAllProjectsFile()
    func didProcessProjects()
}

extension SyncProjectBehavior where Self: UIViewController {
    func loadAllProjectsFile() {
        guard NetworkConnection.isNetworkReachable else {
            refreshControl.endRefreshing()
            showAlert("Error", sMessage: "Please connect to the internet to sync the projects")
            return
        }

        if !refreshControl.isRefreshing {
            showLoading()
        }

        DispatchQueue.global(qos: .userInitiated).async {
            let lastSyncDate = CommonHelper.ifGetPref(PrefsKeys.sKeyProjectSyncDate) ?? ""
            var params = [
                "iCustomerID": CommonHelper.ifGetPref("iCustomerID") ?? "",
                "sToken": CommonUser.currentToken ?? "",
                "sDateTime": lastSyncDate.ifEmpty(then: "1980-1-1 0:0")
            ]

            // When syncing project at first time, `bInitialSync` can be passed and server will return all the records
            if lastSyncDate.isEmpty {
                params["bInitialSync"] = "true"
            }

            guard let result = IFConnection.postRequest(.syncProjects, oParams: params) else {
                DispatchQueue.main.async {
                    self.hidesLoading()
                    self.refreshControl.endRefreshing()
                    self.showAlert("Error", sMessage: "Please check your network connection and try again.")
                }
                return
            }
            DispatchQueue.main.async {
                self.hidesLoading()
                if (result["success"] as? Bool) == true,
                   let fileURL = URL(string: result["sFile"] as? String ?? "")
                {
                    let dtSyncDate = result["dtSyncDate"] as? String ?? NSDate().yyyyMMMMddHHmmss()
                    self.downloadProjectsFile(fileURL, dtSyncDate: dtSyncDate)
                } else {
                    self.showAlert("Error", sMessage: result["message"] as? String ?? "Unknown error.")
                    self.refreshControl.endRefreshing()
                }
            }
        }
    }

    private func downloadProjectsFile(_ url: URL, dtSyncDate: String) {
        // Cancel any previous download task
        let fileDownloadTask = URLSession.shared.downloadTask(with: url) { [weak self] url, _, error in
            guard let self else { return }
            defer { DispatchQueue.main.async { self.hidesLoading() } }
            if let error = error {
                DispatchQueue.main.async {
                    self.showAlert("Error", sMessage: error.localizedDescription)
                    self.refreshControl.endRefreshing()
                }
                return
            }
            guard let url = url else {
                DispatchQueue.main.async {
                    self.showAlert("Error", sMessage: "Unknown error.")
                    self.refreshControl.endRefreshing()
                }
                return
            }
            let path = NSSearchPathForDirectoriesInDomains(.cachesDirectory, .userDomainMask, true)[0]
            let filePath = URL(fileURLWithPath: path).appendingPathComponent(CommonHelper.getFileName() + ".json")
            try? FileManager.default.moveItem(at: url, to: filePath)
            self.parseProjectsFile(filePath, dtSyncDate: dtSyncDate)
        }
        fileDownloadTask.resume()
        if !refreshControl.isRefreshing {
            showLoading("Downloading projects...")
        }
    }

    private func parseProjectsFile(_ filePath: URL, dtSyncDate: String) {
        do {
            DispatchQueue.main.async {
                if !self.refreshControl.isRefreshing {
                    self.showLoading("Processing projects...")
                }
            }
            let data = try Data(contentsOf: filePath)
            let json = try JSONSerialization.jsonObject(with: data, options: [])
            if let json = json as? [String: Any] {
                if let projects = json["lsProject"] as? [[String: Any]] { // lsProject
                    db_Projects.save(projects: projects.map(O_Project.init(dictionary:)))
                }
                if let projectInspections = json["lsProjectInspection"] as? [[String: Any]] { // lsProjectInspection
                    db_Projects.save(projectInspections: projectInspections.map(O_ProjectInspection.init(dictionary:)))
                }
                // Save sync date after processing the file
                CommonHelper.ifSavePref(PrefsKeys.sKeyProjectSyncDate, sValue: dtSyncDate)
            }

            DispatchQueue.main.async {
                self.didProcessProjects()
                self.hidesLoading()
                self.refreshControl.endRefreshing()
            }
        } catch {
            DispatchQueue.main.async {
                self.showAlert("Error", sMessage: error.localizedDescription)
            }
        }
    }
}

class if_Projects: UIViewController, SyncProjectBehavior {
    @objc weak var homeDelegate: if_HomeScreenDelegate?

    @IBOutlet private var tableView: UITableView!
    @IBOutlet private var leftBarButton: UIButton!
    @IBOutlet private var rightBarButton: UIButton!
    @IBOutlet private var titleLabel: UILabel!
    @IBOutlet private var searchBar: UITextField!

    private var allProjects: [O_Project] = []
    private(set) var keywords: String = ""
    lazy var refreshControl: UIRefreshControl = {
        let view = UIRefreshControl()
        view.addTarget(self, action: #selector(startLoadAllProjectsFile), for: .valueChanged)
        return view
    }()

    private var fileDownloadTask: URLSessionTask?
    private lazy var dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.timeZone = TimeZone(abbreviation: "GMT")
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.dateFormat = yyyyMMddHHmmss
        return formatter
    }()

    private var dtSyncDate: String = ""

    override func viewDidLoad() {
        super.viewDidLoad()
        titleLabel.text = "Projects"
        navigationController?.applyBlueTheme()

        tableView.keyboardDismissMode = .onDrag
        tableView.hidesEmptyCells()
        tableView.separatorStyle = .none
        tableView.refreshControl = refreshControl
        tableView.register(ProjectOverviewCell.self)
        tableView.register(EmptyDataViewCell.self)
        tableView.register(headerFooterViewType: ProjectOverviewHeaderView.self)
        if #available(iOS 15.0, *) {
            tableView.sectionHeaderTopPadding = 0.0
        }

        searchBar.delegate = self

        leftBarButton.addTarget(self, tapped: #selector(onLeftButtonTapped))
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        reloadTableView(with: keywords)

        if CommonHelper.ifGetPref(PrefsKeys.kForceSyncProjects) != "1",
           let syncDateValue = CommonHelper.ifGetPref(PrefsKeys.sKeyProjectSyncDate),
           !syncDateValue.isEmpty,
           let syncDate = dateFormatter.date(from: syncDateValue)
        {
            // More than 1 day should auto sync the projects
            if NSDate().daysBetweenDate(syncDate) > 1 {
                loadAllProjectsFile()
            }
        } else {
            loadAllProjectsFile()
        }
    }

    func didProcessProjects() {
        reloadTableView(with: keywords)
        CommonHelper.ifSavePref(PrefsKeys.kForceSyncProjects, sValue: "")
    }
}

extension if_Projects {
    @objc private func startLoadAllProjectsFile() {
        loadAllProjectsFile()
    }

    @objc private func onLeftButtonTapped(_ sender: UIButton) {
        homeDelegate?.goToSetting()
    }

    private var hasNoActiveProjects: Bool {
        allProjects.isEmpty && (searchBar.text ?? "").isEmpty
    }
}

extension if_Projects: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard !hasNoActiveProjects else { return 1 }
        return allProjects.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if hasNoActiveProjects {
            let cell = tableView.dequeueReusableCell(for: indexPath) as EmptyDataViewCell
            cell.configure(withModel: EmptyDataViewCell.ViewType.projects)
            return cell
        } else {
            let cell = tableView.dequeueReusableCell(for: indexPath) as ProjectOverviewCell
            cell.configure(withModel: allProjects[indexPath.row])
            return cell
        }
    }
}

extension if_Projects: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        guard !hasNoActiveProjects else { return 0.01 }
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        guard !hasNoActiveProjects else { return tableView.height }
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        guard !hasNoActiveProjects else { return nil }
        let headerView = tableView.dequeueReusableHeaderFooterView() as ProjectOverviewHeaderView
        headerView.si_setBackgroundColor(.white)
        return headerView
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        guard !hasNoActiveProjects else { return }
        tableView.deselectRow(at: indexPath, animated: true)
        if let pi = UIStoryboard.main().identifier("projectInspections") as? if_ProjectInspections {
            pi.projectID = allProjects[indexPath.row].iProjectID
            pi.homeDelegate = homeDelegate
            navigationController?.pushViewController(pi, animated: true)
        }
    }

    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        if let cell = cell as? EmptyDataViewCell {
            cell.learnMore = { [weak self] in
                guard let self = self,
                      let webView = self.storyboard?.instantiateViewController(withIdentifier: "WebView") as? if_WebView
                else { return }
                webView.sURL = Constants.kProjectLearnMoreURL
                self.navigationController?.pushViewController(webView, animated: true)
            }
        }
    }
}

extension if_Projects: UITextFieldDelegate {
    public func textFieldShouldClear(_ textField: UITextField) -> Bool {
        searchBar.text = ""
        searchBar.resignFirstResponder()
        CommonUI.hideKeyboard()
        reloadTableView(with: keywords)
        return true
    }

    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder()
        return true
    }

    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        let finalText = (textField.text as NSString?)?.replacingCharacters(in: range, with: string) ?? ""

        NSObject.cancelPreviousPerformRequests(
            withTarget: self, selector: #selector(reloadTableView(with:)), object: finalText
        )
        perform(#selector(reloadTableView(with:)), with: finalText, afterDelay: 0.2)
        return true
    }

    @objc private func reloadTableView(with keywords: String) {
        self.keywords = keywords.trimmingCharacters(in: .whitespacesAndNewlines)
        let currentUserID = Int(CommonHelper.ifGetPref("iCustomerID") ?? "") ?? 0
        // Company admin can see all projects
        let isCompanyAdmin = CommonHelper.ifGetPref("sRole") == Constants.kUserRoleCompanyManager
        allProjects = db_Projects.getProjects(with: keywords, inspectorId: currentUserID, isAdmin: isCompanyAdmin)
            .sorted(by: \.iUncompletedIns, ascending: false)
        tableView.reloadData()
    }
}
