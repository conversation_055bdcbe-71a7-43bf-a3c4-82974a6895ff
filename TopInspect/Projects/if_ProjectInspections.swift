//
//  if_ProjectInspections.swift
//  SnapInspect3
//
//  Created by <PERSON> on 2022/7/15.
//  Copyright © 2022 SnapInspect. All rights reserved.
//

import UIKit
import Extensions

class if_ProjectInspections: BaseViewController, SyncProjectBehavior {
    private(set) var keywords: String = ""

    public var projectID: Int = 0
    public weak var homeDelegate: if_HomeScreenDelegate?

    enum Status: Int, CaseIterable {
        case inProgress = 0, completed
    }
    
    @IBOutlet private var tableView: UITableView!
    @IBOutlet private var searchBar: UITextField!
    @IBOutlet private var segmentedControl: SISegmentedControl!

    private var project: O_Project!
    private var lsProjectInspections: [O_ProjectInspection] = []
    private var status: Status = .inProgress
    private var hasInspections = false

    private var currentLatitude: Double = 0.0
    private var currentLongitude: Double = 0.0

    internal lazy var refreshControl: UIRefreshControl = {
        let view = UIRefreshControl();
        view.addTarget(self, action: #selector(startSyncProjects), for: .valueChanged)
        return view;
    }()

    override func viewDidLoad() {
        super.viewDidLoad()

        segmentedControl.roundCorner = 18;
        segmentedControl.names = Status.allCases.map(\.name)
        segmentedControl.font = .sanFranciscoDisplay_Regular(14.0)
        segmentedControl.selectedIndex = 0;
        segmentedControl.delegate = self;

        // Setup table view
        tableView.refreshControl = refreshControl
        tableView.separatorStyle = .none
        tableView.register(ProjectInspectionOverviewCell.self)
        tableView.register(EmptyDataViewCell.self)
        tableView.register(
            .init(nibName: INSPECTION_CELL_IDENTIFIER, bundle: .main),
            forCellReuseIdentifier: INSPECTION_CELL_IDENTIFIER
        )
        if #available(iOS 15.0, *) {
            tableView.sectionHeaderTopPadding = 0.0
        }

        // search bar
        searchBar.delegate = self
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        LocationManager.shared().updateLocation { [weak self] location in
            guard let self = self, let coordinate = location?.coordinate else { return }
            self.currentLatitude = coordinate.latitude
            self.currentLatitude = coordinate.longitude
        }
        segmentedControlValueChanged(segmentedControl.selectedIndex)
    }

    func didProcessProjects() {
        reloadData(with: keywords)
    }
}

extension if_ProjectInspections: SISegmentedControlDelegate {
    open func segmentedControlValueChanged(_ selectedIndex: Int) {
        status = Status(rawValue: selectedIndex) ?? .inProgress;
        reloadData(with: keywords)
    }
}

extension if_ProjectInspections: UITextFieldDelegate {
    public func textFieldShouldClear(_ textField: UITextField) -> Bool {
        searchBar.text = ""
        keywords = ""
        searchBar.resignFirstResponder()
        reloadData(with: keywords)
        CommonUI.hideKeyboard()
        return true
    }

    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder()
        return true
    }

    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        let finalText = (textField.text as NSString?)?.replacingCharacters(in: range, with: string) ?? ""

        NSObject.cancelPreviousPerformRequests(
            withTarget: self, selector: #selector(reloadData(with:)), object: finalText
        )
        perform(#selector(reloadData(with:)), with: finalText, afterDelay: 0.2)
        return true
    }

    @objc private func reloadData(with keywords: String = "") {
        self.keywords = keywords.trimmingCharacters(in: .whitespacesAndNewlines)
        guard let project = db_Projects.getProject(id: projectID) else { return }
        navigationItem.title = project.sName
        self.project = project
        hasInspections = db_Projects.getActiveProjectionInsCount(iSProjectID: project.iSProjectID) > 0

        let projectInspections = status.getProjectInspections(for: project, keywords: keywords)
        if case .inProgress = status {
            var lsCompletedNotUploaded: [O_ProjectInspection] = []
            var lsStartedNotCompleted: [O_ProjectInspection] = []
            var lsNotStarted: [O_ProjectInspection] = []
            projectInspections.forEach {
                if $0.isInspectionCompletedButNotSubmitted {
                    lsCompletedNotUploaded.append($0)
                } else if $0.iInspectionID > 0 {
                    lsStartedNotCompleted.append($0)
                } else {
                    lsNotStarted.append($0)
                }
            }
            lsStartedNotCompleted.sort { p1, p2 in
                p2.dtInsStart.compare(p1.dtInsStart) == .orderedDescending
            }
            
            lsCompletedNotUploaded.sort { p1, p2 in
                p2.dtInsStart.compare(p1.dtInsStart) == .orderedDescending
            }
            lsProjectInspections = lsStartedNotCompleted + lsCompletedNotUploaded + lsNotStarted
        } else {
            let editedInspections = projectInspections
                .filter { $0.isInspectionUploadedButContinueEditing }
                .sorted(by: \.dtInsStart, ascending: false)
            let completedInspections = projectInspections
                .filter { !$0.isInspectionUploadedButContinueEditing }
                .sorted(by: \.dtInsStart, ascending: false)
            lsProjectInspections = editedInspections + completedInspections
        }
        tableView.reloadData()
    }
}

extension if_ProjectInspections: UITableViewDataSource {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard !hasNoActiveProjectInspections else { return 1 }
        return lsProjectInspections.count + 1
    }


    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if hasNoActiveProjectInspections {
            let cell = tableView.dequeueReusableCell(for: indexPath) as EmptyDataViewCell
            cell.configure(withModel: EmptyDataViewCell.ViewType.projectInspections(
                status: status, hasInspections: hasInspections
            ))
            return cell
        } else {
            if indexPath.row == 0 {
                let cell = tableView.dequeueReusableCell(for: indexPath) as ProjectInspectionOverviewCell
                cell.configure(withModel: project)
                return cell
            } else {
                let cell = tableView.dequeueReusableCell(withIdentifier: INSPECTION_CELL_IDENTIFIER, for: indexPath) as! InspectionCell
                configureInspectionCell(cell, withModel: lsProjectInspections[indexPath.row - 1])
                return cell
            }
        }
    }

    private func configureInspectionCell(_ cell: InspectionCell, withModel model: O_ProjectInspection) {
        cell.titleLab.setTextAndVisibility(model.projectInspectionTitle)
        cell.accessoryView = nil;
        cell.viewStatus.isHidden = false;
        if model.isInspectionCompletedButNotSubmitted || model.isInspectionUploadedButContinueEditing {
            cell.accessIndicator.isHidden = true
            cell.viewStatus.backgroundColor = .init(fromHexString: "#3E6BFF")
            let oButton = UIButton(type: .custom)
            oButton.frame = .init(origin: .zero, size: .init(width: 32.0, height: 32.0))
            oButton.contentEdgeInsets = .init(top: 4, left: 4, bottom: 4, right: 4)
            oButton.tag = model.iSProjectAssetInsTypeID
            oButton.setImage(UIImage(named: "icon_upload"), for: .normal)
            oButton.addTarget(self, tapped: #selector(uploadInspection(_:)))
            cell.accessoryView = oButton;
        } else if model.isUploaded {
            if !model.oCustom.insStatus.isEmpty {
                cell.accessIndicator.isHidden = true
                cell.accessoryView = UI_StatusButton(
                    frame: .init(origin: .zero, size: .init(width: 40, height: 40)),
                    sText: CommonUI.getStatusCode(model.oCustom.insStatus),
                    oColor: .init(fromHexString: model.oCustom.insStatusCode)
                )
            } else {
                cell.accessIndicator.isHidden = true
            }
            cell.viewStatus.backgroundColor = .init(fromHexString: "#00E676")
            cell.detailsLab.setTextAndVisibility(model.sInsTypeTitle)
            cell.inspectorLab.setTextAndVisibility(model.inspectInspector.map(\.sName).map({ "Inspector - \($0)" }))
        } else {
            cell.detailsLab.setTextAndVisibility(
                (model.dtInsStart.isEmpty && model.dtInsEnd.isEmpty) ? model.sInsTypeTitle :
                "\(model.sInsTitle) \(model.dtInsStart.ifEmpty(then: "?")) - \(model.dtInsEnd.ifEmpty(then: "?"))")
            cell.inspectorLab.setTextAndVisibility(model.assignedInspector.map(\.sName).map { "Inspector - \($0)" })
            cell.viewStatus.backgroundColor = .init(fromHexString: "#FF5151")
            cell.accessIndicator.isHidden = false
            if (model.iInspectionID > 0) {
                cell.accessIndicator.image = .init(named: "icon_pencil")
            } else {
                cell.accessIndicator.image = .init(named: "ic_forward")
            }
        }
    }
}

extension if_ProjectInspections: UITableViewDelegate {

    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        if let cell = cell as? EmptyDataViewCell {
            cell.learnMore = { [weak self] in
                guard let self = self,
                      let webView = self.storyboard?.instantiateViewController(withIdentifier: "WebView") as? if_WebView
                else { return }
                webView.sURL = Constants.kProjectLearnMoreURL
                self.navigationController?.pushViewController(webView, animated: true)
            }
        }
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        guard !hasNoActiveProjectInspections else { return 0.01 }
        return UITableView.automaticDimension
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        guard !hasNoActiveProjectInspections else { return tableView.height - tableView.safeAreaInsets.bottom }
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        guard !hasNoActiveProjectInspections else { return }
        
        tableView.deselectRow(at: indexPath, animated: true)
        guard let ins = lsProjectInspections[safe: indexPath.row - 1] else { return }
        let cell = tableView.cellForRow(at: indexPath)
        if ins.isUploaded && !ins.isInspectionUploadedButContinueEditing {
            var actionOptions: [String] = []
            if CommonPermission.bEnableEditInspection {
                actionOptions.append(kOptionEditInspection)
                if CommonHelper.bStatus() {
                    actionOptions.append(kOptionUpdateStatus)
                }
            }
            actionOptions.append(kOptionViewReport)
            showOptions(actionOptions,
                selected: { [weak self] option, i in
                    guard let self = self else { return }
                    switch option {
                    case kOptionEditInspection:
                        CommonInspection.downloadInspectionAndContinue(
                            withISInsID: Int32(ins.iSInspectionID),
                            bComplete: true,
                            bSynced: false,
                            from: self,
                            completion: { [weak self] iInsID in
                                guard let self = self else { return }
                                if iInsID > 0 {
                                    ins.iInspectionID = Int(iInsID)
                                    db_Projects.save(projectInspections: [ins])
                                } else {
                                    self.showAlert("Message", sMessage: "The inspection is not available.")
                                }
                            }
                        )
                    case kOptionUpdateStatus:
                        CommonUI.updateInspectionStatus(
                            ins.iSInspectionID,
                            from: self,
                            sourceView: cell,
                            completion: { [weak self] _, sStatus, sStatusCode in
                                guard let self = self else { return }
                                ins.oCustom.insStatus = sStatus ?? ""
                                ins.oCustom.insStatusCode = sStatusCode ?? ""
                                db_Projects.save(projectInspections: [ins])
                                self.tableView.reloadRows(at: [indexPath], with: .automatic)
                            }
                        )
                        break
                    case kOptionViewReport:
                        CommonHelper_SendReport.viewReport_Internal(
                            nil, oVC: self, iInsID: Int32(ins.iSInspectionID),
                            sTitle: ins.sInsTitle,
                            sType: "P"
                        )
                    default:
                        break
                    }
                },
                title: ins.projectInspectionTitle,
                sMessage: nil,
                sourceView: cell,
                cancelButton: "Cancel"
            )
        } else {
            guard let oInsType = db_SetupInspection.getInsType_SInsTypeID(Int32(ins.iInsTypeID)), oInsType.iInsTypeID > 0 else {
                showAlert("Message", sMessage: "The inspection type is not available.")
                return
            }
            if ins.iInspectionID > 0 { // if inspection exists in local, go straightly to edit inspection
                Navigator.pushInspection(Int32(ins.iInspectionID))
            } else {
                if !NetworkConnection.isNetworkReachable {
                    // directly start inspection if no internet connection
                    startInspection(ins, oInsType: oInsType)
                } else { // start inspection
                    if ins.iInspectionID > 0 {
                        Navigator.pushInspection(Int32(ins.iInspectionID))
                    } else if let assignedInspector = ins.assignedInspector, CommonUser.iCustomerID != assignedInspector.userID {
                        self.showAlert("Message", sMessage: String(format: NSLocalizedString("inspectionHasAssignedToOtherAccount", comment: ""), assignedInspector.sName), cancelButton: "Cancel", doneButton: "Start") { [weak self] _ in
                            self?.checkIfInspectionLocked(ins, oInsType: oInsType)
                        }
                    } else {
                        checkIfInspectionLocked(ins, oInsType: oInsType)
                    }
                }
            }
        }
    }

    private func checkIfInspectionLocked(_ projectIns : O_ProjectInspection, oInsType: O_InsType) {
        //  if no internet should start inspection, otherwise if have internet then check _iLock.
        guard NetworkConnection.isNetworkReachable else {
            startInspection(projectIns, oInsType: oInsType)
            notifyNetworkError()
            return
        }

        getProjectInspection(with: projectIns.iSProjectAssetInsTypeID) { [weak self] projectIns in
            guard let self = self, let projectIns = projectIns else {
                return
            }
            // Update inspection local database
            db_Projects.save(projectInspections: [projectIns])

            // Reload project inspection from local database
            guard let projectInspection = db_Projects.getProjectionInspection(withId: projectIns.iSProjectAssetInsTypeID) else {
                return
            }

            // Show the prompt message if inspection has been locked
            if projectInspection.isUploaded {
                self.updateProjectFromServer(with: projectInspection.iProjectID)
                self.showAlert("Message", sMessage: "The inspection is uploaded, please view the report under the `Completed` tab.")
            } else if let inspector = projectInspection.lockedInspector {
                var sMessage = ""
                if inspector.userID == Int32(CommonHelper.ifGetPref("iCustomerID") ?? "") {
                    sMessage = NSLocalizedString("inspectionHasBeenStartedByThisAccount", comment: "")
                } else {
                    let inspectorName = inspector.sName.ifNotEmpty(then: " " + inspector.sName)
                    sMessage = String(format: NSLocalizedString("inspectionHasBeenStartedBy", comment: ""), inspectorName)
                }

                self.showAlert("Message", sMessage: sMessage, cancelButton: "Cancel", doneButton: "Start") { [weak self] _ in
                    self?.startInspection(projectInspection, oInsType: oInsType)
                }
            } else {
                self.startInspection(projectInspection, oInsType: oInsType)
            }
        }
    }
}

extension if_ProjectInspections {

    @objc private func startSyncProjects() {
        loadAllProjectsFile()
    }

    @objc private func uploadInspection(_ sender: UIButton) {
        showAlert("Message", sMessage: "Are you sure to Complete and Upload this inspection?",
                cancelButton: "Cancel", doneButton: "Complete & Upload") { [weak self] _ in
            guard let self = self else { return }
            self.completeAndUploadInspection(with: sender.tag)
        }
    }

    private func completeAndUploadInspection(with iSProjectAssetInsTypeID: Int) {
        guard let projectInspection = db_Projects.getProjectionInspection(withId: iSProjectAssetInsTypeID) else { return }
        let iInsID = Int32(projectInspection.iInspectionID)
        if let oInspection = db_Inspection.getByInsID(iInsID) {
            oInspection.bComplete = true
            db_Inspection.update(oInspection)
        }

        homeDelegate?.upload_Action(iInsID) { [weak self] in
            guard let self else { return }
            self.updateProjectInspectionFromServer(with: projectInspection.iSProjectAssetInsTypeID)
            self.updateProjectFromServer(with: projectInspection.iProjectID)
        }
    }

    private func updateProjectInspectionFromServer(with id: Int) {
        getProjectInspection(with: id) { [weak self] projectInspection in
            guard let self = self, let projectInspection = projectInspection else { return }
            // Update inspection local database
            db_Projects.save(projectInspections: [projectInspection])
            self.reloadData(with: self.keywords)
        }
    }

    private func updateProjectFromServer(with projectID: Int) {
        getProject(with: projectID) { [weak self] project in
            guard let self = self, let project = project else { return }
            // Update project local database
            db_Projects.save(projects: [project])
            self.reloadData(with: self.keywords)
        }
    }

    private var hasNoActiveProjectInspections: Bool {
        lsProjectInspections.isEmpty && (searchBar.text ?? "").isEmpty
    }

    private func startInspection(_ ins: O_ProjectInspection, oInsType: O_InsType) {
        if let arrAssetLayout = db_Common.getAssetLayouts(Int32(ins.iAssetID), sPTC: oInsType.sPTC) as? [O_AssetLayout],
           arrAssetLayout.count > 0 {
            let iInsID = db_SetupInspection.add(arrAssetLayout, iSAssetID: Int32(ins.iAssetID), sAddress1: ins.sAssetAddress1, sAddress2: ins.sAssetAddress2, iSInsTypeID: Int32(oInsType.iSInsTypeID), iSScheduleID: 0, sLat: currentLatitude, sLong: currentLongitude, sCustom1_Schedule: "", dtSchedule: "")
            Navigator.pushInspection(iInsID)
            inspectionDidStarted(ins.iSProjectAssetInsTypeID, iInsID: Int(iInsID))
        } else {
            let iInsID = CommonSetupIns.startInspectionBranch(Int32(ins.iAssetID), sAddress1: ins.sAssetAddress1, sAddress2: ins.sAssetAddress2, iSScheduleID: 0, sPTC: oInsType.sPTC, sType: oInsType.sType, iSInsTypeID: Int32(oInsType.iSInsTypeID), sLat: currentLatitude, sLong: currentLongitude, sCustom1_Schedule: "", dtSchedule: "")
            if iInsID > 0 {
                Navigator.pushInspection(iInsID)
                inspectionDidStarted(ins.iSProjectAssetInsTypeID, iInsID: Int(iInsID))
            } else {
                Navigator.pushLayoutSetup(
                    oInsType.iSInsTypeID,
                    iSScheduleID: 0,
                    iSAssetID: ins.iAssetID,
                    sType: oInsType.sType,
                    sPTC: oInsType.sPTC,
                    sAddress1: ins.sAssetAddress1,
                    sAddress2: ins.sAssetAddress2,
                    sCustom1: nil,
                    dtSchedule: nil
                ) { [weak self] iInsID in
                    self?.inspectionDidStarted(ins.iSProjectAssetInsTypeID, iInsID: Int(iInsID))
                }
            }
        }
    }

    private func inspectionDidStarted(_ iSProjectAssetInsTypeID: Int, iInsID: Int) {
        db_Projects.updateProjectInspection(iSProjectAssetInsTypeID, iInsID: Int32(iInsID))
        lockProjectInspection(
            with: iSProjectAssetInsTypeID,
            iInspectorID: Int(CommonHelper.ifGetPref("iCustomerID") ?? "") ?? 0
        ) { projectInspection in
            guard let projectInspection = projectInspection else { return }
            db_Projects.save(projectInspections: [projectInspection])
        }
    }

    private func getProjectInspection(with iSProjectAssetInsTypeID: Int, completion: @escaping (O_ProjectInspection?) -> Void) {
        guard NetworkConnection.isNetworkReachable else {
            notifyNetworkError()
            return
        }

        showLoading()
        DispatchQueue.global(qos: .userInteractive).async {
            let params = [
                "iCustomerID": CommonHelper.ifGetPref("iCustomerID") ?? "",
                "sToken": CommonUser.currentToken ?? "",
                "iProjectAssetInsTypeID": "\(iSProjectAssetInsTypeID)"
            ]
            let result = IFConnection.postRequest(.getProjectInspection, oParams: params)
            DispatchQueue.main.async {
                self.hidesLoading()
                if let result = result, result["success"] as? Bool == true,
                   let data = result["oProjectInspection"] as? [AnyHashable: Any] {
                    completion(O_ProjectInspection.init(dictionary: data))
                } else {
                    completion(nil)
                }
            }
        }
    }

    private func getProject(with iSProjectID: Int, completion: @escaping (O_Project?) -> Void) {
        guard NetworkConnection.isNetworkReachable else {
            notifyNetworkError()
            return
        }

        if !refreshControl.isRefreshing {
            showLoading()
        }
        DispatchQueue.global(qos: .userInteractive).async {
            let params = [
                "iCustomerID": CommonHelper.ifGetPref("iCustomerID") ?? "",
                "sToken": CommonUser.currentToken ?? "",
                "iProjectID": "\(iSProjectID)"
            ]
            let result = IFConnection.postRequest(.getProject, oParams: params)
            DispatchQueue.main.async {
                self.hidesLoading()
                if let result = result, result["success"] as? Bool == true,
                   let data = result["oProject"] as? [AnyHashable: Any] {
                    completion(O_Project.init(dictionary: data))
                } else {
                    completion(nil)
                }
            }
        }
    }

    private func lockProjectInspection(
        with iSProjectAssetInsTypeID: Int,
        iInspectorID: Int = 0,
        completion: @escaping (O_ProjectInspection?) -> Void
    ) {
        guard NetworkConnection.isNetworkReachable else {
            notifyNetworkError()
            return
        }

        showLoading()
        DispatchQueue.global(qos: .userInteractive).async {
            let params = [
                "iCustomerID": CommonHelper.ifGetPref("iCustomerID") ?? "",
                "sToken": CommonUser.currentToken ?? "",
                "iProjectAssetInsTypeID": "\(iSProjectAssetInsTypeID)",
                "iInspectorID": "\(iInspectorID)"
            ]
            let result = IFConnection.postRequest(.lockProjectInspection, oParams: params)
            DispatchQueue.main.async {
                self.hidesLoading()
                if let result = result, result["success"] as? Bool == true,
                   let data = result["oProjectInspection"] as? [AnyHashable: Any] {
                    completion(O_ProjectInspection.init(dictionary: data))
                } else {
                    completion(nil)
                }
            }
        }
    }

    private func notifyNetworkError() {
        showAlert("Error", sMessage: "Please check your network connection and try again.")
    }
}

extension if_ProjectInspections.Status {
    var name: String {
        switch self {
        case .inProgress:
            return "In Progress"
        case .completed:
            return "Completed"
        }
    }

    var opposite: if_ProjectInspections.Status {
        switch self {
        case .inProgress:
            return .completed
        case .completed:
            return .inProgress
        }
    }

    func getProjectInspections(for project: O_Project, keywords: String = "") -> [O_ProjectInspection] {
        switch self {
        case .inProgress:
            return db_Projects.getInProgressProjectInspections(projectId: project.iSProjectID, keyword: keywords)
        case .completed:
            return db_Projects.getCompletedProjectInspections(projectId: project.iSProjectID, keyword: keywords)
        }
    }
}

private let INSPECTION_CELL_IDENTIFIER = "InspectionCell"
private let kOptionEditInspection = "Edit Inspection"
private let kOptionUpdateStatus = "Update Status"
private let kOptionViewReport = "View Report"

extension O_ProjectInspection {
    fileprivate var projectInspectionTitle: String? {
        var sTitle = sAssetAddress.ifEmpty(then: sTitle);
        if NSString.isNullOrEmpty(sTitle) {
            sTitle = oCustom.title;
        }
        return sTitle;
    }
}
