//
//  IFSync.m
//  InspectionFolio
//
//  Created by <PERSON> on 16/01/14.
//  Copyright (c) 2014 Cloudya. All rights reserved.
//

#import "IFSync_New.h"
#import "db_Sync.h"
#import "db_Common.h"
#import "db_Asset.h"
#import "db_Delete.h"
#import "NSString+Extensions.h"
@import AppFeatures;

@implementation IFSync_New
@synthesize sMessage;
-(NSString *)GetMessage{
    return sMessage;
}
-(BOOL)LoginAPI:(NSString *)sEmail sPassword:(NSString *)sPassword {
    NSDictionary *oReturn = [IFConnection
            PostRequest: EndpointLoginAPI
                oParams:[@{
                        @"UserName": sEmail,
                        @"Password": sPassword,
                        @"sDeviceInfo": @"iPhone",
                        @"sType": @"iOS"
                } mutableCopy]
    ];

    BOOL result = [self processLoginResult:oReturn];
    if (result) {
        [CommonHelper IFSavePref:@"sPassword" sValue:sPassword];
    }
    return result;
}

-(NSDictionary *)SyncAction{
    NSString *sSyncDate = [CommonHelper IFGetPref:PrefsKeys.sSyncDate];
    if (sSyncDate == nil || [sSyncDate isEqualToString:@""]){
        sSyncDate = @"1980-1-1 0:0";
    }
    @try{
        if ([sSyncDate hasPrefix:@"1979-1-1"] || [sSyncDate hasPrefix:@"1979-01-01"]){
            [db_Delete ResetDatabase];
        }
    }@catch(NSException *excc){
        
    }
    NSString *savedCustomerID = [CommonHelper IFGetPref:@"iCustomerID"];
    NSString *customerID = [savedCustomerID length] > 0 ? savedCustomerID : @"";
    [CommonAnalytics trackEvent:@"iOS Sync Start" meta:@{
        @"CustomerID": customerID, @"SyncDate": sSyncDate
    }];
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"iCustomerID"] = customerID;
    params[@"sToken"] = [CommonUser currentToken];
    params[@"sDateTime"] = sSyncDate;
    params[@"sDeviceInfo"] = [CommonHelper GetDeviceInfo];
    
    NSDictionary *oReturn = [IFConnection PostRequest:EndpointServerAppAction oParams: params];
    
    [db_Log InsertLog:@"Sync" sMessage:[NSString stringWithFormat:@"Init Request - %@, %@", customerID, sSyncDate]];
    return oReturn;
}

-(void)SaveConfigToPreference:(NSString *)sKey sPrefTitle:(NSString *)sPrefTitle sJson:(NSString *)sJson {
    @try {
        NSString *sValue = [CommonJson GetJsonKeyValue:sKey sJson:sJson];
        [CommonHelper IFSavePref:sPrefTitle sValue:sValue];
    } @catch (NSException *exception) {
    }
}

-(bool)DownloadProcessConfig:(NSDictionary *)oDic {
    @try {
        [CommonHelper IFSavePref:@"iFileCustomerID" sValue:[oDic valueForKey:@"CustomerID"]];
        [CommonHelper IFSavePref:@"iCompanyID" sValue:[oDic valueForKey:@"CompanyID"]];
        [CommonHelper IFSavePref:@"bNotification" sValue:[oDic valueForKey:@"sNotification"]];
        [CommonHelper IFSavePref:@"iCountryID" sValue:[oDic valueForKey:@"iCountryID"]];
        [CommonHelper IFSavePref:@"sIndustry" sValue:([oDic objectForKey:@"sIndustry"] ? [oDic valueForKey:@"sIndustry"] : @"")];
        [CommonHelper IFSavePref:@"iIndustryID" sValue:[oDic valueForKey:@"iIndustryID"]];
        [CommonHelper IFSavePref:@"sFileEmail" sValue:[[oDic valueForKey:@"Email"] valueForKey:@"text"]];
        [CommonHelper IFSavePref:@"sFirstName" sValue:[[oDic valueForKey:@"FirstName"] valueForKey:@"text"]];
        [CommonHelper IFSavePref:@"sLastName" sValue:[[oDic valueForKey:@"LastName"] valueForKey:@"text"]];
        [CommonHelper IFSavePref:@"sCompanyName" sValue:[[oDic valueForKey:@"CompanyName"] valueForKey:@"text"]];
        [CommonHelper IFSavePref:@"iQPVerID" sValue:[[oDic valueForKey:@"iQPVerID"] valueForKey:@"text"]];
        [CommonHelper IFSavePref:PrefsKeys.sSyncDate sValue:[[oDic valueForKey:PrefsKeys.sSyncDate] valueForKey:@"text"]];
        [CommonHelper IFSavePref:@"sPlanCode" sValue:[[oDic valueForKey:@"sPlanCode"] valueForKey:@"text"]];
        [CommonHelper IFSavePref:@"sPlanName" sValue:[[oDic valueForKey:@"sPlanName"] valueForKey:@"text"]];
        [CommonHelper IFSavePref:@"sPlanDetails" sValue:[[oDic valueForKey:@"sPlanDetails"] valueForKey:@"text"]];
        NSString *sRoleContent = [[oDic valueForKey:PrefsKeys.sCustomRole] valueForKey:@"text"];
        [CommonHelper IFSavePref:PrefsKeys.sCustomRole sValue:sRoleContent];
        [CommonHelper IFSavePref:PrefsKeys.kEnableProject sValue:[oDic valueForKey:@"bProject"]];
        [CommonHelper IFSavePref:PrefsKeys.kUseAssetLayoutV2 sValue:[oDic valueForKey:@"bLayout_V2"]];
        NSString *sPrevFolder = [CommonHelper IFGetPref:Constants.kFolder];
        NSString *sPrevFolderPermission = [CommonHelper IFGetPref:Constants.kFolderPermission];

        [CommonHelper IFSavePref:PrefsKeys.kFolder sValue:[self sGetCompanyConfig:oDic sLabel:@"_bFolder"]];
        // Check if the blueprint is enabled
        [CommonHelper IFSavePref:PrefsKeys.kFloorPlan sValue:[self sGetCompanyConfig:oDic sLabel:@"_bFloorPlan"]];
        // If see the attribute "_ProLayout_AreaOnly" in the company config, then save it to the preference
        // then ignore the sChildID when creating the inspection and load the child layouts from the table ai_Layout
        [CommonHelper IFSavePref:PrefsKeys.kProLayoutAreaOnly
                          sValue:[self sGetCompanyConfig:oDic sLabel:PrefsKeys.kProLayoutAreaOnly]];
        
        // User has enabled the `Product` feature
        [CommonHelper IFSavePref:PrefsKeys.kEnableProduct
                          sValue:[self sGetCompanyConfig:oDic sLabel:Constants.kJsonKeyProduct]];
        
        // User has enabled the `Tasks` feature
        [CommonHelper IFSavePref:PrefsKeys.kEnableTasks
                          sValue:[self sGetCompanyConfig:oDic sLabel:@"NOT"]];

        // Permissions
        [self SaveConfigToPreference:@"bW_QuickEdit_Hide" sPrefTitle:Constants.kEnableQuickEditHide sJson:sRoleContent];
        [self SaveConfigToPreference:@"bDis_EditAsset" sPrefTitle:Constants.kDisableEditAsset sJson:sRoleContent];
        [self SaveConfigToPreference:@"bDis_NewIns" sPrefTitle:Constants.kDisableNewInspection sJson:sRoleContent];
        [self SaveConfigToPreference:@"_iInsTypeID_Hide" sPrefTitle:Constants.kHideInsTypeID sJson:sRoleContent];
        [self SaveConfigToPreference:@"b_FolderPermission" sPrefTitle:Constants.kFolderPermission sJson:sRoleContent];
        
        @try {
            NSString *sCurrentFolder = [CommonHelper IFGetPref:Constants.kFolder];
            NSString *sCurrentFolderPermission = [CommonHelper IFGetPref:Constants.kFolderPermission];
            if ((![sPrevFolder isEqualToString:sCurrentFolder]) || (![sPrevFolderPermission isEqualToString:sCurrentFolderPermission])) {
                [CommonHelper IFSavePref:PrefsKeys.kSelectedAssetViewID sValue:@""];
            }
        } @catch (NSException *eeeccc) {

        }


        @try {
            if ([[[oDic valueForKey:@"bMultiFamily"] valueForKey:@"text"] isEqualToString:@"1"]) {
                [CommonHelper IFSavePref:@"bMultiFamily" sValue:@"1"];
            } else {
                [CommonHelper IFSavePref:@"bMultiFamily" sValue:@"0"];
            }

        } @catch (NSException *eee) {

        }

        @try {
            if ([[[oDic valueForKey:@"bRoom"] valueForKey:@"text"] isEqualToString:@"1"]) {
                [CommonHelper IFSavePref:@"bRoom" sValue:@"1"];
            } else {
                [CommonHelper IFSavePref:@"bRoom" sValue:@"0"];
            }
        } @catch (NSException *eee) {

        }

        [CommonHelper IFSavePref:@"sRole" sValue:[[oDic valueForKey:@"sRole"] valueForKey:@"text"]];
        // Asset View
        NSString *sAssetView = [[oDic valueForKey:@"sAssetView"] valueForKey:@"text"];
        if (sAssetView != nil && [sAssetView length] > 0) {
            [db_AssetView saveWithJsonArrayAssetView:[sAssetView jsonValue]];
        }

        // [CommonHelper IFSavePref:@"bLog" sValue:([oDic valueForKey:@"bLog"] == nil ? false : [oDic valueForKey:@"bLog"])];
        [CommonHelper IFSavePref:@"bLog" sValue:[oDic valueForKey:@"bLog"]];
        [CommonAnalytics trackEvent:@"iOS Sync Start Processing" meta:nil];

        NSString *sCommand = [[oDic valueForKey:@"sCommand"] valueForKey:@"text"] == nil ? @"" : [[oDic valueForKey:@"sCommand"] valueForKey:@"text"];

        NSString *sNewCategory = [[oDic valueForKey:@"Category"] valueForKey:@"text"];
        @try {
            if (sNewCategory != nil && [sNewCategory length] > 0) {
                NSMutableArray *oNewArray = [NSJSONSerialization JSONObjectWithData:[sNewCategory dataUsingEncoding:NSUTF8StringEncoding] options:NSJSONReadingMutableContainers error:nil];

                for (NSDictionary *oDic in oNewArray) {
                    [db_Category ProcessNoticeCategory:[[O_NoticeCategory alloc] initWithDictionary:oDic]];
                }
            }
        } @catch (NSException *ex) {
            DLog(@"");
        }
        @try {
            [CommonHelper IFSavePref:@"bCompanyReview" sValue:[oDic valueForKey:@"CompanyReview"]];
        } @catch (NSException *ex) {

        }
        @try {
            [CommonHelper IFSavePref:@"bCompanyGroup" sValue:[oDic valueForKey:@"CompanyGroup"]];
        } @catch (NSException *ex) {

        }
        @try {
            O_Config *oConfig = [db_Common GetConfigByName:@"PerDetail"];
            if (oConfig == nil || oConfig.iConfigID == 0) {
                oConfig = [[O_Config alloc] init];
                oConfig.sConfigName = @"PerDetail";
            }

            oConfig.sConfigValue = [[oDic valueForKey:@"PerDetail"] valueForKey:@"text"];
            [db_Common UpdateConfig:oConfig];
        } @catch (NSException *ex) {

        }
        @try {
            [CommonHelper IFSavePref:@"jsonStatus" sValue:[[oDic valueForKey:@"jsonStatus"] valueForKey:@"text"]];
            // CustomInfo1st to override AssetAttributes
            NSString *sCustomInfo1st = [[oDic valueForKey:@"CustomInfo1st"] valueForKey:@"text"];
            [CommonHelper IFSavePref:PrefsKeys.kAssetAttributes sValue:sCustomInfo1st];
            
            // CustomInfo2nd
            NSString *sCustomInfo2nd = [[oDic valueForKey:@"CustomInfo2nd"] valueForKey:@"text"];
            [CommonHelper IFSavePref:PrefsKeys.kAssetAttributes2 sValue:sCustomInfo2nd];
            
        } @catch (NSException *ex) {

        }

        @try {
            [CommonHelper IFSavePref:PrefsKeys.kContactType
                              sValue:[[oDic valueForKey:PrefsKeys.kContactType] valueForKey:@"text"]];
            
            // Multi-login users
            NSString *sLogins = [[oDic valueForKey:Constants.kJsonKeyLogins] valueForKey:@"text"];
            if (sLogins != nil && [sLogins length] > 0) {
                [AppEnvironmentBridge.accountService processSubAccountsJson:sLogins];
            } else {
                [AppEnvironmentBridge.accountService removeSubAccounts];
            }
        } @catch (NSException *ex) {

        }

        return ([sCommand rangeOfString:@"SUBMIT"]).location != NSNotFound;
    }
    @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        return false;
    }
}
-(NSString *) sGetCompanyConfig:(NSDictionary  *) oDic sLabel:(NSString *)sLabel{
    @try{
        
        return [CommonJson GetJsonKeyValue:sLabel sJson: [[oDic valueForKey:@"sCompanyCustom1"] valueForKey:@"text"]];
    }@catch(NSException *exx){
        
    }
    return @"";
}
-(int)ProcessLayouts:(NSDictionary *)oDic{
    @try {
        
        NSArray *arLayout = [oDic valueForKey:@"LT"];
        //NSMutableArray *oTempContainer = [[NSMutableArray alloc] init];
        int iCount = 0;
        for (int i=0; i< arLayout.count; i++){
            iCount ++;
            @autoreleasepool {
                NSDictionary *oLayout =  [arLayout objectAtIndex:i];
               // NSString *sPTC = [oLayout valueForKey:@"P"];
               // //if ([oTempContainer indexOfObject:sPTC] == NSNotFound){
                //    [oTempContainer addObject:sPTC];
               //     [db_Sync DeleteLayouts:sPTC];
               // }
                [db_Sync InsertLayout:[[oLayout valueForKey:@"U"] intValue]
                          iSPLayoutID:[[oLayout valueForKey:@"O"] intValue]
                                 sPTC:[oLayout valueForKey:@"P"]
                               sQType:[oLayout valueForKey:@"Q"]
                                sName:[[oLayout valueForKey:@"T"] valueForKey:@"text"]
                           sFV1Config:[[oLayout valueForKey:@"A"] valueForKey:@"text"]
                           sFV2Config:[[oLayout valueForKey:@"B"] valueForKey:@"text"]
                           sFV3Config:[[oLayout valueForKey:@"C"] valueForKey:@"text"]
                           sFV4Config:[[oLayout valueForKey:@"D"] valueForKey:@"text"]
                           sFV5Config:[[oLayout valueForKey:@"E"] valueForKey:@"text"]
                           sFV6Config:[[oLayout valueForKey:@"F"] valueForKey:@"text"]
                           sSV1Config:[[oLayout valueForKey:@"G"] valueForKey:@"text"]
                           sSV2Config:[[oLayout valueForKey:@"H"] valueForKey:@"text"]
                           sSV3Config:[[oLayout valueForKey:@"I"] valueForKey:@"text"]
                           sSV4Config:[[oLayout valueForKey:@"J"] valueForKey:@"text"]
                           sSV5Config:[[oLayout valueForKey:@"K"] valueForKey:@"text"]
                           sSV6Config:[[oLayout valueForKey:@"L"] valueForKey:@"text"]
                             sFConfig:[[oLayout valueForKey:@"M"] valueForKey:@"text"]
                             sSConfig:[[oLayout valueForKey:@"N"] valueForKey:@"text"]
                             bAutoAdd:[[oLayout valueForKey:@"R"] boolValue]
                                iMark:[[oLayout valueForKey:@"V"] intValue]
                               iOrder:[[oLayout valueForKey:@"S"] intValue]
                ];
            }
        }
        
        [CommonAnalytics trackEvent:@"iOS Sync Process Layout" meta:@{
            @"Total": [NSString stringWithFormat:@"%ld", (unsigned long)arLayout.count]
        }];

        return iCount;
    }
    @catch (NSException *exception) {
        [db_Log InsertLog:@"Sync" sMessage:[NSString stringWithFormat:@"Exception ProcessLayouts - %@", [exception description]]];
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        return 0;
    }
    
}
-(int)ProcessInsTypes:(NSDictionary *)oDic{
    @try {
        NSMutableArray *arLayout = [[NSMutableArray alloc] init];
        id oLayoutTemp = [oDic valueForKey:@"INT"];
        if ([oLayoutTemp isKindOfClass:[NSArray class]]){
            arLayout = oLayoutTemp;
        }
        else if (oLayoutTemp == nil){
            return 0;
        }
        else{
            
            [arLayout addObject:oLayoutTemp];
        }
        int iCount = 0;
        for (int i=0; i< arLayout.count; i++){
            iCount++;
            NSDictionary *oLayout =  [arLayout objectAtIndex:i];
            int iInsTypeID = [[oLayout valueForKey:@"I"] intValue];
            O_InsType *oInsType = [db_Sync GetInsType:iInsTypeID];
            bool bDeleted = [[oLayout valueForKey:@"B"] boolValue];
            if (oInsType != nil && oInsType.iInsTypeID > 0){
                if (bDeleted){
                    [db_Sync DeleteInsType:iInsTypeID];
                }
                else{
                    [db_Sync UpdateInsType_WithCustom:[oLayout valueForKey:@"P"] sInsTitle:[[oLayout valueForKey:@"E"] valueForKey:@"text" ] sType:[oLayout valueForKey:@"T"] iSInsTypeID:iInsTypeID sCustom1:[[oLayout valueForKey:@"CUS1"] valueForKey:@"text" ]];
                }
            }
            else{
                if (!bDeleted){
                     [db_Sync InsertInsType_WithCustom1:[oLayout valueForKey:@"P"] sInsTitle:[[oLayout valueForKey:@"E"] valueForKey:@"text" ] sType:[oLayout valueForKey:@"T"] iSInsTypeID:iInsTypeID sCustom1:[[oLayout valueForKey:@"CUS1"] valueForKey:@"text" ]];
                }
            }
        }
                                                                              
        [CommonAnalytics trackEvent:@"iOS Sync Process InsTypes" meta:@{
            @"Total": [NSString stringWithFormat:@"%ld", (unsigned long)arLayout.count]
        }];
        
        return iCount;
    }
    @catch (NSException *exception) {
        [db_Log InsertLog:@"Sync" sMessage:[NSString stringWithFormat:@"Exception ProcessInsTypes - %@", exception.description]];
                    [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        return 0;
    }
    
}
-(int)ProcessCheckList:(NSDictionary *)oDic{
    @try {
        NSMutableArray *arLayout = [[NSMutableArray alloc] init];
        id oLayoutTemp = [oDic valueForKey:@"CLST"];
        if ([oLayoutTemp isKindOfClass:[NSArray class]]){
            arLayout = oLayoutTemp;
        }
        else if (oLayoutTemp == nil){
            return 0;
        }
        else{
            
                [arLayout addObject:oLayoutTemp];
        }
        int iCount = 0;
        for (int i=0; i< arLayout.count; i++){
            iCount++;
            
            NSDictionary *oLayout =  [arLayout objectAtIndex:i];
            [db_Sync DeleteLayouts:[[oLayout valueForKey:@"P"] valueForKey:@"text" ]];
            int iSCheckListID = [[oLayout valueForKey:@"C"] intValue];
            O_CheckList *oCheckList = [db_Sync GetCheckList:iSCheckListID];
            bool bDeleted = [[oLayout valueForKey:@"B"] boolValue];
            if (oCheckList != nil && oCheckList.iSCheckListID > 0){
                if (bDeleted){
                    [db_Sync DeleteCheckList:iSCheckListID];
                }
                else{
                    [db_Common UpdateCheckList:[[oLayout valueForKey:@"C"] intValue] sTitle:[[oLayout valueForKey:@"L"] valueForKey:@"text"] sPTC:[[oLayout valueForKey:@"P"] valueForKey:@"text" ] iLayoutVerID:[[oLayout valueForKey:@"V"] intValue]];
                }
            }
            else{
                if (!bDeleted){
                    [db_Common InsertCheckList:[[oLayout valueForKey:@"C"] intValue] sTitle:[[oLayout valueForKey:@"L"] valueForKey:@"text"] sPTC:[[oLayout valueForKey:@"P"] valueForKey:@"text" ] iLayoutVerID:[[oLayout valueForKey:@"V"] intValue]];
                }
            }
        }
        
        [CommonAnalytics trackEvent:@"iOS Sync Process Checklist" meta:@{
            @"Total": [NSString stringWithFormat:@"%ld", (unsigned long)arLayout.count]
        }];
                                                                            
        return iCount;
    }
    @catch (NSException *exception) {
        [db_Log InsertLog:@"Sync" sMessage:[NSString stringWithFormat:@"Exception ProcessCheckList - %@", exception.description]];
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        return 0;
    }
}
-(int)ProcessProperties:(NSDictionary *)oDic oProgress:(MBProgressHUD *)oProgress{
    @autoreleasepool {
        @try {
          
            NSMutableArray *arWords = [[NSMutableArray alloc] init];
            id oWordTemp = [oDic valueForKey:@"OP"];
            if ([oWordTemp isKindOfClass:[NSArray class]]){
                arWords = oWordTemp;
            }
            else if (oWordTemp == nil){
                return 0;
            }
            else{
                [arWords addObject:oWordTemp];
            }
            int iCount = 0;
            for (int i=0; i< arWords.count; i++){
                iCount++;
                if (iCount > 300){
                    if (iCount % 100 == 0){
                        [oProgress setDetailsLabelText:[NSString stringWithFormat:@"Processed %d Assets. Please Wait", iCount]];
                    }
                }
                NSDictionary *oLayout =  [arWords objectAtIndex:i];
                O_Asset *oAsset = [db_Asset GetPropertyBySAssetID:[[oLayout valueForKey:@"P"] intValue]];
                if ([[oLayout valueForKey:@"N"] boolValue]){
                    [db_Delete DeleteProperty:[[oLayout valueForKey:@"P"] intValue]];
                }
                else{
                    if (oAsset == nil || oAsset.iAssetID == 0){
                        oAsset = [[O_Asset  alloc] init];
                        oAsset.iAssetID = 0;
                    }
                    else{
                        [db_Delete DeleteProperty_3Table:oAsset.iSAssetID ];
                    }
                    oAsset.iSAssetID = [[oLayout valueForKey:@"P"] intValue];
                    @try{
                        oAsset.iSPAssetID = [[oLayout valueForKey:@"PP"] intValue];
                    }@catch(NSException *ex){
                        oAsset.iSPAssetID = 0;
                    }
                    
                    oAsset.sAddress1 = [[oLayout valueForKey:@"A"] valueForKey:@"text"];
                    oAsset.sAddress2 = [[oLayout valueForKey:@"B"] valueForKey:@"text"];
                    oAsset.iCustomerID = [[oLayout valueForKey:@"M"] intValue];
                    oAsset.sKey = [[oLayout valueForKey:@"K"] valueForKey:@"text"];
                    oAsset.sAlarm = [[oLayout valueForKey:@"S"] valueForKey:@"text"];
                    oAsset.dtInsDue = [[oLayout valueForKey:@"D"] valueForKey:@"text"];
                    oAsset.sFilter = [CommonHelper FilterString:oAsset.sAddress1 sAddress2:oAsset.sAddress2];
                    oAsset.iPLVerID = [[oLayout valueForKey:@"V"] intValue];
                    oAsset.iGroupID = [[oLayout valueForKey:@"GD"] intValue];
                    @try{
                        oAsset.sCustom1 = [[oLayout valueForKey:@"CUS1"] valueForKey:@"text"];
                    }@catch(NSException *Eee){
                        
                    }
                    @try{
                        oAsset.bPush = [[oLayout valueForKey:@"BA"] intValue] == 1 ? true : false;
                    }@catch(NSException *Eee){
                        
                    }
                    @try{
                        
                        oAsset.sCustom3 =  [[oLayout valueForKey:@"R"] valueForKey:@"text"];
                    }@catch(NSException *Eee){
                        
                    }
                    [db_Asset UpdateProperty:oAsset];
                    @try{
                        
                        //Attention. CUS2 is used for the app part only. will depends on the app requirement. Can not based on the sCustom2 return info
                        //oAsset.sCustom2 = [[oLayout valueForKey:@"CUS2"] valueForKey:@"text"];
                        int iSFileID = [[oLayout valueForKey:@"PTOID"] intValue];
                        if (iSFileID > 0){
                            O_File *oFile = [db_Media GetFileByServerID:iSFileID];
                            if (oFile == nil || oFile.iFileID == 0){
                                oFile = [[O_File alloc] init];
                                oFile.iSObjectID = oAsset.iSAssetID;
                                oFile.iSFileID = iSFileID;
                                oFile.iFileID = 0;
                                oFile.sFile = @"";
                                oFile.sLat = @"";
                                oFile.sLong = @"";
                                oFile.sComments = @"";
                                oFile.bUploaded = 1;
                                oFile.bDeleted = 0;
                                oFile.dtDateTime = [CommonHelper GetDateString:[NSDate new]];
                                oFile.iSize = 0;
                                oFile.sCustom1 = @"";
                                oFile.sCustom2 = @"";
                                [db_Media UpdateFile:oFile];
                                //oAsset.sCustom2 = [CommonJson AddJsonKeyValueString:@"FILE" sValue:[NSString stringWithFormat:@"%d", iFileID] sJson:oAsset.sCustom2];
                                
                            }
                            [db_Asset MarkFileAssetPhoto:oFile.iFileID iSAssetID:oAsset.iSAssetID];
                            
                        }
                    }@catch(NSException *EEee){
                        
                    }
                    
                    
                }
                
            }
           
            [CommonAnalytics trackEvent:@"iOS Sync Process Properties" meta:@{
                @"Total": [NSString stringWithFormat:@"%ld", (unsigned long)arWords.count]
            }];
                                                                                
            return iCount;
        }
        @catch (NSException *exception) {
            [db_Log InsertLog:@"Sync" sMessage:[NSString stringWithFormat:@"Exception ProcessProperties - %@", exception.description]];
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return 0;
        }
    }
}
-(int)ProcessAssetAttribute:(NSDictionary *)oDic{
    @autoreleasepool {
        @try {
            NSMutableArray *arWords = [[NSMutableArray alloc] init];
            id oWordTemp = [oDic valueForKey:@"AAT"];
            if ([oWordTemp isKindOfClass:[NSArray class]]){
                arWords = oWordTemp;
            }
            else if (oWordTemp == nil){
                return 0;
            }
            else{
                [arWords addObject:oWordTemp];
            }
            NSMutableArray *arrAssetAttributes = [[NSMutableArray alloc] init];;
            for (int i=0; i< arWords.count; i++){
                NSDictionary *oCon = arWords[i];
                
                int iAssetAttributeID = [[oCon valueForKey:@"AAID"] intValue];
                NSString *sLabel = [[oCon valueForKey:@"sLabel"] valueForKey:@"text"];
                NSString *sType = [[oCon valueForKey:@"sType"] valueForKey:@"text"];
                NSDictionary *oDic = @{
                        @"iAssetAttributeID": [@(iAssetAttributeID) stringValue],
                        @"sLabel": sLabel,
                        @"sType": sType
                };
                [arrAssetAttributes addObject: oDic];
            }
            NSData *data = [NSJSONSerialization dataWithJSONObject:arrAssetAttributes options:NSJSONWritingPrettyPrinted error:nil];
            NSString *json = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
                            
            //NSData *jsonData = [NSJSONSerialization dataWithJSONObject:arWords options:NSJSONWritingPrettyPrinted error:nil];
            //NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
            [CommonHelper IFSavePref:PrefsKeys.kAssetAttributes sValue:json];
            
            return (int)[arWords count] ;
        }
        @catch (NSException *exception) {
            [db_Log InsertLog:@"Sync" sMessage:[NSString stringWithFormat:@"Exception ProcessAssetAttributes - %@", exception.description]];
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return 0;
        }
    }
}
-(int)ProcessPropertyLayout:(NSDictionary *)oDic  oProgress:(MBProgressHUD *)oProgress{
    @autoreleasepool {
        @try {
            NSMutableArray *arWords = [[NSMutableArray alloc] init];
            id oWordTemp = [oDic valueForKey:@"AL"];
            if ([oWordTemp isKindOfClass:[NSArray class]]){
                arWords = oWordTemp;
            }
            else if (oWordTemp == nil){
                return 0;
            }
            else{
                [arWords addObject:oWordTemp];
            }
            int iCount = 1;
            for (int i=0; i< arWords.count; i++){
                iCount++;
                if (iCount > 600){
                    if (iCount % 100 == 0){
                        [oProgress setDetailsLabelText:[NSString stringWithFormat:@"Processed %d Asset Layouts. Please Wait", iCount]];
                    }
                }
                NSDictionary *oCon =  [arWords objectAtIndex:i];
                O_AssetLayout *oAssetLayout = [[O_AssetLayout alloc] init];
                oAssetLayout.iSAssetID = [[oCon valueForKey:@"I"] intValue];
                oAssetLayout.iSLayoutID = [[oCon valueForKey:@"L"] intValue];
                oAssetLayout.sName = [[oCon valueForKey:@"N"] valueForKey:@"text"];
                oAssetLayout.sMoreItems = [[oCon valueForKey:@"M"] valueForKey:@"text"];
                oAssetLayout.sChildID = [[oCon valueForKey:@"A"]  valueForKey:@"text"];
                oAssetLayout.iSort = [[oCon valueForKey:@"S"] intValue];
                //oAssetLayout.sPTC = [oCon valueForKey:@"P"];
                oAssetLayout.iSAssetLayoutID = [[oCon valueForKey:@"P"] intValue];
                [db_Asset InsertAssetLayout:oAssetLayout];

            
            }
            
            [CommonAnalytics trackEvent:@"iOS Sync Process PropertyLayouts" meta:@{
                @"Total": [NSString stringWithFormat:@"%ld", (unsigned long)arWords.count]
            }];
                                                                                
            return iCount;
        }
        @catch (NSException *exception) {
            [db_Log InsertLog:@"Sync" sMessage:[NSString stringWithFormat:@"Exception ProcessPropertyLayout - %@", exception.description]];
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return 0;
        }
    }
}
-(int)ProcessInsAlerts:(NSDictionary *)oDic{
    @autoreleasepool {
        @try {
            //[db_Delete DeleteSchedules];
            NSMutableArray *arWords = [[NSMutableArray alloc] init];
            id oWordTemp = [oDic valueForKey:@"IA"];
            if ([oWordTemp isKindOfClass:[NSArray class]]){
                arWords = oWordTemp;
            }
            else if (oWordTemp == nil){
                return 0;
            }
            else{
                [arWords addObject:oWordTemp];
            }
            int iCount = 1;
            for (int i=0; i< arWords.count; i++){
                iCount ++;
                 NSDictionary *oLayout =  [arWords objectAtIndex:i];
                [db_Asset InsertInsAlert:[[oLayout valueForKey:@"M"] intValue] iLayoutID:[[oLayout valueForKey:@"L"] intValue] sPTC:[[oLayout valueForKey:@"P"] valueForKey:@"text"] sValue1:[[oLayout valueForKey:@"A"] valueForKey:@"text"] sValue2:[[oLayout valueForKey:@"B"] valueForKey:@"text"] sValue3:[[oLayout valueForKey:@"C"] valueForKey:@"text"] sValue4:[[oLayout valueForKey:@"D"] valueForKey:@"text"] sValue5:[[oLayout valueForKey:@"E"] valueForKey:@"text"] sValue6:[[oLayout valueForKey:@"F"] valueForKey:@"text"]];
               
            }
            
            [CommonAnalytics trackEvent:@"iOS Sync Process InsAlerts" meta:@{
                @"Total": [NSString stringWithFormat:@"%ld", (unsigned long)arWords.count]
            }];
                                                                                                 
            return iCount;
        }
        @catch (NSException *exception) {
            [db_Log InsertLog:@"Sync" sMessage:[NSString stringWithFormat:@"Exception ProcessInsAlerts - %@", exception.description]];
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return 0;
        }
    }
}
-(int)ProcessContact:(NSDictionary *)oDic{
    @autoreleasepool {
        @try {
            NSMutableArray *arWords = [[NSMutableArray alloc] init];
            id oWordTemp = [oDic valueForKey:@"CN"];
            if ([oWordTemp isKindOfClass:[NSArray class]]){
                arWords = oWordTemp;
            }
            else if (oWordTemp == nil){
                return 0;
            }
            else{
                [arWords addObject:oWordTemp];
            }
            int iCount = 1;
            for (int i=0; i< arWords.count; i++){
                iCount ++;
                NSDictionary *oCon = arWords[i];
                [db_Asset UpdateContact:[[O_Contact alloc] initWithDictionary:oCon]];
                
            }
            
            [CommonAnalytics trackEvent:@"iOS Sync Process Contacts" meta:@{
                @"Total": [NSString stringWithFormat:@"%ld", (unsigned long)arWords.count]
            }];
                                                                                
            return iCount;
        }
        @catch (NSException *exception) {
            [db_Log InsertLog:@"Sync" sMessage:[NSString stringWithFormat:@"Exception ProcessContact - %@", exception.description]];
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return 0;
        }
    }
}
-(void)ProcessCustomer:(NSDictionary *)oDic{
    @autoreleasepool {
        @try {
            O_Config *oConfig = [db_Common GetConfigByName:@"Users"];
            if (oConfig == nil || oConfig.iConfigID == 0){
                oConfig = [[O_Config alloc] init];
                oConfig.sConfigName = @"Users";
            }
            NSMutableArray *oArray = [[NSMutableArray alloc] init];
            
            for (NSDictionary *oTemp in [oDic objectForKey:@"SU"]){
                NSString *sID =[oTemp valueForKey:@"I"];
                NSString *sName =[[oTemp valueForKey:@"N"] valueForKey:@"text"];
                NSString *sEmail =[[oTemp valueForKey:@"E"] valueForKey:@"text"];
                
                NSMutableDictionary *oCustomer = [@{@"ID": sID, @"Name": sName, @"Email": sEmail} mutableCopy];
                [oArray addObject:oCustomer];
            }
            
            oConfig.sConfigValue = [CommonJson sArrayToJsonString:oArray];
            [db_Common UpdateConfig:oConfig];
            
        }@catch(NSException *exx){
            //NSString *cc = @"ll";
        }
 
    }
}
-(int)ProcessSchedules:(NSDictionary *)oDic{
    @autoreleasepool {
        @try {
            //[db_Delete DeleteSchedules];
            NSMutableArray *arWords = [[NSMutableArray alloc] init];
            id oWordTemp = [oDic valueForKey:@"SC"];
            if ([oWordTemp isKindOfClass:[NSArray class]]){
                arWords = oWordTemp;
            }
            else if (oWordTemp == nil){
                return 0;
            }
            else{
                [arWords addObject:oWordTemp];
            }
            int iCount = 1;
            for (int i=0; i< arWords.count; i++){
                iCount ++;
                NSDictionary *oLayout =  [arWords objectAtIndex:i];
                bool bDeleted = [[oLayout valueForKey:@"M"] boolValue];
                int iSScheduleID = [[oLayout valueForKey:@"I"] intValue];
                bool bSchedule = [[oLayout valueForKey:@"N"] boolValue];
                @autoreleasepool {
                    [db_Delete DeleteEvent:iSScheduleID];

                    
                    if (bDeleted || bSchedule){
                        [db_Delete DeleteSchedule:iSScheduleID];
                    }
                    else{
                        [db_Delete DeleteSchedule:iSScheduleID];
                        O_Schedule *oSchedule = [[O_Schedule alloc] init];
                        oSchedule.iSScheduleID = [[oLayout valueForKey:@"I"] intValue];
                        oSchedule.iSAssetID = [[oLayout valueForKey:@"A"] intValue];
                        oSchedule.iSInsTypeID =[[oLayout valueForKey:@"T"] intValue];
                        oSchedule.sAddress1 =  [CommonHelper EscapeString:[[oLayout valueForKey:@"B"] valueForKey:@"text"]];
                        oSchedule.sAddress2 = [CommonHelper EscapeString: [[oLayout valueForKey:@"C"] valueForKey:@"text"]];
                        oSchedule.sInsTitle = [CommonHelper EscapeString: [[oLayout valueForKey:@"X"] valueForKey:@"text"]];
                        oSchedule.sType = [oLayout valueForKey:@"S"];
                        oSchedule.sPTC = [oLayout valueForKey:@"P"];
                        oSchedule.dtDateTime = [[oLayout valueForKey:@"D"] valueForKey:@"text"];
                        oSchedule.sRRule = [[oLayout valueForKey:@"RR"] valueForKey:@"text"];
                        oSchedule.sEXRule = [[oLayout valueForKey:@"ER"] valueForKey:@"text"];

                        @try{
                            NSMutableDictionary *oCustom1 = [[NSMutableDictionary alloc] init];
                        
                            [oCustom1 setValue:[[oLayout valueForKey:@"AD"] valueForKey:@"text"] forKey:@"sAddInfo"];
                         /*   @try{
                                if ([[[oLayout valueForKey:@"RC"] valueForKey:@"text"] length] > 0){
                                [oCustom1 setValue:[[oLayout valueForKey:@"RC"] valueForKey:@"text"] forKey:@"RCode"];
                                }
                            }@catch(NSException *eeee){
                                
                            }*/
                            @try{
                                if ([[[oLayout valueForKey:@"RE"] valueForKey:@"text"] length] > 0){
                                    [oCustom1 setValue:[[oLayout valueForKey:@"RE"] valueForKey:@"text"] forKey:@"REmail"];
                                }
                            }@catch(NSException *eeee){
                                
                            }
                            @try{
                                if ([[[oLayout valueForKey:@"RN"] valueForKey:@"text"] length] > 0){
                                    [oCustom1 setValue:[[oLayout valueForKey:@"RN"] valueForKey:@"text"] forKey:@"RName"];
                                }
                            }@catch(NSException *eeee){
                                
                            }
                            oSchedule.sCustom1 = [CommonHelper DictionaryToJson:oCustom1];
                            
                        }@catch(NSException *eee){
                            
                        }
                        [db_Schedule SaveSchedule:oSchedule];
                    }
                    
                }
            }

            [CommonAnalytics trackEvent:@"iOS Sync Process Schedules" meta:@{
                @"Total": [NSString stringWithFormat:@"%ld", (unsigned long)arWords.count]
            }];
                                                                                
            return iCount;
        }
        @catch (NSException *exception) {
                        [db_Log InsertLog:@"Sync" sMessage:[NSString stringWithFormat:@"Exception ProcessSchedules - %@", exception.description]];
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return 0;
        }
    }
}
-(int)ProcessQuickPhrase:(NSDictionary *)oDic{
    @autoreleasepool {
        @try {
            NSMutableArray *arWords = [[NSMutableArray alloc] init];
            id oWordTemp = [oDic valueForKey:@"QP"];
            if ([oWordTemp isKindOfClass:[NSArray class]]){
                arWords = oWordTemp;
            }
            else if (oWordTemp == nil){
                return 0;
            }
            else{
                [arWords addObject:oWordTemp];
            }
            int iCount = 1;
            for (int i=0; i< arWords.count; i++){
                iCount ++;
                NSDictionary *oLayout =  [arWords objectAtIndex:i];
                bool bDeleted = [[oLayout valueForKey:@"B"] boolValue];
                int iSQuickPhraseID = [[oLayout valueForKey:@"S"] intValue];
                if (bDeleted){
                    [db_Delete DeleteQuickPhrase:iSQuickPhraseID];
                }
                else{
                    [db_Delete DeleteQuickPhrase:iSQuickPhraseID];
                    [db_Common InsertQuickPhrase:[[oLayout valueForKey:@"Q"] valueForKey:@"text"] iSQuickPhraseID:[[oLayout valueForKey:@"S"] intValue]];
                }
            }
            
            [CommonAnalytics trackEvent:@"iOS Sync Process AutoTexts" meta:@{
                @"Total": [NSString stringWithFormat:@"%ld", (unsigned long)arWords.count]
            }];
                                                                                
            return iCount;
        }
        @catch (NSException *exception) {
                        [db_Log InsertLog:@"Sync" sMessage:[NSString stringWithFormat:@"Exception ProcessQuickPhrase - %@", exception.description]];
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            return 0;
        }
    }
    
}

- (BOOL)LoginAPIWithToken:(NSString *)idToken ssoAuthEndpoint:(Endpoint)authEndpoint{
    NSDictionary *oReturn = [IFConnection PostRequest:authEndpoint oParams:[@{
            @"sIDToken": idToken, @"sType": @"iOS"
    } mutableCopy]];
    return [self processLoginResult:oReturn];
}

- (BOOL)processLoginResult: (NSDictionary *)oReturn {
    if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]) {
        NSString *sCustomerID = [[oReturn valueForKey:@"iCustomerID"] stringValue];
        NSString *sToken = [oReturn valueForKey:@"sToken"];
        @try {
            NSString *sCompanyID = [CommonHelper IFGetPref:@"iCompanyID"];
            if (sCompanyID != nil && [sCompanyID intValue] > 0) {
                if ([[oReturn valueForKey:@"iCompanyID"] intValue] != [sCompanyID intValue]) {
                    [db_Delete ResetDatabase];
                    [CommonHelper IFSavePref:PrefsKeys.sSyncDate sValue:@""];
                }
            }
        } @catch (NSException *eeee) {

        }

        // Save the user details into preferences
        [AppEnvironmentBridge.accountService processMainAccount:oReturn];
        [AppEnvironmentBridge.accountService setCurrentAccountWithId:[sCustomerID integerValue]];
        
        NSString *sEmail = [oReturn valueForKey:@"sEmail"];
        [CommonHelper IFSavePref:@"sEmail" sValue:sEmail];
        [CommonHelper IFSavePref:@"iCustomerID" sValue:sCustomerID];
        [CommonHelper IFSavePref:@"sFirstName" sValue:oReturn[@"sFirstName"]];
        [CommonHelper IFSavePref:@"sLastName" sValue:oReturn[@"sLastName"]];
        @try {
            if (oReturn[@"bLoginAs"] != nil) {
                [CommonHelper IFSavePref:@"bLoginAs" sValue:[oReturn valueForKey:@"bLoginAs"]];
            }
        }
        @catch (NSException *ex) {

        }
        [if_AppDelegate logUser];

        @try {
            [CommonHelper IFSavePref:@"sIntercomHash" sValue:[oReturn valueForKey:@"sHash"]];
        } @catch (NSException *ex) {

        }
        return YES;
    } else if (oReturn != nil && (![[oReturn valueForKey:@"bSuccess"] boolValue])) {
        sMessage = [oReturn valueForKey:@"message"];
    } else {
        sMessage = @"There is a problem with internet connection, Please try again.";
    }
    return NO;
}

@end
