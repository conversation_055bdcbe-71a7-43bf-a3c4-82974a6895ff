//
//  AVCaptureVideoDataOutput+VideoConnection.m
//  SnapInspect3
//
//  Created by SnapInspect Team on 6/10/2023.
//  Copyright © 2023 SnapInspect. All rights reserved.
//

#import "AVCaptureVideoDataOutput+VideoConnection.h"

@implementation AVCaptureVideoDataOutput (VideoConnection)

- (AVCaptureConnection *)getVideoConnection {
    for (AVCaptureConnection *connection in self.connections) {
        for (AVCaptureInputPort *port in connection.inputPorts) {
            if ([port.mediaType isEqual:AVMediaTypeVideo]) {
                return connection;
            }
        }
    }

    return nil;
}

@end 
