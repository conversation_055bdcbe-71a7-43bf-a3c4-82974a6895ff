//
//  if_ExistAsset.m
//  InspectionFolio
//
//  Created by <PERSON> on 1/02/14.
//  Copyright (c) 2014 Cloudya. All rights reserved.
//

#import "if_ExistAsset.h"
#import "if_AssetDetail.h"
#import "db_Common.h"
#import "db_Asset.h"
#import <MessageUI/MFMailComposeViewController.h>
#import "if_Assets_2nd.h"
#import "Reachability.h"
#import "if_ServerEmail.h"
#import "if_AssetRoute.h"
#import "NSArray+HighOrderFunction.h"
@import AppFeatures;

@interface if_ExistAsset ()<CLLocationManagerDelegate>
@property (strong, nonatomic)  CLLocationManager *locationManager;
@end

@implementation if_ExistAsset{
    NSArray *arrInsType;
    int iWidth;
    CGFloat currentLatitude;
    CGFloat currentLongitude;
}

@synthesize tableContents,sortedKeys,headerView, iSAssetID, sAddress1, arrCompletedIns, arrCurrentIns, arrUploadedIns;
- (id)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil
{
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if (self) {
        // Custom initialization
    }
    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    [if_AppDelegate ShowTutorial:@"TUT_Asset_HEAD" sMessage:@"TUT_Asset_BODY"];
    CGSize oSize = [[UIScreen mainScreen] bounds].size;
    iWidth = oSize.width;
    UIBarButtonItem *btn_Back = [[UIBarButtonItem alloc] initWithTitle:@"Back" style:UIBarButtonItemStylePlain target:self action:@selector(btn_Back:)];
    [btn_Back setTintColor:[UIColor whiteColor]];
    [self.navigationController.navigationBar.topItem setBackBarButtonItem:btn_Back];


}
-(void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
       // self.navigationItem.prompt = [if_AppDelegate getsMessage];
    [CommonAnalytics trackEvent:@"iOS Exist Asset" meta:nil];
    self.navigationItem.title = sAddress1;
    arrCompletedIns = [db_Inspection GetInspectionsBySAssetID:iSAssetID bCompleted:true bSynced:false bExcludeChild:false];
    arrCurrentIns =[db_Inspection GetInspectionsBySAssetID:iSAssetID bCompleted:false bSynced:false bExcludeChild:false];
    arrUploadedIns = [db_Inspection GetInspectionsBySAssetID:iSAssetID bCompleted:true bSynced:true bExcludeChild:false];
    if (arrCompletedIns == nil || [arrCompletedIns count] == 0){
        O_InspectionTemp *oIns = [[O_InspectionTemp alloc] init];
        oIns.sTitle = @"No Completed Inspections.";
        oIns.bEmpty = true;
        [arrCompletedIns addObject:oIns];
    }
    if (arrCurrentIns == nil || [arrCurrentIns count] == 0){
        O_InspectionTemp *oIns = [[O_InspectionTemp alloc] init];
        oIns.sTitle = @"No Inspection in Progress.";
        oIns.bEmpty = true;
        [arrCurrentIns addObject:oIns];
    }
    if (arrUploadedIns == nil || [arrUploadedIns count] == 0){
        O_InspectionTemp *oIns = [[O_InspectionTemp alloc] init];
        oIns.sTitle = @"No Inspection in Progress.";
        oIns.bEmpty = true;
        [arrUploadedIns addObject:oIns];
    }

	// Do any additional setup after loading the view.
    
    //O_Asset *oAsset = [db_Asset GetPropertyBySAssetID:iSAssetID];
    //if ([[CommonHelper IFGetPref:@"sIndustry"] isEqualToString:@"RPM"] || oAsset.iSPAssetID> 0){
    //    oResult = [NSMutableArray arrayWithObject:sAddress1];
   // }
   // else{
   //     oResult = [NSMutableArray arrayWithObjects:sAddress1, @"Apartments", nil];
   // }

    //[self ShowStartInspection];
  //  UIBarButtonItem *btn_NewIns = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemAdd target:self action:@selector(Start_Inspection:)];
  //  [btn_NewIns setTintColor:[UIColor whiteColor]];
  //  [self.navigationItem setRightBarButtonItem:btn_NewIns];
    NSMutableArray *oResult = nil;
    int iChildCount = [db_Asset ValidateChildAssetExist:iSAssetID];
    if (iChildCount > 0){
        oResult = [NSMutableArray arrayWithObjects:sAddress1, @"Apartments", nil];
    }
    else{
        oResult = [NSMutableArray arrayWithObject:sAddress1];
    }
    NSMutableArray *routeBtns = [NSMutableArray arrayWithObjects:@"Map", nil];
    self.tableContents = [[NSDictionary alloc] initWithObjectsAndKeys:oResult, @"Asset Info",routeBtns, @"Map Info", arrCurrentIns, @"Inspection in Progress",  arrCompletedIns, @"Completed Inspections (Ready to Upload)", arrUploadedIns,  @"Uploaded Inspections", nil];
    self.sortedKeys = [[NSArray alloc] initWithObjects:@"Asset Info",@"Map Info", @"Inspection in Progress", @"Completed Inspections (Ready to Upload)", @"Uploaded Inspections", nil];
    [self DisplayStartInspectionButton:false];
    [db_Log InsertLog:@"Event" sMessage:[NSString stringWithFormat:@"Exist Address View - %@", sAddress1]];
    [oTableView reloadData];
    @try{
        [self startUserLocationSearch];
    }@catch(NSException *eee){
        
    }
}

-(void)DisplayStartInspectionButton:(bool) bForce{
    @try {
        if (headerView== nil || bForce){
            //   UILongPressGestureRecognizer *longpressGesture =[[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(longPressHandler:)];
            //  longpressGesture.minimumPressDuration = 3;
            //  [longpressGesture setDelegate:(id)self];
            //[self.button1 addGestureRecognizer:longpressGesture];
            if ([if_AppDelegate biPad]){
                headerView  = [[UIView alloc] initWithFrame:CGRectMake(0, 0, iWidth, 70)];
                UIButton *oButton = [[UIButton alloc] initWithFrame:CGRectMake((iWidth / 2 - 155), 20, 310, 50)];
                oButton.selected=NO;
                [oButton setTitle:@"Start New Inspection" forState:UIControlStateNormal];
                oButton.layer.cornerRadius = 4;
                //[oButton]
                [oButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
                oButton.layer.backgroundColor = [[UIColor colorWithRed:255/255.0 green:60/255.0 blue:30/255.0 alpha:0.8] CGColor] ;
                [oButton addTarget:self action:@selector(Start_Inspection:)
                  forControlEvents:UIControlEventTouchUpInside];
                //    [oButton addGestureRecognizer:longpressGesture];
                [headerView addSubview:oButton];
            }
            else{
                headerView  = [[UIView alloc] initWithFrame:CGRectMake(0, 0, iWidth, 70)];
                UIButton *oButton = [[UIButton alloc] initWithFrame:CGRectMake((iWidth / 2 - 150), 20, 300, 50)];
                [oButton setTitle:@"Start New Inspection" forState:UIControlStateNormal];
                oButton.titleLabel.font = [UIFont systemFontOfSize:14.0];
                [oButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
                oButton.layer.cornerRadius = 4;
                // [btn_Login setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
                oButton.layer.backgroundColor = [[UIColor colorWithRed:255/255.0 green:60/255.0 blue:30/255.0 alpha:0.8] CGColor] ;
                [oButton addTarget:self action:@selector(Start_Inspection:)
                  forControlEvents:UIControlEventTouchUpInside];
                // [oButton addGestureRecognizer:longpressGesture];
                [headerView addSubview:oButton];
            }
            //[headerView setBackgroundColor:[UIColor blackColor]];
            //  self.tbl_View.tableFooterView = self.footerView;
            oTableView.tableHeaderView = headerView;
        }
        
    }
    @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
}
-(void)btn_Back:(UIBarButtonItem *)myButton{
    [self.navigationController popViewControllerAnimated:YES];
}
-(void) willRotateToInterfaceOrientation:(UIInterfaceOrientation)toInterfaceOrientation duration:(NSTimeInterval)duration {
    CGSize oSize = [[UIScreen mainScreen] bounds].size;
    iWidth = oSize.height;
    [self DisplayStartInspectionButton:true];
}
//UIButton *oButton;

/*- (void)ShowStartInspection{
    @try {
        if (headerView == nil){
            headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 320, 60)];
            //[headerView setBackgroundColor:[UIColor blackColor]];
            oButton = [UIButton buttonWithType:UIButtonTypeCustom];
            if ([if_AppDelegate biPad]){
                oButton.frame = CGRectMake(350,20,300,50);
            }
            else{
                UIInterfaceOrientation interfaceOrientation = [UIApplication sharedApplication].statusBarOrientation;
                CGSize oSize = [[UIScreen mainScreen] bounds].size;
                if(UIInterfaceOrientationIsPortrait(interfaceOrientation)){
                    oButton.frame = CGRectMake(oSize.width / 2 - 150,20,300,50);
                }else if(UIInterfaceOrientationIsLandscape(interfaceOrientation)){
                    oButton.frame = CGRectMake(oSize.height / 2 - 150,20,300,50);
                }
                
                
            }
            [oButton setTitle:@"Start New Inspection" forState:UIControlStateNormal];
            [oButton setTitleColor:[UIColor darkGrayColor] forState:UIControlStateNormal];
            oButton.layer.cornerRadius = 4;
            // [btn_Login setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
            oButton.layer.backgroundColor = [[UIColor colorWithRed:255/255.0 green:60/255.0 blue:30/255.0 alpha:0.8] CGColor] ;
            [oButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
            [oButton addTarget:self action:@selector(Start_Inspection:)
              forControlEvents:UIControlEventTouchUpInside];
          //  [headerView addSubview:<#(UIView *)#>]
            [headerView addSubview:oButton];
        }
        [oTableView setTableHeaderView:headerView];
    }
    @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
}*/
-(IBAction)Start_Inspection:(UIButton *)sender{
    @autoreleasepool {
        
        int iChildCount = [db_Asset ValidateChildAssetExist:iSAssetID];
        if (iChildCount > 0){
                UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"I want to" message:@"" preferredStyle:UIAlertControllerStyleActionSheet];
            UIAlertAction *oAlertBuilding = [UIAlertAction actionWithTitle:@"Inspect the Building" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
                [self Start_Inspection_Step_2:sender];
            }];
            UIAlertAction *oAlertApartment = [UIAlertAction actionWithTitle:@"View Apartments" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
                if_Assets_2nd *oAssets = [self.storyboard instantiateViewControllerWithIdentifier:@"Assets_2nd"];
                oAssets.iPAssetID = iSAssetID;
                oAssets.sTitle = sAddress1;
                [self.navigationController pushViewController:oAssets animated:YES];
            }];
            [alert addAction:oAlertBuilding];
            [alert addAction:oAlertApartment];
            UIAlertAction *oClose = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil];
            [alert addAction:oClose];

            alert.modalPresentationStyle = UIModalPresentationPopover;
            alert.popoverPresentationController.sourceView = sender;
            alert.popoverPresentationController.sourceRect = sender.bounds;
            [self presentViewController:alert animated:YES completion:nil];
            
        }
        else{
           // oResult = [NSMutableArray arrayWithObject:sAddress1];
            [self Start_Inspection_Step_2:sender];
        }
    }

}
-(void)Start_Inspection_Step_2:(id)sender{
    if (![self validateNewInspection]) {
        return;
    }
    
    if (arrInsType) {
        arrInsType = nil;
    }

    arrInsType = [db_Common GetIntTypes];
    @weakify(self)
    [InsTypeSelectionView show:arrInsType didSelectOption:^(O_InsType * _Nonnull oInsType) {
        @strongify(self)
            @try {
                MBProgressHUD *hud = [[MBProgressHUD alloc] si_initWithView:self.view];
                [self.navigationController.view addSubview:hud];
                hud.labelText = @"Processing...";
                
                [hud showAnimated:YES whileExecutingBlock:^{
                    NSArray *arrAssetLayout = [db_Common GetAssetLayouts:iSAssetID sPTC:oInsType.sPTC];
                    O_Asset *oAsset = [db_Asset GetPropertyBySAssetID:iSAssetID];
                    if (arrAssetLayout != nil && [arrAssetLayout count] > 0){
                        [db_Log InsertLog:@"Ins Setup" sMessage:[NSString stringWithFormat:@"Load Ins From Existing Layout - InsTypeID: %d - InsTypeCount: %d - %@, %@", oInsType.iSInsTypeID, (int)[arrInsType count], sAddress1, oAsset.sAddress2]];
                        int iInsID = [db_SetupInspection AddInspection:arrAssetLayout iSAssetID:oAsset.iSAssetID sAddress1:oAsset.sAddress1 sAddress2:oAsset.sAddress2 iSInsTypeID:oInsType.iSInsTypeID iSScheduleID:0 sLat:currentLatitude sLong:currentLongitude sCustom1_Schedule:@"" dtSchedule:@""];
                        [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                            [self PushInspection:iInsID];
                        }];
                        
                    }
                    else{
                        int iInsID = [CommonSetupIns StartInspectionBranch:iSAssetID sAddress1:oAsset.sAddress1 sAddress2:oAsset.sAddress2 iSScheduleID:0 sPTC:oInsType.sPTC sType:oInsType.sType iSInsTypeID:oInsType.iSInsTypeID  sLat:currentLatitude sLong:currentLongitude sCustom1_Schedule:@"" dtSchedule:@""];
                        if (iInsID > 0){
                            [db_Log InsertLog:@"Ins Setup" sMessage:[NSString stringWithFormat:@"Load Ins By Pass - InsTypeID: %d - InsTypeCount: %d - %@, %@", oInsType.iSInsTypeID, (int)[arrInsType count], sAddress1, oAsset.sAddress2]];
                            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                                [self PushInspection:iInsID];
                            }];
                        }
                        else{
                            [db_Log InsertLog:@"Ins Setup" sMessage:[NSString stringWithFormat:@"Setup Layout - InsTypeID: %d - InsTypeCount: %d - %@, %@", oInsType.iSInsTypeID, (int)[arrInsType count], sAddress1, oAsset.sAddress2]];
                            [Navigator PushLayoutSetup: oInsType.iSInsTypeID
                                          iSScheduleID: 0
                                             iSAssetID: oAsset.iSAssetID
                                                 sType: oInsType.sType
                                                  sPTC: oInsType.sPTC
                                             sAddress1: oAsset.sAddress1
                                             sAddress2: oAsset.sAddress2
                                              sCustom1: nil
                                            dtSchedule: @""
                                 onInspectionDidCreate: nil
                            ];
                        }
                    }
                } completionBlock:^{
                    [hud removeFromSuperview];
                }];
            }
            @catch (NSException *exception) {
                [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            }
            
        }];
}
- (void)PushInspection: (int)iInsID {
    [Navigator PushInspection:iInsID];
}

-(void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
}
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    //if (self.sortedKeys == nil || [self.sortedKeys count] == 0){
    //    [self WorkOutSort];
    //}
    return [self.sortedKeys count];
}
- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    CGRect frame = tableView.frame;
    // UIButton *oTest = [UIButton buttonWithType:UIBU
    

    UIView *section_headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, frame.size.width, frame.size.height)];
    UILabel *title = [[UILabel alloc] initWithFrame:CGRectMake(20, section == 0 ? 10 : 0, frame.size.width, 30)];
    [title setTextColor:[UIColor grayColor]];
   // [title setBackgroundColor:[UIColor redColor]];
    title.text = [self.sortedKeys objectAtIndex:section];
    if (section == 4){
        
       //  = [[UIButton alloc] initWithFrame:CGRectMake(frame.size.width-60, 10, 50, 30)];
        // addButton.titleLabel.text = @"Edit";
        UIButton *addButton = [UIButton buttonWithType:UIButtonTypeDetailDisclosure];
        [addButton setFrame:CGRectMake(frame.size.width - 60, 0, 40, 40)];
        //[addButton setTitle:@"Refresh" forState:UIControlStateNormal];
        [addButton setTintColor:[UIColor blackColor]];
        [addButton setTitleColor:[UIColor colorWithRed:22/255.0f green:96/255.0f blue:171/255.0f alpha:1] forState:UIControlStateNormal];
        
        [addButton addTarget:self action:@selector(btn_RefreshPropertyInspection:) forControlEvents:UIControlEventTouchUpInside];
        [section_headerView addSubview:addButton];
    }
   // [section_headerView setBackgroundColor:[UIColor greenColor]];

    [section_headerView addSubview:title];
    
    
    return section_headerView;
}

-(IBAction)btn_RefreshPropertyInspection:(id)sender{
    Reachability* reachability = [Reachability reachabilityForInternetConnection];
    NetworkStatus oStatus = [reachability currentReachabilityStatus];
    if (oStatus == NotReachable){
        [self ShowAlert:@"Error" sMessage:@"No Internet Access."];
        [db_Log InsertLog:@"Upload" sMessage:@"No Internet"];
        
    }
    else{
        MBProgressHUD *hud = [[MBProgressHUD alloc] si_initWithView:self.view];
        [self.navigationController.view addSubview:hud];
        hud.labelText = @"Connecting to Server";
        
        [hud showAnimated:YES whileExecutingBlock:^{
            NSMutableDictionary *oRefreshToken = [[NSMutableDictionary alloc] initWithObjectsAndKeys:[CommonHelper IFGetPref:@"iCustomerID"], @"iCustomerID", [CommonUser currentToken], @"sToken", [NSString stringWithFormat:@"%d", iSAssetID], @"iObjectID", nil];
            [hud setDetailsLabelText:@"Downloading Data..."];
            NSDictionary *oReturn = [IFConnection PostRequest:EndpointSearchInspectionAPI  oParams:oRefreshToken];
            //NSString *cc = @"bb";
            if ([[oReturn valueForKey:@"success"] boolValue]){
                for (NSMutableDictionary *oTemp in [oReturn valueForKey:@"lsInspection"]){
                    int iSInsID = [[oTemp valueForKey:@"iInspectionID"] intValue];
                    
                    O_Inspection *oIns = [db_Inspection GetInspectionBySInsID:iSInsID];
                
                   // oIns.i
                    oIns.iSAssetID = [[oTemp valueForKey:@"iPropertyID"] intValue];
                    oIns.sInsTitle = [oTemp valueForKey:@"sInsTitle"];
                    oIns.sType = [oTemp valueForKey:@"sType"];
                    oIns.sTitle = [oTemp valueForKey:@"sTitle"];
                    O_InsType *oInsType = [db_SetupInspection GetInsType_SInsTypeID:[[oTemp valueForKey:@"iInsTypeID"] intValue]];
                    oIns.sPTC = @"";
                    if (oInsType != nil || oInsType.iInsTypeID  > 0){
                        oIns.sPTC = oInsType.sPTC;
                    }
                    
                    oIns.sLat = @"";
                    oIns.sLong = @"";
                    oIns.sAddress1 = [oTemp valueForKey:@"sAddress1"];
                    oIns.sAddress2 = [oTemp valueForKey:@"sAddress2"];
                    
                    oIns.iSInsID =  iSInsID;
                    oIns.iSInsTypeID = [[oTemp valueForKey:@"iInsTypeID"] intValue];
                    oIns.dtStartDate = [oTemp valueForKey:@"dtStart"];
                    oIns.dtEndDate = [oTemp valueForKey:@"dtEnd"];
                    oIns.iSScheduleID = 0;
                    oIns.bSynced = true;
                    oIns.bComplete = true;
                    oIns.bDeleted  = false;
                    [db_Inspection UpdateInspection:oIns];
                    [db_Inspection UpdateInspection:oIns];
                }
                arrUploadedIns = [db_Inspection GetInspectionsBySAssetID:iSAssetID bCompleted:true bSynced:true bExcludeChild:false];
                if (arrUploadedIns == nil || [arrUploadedIns count] == 0){
                    O_InspectionTemp *oIns = [[O_InspectionTemp alloc] init];
                    oIns.sTitle = @"No Inspection in Progress.";
                    oIns.bEmpty = true;
                    [arrUploadedIns addObject:oIns];
                }
                NSMutableArray *oResult = nil;
                int iChildCount = [db_Asset ValidateChildAssetExist:iSAssetID];
                if (iChildCount > 0){
                    oResult = [NSMutableArray arrayWithObjects:sAddress1, @"Apartments", nil];
                }
                else{
                    oResult = [NSMutableArray arrayWithObject:sAddress1];
                }
                    self.tableContents = [[NSDictionary alloc] initWithObjectsAndKeys:oResult, @"Asset Info", arrCurrentIns, @"Inspection in Progress",  arrCompletedIns, @"Completed Inspections (Ready to Upload)", arrUploadedIns,  @"Uploaded Inspections", nil];
                [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                    
                    [oTableView reloadData];
                }];
            }
     
            
        } completionBlock:^{
            
            
            [hud removeFromSuperview];
        }];
    }
}
- ( CGFloat )tableView:( UITableView *)tableView heightForHeaderInSection:( NSInteger )section

{
    
    return 40 ;
    
}
- (NSString *)tableView:(UITableView *)tableView
titleForHeaderInSection:(NSInteger)section
{
    //if (self.sortedKeys == nil || [self.sortedKeys count] == 0){
    //    [self WorkOutSort];
    //}
    return [self.sortedKeys objectAtIndex:section];
}
- (NSInteger)tableView:(UITableView *)table numberOfRowsInSection:(NSInteger)section {
    //if (self.sortedKeys == nil || [self.sortedKeys count] == 0){
    //    [self WorkOutSort];
    // }
    //  NSString *sString =[self.sortedKeys objectAtIndex:section];
    //  NSArray *listData1 = [self.tableContents objectForKey:sString];
    NSArray *listData =[self.tableContents objectForKey:
                        [self.sortedKeys objectAtIndex:section]];
    return [listData count];
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath;{
	if ([indexPath section] == 0 || [indexPath section] == 1){
        return 50;
    }
    return 60;
}
- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    @try {
        static NSString *SimpleTableIdentifier = @"DisplayIdentifier";
        

        UITableViewCell * cell = [tableView
                                  dequeueReusableCellWithIdentifier: SimpleTableIdentifier];
        
        if(cell == nil) {
            
           cell = [[UITableViewCell alloc]
                    initWithStyle:UITableViewCellStyleSubtitle
                    reuseIdentifier:SimpleTableIdentifier];

        }
        if ([indexPath section] == 0){
            cell.textLabel.text = [[self.tableContents objectForKey:
                                    [self.sortedKeys objectAtIndex:[indexPath section]]] objectAtIndex:[indexPath row]];
            cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
        }
        else if ([indexPath section] == 1) {
            NSString *btntitle =(NSString*)[[self.tableContents objectForKey:
                                                               [self.sortedKeys objectAtIndex:[indexPath section]]] objectAtIndex:[indexPath row]];
            cell.textLabel.text = btntitle;
            cell.textLabel.lineBreakMode = NSLineBreakByWordWrapping;
            cell.textLabel.numberOfLines = 0;
            cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
        }
        else if ([indexPath section ] > 1){
            O_InspectionTemp *oInsTemp =((O_InspectionTemp *)[[self.tableContents objectForKey:
                                                               [self.sortedKeys objectAtIndex:[indexPath section]]] objectAtIndex:[indexPath row]]);
            cell.textLabel.text = [oInsTemp sTitle];
            cell.textLabel.lineBreakMode = NSLineBreakByWordWrapping;
            cell.textLabel.numberOfLines = 0;
            cell.detailTextLabel.text = (oInsTemp.dtStartDate == nil && oInsTemp.dtEndDate == nil) ? @"" : [NSString stringWithFormat:@"%@  %@ - %@", oInsTemp.sInsTitle, oInsTemp.dtStartDate == nil ? @"?" : oInsTemp.dtStartDate, oInsTemp.dtEndDate == nil ? @"?" : oInsTemp.dtEndDate];
            if (oInsTemp.bEmpty){
                cell.accessoryType = UITableViewCellAccessoryNone;
            }
            else if ([indexPath section] == 4){
                cell.accessoryType = UITableViewCellAccessoryCheckmark;
            }
            else{
                cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
            }
        }
        
        cell.selectionStyle = UITableViewCellSelectionStyleNone;

        return cell;
    }
    @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
}
- (void)tableView:(UITableView *)tableView
didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    @try {
        [tableView deselectRowAtIndexPath:indexPath animated:YES];
        if ([indexPath section] == 0){

            if ([indexPath row] == 0){
                 if_AssetDetail *oDetail = [self.storyboard instantiateViewControllerWithIdentifier:@"AssetDetail"];
                oDetail.iSAssetID = iSAssetID;
                 [self.navigationController pushViewController:oDetail animated:YES];
            }
            else if ([indexPath row] == 1){
                if_Assets_2nd *oAssets = [self.storyboard instantiateViewControllerWithIdentifier:@"Assets_2nd"];
                oAssets.iPAssetID = iSAssetID;
                oAssets.sTitle = sAddress1;
                [self.navigationController pushViewController:oAssets animated:YES];
            }
            
        }
        else if ([indexPath section] == 1) {
            if_AssetRoute *oDetail = [self.storyboard instantiateViewControllerWithIdentifier:@"AssetRoute"];
            oDetail.address = sAddress1;
            oDetail.iSAssetID = iSAssetID;
            [self.navigationController pushViewController:oDetail animated:YES];
        }
        else if ([indexPath section] == 2){
            O_InspectionTemp *oTemp = [arrCurrentIns objectAtIndex:[indexPath row]];
            if (oTemp.iInsID > 0 ){
                 [self PushInspection:oTemp.iInsID];
            }
        }
        else if ([indexPath section] == 3){
            O_InspectionTemp *oTemp = [arrCompletedIns objectAtIndex:[indexPath row]];
            if (oTemp.iInsID > 0){
                 [self PushInspection:oTemp.iInsID];
            }
        }
        else if ([indexPath section] == 4){
            O_InspectionTemp *oTemp = [arrUploadedIns objectAtIndex:[indexPath row]];
            if (oTemp.iSInsID > 0){
                [self ViewReport:oTemp.iSInsID sTitle:oTemp.sTitle sInsTitle:oTemp.sInsTitle oRect:[[tableView cellForRowAtIndexPath:indexPath] bounds]  oView:[tableView cellForRowAtIndexPath:indexPath]];

            }
        }
        
        
        
    }
    @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
}
   -(void)ViewReport:(int)iInsID sTitle:(NSString *)sTitle sInsTitle:(NSString *)sInsTitle oRect:(CGRect)oRect oView:(UIView *)oView{
    if ([self.navigationController.visibleViewController isKindOfClass:[UIAlertController class]]) {
        return;
    }
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:sTitle message:@"" preferredStyle:UIAlertControllerStyleActionSheet];
    
    UIAlertAction *oViewPDF = [UIAlertAction actionWithTitle:@"View PDF" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
        MBProgressHUD *hud = [[MBProgressHUD alloc] si_initWithView:self.view];
        [self.navigationController.view addSubview:hud];
        hud.labelText = @"Connecting to Server";
        
        [hud showAnimated:YES whileExecutingBlock:^{
            NSMutableDictionary *oVideoToken = [[NSMutableDictionary alloc] initWithObjectsAndKeys:[CommonHelper IFGetPref:@"iCustomerID"], @"iCustomerID", [CommonUser currentToken], @"sToken", [NSString stringWithFormat:@"%d", iInsID], @"iInsID", nil];
            [hud setDetailsLabelText:@"Processing Request"];
            NSDictionary *oReturn = [IFConnection PostRequest:EndpointGetReport oParams:oVideoToken];
            if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                    [db_Log InsertLog:@"Report" sMessage:[NSString stringWithFormat:@"View Report %d", iInsID]];
                    if_WebView *oWebView = [self.storyboard instantiateViewControllerWithIdentifier:@"WebView"];
                    oWebView.sURL = [oReturn valueForKey:@"sURL"];
                    oWebView.sTitle = sTitle;
                    [self.navigationController pushViewController:oWebView animated:YES];
                }];
            }
            else if (oReturn != nil && (![[oReturn valueForKey:@"success"] boolValue])){
                [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                    [self ShowAlert:@"Error" sMessage:[oReturn valueForKey:@"message"]];
                }];
            }
        } completionBlock:^{
            [hud removeFromSuperview];
        }];
    }];
    [alert addAction:oViewPDF];
    
    UIAlertAction *oSendPDF = [UIAlertAction actionWithTitle:@"Send PDF" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
        BOOL toServer = [CommonHelper IFGetPref_Bool:PrefsKeys.bLocalEmailClient];
        MBProgressHUD *hud = [[MBProgressHUD alloc] si_initWithView:self.view];
        [self.navigationController.view addSubview:hud];
        hud.labelText = @"Processing...";
        if (!toServer) { // send email to server
            DLog(@"send email to server");
            hud.labelText = @"Connecting to Server";
            
            [hud showAnimated:YES whileExecutingBlock:^{
                NSMutableDictionary *oVideoToken = [[NSMutableDictionary alloc] initWithObjectsAndKeys:[CommonHelper IFGetPref:@"iCustomerID"], @"iCustomerID", [CommonUser currentToken], @"sToken", [NSString stringWithFormat:@"%d", iInsID], @"iInsID", nil];
                [hud setDetailsLabelText:@"Processing Request"];
                //NSString *cc =[[CommonHelper IFGetPref:@"iCustomerID"] copy];
                NSDictionary *oReturn = [IFConnection PostRequest:EndpointGetReportEmail oParams:oVideoToken];
                if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                        [db_Log InsertLog:@"Report" sMessage:[NSString stringWithFormat:@"Send Report %d", iInsID]];
                        
                        [self SendSeverEmail:[oReturn valueForKey:@"sToEmail"] withSubject:[oReturn valueForKey:@"sSubject"] withBody:[oReturn valueForKey:@"sMessageBody"] insID:iInsID ];
                    }];
                }
                else if (oReturn != nil && (![[oReturn valueForKey:@"success"] boolValue])){
                    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                        [self ShowAlert:@"Error" sMessage:[oReturn valueForKey:@"message"]];
                    }];
                }
            } completionBlock:^{
                [hud removeFromSuperview];
            }];
        } else {
            //hud = [[MBProgressHUD alloc] si_initWithView:self.navigationController.view];
            hud.labelText = @"Connecting to Server";
            
            [hud showAnimated:YES whileExecutingBlock:^{
                NSMutableDictionary *oVideoToken = [[NSMutableDictionary alloc] initWithObjectsAndKeys:[CommonHelper IFGetPref:@"iCustomerID"], @"iCustomerID", [CommonUser currentToken], @"sToken", [NSString stringWithFormat:@"%d", iInsID], @"iInsID", nil];
                [hud setDetailsLabelText:@"Processing Request"];
                NSDictionary *oReturn = [IFConnection PostRequest:EndpointGetReportEmail oParams:oVideoToken];
                if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                        [db_Log InsertLog:@"Report" sMessage:[NSString stringWithFormat:@"Send Report %d", iInsID]];
                        [self SendEmail:[oReturn valueForKey:@"sToEmail"] withSubject:[oReturn valueForKey:@"sSubject"] withBody:[oReturn valueForKey:@"sMessageBody"] ];
                    }];
                }
                else if (oReturn != nil && (![[oReturn valueForKey:@"success"] boolValue])){
                    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                        [self ShowAlert:@"Error" sMessage:[oReturn valueForKey:@"message"]];
                    }];
                }
            } completionBlock:^{
                [hud removeFromSuperview];
            }];
        }
        
    }];
    [alert addAction:oSendPDF];
  /*  UIAlertAction *oSendWord = [UIAlertAction actionWithTitle:@"Send Word Doc" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
        
        BOOL toServer = [CommonHelper IFGetPref_Bool:bLocalEmailClient];
        if (!hud){
            hud = [[MBProgressHUD alloc] si_initWithView:self.navigationController.view];
        }
        if (!toServer) { // send email to server
            DLog(@"send email to server");
            //MBProgressHUD *hud = [[MBProgressHUD alloc] si_initWithView:self.navigationController.view];

            [self.navigationController.view addSubview:hud];
            hud.labelText = @"Connecting to Server";
            
            [hud showAnimated:YES whileExecutingBlock:^{
                NSMutableDictionary *oVideoToken = [[NSMutableDictionary alloc] initWithObjectsAndKeys:[CommonHelper IFGetPref:@"iCustomerID"], @"iCustomerID", [CommonUser currentToken], @"sToken", [NSString stringWithFormat:@"%d", iInsID], @"iInsID", @"D", @"sType", nil];
                [hud setDetailsLabelText:@"Processing Request"];
                NSDictionary *oReturn = [IFConnection PostRequest:EndpointGetReportEmail oParams:oVideoToken];
                if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                        [db_Log InsertLog:@"Report" sMessage:[NSString stringWithFormat:@"Send Report %d", iInsID]];
                        
                        [self SendSeverEmail:[oReturn valueForKey:@"sSentToEmail"] withSubject:[oReturn valueForKey:@"sSubject"] withBody:[oReturn valueForKey:@"sMessageBody"] insID:iInsID ];
                    }];
                }
                else if (oReturn != nil && (![[oReturn valueForKey:@"success"] boolValue])){
                    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                        [self ShowAlert:@"Error" sMessage:[oReturn valueForKey:@"message"]];
                    }];
                }
            } completionBlock:^{
                [hud removeFromSuperview];
            }];
        } else {
           // hud = [[MBProgressHUD alloc] si_initWithView:self.navigationController.view];
            [self.navigationController.view addSubview:hud];
            hud.labelText = @"Connecting to Server";
            
            [hud showAnimated:YES whileExecutingBlock:^{
                NSMutableDictionary *oVideoToken = [[NSMutableDictionary alloc] initWithObjectsAndKeys:[CommonHelper IFGetPref:@"iCustomerID"], @"iCustomerID", [CommonUser currentToken], @"sToken", [NSString stringWithFormat:@"%d", iInsID], @"iInsID",@"D", @"sType",  nil];
                [hud setDetailsLabelText:@"Processing Request"];
                NSDictionary *oReturn = [IFConnection PostRequest:EndpointGetReportEmail oParams:oVideoToken];
                if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                        [db_Log InsertLog:@"Report" sMessage:[NSString stringWithFormat:@"Send Report %d", iInsID]];
                        [self SendEmail:[oReturn valueForKey:@"sSentToEmail"] withSubject:[oReturn valueForKey:@"sSubject"] withBody:[oReturn valueForKey:@"sMessageBody"] ];
                    }];
                }
                else if (oReturn != nil && (![[oReturn valueForKey:@"success"] boolValue])){
                    [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                        [self ShowAlert:@"Error" sMessage:[oReturn valueForKey:@"message"]];
                    }];
                }
            } completionBlock:^{
                [hud removeFromSuperview];
            }];
        }

        
    }];
    [alert addAction:oSendWord];*/

       if ([CommonPermission bEnableNewInspection] && [CommonPermission bEnableCopyInspection]) {
           UIAlertAction *oCopyInspection_Empty = [UIAlertAction actionWithTitle:@"Copy Inspection" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
               
               [self CopyInspectionAlert:oView oRect:oRect iInsID:iInsID bMediaTransfer:false];
               
           }];
           [alert addAction:oCopyInspection_Empty];
           
           UIAlertAction *oCopyInspection= [UIAlertAction actionWithTitle:@"Copy Inspection (With Photos)" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
               
               [self CopyInspectionAlert:oView oRect:oRect iInsID:iInsID bMediaTransfer:true];
               
           }];
           [alert addAction:oCopyInspection];
       }
    
    UIAlertAction *oClose = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil];
    [alert addAction:oClose];
    
    if ([if_AppDelegate biPad]){
        [alert setModalPresentationStyle:UIModalPresentationFullScreen];
        UIPopoverPresentationController *popPresenter = [alert
                                                         popoverPresentationController];
        // popPresenter.sourceView = button;
        popPresenter.sourceView = oView;
        popPresenter.sourceRect = oRect;
        
        [self presentViewController:alert animated:YES completion:nil];
    }
    else{
        [self presentViewController:alert animated:YES completion:nil];
    }
    
}
-(void)CopyInspectionAlert:(UIView *)oView oRect:(CGRect) oRect iInsID:(int)iSInsID bMediaTransfer:(bool)bMediaTransfer{
    if (![self validateNewInspection]) {
        return;
    }
    
    UIAlertController *oChildAlert = [UIAlertController alertControllerWithTitle:@"Choose an Inspection Type" message:@"" preferredStyle:UIAlertControllerStyleActionSheet];
    
    
    O_Inspection *oTemp = [db_Inspection GetInspectionBySInsID:iSInsID];
    
    arrInsType = [db_Common GetIntTypes];
    
    NSMutableArray *filteredInsTypes = [NSMutableArray array];
    @weakify(self)
    for (O_InsType *oInsType in arrInsType) {
        if ([oInsType.sPTC isEqualToString:oTemp.sPTC] && [oInsType.sType isEqualToString:oTemp.sType]) {
            [filteredInsTypes addObject:oInsType];
        }
    }
    
    [InsTypeSelectionView show:filteredInsTypes didSelectOption:^(O_InsType * _Nonnull oInsType) {
        @strongify(self)
        @try {
            MBProgressHUD *hud = [[MBProgressHUD alloc] si_initWithView:self.view];
            [self.navigationController.view addSubview:hud];
            
            hud.labelText = @"Processing...";
            
            [hud showAnimated:YES whileExecutingBlock:^{
                [hud setDetailsLabelText:@"Connecting to Server..."];
                [db_SetupInspection DuplicateInspection:iSInsID 
                                               oInsType:oInsType
                                                 bPhoto:bMediaTransfer
                                                   sLat:currentLatitude sLong:currentLongitude
                                             completion:^(int iInsID, NSString *sMessage) {
                    @strongify(self)
                    if (iInsID > 0) {
                        [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                            [self PushInspection:iInsID];
                        }];
                    } else if (sMessage != nil) {
                        [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                            [self ShowAlert:@"Error" sMessage:sMessage];
                        }];
                    }
                }];
                
            } completionBlock:^{
                [hud removeFromSuperview];
            }];
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }
    }];
}

-(void)SendSeverEmail:(NSString *)to withSubject:(NSString *)subject withBody:(NSString *)body insID:(int)iInsID{
    @try {
        if_ServerEmail *vc = [self.storyboard instantiateViewControllerWithIdentifier:@"if_ServerEmail"];
     /*   NSMutableArray *oArray= [[NSMutableArray alloc] init];
        if ([to containsString:@","]){
            oArray = [NSMutableArray arrayWithArray: [to componentsSeparatedByString:@","]];
            @try{
                [oArray removeObjectIdenticalTo:[NSNull null]];
            }@catch(NSException *ex){
                
            }
        }
        else{
            oArray = [NSMutableArray arrayWithArray: [to componentsSeparatedByString:@";"]];
            @try{
                [oArray removeObjectIdenticalTo:[NSNull null]];
            }@catch(NSException *ex){
                
            }
        }*/
        [vc setToRecipients:to];
        //[mailController setToRecipients:[[NSArray alloc] initWithObjects:@"<EMAIL>", nil] ];
        [vc setSubject:subject];
        [vc setBody:body  isHTML:YES];
        [vc setInsID:iInsID];
        [self presentViewController:vc animated:YES completion:nil];
    }
    @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
    
    
}

-(void)SendEmail:(NSString *)to withSubject:(NSString *)subject withBody:(NSString *)body{
    @try {
        MFMailComposeViewController *mailController = [[MFMailComposeViewController alloc] init];
        //;
        
        [mailController setToRecipients:[to componentsSeparatedByString:@";"]];
        //[mailController setToRecipients:[[NSArray alloc] initWithObjects:@"<EMAIL>", nil] ];
        [mailController setSubject:subject];
        [mailController setMessageBody:body isHTML:YES];
        
        mailController.mailComposeDelegate = (id)self;
        
        // UINavigationController *myNavController = [self navigationController];
        
        if ( mailController != nil ) {
            if ([MFMailComposeViewController canSendMail]){
                [self.navigationController presentViewController:mailController animated:YES completion:nil];
            }
        }
    }
    @catch (NSException *exception) {
             [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
    
    
}

-(void)mailComposeController:(MFMailComposeViewController*)controller didFinishWithResult:(MFMailComposeResult)result error:(NSError*)error {
    // [self dismissModalViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}
- (void)pickerView:(UIPickerView *)pickerView didSelectRow: (NSInteger)row inComponent:(NSInteger)component {
}

- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component {
	return [arrInsType count];
}

- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
	return 1;
}

- (NSString *)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component {
    O_InsType *oInsType = [arrInsType objectAtIndex:row];
	return oInsType.sInsTitle;
}
- (void)didReceiveMemoryWarning
{
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}
-(void)startUserLocationSearch{
    
    self.locationManager = [[CLLocationManager alloc]init];
    self.locationManager.delegate = self;
    self.locationManager.distanceFilter = kCLDistanceFilterNone;
    self.locationManager.desiredAccuracy = kCLLocationAccuracyHundredMeters;
    
    //Remember your pList needs to be configured to include the location persmission - adding the display message  (NSLocationWhenInUseUsageDescription)
    
    if ([self.locationManager respondsToSelector:@selector(requestWhenInUseAuthorization)]) {
        [self.locationManager requestWhenInUseAuthorization];
    }
    [self.locationManager startUpdatingLocation];
}

-(void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray<CLLocation *> *)locations{
    
    [self.locationManager stopUpdatingLocation];
    CGFloat usersLatitude = self.locationManager.location.coordinate.latitude;
    CGFloat usersLongidute = self.locationManager.location.coordinate.longitude;
    
    currentLatitude = usersLatitude;
    currentLongitude = usersLongidute;
}
@end
