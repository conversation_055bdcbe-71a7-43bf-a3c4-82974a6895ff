//
//  if_Inbox.swift
//  SnapInspect3
//
//  Created by <PERSON> on 2021/1/18.
//  Copyright © 2021 SnapInspect. All rights reserved.
//

import AppFeatures
import UIKit
import UIScrollView_InfiniteScroll

class if_Inbox: BaseViewController {
    private let pageSize = 20
    private var currentPage = 0
    private var numOfItemOnCurrentPage = -1

    @IBOutlet private var tableView: UITableView!
    @IBOutlet private var ivInbox: UIImageView!
    @IBOutlet private var lblNoInbox: UILabel!
    @IBOutlet private var btnClearAll: UIBarButtonItem!
    @IBOutlet private var btnMore: UIBarButtonItem!

    private var inboxItems: [O_Inbox] = []
    /// grouped by day
    private var itemsGrouped: [Date: [O_Inbox]] = [:]

    @objc public var defaultAction: O_InboxAction?

    override func viewDidLoad() {
        super.viewDidLoad()
        setupViews()
        showLoading("Processing. Please wait ...")
        loadDataIfNeed()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        guard let action = defaultAction else {
            return
        }
        parseInboxAction(action)
        defaultAction = nil
    }
}

extension if_Inbox {
    private func itemsDateHeaderTitle(for section: Int) -> String {
        let day = itemsGrouped.keys.sorted(by: >)[section] as NSDate
        if day.isToday {
            return "Today"
        } else if day.isYesterday {
            return "Yesterday"
        }

        return day.toScheduleHeaderTitle()
    }

    private func itemsSection(at index: Int) -> [O_Inbox] {
        let days = itemsGrouped.keys.sorted(by: >)
        return itemsGrouped[days[index]] ?? []
    }
}

extension if_Inbox {
    private func setupViews() {
        navigationItem.rightBarButtonItems?.removeAll(where: {
            $0 == self.btnMore
        })
        navigationItem.title = "Inbox"
        view.backgroundColor = .white

        tableView.hidesEmptyCells()
        tableView.register(InboxCell.self)
        tableView.register(headerFooterViewType: TableViewHeaderFooterView.self)

        tableView.delegate = self
        tableView.dataSource = self

        updateNoDataView(false)

        let refreshControl = UIRefreshControl()
        refreshControl.addTarget(self, action: #selector(onRefreshControlValueChanged), for: .valueChanged)
        tableView.refreshControl = refreshControl

        tableView.infiniteScrollTriggerOffset = 100.0
        tableView.addInfiniteScroll { [weak self] _ in
            self?.loadDataIfNeed(false)
        }
        tableView.setShouldShowInfiniteScrollHandler { [weak self] _ in
            guard let self = self else { return false }
            return self.numOfItemOnCurrentPage >= self.pageSize
        }
    }

    private func reloadTableView() {
        tableView.reloadData()
        updateNoDataView(inboxItems.isEmpty)
    }

    private func updateNoDataView(_ visible: Bool) {
        ivInbox.isHidden = !visible
        lblNoInbox.isHidden = !visible
    }

    private func loadDataIfNeed(_ isLatest: Bool = true) {
        if isLatest {
            inboxItems.removeAll()
            currentPage = 0
        }

        let params = [
            "iCustomerID": CommonHelper.ifGetPref("iCustomerID") ?? "",
            "sToken": CommonUser.currentToken ?? "",
            "bDismiss": "false",
            "iDisplayLength": "\(pageSize)",
            "iDisplayStart": "\(currentPage * pageSize)"
        ]

        DispatchQueue.global(qos: .utility).async {
            let response = IFConnection.postRequest(.getInbox, oParams: params)
            DispatchQueue.main.async { [weak self] in
                self?.hidesLoading()
                self?.tableView.refreshControl?.endRefreshing()
                self?.tableView.finishInfiniteScroll()

                if let result = response, let success = result["success"] as? Bool, success {
                    self?.processResult(result)
                    self?.reloadTableView()
                } else {
                    self?.showAlert("Error", sMessage: "Please make sure you are connected to Internet.")
                }
            }
        }
    }

    private func processResult(_ result: [AnyHashable: Any]) {
        guard let lsInbox = result["lsInbox"] as? [[String: Any]] else {
            return
        }

        let allUsers = CommonJson.getAllUsers()
        inboxItems.append(contentsOf: lsInbox.map {
            let inbox = O_Inbox(dictionary: $0)!
            inbox.sName = CommonComments.getPropertyManager(Int32(inbox.iFromCustomerID), users: allUsers)
            inbox.color = CommonComments.getColor(Int32(inbox.iFromCustomerID), users: allUsers)
            return inbox
        })

        itemsGrouped.removeAll()
        for inbox in inboxItems {
            let date = (inbox.dateTime as NSDate).startOfDay() as Date

            if itemsGrouped.keys.contains(date) {
                itemsGrouped[date]!.append(inbox)
            } else {
                itemsGrouped[date] = [inbox]
            }
        }

        numOfItemOnCurrentPage = lsInbox.count
        if numOfItemOnCurrentPage == pageSize {
            currentPage += 1
        }
    }

    private func clearAllInbox() {
        guard !inboxItems.isEmpty else { return }

        showLoading("Processing. Please wait ...")
        for inbox in inboxItems {
            guard let iCustomerID = CommonHelper.ifGetPref("iCustomerID"),
                  let sToken = CommonUser.currentToken else { return }
            let params = [
                "iCustomerID": iCustomerID,
                "sToken": sToken,
                "iInboxID": String(inbox.iInboxID)
            ]
            guard
                let result = IFConnection.postRequest(.dismissInbox, oParams: params),
                result["success"] as? Bool ?? false
            else {
                return showAlert("Error", sMessage: "Please make sure you are connected to Internet.")
            }
        }

        showAlert("Success", sMessage: "All Clear")
    }
}

extension if_Inbox: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        itemsGrouped.keys.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        itemsSection(at: section).count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(for: indexPath) as InboxCell
        cell.displayItem(oInbox: itemsSection(at: indexPath.section)[indexPath.row])
        return cell
    }
}

extension if_Inbox: UITableViewDelegate {
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = tableView.dequeueReusableHeaderFooterView() as TableViewHeaderFooterView
        headerView.si_setBackgroundColor(.white)
        headerView.titleLabel.numberOfLines = 0
        headerView.titleLabel.lineBreakMode = .byWordWrapping
        headerView.titleLabel.textColor = .color_B3B4B4()
        headerView.titleLabel.font = .sfCompactText_Semibold(17.0)
        headerView.title = itemsDateHeaderTitle(for: section)
        headerView.insets = .init(top: 10.0, left: 20.0, bottom: 10.0, right: 20.0)
        return headerView
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        parseInboxAction(itemsSection(at: indexPath.section)[indexPath.row].sAction)
    }
}

extension if_Inbox {
    @objc public func parseInboxAction(_ action: O_InboxAction) {
        guard action.isAvailable, let nav = navigationController,
              let value = Int(action.param) else {
            return
        }
        switch action.type {
        case .viewInsComments:
            if_InspectionComment.push(storyboard!, from: self, data: Int32(value))
        case .viewTaskComments:
            nav.pushViewController(if_TaskComments(taskId: value), animated: true)
        case .updateInsStatus:
            nav.pushViewController(if_InspectionInfo(insId: value), animated: true)
        default:
            break
        }
    }
}

// MARK: - IBAction

extension if_Inbox {
    @objc private func onRefreshControlValueChanged() {
        loadDataIfNeed()
    }

    @IBAction private func backAction(sender: UIButton) {
        dismiss(animated: true, completion: nil)
        navigationController?.popViewController(animated: true)
    }

    @IBAction private func clearAction(sender: UIButton) {
        clearAllInbox()
        loadDataIfNeed()
    }
}
