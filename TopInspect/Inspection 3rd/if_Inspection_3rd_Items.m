//
//  if_Inspection_3rd_Items.m
//  SnapInspect3
//
//  Created by <PERSON> on 2020/1/5.
//  Copyright © 2020 SnapInspect. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <Photos/Photos.h>
#import <CoreServices/CoreServices.h>

#import "if_Inspection_3rd_Items.h"
#import "if_Inspection_3rd_Items_ViewModel.h"
#import "if_Inspection_3rd.h"
#import "InspectionItemNameView.h"
#import "InspectionTimerManager.h"
#import "NSArray+HighOrderFunction.h"
#import "CommonValidate.h"
#import "CommonInspection.h"
#import "if_reminder_list.h"
#import "if_SelectRating.h"
#import "SignatureViewController.h"
#import "ShootVideoViewController.h"
#import "ScanNavigationController.h"
#import "db_Common.h"
#import "db_Delete.h"
#import "RCEasyTipView.h"
#import "EmptyTableViewCell.h"
@import AppFeatures;

@interface if_Inspection_3rd_Items () <
        UITableViewDataSource, UITableViewDelegate,
        InsFullViewCellDelegate, InsVideoViewCellDelegate, InsSignViewCellDelegate,
        RCEasyTipViewDelegate, ScanNavigationControllerDelegate,
        SCNavigationControllerDelegate, SCCaptureCameraControllerDelegate,
        ScannerCameraControllerDelegate, ShootVideoViewControllerDelegate,
        VRNavigationControllerDelegate
        >
@property (strong, nonatomic) if_Inspection_3rd_Items_ViewModel *viewModel;
@property (strong, nonatomic) InspectionTimerManager *timerManager;

@property (weak, nonatomic) IBOutlet UIView *backgroundView;
@property (weak, nonatomic) IBOutlet UITableView *tableView;
@property (weak, nonatomic) IBOutlet UIButton *btnTimerInfo;
@property (weak, nonatomic) IBOutlet UIButton *btnAction;
@property (weak, nonatomic) IBOutlet UILabel *lbsBottomText;
@property (weak, nonatomic) IBOutlet UIView *vBottomToolbar;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *heightButtonToolbar;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *heightBackgroundView;

@property (weak, nonatomic) InspectionItemNameView *itemNameView;
@property (weak, nonatomic) UIView *tipBGView;
@property (weak, nonatomic) RCEasyTipView *tipView;

@property (assign, nonatomic) int iVideoInsItemID;
@property (assign, nonatomic) int iConfigParamID;
@property (copy, nonatomic) NSIndexPath *refreshIndexPath;
@end

@implementation if_Inspection_3rd_Items

- (void)viewDidLoad {
    [super viewDidLoad];

    self.navigationItem.title = self.viewModel.navigationTitle;
    self.view.backgroundColor = UIColor.whiteColor;
    self.backgroundView.backgroundColor = UIColor.color_navigationBar;
    
    NSMutableArray *rightBarItems = [NSMutableArray array];
    if (!self.viewModel.oPInsItem.bCompleted) {
        [rightBarItems addObject: [[UIBarButtonItem alloc] initWithBarButtonSystemItem: UIBarButtonSystemItemDone target: self action: @selector(onDoneItemTapped)]];
    }
    self.navigationItem.rightBarButtonItems = rightBarItems;
    
    UIBarButtonItem *backBarItem = [UIBarButtonItem backButtonWithTarget:self action:@selector(onBackButtonTapped)];
    self.navigationItem.leftBarButtonItem = backBarItem;

    self.tableView.estimatedRowHeight = UITableViewAutomaticDimension;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.backgroundColor = UIColor.clearColor;
    self.tableView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
    [self.tableView hidesEmptyCells];
    [self.tableView registerCellClass: InsFullViewCell.class];
    [self.tableView registerNibWithClass: InsVideoViewCell.class];
    [self.tableView registerNibWithClass: InsSignViewCell.class];
    [self.tableView registerNibWithClass: EmptyTableViewCell.class];

    if (![CommonHelper bKioskMode]) {
        UIImage *actionImage = [UIImage imageNamed:@"icon_inspection_item_action" withTintColor: UIColor.color_navigationBar];
        [self.btnAction setImage: actionImage forState: UIControlStateNormal];
        [self.btnAction addTarget: self tapped: @selector(onAddItemTapped)];
    }
    
    self.itemNameView.sName = self.sTitle;
    [self.itemNameView updateInstruction: self.viewModel.sParentPromptText bShowPrompt: self.viewModel.bShowPrompt];
    [self.itemNameView layoutIfNeeded];
    [self.itemNameView fadeIn];

    UIFont *btnTitleFont = [UIFont SFCompactText_Regular: 14.0];
    self.btnTimerInfo.titleLabel.font = btnTitleFont;
    self.btnTimerInfo.userInteractionEnabled = false;
    [self.btnTimerInfo setTitleColor: UIColor.color_4A4A4A forState: UIControlStateNormal];

    self.lbsBottomText.textColor = UIColor.color_4A4A4A;
    self.lbsBottomText.font = btnTitleFont;;

    [self updateBackgroundViewHeight: 0.0];
    [self timerStart];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear: animated];
    [self.navigationController setNavigationBarShadowImageHidden: YES];
    if (self.refreshIndexPath != nil) {
        [self.viewModel reloadInsItemWithIndex: self.refreshIndexPath.row];
        
        self.viewModel.cellHeightsDictionary[self.refreshIndexPath] = nil;
        [self.tableView safe_reloadRowsAtIndexPaths: @[self.refreshIndexPath]
                                   withRowAnimation: UITableViewRowAnimationAutomatic];
        self.refreshIndexPath = nil;
    } else {
        [self RefreshDisplay];
    }

    self.btnAction.hidden = self.viewModel.isSimpleInsItem;
    [self.timerManager registerDidBecomeActiveNotification];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear: animated];
    [self.navigationController setNavigationBarShadowImageHidden: NO];
    if (self.isBeingPopped) {
        [self timerEnd];
        UIViewController *previous = [self.navigationController topViewController];
        if ([previous isKindOfClass: if_Inspection_3rd.class]) {
            [(if_Inspection_3rd *)previous timerStart];
        }
    }
    [self.timerManager removeDidBecomeActiveNotification];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    [self updateFirstCellCorners];
}

- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id <UIViewControllerTransitionCoordinator>)coordinator {
    @weakify(self)
    [coordinator animateAlongsideTransition:^(id<UIViewControllerTransitionCoordinatorContext> context) {
    } completion:^(id<UIViewControllerTransitionCoordinatorContext> context) {
        @strongify(self)
        safe_dispatch_main_async(^{
            [self.tableView reloadData];
        });
    }];

    [super viewWillTransitionToSize:size withTransitionCoordinator:coordinator];
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (self.viewModel.isSimpleInsItem) return 1;
    return self.viewModel.arrInsItems.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithClass:self.viewModel.insItemCellClass forIndexPath: indexPath];
    [self configureCell:cell forRowAtIndexPath:indexPath];
    return cell;
}

- (void)configureCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    O_Item *oItem = [self.viewModel.arrInsItems safe_objectAtIndex: indexPath.row];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;

    if ([cell isKindOfClass: InsFullViewCell.class]) {
        InsFullViewCell *fullViewCell = (InsFullViewCell *)cell;
        fullViewCell.fullDelegate = self;
        fullViewCell.viewType = self.viewModel.isSimpleInsItem ? InsItemViewTypeSimple : InsItemViewTypeFull;

        O_Item *targetItem = self.viewModel.isSimpleInsItem ? self.viewModel.oPInsItem : oItem;
        [fullViewCell ProcessingCell: targetItem oPInsItem: self.viewModel.oPInsItem iWidth: self.viewModel.iWidth
                         bShowPrompt: self.viewModel.bShowPrompt indexPath: indexPath];

        [fullViewCell.btnMenu addTarget:self tapped:@selector(onMenuBtnTapped:)];
        fullViewCell.btnMenu.tag = oItem.iInsItemID;

        [fullViewCell.btnInfo addTarget:self tapped:@selector(onClickedRowInfo:)];
        fullViewCell.btnInfo.tag = oItem.iInsItemID;
    } else if ([cell isKindOfClass: InsVideoViewCell.class]) {
        InsVideoViewCell *videoViewCell = (InsVideoViewCell *)cell;
        videoViewCell.oCellVideoDelegate = self;

        [videoViewCell ProcessingCell: oItem.iInsItemID iInsID: self.viewModel.iInsID sName: oItem.sName
                               iWidth: self.viewModel.iWidth iParamID: 1 bShowPrompt: self.viewModel.bShowPrompt
                      sInsItemCustom1: oItem.sCustom1 iSLayoutID: oItem.iSLayoutID iVideoID:0 iNotificationID:0];

        [videoViewCell.btnMenu addTarget:self tapped:@selector(menuVideoClick:)];
        videoViewCell.btnMenu.tag = oItem.iInsItemID;
        [videoViewCell.btnInfo addTarget:self tapped:@selector(onClickedRowInfo:)];
        videoViewCell.btnInfo.tag = oItem.iInsItemID;
    } else if ([cell isKindOfClass: InsSignViewCell.class]) {
        InsSignViewCell *signViewCell = (InsSignViewCell *)cell;
        signViewCell.oCellSignDelegate = self;
        [signViewCell ProcessingCell: oItem iWidth: self.viewModel.iWidth  bShowPrompt: self.viewModel.bShowPrompt];
        [signViewCell.btnMenu addTarget:self tapped:@selector(menuSignClick:)];
        signViewCell.btnMenu.tag = oItem.iInsItemID;
        [signViewCell.btnInfo addTarget:self tapped:@selector(onClickedRowInfo:)];
        signViewCell.btnInfo.tag = oItem.iInsItemID;
    }
    
    if (indexPath.section == 0 && indexPath.row == 0) {
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(updateFirstCellCorners) object:nil];
        [self performSelector:@selector(updateFirstCellCorners) withObject:nil afterDelay: 0.2];
    } else {
        if (cell.layer.mask != nil) cell.layer.mask = nil;
    }
}

- (void)tableView:(UITableView *)tableView didEndDisplayingCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    self.viewModel.cellHeightsDictionary[indexPath] = @(cell.height);
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewAutomaticDimension;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSNumber *height = self.viewModel.cellHeightsDictionary[indexPath];
    return height != nil ? height.floatValue : UITableViewAutomaticDimension;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    [self updateBackgroundViewHeight: scrollView.contentOffset.y];
}

#pragma mark - Lazy properties

- (InspectionTimerManager *)timerManager {
    if (_timerManager == nil) {
        InspectionTimerManager *manager = [[InspectionTimerManager alloc] initWithInsId: self.iInsID];
        if ([CommonHelper IFGetPref_Bool: PrefsKeys.bDisplayInspectionTimer]) {
            @weakify(self)
            manager.timerLoops = ^(NSString *timestamp) {
                @strongify(self)
                [self.btnTimerInfo setTitle: timestamp forState: UIControlStateNormal];
            };
        }
        _timerManager = manager;
    }
    return _timerManager;
}

- (InspectionItemNameView *)itemNameView {
    if (_itemNameView == nil) {
        InspectionItemNameView *nameView = [UIView viewFromNibWithClass: InspectionItemNameView.class];
        nameView.autoresizingMask = UIViewAutoresizingNone;
        [nameView.btnInfo addTarget: self tapped: @selector(onClickedSectionInfo:)];
        self.tableView.tableHeaderView = nameView;
        _itemNameView = nameView;
    }
    return _itemNameView;
}

- (if_Inspection_3rd_Items_ViewModel *)viewModel {
    if (_viewModel == nil) {
        if_Inspection_3rd_Items_ViewModel *viewModel = [[if_Inspection_3rd_Items_ViewModel alloc] init];
        viewModel.iInsID = self.iInsID;
        viewModel.iPInsItemID = self.iPInsItemID;
        @weakify(self);
        viewModel.didUpdateEditedInsItems = ^() {
            @strongify(self);
            [self updateBottomText];
        };
        [viewModel reloadInspectionData];
        _viewModel = viewModel;
    }
    return _viewModel;
}

#pragma mark - Actions

- (void)onMenuBtnTapped: (UIButton *)sender {
    if ([CommonHelper bKioskMode]) {
        return [self ShowAlert:@"Message" sMessage:@"You can not edit this inspection."];
    }

    O_Item *oInsItem = [self getItemWithSender: sender];
    if (oInsItem == nil) return;

    CGPoint buttonOrigin = sender.frame.origin;
    // this converts the coordinate system of the origin from the button's superview to the table view's coordinate system.
    CGPoint originInTableView = [self.tableView convertPoint: buttonOrigin fromView: [sender superview]];
    NSIndexPath *rowIndexPath = [self.tableView indexPathForRowAtPoint: originInTableView];

    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Action" message:nil preferredStyle:UIAlertControllerStyleActionSheet];

    @weakify(self)
    if ([self.viewModel showsAddItemButtonWithInsItem: oInsItem]) {
        UIAlertAction *actionDuplicate = [UIAlertAction actionWithTitle:@"Duplicate Item"
                style:UIAlertActionStyleDefault handler:^(UIAlertAction *_Nonnull action) {
                    @strongify(self)
                    [self showDuplicateInsItem: oInsItem];
                }];
        [alert addAction:actionDuplicate];
    }
    
    if ([self.viewModel showsItemFloorPlanWithInsItem: oInsItem]) {
        UIAlertAction *actionFloorPlan = [UIAlertAction actionWithTitle:@"Blueprint Annotation"
                style:UIAlertActionStyleDefault handler:^(UIAlertAction *_Nonnull action) {
                    @strongify(self)
                    [self showFloorPlanDropPin: oInsItem];
                }];
        [alert addAction:actionFloorPlan];
    }

    if ([CommonValidate bItemReviewEnabled: oInsItem.sConfig]) {
        UIAlertAction *actionReview = [UIAlertAction actionWithTitle:@"Review Notes"
                style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
            @strongify(self)
            [self ShowAlert: @"Review Notes" sMessage: [CommonValidate sItemReviewNotes: oInsItem.sConfig]];
        }];
        [alert addAction:actionReview];
    }

    UIAlertAction* actionEditItem = [UIAlertAction actionWithTitle:@"Edit Name"
            style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {

        UIAlertController *editNameAlert = [UIAlertController alertControllerWithTitle: @"Edit Name"
                message:@"" preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler: nil];

        @weakify(editNameAlert)
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"OK"
                style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
                    @strongify(self, editNameAlert)
                    UITextField *oTextField = editNameAlert.textFields.firstObject;
                    NSString *name = [oTextField.text trimAllSidesWhitespace];
                    if ([name length] > 0) {
                        oInsItem.sName = name;
                        [db_InsItem UpdateInsItemName:oInsItem.iInsItemID sName:name sNameChanged:@"c"];
                        [db_InsItem UpdateInsItem:oInsItem];
                        self.viewModel.cellHeightsDictionary[rowIndexPath] = nil;
                        [self.tableView safe_reloadRowsAtIndexPaths: @[rowIndexPath] withRowAnimation: UITableViewRowAnimationNone];
                    } else {
                        [self ShowAlert:@"Error" sMessage:@"Please enter Item Name"];
                    }
                }];
        [editNameAlert addAction:cancelAction];
        [editNameAlert addAction:okAction];
        [editNameAlert addTextFieldWithConfigurationHandler:^(UITextField *textField) {
            textField.placeholder = @"Item Name";
            textField.clearButtonMode = UITextFieldViewModeWhileEditing;
            textField.text = oInsItem.sName;
        }];
        [self presentViewController: editNameAlert animated: YES completion: nil];
    }];
    [alert addAction:actionEditItem];

    if ([CommonHelper IFGetPref_Bool: @"bNotification"]) {
        UIAlertAction* actionNot = [UIAlertAction actionWithTitle:@"Tasks"
                style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            @strongify(self)
            if_NoticeList *oReminderList = [[if_NoticeList alloc] init];
            oReminderList.iInsItemID = @(oInsItem.iInsItemID);
            oReminderList.iInsID = @(self.viewModel.iInsID);
            oReminderList.sTitle = oInsItem.sName;
            [CommonAnalytics trackEvent:@"iOS Ins 3nd Full NoticeList" meta:@{
                @"ID - ItemID": [NSString stringWithFormat:@"%d - %d", self.viewModel.iInsID, oInsItem.iInsItemID],
                @"Name": oInsItem.sName ?: @""
            }];
            [self.navigationController pushViewController:oReminderList animated:YES];
        }];
        [alert addAction:actionNot];
    }

    if ([self.viewModel showsAddItemButtonWithInsItem: oInsItem]) {
        UIAlertAction *actionDel = [UIAlertAction actionWithTitle:@"Delete Item" style:UIAlertActionStyleDefault
                handler:^(UIAlertAction *_Nonnull action) {
                    @strongify(self)
                    if (oInsItem != nil && oInsItem.iSInsItemID > 0) {
                        [self ShowAlert:@"Message" sMessage:@"Edit Inspection does not allow Delete Item."];
                        return;
                    }
                    [self ShowAlert:@"Caution"
                           sMessage:[NSString stringWithFormat: @"Are your sure to DELETE this item - %@?", oInsItem.sName]
                       cancelButton:@"Cancel"
                         doneButton:@"Yes, Delete Item"
                         doneAction:^(UIAlertAction *action) {
                             @strongify(self)
                             [self.viewModel removeInsItem:oInsItem];
                             [self.tableView deleteRowsAtIndexPaths:@[rowIndexPath] withRowAnimation:UITableViewRowAnimationAutomatic];
                         }];
                }];
        [alert addAction:actionDel];
    }

    UIAlertAction* actionCancel = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler: nil];
    [alert addAction:actionCancel];

    alert.modalPresentationStyle = UIModalPresentationPopover;
    alert.popoverPresentationController.sourceView = sender;
    alert.popoverPresentationController.sourceRect = sender.bounds;
    [self presentViewController:alert animated:YES completion:nil];
}

- (void)showDuplicateInsItem:(O_Item *)oInsItem {
    UIAlertController *editNameAlert = [UIAlertController alertControllerWithTitle:@"Edit Item Name"
                                                                           message:@"" preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil];

    @weakify(editNameAlert, self, oInsItem)
    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"OK"
                                                       style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
                @strongify(self, editNameAlert)
                UITextField *oTextField = editNameAlert.textFields.firstObject;
                NSString *name = [oTextField.text trimAllSidesWhitespace];
                if ([name length] > 0) {
                    BOOL success = [self.viewModel duplicateFullItem:oInsItem sName: name];
                    if (success) {
                        [self RefreshDisplay];
                        [self.tableView scrollsToLastAnimated: YES];
                    } else {
                        [self ShowAlert:@"Error" sMessage:@"Item can not be added."];
                    }
                } else {
                    [self ShowAlert:@"Error" sMessage:@"Please enter Item Name"];
                }
            }];
    [editNameAlert addAction:cancelAction];
    [editNameAlert addAction:okAction];
    [editNameAlert addTextFieldWithConfigurationHandler:^(UITextField *textField) {
        @strongify(oInsItem)
        textField.placeholder = @"Please Enter Item Name";
        textField.clearButtonMode = UITextFieldViewModeWhileEditing;
        textField.text = [NSString stringWithFormat:@"%@ - copy", oInsItem.sName] ;;
    }];
    [self presentViewController:editNameAlert animated:YES completion:nil];
}

- (O_Item *)getItemWithSender: (UIView *)sender {
    if (self.viewModel.isSimpleInsItem) {
        return self.viewModel.oPInsItem;
    } else {
        NSInteger iInsItemID = sender.tag;
        if (iInsItemID <= 0) return nil;
        NSArray *items = [self.viewModel.arrInsItems filter:^BOOL(O_Item *item) {
            return item.iInsItemID == iInsItemID;
        }];
        return items.firstObject;
    }
}

- (void)menuSignClick:(UIButton*)sender {
    if ([CommonHelper bKioskMode]) {
        return [self ShowAlert:@"Message" sMessage:@"You can not edit this inspection."];
    }

    O_Item *oInsItem = [self getItemWithSender: sender];
    if (oInsItem == nil) return;

    CGPoint buttonOrigin = sender.frame.origin;
    // this converts the coordinate system of the origin from the button's superview to the table view's coordinate system.
    CGPoint originInTableView = [self.tableView convertPoint:buttonOrigin fromView:sender.superview];
    NSIndexPath *rowIndexPath = [self.tableView indexPathForRowAtPoint: originInTableView];

    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Action" message:nil preferredStyle:UIAlertControllerStyleActionSheet];
    @weakify(self, oInsItem)
    UIAlertAction* actionCopyItem = [UIAlertAction actionWithTitle:@"Duplicate Item" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        @strongify(self, oInsItem)
        [self showDuplicateInsItem: oInsItem];
    }];
    [alert addAction:actionCopyItem];

    if ([CommonValidate bItemReviewEnabled:oInsItem.sConfig]) {
        UIAlertAction *actionReview = [UIAlertAction actionWithTitle:@"Review Notes"
                style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
                    @strongify(self, oInsItem)
                    [self ShowAlert: @"Review Notes" sMessage: [CommonValidate sItemReviewNotes: oInsItem.sConfig]];
                }];
        [alert addAction:actionReview];
    }

    UIAlertAction* actionEditItem = [UIAlertAction actionWithTitle:@"Edit Item"
            style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                UIAlertController *editNameAlert = [UIAlertController alertControllerWithTitle:@"Edit Item Name"
                                                                                       message:@"" preferredStyle:UIAlertControllerStyleAlert];
                UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil];

                @weakify(editNameAlert)
                UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"OK"
                                                                   style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
                            @strongify(self, editNameAlert)
                            UITextField *oTextField = editNameAlert.textFields.firstObject;
                            NSString *name = [oTextField.text trimAllSidesWhitespace];
                            if ([name length] > 0) {
                                oInsItem.sName = name;
                                [db_InsItem UpdateInsItemName:oInsItem.iInsItemID sName:name sNameChanged:@"c"];
                                [db_InsItem UpdateInsItem:oInsItem];
                                [self.tableView safe_reloadRowsAtIndexPaths:@[rowIndexPath] withRowAnimation:UITableViewRowAnimationNone];
                            } else {
                                [self ShowAlert:@"Error" sMessage:@"Please enter Item Name"];
                            }
                        }];
                [editNameAlert addAction:cancelAction];
                [editNameAlert addAction:okAction];
                [editNameAlert addTextFieldWithConfigurationHandler:^(UITextField *textField) {
                    textField.placeholder = @"Please Enter Item Name";
                    textField.clearButtonMode = UITextFieldViewModeWhileEditing;
                    textField.text = oInsItem.sName;
                }];
                [self presentViewController:editNameAlert animated:YES completion:nil];
            }];
    [alert addAction:actionEditItem];

    NSMutableArray *arrPhotos = [db_Media SearchPhotos:0 iInsItemID:oInsItem.iInsItemID iInsID:oInsItem.iInsID];
    if (arrPhotos != nil && [arrPhotos count] == 1){
        UIAlertAction* actionResignItem = [UIAlertAction actionWithTitle:@"Remove Signature" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            @strongify(self)
            [self ShowAlert: @"Attention" sMessage: @"Are you sure to remove this signature?"
               cancelButton: @"Cancel" doneButton: @"Yes"
                 doneAction: ^(UIAlertAction *action) {
                     [self DeleteSign:oInsItem.iInsItemID];
                }];
        }];
        [alert addAction:actionResignItem];
    }

    UIAlertAction *actionDel = [UIAlertAction actionWithTitle:@"Delete Item" style:UIAlertActionStyleDefault
            handler:^(UIAlertAction *_Nonnull action) {
                @strongify(self)
                if (oInsItem != nil && oInsItem.iSInsItemID > 0) {
                    [self ShowAlert:@"Message" sMessage:@"Edit Inspection does not allow Delete Item."];
                    return;
                }
                [self ShowAlert:@"Caution"
                       sMessage:[NSString stringWithFormat: @"Are your sure to DELETE this item - %@?", oInsItem.sName]
                   cancelButton:@"Cancel"
                     doneButton:@"Yes, Delete Item"
                     doneAction:^(UIAlertAction *action) {
                         @strongify(self)
                         [self.viewModel removeInsItem:oInsItem];
                         [self.tableView deleteRowsAtIndexPaths:@[rowIndexPath] withRowAnimation:UITableViewRowAnimationAutomatic];
                     }];
            }];
    [alert addAction:actionDel];

    UIAlertAction* actionCancel = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler: nil];
    [alert addAction:actionCancel];

    alert.modalPresentationStyle = UIModalPresentationPopover;
    alert.popoverPresentationController.sourceView = sender;
    alert.popoverPresentationController.sourceRect = [sender bounds];
    [self presentViewController:alert animated:YES completion:nil];
}


- (void)menuVideoClick:(UIButton*)sender {
    O_Item *oInsItem = [self getItemWithSender: sender];
    if (oInsItem == nil) return;

    CGPoint buttonOrigin = sender.frame.origin;
    // this converts the coordinate system of the origin from the button's superview to the table view's coordinate system.
    CGPoint originInTableView = [self.tableView convertPoint:buttonOrigin fromView:sender.superview];
    NSIndexPath *rowIndexPath = [self.tableView indexPathForRowAtPoint: originInTableView];

    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Action" message:nil preferredStyle:UIAlertControllerStyleActionSheet];

    @weakify(self)
    
    if ([CommonHelper bKioskMode]) {
        // Only display resume recording option for the requested inspection item
        //Edit Video
        NSArray *arrVideos = [db_Media SearchInsItemVideos:oInsItem.iInsItemID iInsID:self.viewModel.iInsID];

        if (arrVideos != nil && [arrVideos count] == 1) {
            UIAlertAction* actionEditVideo = [UIAlertAction actionWithTitle:@"Resume Recording"
                                                                      style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                @strongify(self)
                self.refreshIndexPath = rowIndexPath;
                [self GoToVideo:oInsItem.iInsItemID iParamID:1 video: (O_Video *) arrVideos[0]];
            }];

            [alert addAction:actionEditVideo];

            UIAlertAction* actionDeleteVideo = [UIAlertAction actionWithTitle:@"Delete Video" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                @strongify(self)
                if (oInsItem != nil && oInsItem.iSInsItemID > 0) {
                    [self ShowAlert:@"Message" sMessage:@"Edit Inspection does not allow Delete Video."];
                    return;
                }
                [self ShowAlert: @"Caution" sMessage: @"Are your sure to DELETE this video?" cancelButton: @"Cancel" doneButton: @"Yes, Delete Video"
                     doneAction: ^(UIAlertAction * _Nonnull action) {
                         [self DeleteVideo:1 iInsItemID:oInsItem.iInsItemID iInsID:self.viewModel.iInsID];
                     }];
            }];

            [alert addAction:actionDeleteVideo];
        }
    } else {
        BOOL bAddItemButton = [self.viewModel showsAddItemButtonWithInsItem: oInsItem];
        if (bAddItemButton) {
            UIAlertAction *actionDuplicate = [UIAlertAction actionWithTitle:@"Duplicate Item"
                                                                      style:UIAlertActionStyleDefault handler:^(UIAlertAction *_Nonnull action) {
                @strongify(self)
                [self showDuplicateInsItem: oInsItem];
            }];
            [alert addAction:actionDuplicate];
        }
        
        if ([CommonValidate bItemReviewEnabled: oInsItem.sConfig]) {
            UIAlertAction *actionReview = [UIAlertAction actionWithTitle:@"Review Notes"
                                                                   style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
                @strongify(self)
                [self ShowAlert: @"Review Notes" sMessage: [CommonValidate sItemReviewNotes: oInsItem.sConfig]];
            }];
            [alert addAction:actionReview];
        }
        
        UIAlertAction* actionEditItem = [UIAlertAction actionWithTitle:@"Edit Item"
                                                                 style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            UIAlertController *editNameAlert = [UIAlertController alertControllerWithTitle:@"Edit Item Name"
                                                                                   message:@"" preferredStyle:UIAlertControllerStyleAlert];
            UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil];
            
            @weakify(editNameAlert)
            UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"OK"
                                                               style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
                @strongify(self, editNameAlert)
                UITextField *oTextField = editNameAlert.textFields.firstObject;
                NSString *name = [oTextField.text trimAllSidesWhitespace];
                if ([name length] > 0) {
                    oInsItem.sName = name;
                    [db_InsItem UpdateInsItemName:oInsItem.iInsItemID sName:name sNameChanged:@"c"];
                    [db_InsItem UpdateInsItem:oInsItem];
                    [self.tableView safe_reloadRowsAtIndexPaths:@[rowIndexPath] withRowAnimation:UITableViewRowAnimationNone];
                } else {
                    [self ShowAlert:@"Error" sMessage:@"Please enter Item Name"];
                }
            }];
            [editNameAlert addAction:cancelAction];
            [editNameAlert addAction:okAction];
            [editNameAlert addTextFieldWithConfigurationHandler:^(UITextField *textField) {
                textField.placeholder = @"Please Enter Item Name";
                textField.clearButtonMode = UITextFieldViewModeWhileEditing;
                textField.text = oInsItem.sName;
            }];
            [self presentViewController:editNameAlert animated:YES completion:nil];
        }];
        
        [alert addAction:actionEditItem];
        
        UIAlertAction *actionDel = [UIAlertAction actionWithTitle:@"Delete Item" style:UIAlertActionStyleDefault
                                                          handler:^(UIAlertAction *_Nonnull action) {
            @strongify(self)
            if (oInsItem != nil && oInsItem.iSInsItemID > 0) {
                [self ShowAlert:@"Message" sMessage:@"Edit Inspection does not allow Delete Item."];
                return;
            }
            [self ShowAlert:@"Caution"
                   sMessage:[NSString stringWithFormat: @"Are your sure to DELETE this item - %@?", oInsItem.sName]
               cancelButton:@"Cancel"
                 doneButton:@"Yes, Delete Item"
                 doneAction:^(UIAlertAction *action) {
                @strongify(self)
                NSString *sConfig = self.viewModel.oPInsItem.sConfig;
                if ([sConfig rangeOfString:@"VADD"].location != NSNotFound) {
                    [self.viewModel removeInsItem:oInsItem];
                    [self.tableView deleteRowsAtIndexPaths:@[rowIndexPath] withRowAnimation:UITableViewRowAnimationAutomatic];
                } else {
                    [self ShowAlert: @"Message"
                           sMessage: @"This item can not be deleted as your plan does not include Multi-Video function. <NAME_EMAIL> for more details."];
                }
            }];
        }];
        [alert addAction:actionDel];
        
        //Edit Video
        NSArray *arrVideos = [db_Media SearchInsItemVideos:oInsItem.iInsItemID iInsID:self.viewModel.iInsID];
        
        if (arrVideos != nil && [arrVideos count] == 1) {
            UIAlertAction* actionEditVideo = [UIAlertAction actionWithTitle:@"Resume Recording" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                @strongify(self)
                self.refreshIndexPath = rowIndexPath;
                [self GoToVideo:oInsItem.iInsItemID iParamID:1 video: (O_Video *) arrVideos[0]];
            }];
            
            [alert addAction:actionEditVideo];
            
            UIAlertAction* actionDeleteVideo = [UIAlertAction actionWithTitle:@"Delete Video" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                @strongify(self)
                if (oInsItem != nil && oInsItem.iSInsItemID > 0) {
                    [self ShowAlert:@"Message" sMessage:@"Edit Inspection does not allow Delete Video."];
                    return;
                }
                [self ShowAlert: @"Caution" sMessage: @"Are your sure to DELETE this video?" cancelButton: @"Cancel" doneButton: @"Yes, Delete Video"
                     doneAction: ^(UIAlertAction * _Nonnull action) {
                    [self DeleteVideo:1 iInsItemID:oInsItem.iInsItemID iInsID:self.viewModel.iInsID];
                }];
            }];
            
            [alert addAction:actionDeleteVideo];
        }
    }
    
    // Don't display the alert if there is no any action
    if ([alert.actions isEmpty]) return;

    UIAlertAction* actionCancel = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler: nil];
    [alert addAction:actionCancel];

    alert.modalPresentationStyle = UIModalPresentationPopover;
    alert.popoverPresentationController.sourceView = sender;
    alert.popoverPresentationController.sourceRect = [sender bounds];
    [self presentViewController:alert animated:YES completion:nil];
}

- (void)onDoneItemTapped {
    if (self.viewModel.hasCompulsoryItems) {
        [self onBackButtonTapped];
    } else {
        [self.viewModel completeInsItem];
        [self.navigationController popViewControllerAnimated: YES];
    }
}

- (void)onBackButtonTapped {
    if (self.viewModel.hasCompulsoryItems) {
        [self showIncompletedAreaAlert];
    } else {
        [self.navigationController popViewControllerAnimated: YES];
    }
}

- (void)showIncompletedAreaAlert {
    [self.viewModel markInsItemsAsRead];
    @weakify(self)
    [self ShowAlert:@"Incompleted Area"
           sMessage:@"The inspection can not be uploaded until all compulsory questions are answered. Please follow ! mark to complete inspection."
       cancelButton:@"View Incompleted Items"
       cancelAction:^(UIAlertAction * _Nonnull action) {
            @strongify(self)
            [self reloadCellData];
            [self scrollViewToFirstInvalidCell];
        }
         doneButton:@"Skip For Now"
         doneAction:^(UIAlertAction * _Nonnull action) {
            @strongify(self)
            [self.navigationController popViewControllerAnimated: YES];
        }
     ];
}

- (void)onAddItemTapped {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Action" message: nil
            preferredStyle:UIAlertControllerStyleActionSheet];
    @weakify(self)
    UIAlertAction *oAddNewItem = [UIAlertAction actionWithTitle:@"Add New Item"
            style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        @strongify(self)
        [self ShowAddItem];
    }];
    [alert addAction:oAddNewItem];
    [alert addAction:[UIAlertAction actionWithTitle:@"Close" style:UIAlertActionStyleCancel handler:nil]];

    alert.modalPresentationStyle = UIModalPresentationPopover;
    alert.popoverPresentationController.sourceView = self.btnAction;
    alert.popoverPresentationController.sourceRect = [self.btnAction bounds];
    [self presentViewController:alert animated:YES completion:nil];
}

- (void)ShowAddItem {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"New Item Name"
            message: nil preferredStyle:UIAlertControllerStyleAlert];

    [alert addAction:[UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil]];
    @weakify(self, alert)
    UIAlertAction *saveAction = [UIAlertAction actionWithTitle:@"Save" style:UIAlertActionStyleDefault
            handler:^(UIAlertAction *action) {
                @strongify(self, alert)
                UITextField *oTextField = alert.textFields.firstObject;
                NSString *sResult = [oTextField.text trimAllSidesWhitespace];
                if (sResult.length > 0) {
                    [self.viewModel addInsItemWithName:sResult];
                    [self RefreshDisplay];
                    [self.tableView scrollsToLastAnimated: YES];
                } else {
                    [self ShowAlert:@"Error" sMessage:@"Item name can not be empty."];
                }
            }];
      
      [alert addAction: saveAction];
      [alert addTextFieldWithConfigurationHandler:^(UITextField *textField) {
          textField.placeholder = @"New Item Name";
          [textField resignFirstResponder];
      }];
      [self presentViewController:alert animated:NO completion:nil];
}

- (void)updateFirstCellCorners {
    NSInteger itemSection = 0;
    UITableViewCell *cell = [self.tableView cellForRowAtIndexPath: [NSIndexPath indexPathForRow:0 inSection: itemSection]];
    [cell addRounding: UIRectCornerTopLeft | UIRectCornerTopRight radius: 15.0];
}

- (void)updateBottomText {
    if (self.viewModel.isSimpleInsItem) {
        self.lbsBottomText.text = [NSString stringWithFormat: @"%d out of 1", self.viewModel.oPInsItem.edited ? 1 : 0];
    } else {
        self.lbsBottomText.text = [NSString stringWithFormat: @"%ld out of %ld",
                               self.viewModel.editedInsItems.count, self.viewModel.arrInsItems.count];
    }
}

- (void)updateBackgroundViewHeight: (CGFloat)offsetY {
    CGFloat fixedOffset = self.itemNameView.height + 50.0;
    if (offsetY < fixedOffset) {
        CGFloat offset = MIN(offsetY, 0);
        if (self.heightBackgroundView.constant != ABS(offset) + fixedOffset) {
            self.heightBackgroundView.constant = ABS(offset) + fixedOffset;
        }
    } else {
        self.heightBackgroundView.constant = 0.0;
    }
}

- (void)RefreshDisplay {
    [self.viewModel reloadInspectionData];
    [self.tableView reloadData];
    [self updateBottomText];
}

- (void)scrollViewToFirstInvalidCell {
    NSInteger validIndex = [self.viewModel.arrInsItems firstIndex:^BOOL(O_Item *item) {
        return item.hasCompulsoryConfigs;
    }];
    if (NSNotFound == validIndex) return;
    
    [self.tableView scrollToRowAtIndexPath: [NSIndexPath indexPathForRow:validIndex inSection:0]
                          atScrollPosition: UITableViewScrollPositionTop
                                  animated:YES];
}

- (void)showFloorPlanDropPin:(O_Item *)oInsItem {
    if_InspectionItemFloorPlan *oFloorPlan = [[if_InspectionItemFloorPlan alloc] initWithInsItem: oInsItem];
    [self.navigationController pushViewController:oFloorPlan animated:YES];
}

#pragma mark - InsFullViewCellDelegate

- (void)reloadCellData {
    [self RefreshDisplay];
}

- (void)reloadCellAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath == nil) return;
    InsFullViewCell *cell = [self.tableView cellForRowAtIndexPath: indexPath];
    if (![cell isKindOfClass: InsFullViewCell.class]) return;
    
    self.viewModel.cellHeightsDictionary[indexPath] = nil;
    [self.viewModel reloadInsItemWithIndex: indexPath.row];
    
    [self configureCell: cell forRowAtIndexPath: indexPath];
    
    [UIView setAnimationsEnabled:NO];
    [self.tableView beginUpdates];
    [self.tableView endUpdates];
    [UIView setAnimationsEnabled:YES];
}

- (void)selectProduct:(int)iInsItemID iConfigNumber:(int)iConfigNumber indexPath:(NSIndexPath *)indexPath {
    O_InsItem *oInsItem = [db_InsItem GetInsItem: iInsItemID];
    if (oInsItem.iSInsItemID > 0) {
        [self ShowAlert:@"Message" sMessage:@"Cost can not be modified under Edit Inspection mode."];
        return;
    }

    CostItemsViewController *itemProducts = [[CostItemsViewController alloc] initWithIInsItemID: iInsItemID
                                                                                  iConfigNumber: iConfigNumber];
    [self.navigationController pushViewController:itemProducts animated:YES];
    self.refreshIndexPath = indexPath;
}

- (void)WriteComments:(int)_iInsItemID iConfigNumber:(int)iConfigNumber indexPath:(NSIndexPath *)indexPath {
    if_Comment *oComment = [self.storyboard instantiateViewControllerWithIdentifier:@"Comment"];
    oComment.iInsItemID = _iInsItemID;
    oComment.iConfigNumber = iConfigNumber;
    oComment.iInsID = self.viewModel.oIns.iInsID;
    oComment.iPInsItemID = self.viewModel.oPInsItem.iPInsItemID;
    [self.navigationController pushViewController:oComment animated:YES];
    self.refreshIndexPath = indexPath;
}

- (void)SelectRating:(int)_iInsItemID iConfigNumber:(int)iConfigNumber indexPath:(NSIndexPath *)indexPath {
    if_SelectRating *oSelect = [self.storyboard instantiateViewControllerWithIdentifier:@"if_selectrating"];
    oSelect.iInsItemID = _iInsItemID;
    oSelect.iConfigNumber = iConfigNumber;
    [self.navigationController pushViewController:oSelect animated:YES];
    self.refreshIndexPath = indexPath;
}

- (void)ShowMorePhotos:(int)iConfigNumber iInsItemID:(int)iInsItemID iInsID:(int)iInsID indexPath:(NSIndexPath *)indexPath {
    O_InsItem *oInsItem = [db_InsItem GetInsItem: iInsItemID];
    if (oInsItem.iInsItemID == 0) return;
    
    NSString *sValue = [CommonInspection GetInsItemValue: oInsItem iConfigNumber: iConfigNumber];
    NSArray *photos = [db_Media SearchBulkPhotos: sValue];
    NSArray *photoIds = [photos map:^id _Nonnull(O_Photo *photo) {
        return [@(photo.iPhotoID) stringValue];
    }];
    
    if_AllPhotos *allPhotos = [self.storyboard identifier: @"allPhotos"];
    allPhotos.photoArray = photoIds;
    allPhotos.viewPhoto = ^(NSInteger photoID) {
        [Navigator PushDisplayPhotoID: (int)photoID photoArray: photoIds];
    };
    [self.navigationController pushViewController:allPhotos animated:YES];
    self.refreshIndexPath = indexPath;
}

- (void)ShowMoreScans:(int)iConfigNumber iInsItemID:(int)iInsItemID iInsID:(int)iInsID indexPath:(NSIndexPath *)indexPath {
    O_InsItem *oInsItem = [db_InsItem GetInsItem: iInsItemID];
    if (oInsItem.iInsItemID == 0) return;
    
    NSString *sValue = [CommonInspection GetInsItemValue: oInsItem iConfigNumber: iConfigNumber];
    NSArray *photos = [db_Media SearchBulkPhotos: sValue];
    NSArray *photoIds = [photos map:^id _Nonnull(O_Photo *photo) {
        return [@(photo.iPhotoID) stringValue];
    }];
    
    if_AllPhotos *allPhotos = [self.storyboard identifier: @"allPhotos"];
    allPhotos.photoArray = photoIds;
    allPhotos.viewPhoto = ^(NSInteger photoID) {
        [Navigator PushDisplayPhotoID: (int)photoID photoArray: photoIds];
    };
    [self.navigationController pushViewController:allPhotos animated:YES];
    self.refreshIndexPath = indexPath;
}

- (void)TakePhotos:(int)iConfigNumber iInsItemID:(int)iInsItemID iInsID:(int)iInsID indexPath:(NSIndexPath *)indexPath {
    if (![self checkGPSLocationPermission]) return;
    SCNavigationController *nav = [
        [SCNavigationController alloc] initWithDelegate:self
                                              iParamID:iConfigNumber
                                            iInsItemID:iInsItemID
                                                iInsID:iInsID
                                            iSObjectID:0
                                      cameraSaveOption:CameraSaveOptionPhoto
                                          cameraOption:CameraOptionMultiple
                                            iPhotoSize:[db_Inspection GetInsTypePhotoSize:iInsID]];
    [nav showCameraWithParentController:self];
    self.refreshIndexPath = indexPath;
}

- (void)TakeScans:(int)iConfigNumber iInsItemID:(int)iInsItemID iInsID:(int)iInsID indexPath:(NSIndexPath *)indexPath {
    [Navigator showScanViewControllerWithConfigNumber:iConfigNumber
                                                insID:iInsID
                                            insItemID:iInsItemID
                                          scannerType:ScannerTypeDocument
                             presentingViewController:self
                                       scanViewDelete:self
                                      scanNavDelegate:self];
    self.refreshIndexPath = indexPath;
}

- (void)ShowImage:(int)iPhotoID arrPhotoGallery:(NSArray *)arrPhotoGallery indexPath:(NSIndexPath *)indexPath {
    @try {
        [Navigator PushDisplayPhotoID: iPhotoID photoArray: arrPhotoGallery];
        self.refreshIndexPath = indexPath;
    } @catch (NSException *exception) {
        Log_Exception(exception)
    }
}

- (void)Download_ShowImage:(int)iPhotoID arrPhotoGallery:(NSArray *)arrPhotoGallery indexPath:(NSIndexPath *)indexPath {
    if (![(if_AppDelegate *) [[UIApplication sharedApplication] delegate] bInternet:false]) return;
    
    UITableViewCell *cell = [self.tableView cellForRowAtIndexPath: indexPath];
    
    @weakify(self)
    [self ShowAlert: @"Photo In Cloud"
           sMessage: @"The photo is already in our Cloud Server. You can download to view it. Or just to view comments without download the photo."
         alertStyle: UIAlertControllerStyleActionSheet
         sourceView: cell
       cancelButton: @"Cancel"
       cancelAction: nil
         doneButton: @"Download Photo"
         doneAction: ^(UIAlertAction *action) {
             @try {
                 @strongify(self)
                 [self showLoading: @"Connecting to Server"];
                 O_Photo *oPhoto = [db_Media GetPhoto:iPhotoID];
                 NSDictionary *oVideoToken = @{
                         @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
                         @"sToken": [CommonUser currentToken],
                         @"iPhotoID": [NSString stringWithFormat:@"%d", oPhoto.iSPhotoID]
                 };

                 [self showLoading:@"Processing Request"];
                 dispatch_background_async(^{
                     NSDictionary *oReturn = [IFConnection PostRequest:EndpointGetPhoto oParams: oVideoToken.mutableCopy];
                     if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]) {
                         NSData *oData = [IFConnection DownloadFile_Data:[NSURL URLWithString:[oReturn valueForKey:@"sURL"]]];
                         safe_dispatch_main_async((^{
                             if (oData != nil) {
                                 NSString *sPath = [[if_AppDelegate GetGlobalVar] valueForKeyPath:@"sFilePath"];
                                 NSString *sUniqueFileName = [CommonHelper GetUniqueFileName];
                                 NSString *sPhotoName = [NSString stringWithFormat:@"p_%@.jpg", sUniqueFileName];
                                 NSString *sPhotoPath = [sPath stringByAppendingPathComponent:sPhotoName];
                                 
                                 [oData writeToFile:sPhotoPath atomically:NO];
                                 oPhoto.sFile = sPhotoName;
                                 
                                 /// if not saving thumb
                                 if (![CommonHelper IF_FileExist:oPhoto.sThumb]) {
                                     NSString *sThumbName = [NSString stringWithFormat:@"pt_%@.jpg", sUniqueFileName];
                                     NSString *sThumbPath = [sPath stringByAppendingPathComponent:sThumbName];
                                     UIImage *oTempImage = [CommonHelper processImage_Thumb:[UIImage imageWithData:oData]];
                                     [UIImageJPEGRepresentation(oTempImage, 0.8) writeToFile:sThumbPath atomically:YES];
                                     oPhoto.sThumb = sThumbName;
                                 }
                                 
                                 int photoId = [db_Media UpdatePhoto:oPhoto];
                                 [Navigator PushDisplayPhotoID: photoId photoArray: arrPhotoGallery];
                             } else {
                                 [self ShowAlert:@"Error" sMessage:@"Error when download photo, please try again."];
                             }
                             
                             [self hidesLoading];
                         }));
                     } else {
                         safe_dispatch_main_async(^{ [self hidesLoading]; });
                     }
                 });
             } @catch (NSException *exception) {
                 Log_Exception(exception)
             }
         }
        done2Button: @"View Photo Only"
        done2Action: ^(UIAlertAction *action) {
            @try {
                [Navigator PushDisplayPhotoID: iPhotoID photoArray: arrPhotoGallery];
            } @catch (NSException *exception) {
                Log_Exception(exception)
            }
        }
        done3Button: @"Download All Photos"
        done3Action:^(UIAlertAction * _Nonnull action) {
            @try {
                @strongify(self)
                [self showLoading:@"Connecting to Server"];
               
                dispatch_background_async(^{
                    NSMutableArray *arrPhotos = [db_Media SearchPhotosSync: self.viewModel.iInsID];
                    safe_dispatch_main_async(^{ [self showLoading:@"Processing Request"]; })
                    
                    int iCounter = 0;
                    for (O_Photo *oPhoto in arrPhotos) {
                        if ([CommonMedia DownloadPhotoFromServer: oPhoto]) {
                            iCounter ++;
                            safe_dispatch_main_async((^{
                                [self showLoading: [NSString stringWithFormat:@"%d/%ld photo downloaded.", iCounter, (long)arrPhotos.count]];
                            }))
                        }
                    }
                    
                    safe_dispatch_main_async((^{
                        if (iCounter != [arrPhotos count]) {
                            [self ShowAlert:@"Message"
                                   sMessage:[NSString stringWithFormat:@"%d/%ld photo downloaded.", iCounter, (long)arrPhotos.count]];
                        }
                        [self RefreshDisplay];
                        [self hidesLoading];
                    }))
                });
                
            } @catch (NSException *exception) {
                Log_Exception(exception)
            }
        }
     ];
    
    self.refreshIndexPath = indexPath;
}

#pragma mark - SCCaptureCameraControllerDelegate

- (void)PostProcessPhotoID:(int)_iPhotoID iInsItemID:(int)_iInsItemID iParamID:(int)_iParamID {
    [self.viewModel updateInsItem: _iInsItemID withParamID: _iParamID photoID: _iPhotoID];
    [self reloadCellData];
}

- (void)PostPreviewPhotoID:(int)iPhotoID iInsItemID:(int)iInsItemID iParamID:(int)iParamID {
    O_InsItem *oInsItem = [db_InsItem GetInsItem:iInsItemID];
    NSString *sValue = [CommonInspection GetInsItemValue: oInsItem iConfigNumber:iParamID];
    NSArray *photos = [db_Media SearchBulkPhotos: sValue];
    NSArray *arrPhotoID = [photos map:^id _Nonnull(O_Photo *photo) {
        return [@(photo.iPhotoID) stringValue];
    }];
    [Navigator PushDisplayPhotoID:iPhotoID photoArray:arrPhotoID];
}

#pragma mark - InsVideoViewCellDelegate

- (void)DeleteVideo:(int)iParamID iInsItemID:(int)iInsItemID iInsID:(int)iInsID {
    NSArray *arrMedia = [db_Media SearchInsItemVideos:iInsItemID iInsID:iInsID];
    for (O_Video *oVideo in arrMedia) {
        oVideo.bDeleted = 1;

        [db_Media UpdateVideo:oVideo];
        [CommonHelper DeleteVideoFilesIfNeed:oVideo];
    }
    O_InsItem *oInsItem = [db_InsItem GetInsItem:iParamID];
    if (iParamID == 1) {
        oInsItem.sValue1 = @"";
    } else if (iParamID == 2) {
        oInsItem.sValue2 = @"";
    } else if (iParamID == 3) {
        oInsItem.sValue3 = @"";
    } else if (iParamID == 4) {
        oInsItem.sValue4 = @"";
    } else if (iParamID == 5) {
        oInsItem.sValue5 = @"";
    } else if (iParamID == 6) {
        oInsItem.sValue6 = @"";
    }
    [db_InsItem UpdateInsItemValue:oInsItem];

    [self RefreshDisplay];

    [CommonAnalytics trackEvent:@"iOS Ins 3nd Delete Video" meta:@{
            @"ID - ItemID": [NSString stringWithFormat:@"%d - %d", self.viewModel.iInsID, oInsItem.iInsItemID],
            @"Name": [oInsItem.sName length] > 0 ? oInsItem.sName : @""
    }];
}

- (void)GoToVideo:(int)iInsItemID iParamID:(int)iParamID video:(O_Video*)oVideo {
    @try {
        if (![self checkGPSLocationPermission]) return;
        O_InsItem *oInsItem = [db_InsItem GetInsItem:iInsItemID];
        if (oInsItem != nil && oInsItem.iSInsItemID > 0) {
            [self ShowAlert:@"Message" sMessage:@"Edit Inspection does not allow Resume Record Video."];
            return;
        }

        self.iConfigParamID = iParamID;
        //Use iParamID to decide save to which sValue1 - 6
        self.iVideoInsItemID = iInsItemID;
#if 0
        [self startCameraControllerFromViewController:self usingDelegate:self];
#endif

        O_InsItem *oPInsItem = [db_InsItem GetInsItem:oInsItem.iPInsItemID];
        NSTimeInterval totalTime = [oPInsItem.sConfig1 doubleValue] / 1000;
        
        VRNavigationController *nav = [[VRNavigationController alloc] 
            initWithDelegate:self 
                    iParamID:self.iConfigParamID 
                  iInsItemID:self.iVideoInsItemID 
                      iInsID:self.viewModel.iInsID 
             iNotificationID:0 
                   totalTime:totalTime 
                videoQuality:[CommonVideo videoQualityWithInspectionID:self.viewModel.iInsID]
        ];
        
        [nav showCameraWithParentController:self];
    } @catch (NSException *ex) {
        Log_Exception(ex)
    }
}

- (void)GoToVideo:(int)iInsItemID iParamID:(int)iParamID indexPath:(NSIndexPath *)indexPath {
    @try {
        if (![self checkGPSLocationPermission]) return;
        
        O_InsItem *oInsItem = [db_InsItem GetInsItem:iInsItemID];
        if (oInsItem != nil && oInsItem.iSInsItemID > 0) {
            [self ShowAlert:@"Message" sMessage:@"Please add a new item to start recording."];
            return;
        }

        self.refreshIndexPath = indexPath;
        self.iConfigParamID = iParamID;
        //Use iParamID to decide save to which sValue1 - 6
        self.iVideoInsItemID = iInsItemID;
#if 0
        [self startCameraControllerFromViewController:self usingDelegate:self];
#endif

        O_InsItem *oPInsItem = [db_InsItem GetInsItem:oInsItem.iPInsItemID];
        VRNavigationController *nav = [[VRNavigationController alloc] 
            initWithDelegate:self 
                    iParamID:self.iConfigParamID 
                  iInsItemID:self.iVideoInsItemID 
                      iInsID:self.viewModel.iInsID 
             iNotificationID:0 
                   totalTime:[oPInsItem.sConfig1 doubleValue] / 1000
                videoQuality:[CommonVideo videoQualityWithInspectionID:self.viewModel.iInsID]
        ];
        [nav showCameraWithParentController:self];
    } @catch (NSException *ex) {
        Log_Exception(ex)
    }
}

- (void)DisplayVideo:(int)iVideoID sFile:(NSString *)sFile indexPath:(NSIndexPath *)indexPath {
    self.refreshIndexPath = indexPath;
    NSString *filepath   =   [CommonHelper IF_FilePath:sFile];
    NSURL    *fileURL    =   [NSURL fileURLWithPath:filepath];
    [self playVideoWithURL:fileURL];
}

- (BOOL)startCameraControllerFromViewController:(UIViewController*)controller usingDelegate:(id )delegate {
    @try {
        if (![UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]
                || (delegate == nil) || (controller == nil)) { return NO; }

        O_InsItem *oInsItem = [db_InsItem GetInsItem: self.iVideoInsItemID];
        O_InsItem *oPInsItem = [db_InsItem GetInsItem: oInsItem.iPInsItemID];

        UIImagePickerController *cameraUI = [[UIImagePickerController alloc] init];
        cameraUI.sourceType = UIImagePickerControllerSourceTypeCamera;
        if (oPInsItem.sConfig1 == nil || [oPInsItem.sConfig1 isEqualToString:@""]){
            cameraUI.videoMaximumDuration = 600;
        } else {
            @try {
                NSDictionary *json = [oPInsItem.sConfig1 jsonValue];
                if (json != nil) {
                    cameraUI.videoMaximumDuration = [json[@"iD"] intValue];
                } else {
                    cameraUI.videoMaximumDuration = [oPInsItem.sConfig1 intValue];
                }
            } @catch(NSException *ex) {
                [db_Log ProcessException:@"Exception"
                                sMessage:[NSString stringWithFormat:@"%@ %@ || VideoLength %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd), oPInsItem.sConfig1]
                                        exception:ex];
            }
        }

        cameraUI.videoQuality = UIImagePickerControllerQualityTypeMedium;
        cameraUI.mediaTypes = @[(NSString *) kUTTypeMovie];
        cameraUI.allowsEditing = NO;
        cameraUI.delegate = delegate;

        [controller presentViewController:cameraUI animated:YES completion:nil];
        return YES;
    } @catch (NSException *ex) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex];
        return NO;
    }
}

- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<UIImagePickerControllerInfoKey, id> *)info {
    @try {
        NSURL *videoURL = info[UIImagePickerControllerMediaURL];
        [self showLoading:@"Processing video..."];
        
        
        [db_Media SaveVideo:videoURL 
                videoQuality:[CommonVideo videoQualityWithInspectionID:self.viewModel.iInsID]
                 iInsItemID:self.iVideoInsItemID iInsID:self.viewModel.iInsID 
               iConfigParamID:1 iNoticeID:0
                  completion:^(int iVideoID) {
            [self hidesLoading];
            if (iVideoID > 0) {
                if ([[CommonHelper IFGetPref:PrefsKeys.bSaveCamera] boolValue]) {
                    [[PHPhotoLibrary sharedPhotoLibrary] performChanges:^{
                        [PHAssetChangeRequest creationRequestForAssetFromImageAtFileURL:videoURL];
                    } completionHandler:^(BOOL success, NSError *error) {
                        if (success) {
                            DLog(@"Wrote image with metadata to Photo Library %@", videoURL.absoluteString);
                        } else {
                            DLog(@"Error writing image with metadata to Photo Library: %@", error);
                        }
                    }];
                }
                
                [picker dismissViewControllerAnimated:YES completion:nil];
                [self.tableView reloadData];
            } else {
                [self ShowAlert:@"Error" sMessage:@"Video Saving Failed, please try again."];
            }
        }];
    } @catch (NSException *ex) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:ex];
    }
}

- (void)video:(NSString*)videoPath didFinishSavingWithError:(NSError*)error contextInfo:(void*)contextInfo {
    if (error) {
        [self ShowAlert: @"Error" sMessage: @"Video Saving Failed"];
    }
}

- (void)ScrollBackTableView:(UITextField *)oTextField {

}

- (void)ScrollToTableView:(UITextField *)oTextField {

}

- (void)UpdateInsItem_CurrentList:(O_InsItem *)oInsItem {

}

#pragma mark - ScanViewDelegate
- (void)scannerCameraController:(SICameraScannerViewController *)viewController postProcessPhotoID:(NSInteger)iPhotoID iInsItemID:(NSInteger)iInsItemID iParamID:(NSInteger)iParamID {
    @weakify(self)
    [UIApplication.sharedApplication.topMost ShowAlert: nil
           sMessage: @"Scan Saved"
       cancelButton: @"OK"
       cancelAction:^(UIAlertAction * _Nonnull action) {
        @strongify(self)
        [self.presentedViewController dismissViewControllerAnimated:YES completion:nil];
        [self PostProcessPhotoID:(int)iPhotoID iInsItemID:(int)iInsItemID iParamID:(int)iParamID];
    }];
}

- (BOOL)willDismissNavigationController:(ScanNavigationController *)scanNavigationController {
    return YES;
}

#pragma mark - VRNavigationControllerDelegate
- (BOOL)willDismissVRNavigationController:(VRNavigationController *)navigationController {
    return YES;
}

#pragma mark - InsSignViewCellDelegate

- (void)GoToSign:(int)iInsItemID sTempTitle:(NSString *)sTempTitle indexPath:(NSIndexPath *)indexPath {
    SignatureViewController *oView = [self.storyboard instantiateViewControllerWithIdentifier:@"Signature"];
    oView.iInsID  = self.viewModel.iInsID;
    oView.iInsItemID = iInsItemID;
    oView.sTitle = sTempTitle;
    [CommonAnalytics trackEvent:@"iOS Ins 3nd Goto" meta:@{
        @"ID - ItemID": [NSString stringWithFormat:@"%d - %d", self.viewModel.iInsID, iInsItemID],
        @"Name": sTempTitle ?: @""
    }];
    [self.navigationController pushViewController:oView animated:YES];
    self.refreshIndexPath = indexPath;
}

- (void)Download_ShowImage:(int)iPhotoID indexPath: (NSIndexPath *) indexPath {
    [self showLoading:@"Connecting to Server"];
    O_Photo *oPhoto = [db_Media GetPhoto:iPhotoID];
    NSDictionary *oVideoToken = @{
            @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
            @"sToken": [CommonUser currentToken],
            @"iPhotoID": [NSString stringWithFormat:@"%d", oPhoto.iSPhotoID]
    };

    [self showLoading:@"Processing Request"];
    dispatch_background_async(^{
        NSDictionary *oReturn = [IFConnection PostRequest:EndpointGetPhoto oParams:oVideoToken.mutableCopy];
        if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]) {
            NSData *oData = [IFConnection DownloadFile_Data:[NSURL URLWithString:[oReturn valueForKey:@"sURL"]]];
            safe_dispatch_main_async((^{
                if (oData != nil) {
                    NSString *sPath = [[if_AppDelegate GetGlobalVar] valueForKeyPath:@"sFilePath"];
                    NSString *sUniqueFileName = [CommonHelper GetUniqueFileName];
                    NSString *sPhotoName = [NSString stringWithFormat:@"p_%@.jpg", sUniqueFileName];
                    NSString *sPhotoPath = [sPath stringByAppendingPathComponent:sPhotoName];

                    [oData writeToFile:sPhotoPath atomically:NO];
                    oPhoto.sFile = sPhotoName;

                    /// if not saving thumb
                    if (![CommonHelper IF_FileExist:oPhoto.sThumb]) {
                        NSString *sThumbName = [NSString stringWithFormat:@"pt_%@.jpg", sUniqueFileName];
                        NSString *sThumbPath = [sPath stringByAppendingPathComponent:sThumbName];
                        UIImage *oTempImage = [CommonHelper processImage_Thumb:[UIImage imageWithData:oData]];
                        [UIImageJPEGRepresentation(oTempImage, 0.8) writeToFile:sThumbPath atomically:YES];
                        oPhoto.sThumb = sThumbName;
                    }

                    [db_Media UpdatePhoto:oPhoto];
                    [self reloadCellData];
                } else {
                    [self ShowAlert:@"Error" sMessage:@"Error when download photo, please try again."];
                }

                [self hidesLoading];
            }));
        } else {
            safe_dispatch_main_async(^{
                [self hidesLoading];
            });
        }
    });
}

- (void)DeleteSign:(int)iInsItemID {
    [db_Delete DeletePhoto_ByItemID:iInsItemID];
    [db_Media DeleteSignature:iInsItemID];
    [self RefreshDisplay];
}

#pragma mark - Tip View

- (void)onClickedSectionInfo: (UIButton *)sender {
    NSString *promptText = self.viewModel.sParentPromptText;

    CGRect headerRect = self.itemNameView.frame;
    CGRect rect = [self.tableView convertRect: headerRect toView: self.view];

    [self addTipBackgroundView];

    BOOL isBottom = rect.origin.y > self.view.bounds.size.height - 200;
    [self addTipView:sender rect:rect promptText:promptText bottom:isBottom];
}

- (void)onClickedRowInfo:(UIButton *)sender {
    O_Item *oItem = [self getItemWithSender: sender];
    UITableViewCell *cell = (UITableViewCell *) sender.superview.superview;
    NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];

    if (oItem == nil || indexPath == nil) return;

    CGRect rect = [self getCellPosition: indexPath];
    [self addTipBackgroundView];

    BOOL isBottom = rect.origin.y > self.view.bounds.size.height - 200;
    NSString *promptText = [db_Common GetInstruction:oItem.iSLayoutID sConfig:oItem.sConfig];
    [self addTipView:sender rect:rect promptText:promptText bottom:isBottom];
}

- (void)addTipView:(UIButton*)sender rect:(CGRect)rect promptText:(NSString*)promptText bottom:(BOOL)isBottom {
    RCEasyTipPreferences *preferences = [[RCEasyTipPreferences alloc] initWithDefaultPreferences];
    preferences.drawing.backgroundColor = [UIColor colorWithRed:78.0f/255 green:105.0f/255 blue:1.0f alpha:1.0f];
    preferences.drawing.arrowPostion = isBottom? ArrowPositionBottom : ArrowPositionTop;
    preferences.animating.showDuration = 1.5;
    preferences.animating.dismissDuration = 0.5;
    preferences.animating.dismissTransform = CGAffineTransformMakeTranslation(0, -15);
    preferences.animating.showInitialTransform = CGAffineTransformMakeTranslation(0, -15);
    preferences.positioning.maxWidth = MIN(UIScreen.mainScreen.bounds.size.width * 5 / 6, 500.0);

    RCEasyTipView *tipView = [[RCEasyTipView alloc] initWithPreferences:preferences];
    tipView.text = promptText;
    tipView.delegate = self;

    CGFloat offsetX = 10, y;
    if ([sender.superview.superview isKindOfClass: UITableViewCell.class]) {
        y = rect.origin.y < 0 ? 37.0 : rect.origin.y + (isBottom ? 5 : 37.0);
    } else {
        CGRect rect = [self.itemNameView convertRect: sender.frame  toView: self.view];
        y = CGRectGetMaxY(rect);
    }
    tipView.frameRect = CGRectMake(offsetX, y, rect.size.width, rect.size.height);
    tipView.tipPointX = INS_FULL_LEFT_MARGIN - offsetX + 15;
    [tipView showAnimated:YES forView:sender withinSuperView:self.view];

    self.tipView = tipView;
}

- (void)addTipBackgroundView {
    UIView *bgView = [UIView new];
    bgView.backgroundColor = [UIColor colorWithWhite:0.0 alpha:0.3];

    [self.view addSubview:bgView];
    [bgView mas_makeConstraints: ^(MASConstraintMaker *maker) {
        maker.edges.equalTo(self.view);
    }];

    UITapGestureRecognizer *tapRecognizer = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tipBackgroundTapAction:)];
    tapRecognizer.numberOfTapsRequired = 1;
    [bgView addGestureRecognizer:tapRecognizer];

    self.tipBGView = bgView;
}

- (void)tipBackgroundTapAction:(UITapGestureRecognizer *)tap {
    if (self.tipView) {
        @weakify(self)
        [self.tipView dismissWithCompletion:^{
            @strongify(self)
            self.tipView = nil;
        }];
    }
}

- (void)didShowTip:(RCEasyTipView *)tipView {

}

- (void)didDismissTip:(RCEasyTipView *)tipView {
    [self.tipBGView removeFromSuperview];
    self.tipBGView = nil;
}

- (CGRect)getCellPosition:(NSIndexPath*) indexPath {
    CGRect rectInTableView = [self.tableView rectForRowAtIndexPath:indexPath];
    CGRect rectInSuperview = [self.tableView convertRect:rectInTableView toView:[self.tableView superview]];
    return rectInSuperview;
}

- (void)timerStart {
    [self.timerManager start];
}

- (void)timerEnd {
    [self.timerManager end];
}

@end
