//
//  CommonHelper+Extensions.swift
//  SnapInspect3
//
//  Created by <PERSON> on 6/2/25.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

import Foundation

extension CommonHelper {
    @objc
    static func getPref(_ key: String) -> String? {
        var result: String?
        environment.prefsOps { prefsManager in
            result = prefsManager.ifGetPref(key)
        }
        return result
    }

    @discardableResult
    @objc
    static func savePref(_ key: String, sValue value: String?) -> Bool {
        var result = false
        environment.prefsOps { prefsManager in
            result = prefsManager.ifSavePref(key, sValue: value)
        }
        return result
    }

    @objc
    static func getPrefBool(_ key: String) -> Bool {
        var result = false
        environment.prefsOps { prefsManager in
            result = prefsManager.ifGetPrefBool(key)
        }
        return result
    }

    @objc
    static func getPrefBool(_ key: String, defaultValue: Bool) -> <PERSON><PERSON> {
        var result = defaultValue
        environment.prefsOps { prefsManager in
            result = prefsManager.ifGetPrefBool(key, defaultValue: defaultValue)
        }
        return result
    }

    @objc
    static func getPrefInt(_ key: String, defaultValue: Int) -> Int {
        var result = defaultValue
        environment.prefsOps { prefsManager in
            result = (try? prefsManager.getInt(forKey: key)) ?? defaultValue
        }
        return result
    }

    @objc
    static func savePrefInt(_ key: String, value: Int) {
        environment.prefsOps { prefsManager in
            try? prefsManager.setInt(value, forKey: key)
        }
    }

    @objc
    static func getPrefDouble(_ key: String, defaultValue: Double) -> Double {
        var result = defaultValue
        environment.prefsOps { prefsManager in
            result = (try? prefsManager.getDouble(forKey: key)) ?? defaultValue
        }
        return result
    }

    @objc
    static func savePrefDouble(_ key: String, value: Double) {
        environment.prefsOps { prefsManager in
            try? prefsManager.setDouble(value, forKey: key)
        }
    }

    @objc
    static func removePref(_ key: String) {
        environment.prefsOps { prefsManager in
            prefsManager.ifRemovePref(key)
        }
    }

    @objc
    static func clearAllPreferences() {
        environment.prefsOps { prefsManager in
            prefsManager.clearAllPreferences()
        }
    }
}
