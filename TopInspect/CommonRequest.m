//
//  CommonRequest.m
//  SnapInspect3
//
//  Created by <PERSON> on 16/02/18.
//  Copyright © 2018 SnapInspect. All rights reserved.
//

#import "CommonRequest.h"

@implementation CommonRequest

+ (NSDictionary *)SI_UploadFileURL: (NSURL *)fileURL iPropertyID: (int)iPropertyID {
    @try {
        NSDictionary *oDic = @{
                @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
                @"sToken": [CommonUser currentToken],
                @"dtDateTaken": [CommonHelper GetDateString:[NSDate date]],
                @"iPropertyID": [@(iPropertyID) stringValue],
                @"sClientFileName": fileURL.lastPathComponent,
                @"iSize": [@([CommonHelper GetFileSizeFromPath:[fileURL path]]) stringValue]
        };
        NSDictionary *oReturn = [IFConnection PostRequest:EndpointUploadAssetFile oParams:[oDic mutableCopy]];
        return oReturn;
    } @catch (NSException *ex) {
        
    }
    
    return nil;
}

+(NSDictionary *)SI_UploadFile:(O_File *)oFile {
    @try {
        NSDictionary *oDic = @{
                @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
                @"sToken": [CommonUser currentToken],
                @"dtDateTaken": oFile.dtDateTime,
                @"iPropertyID": [NSString stringWithFormat:@"%d", oFile.iSObjectID],
                @"sClientFileName": oFile.sFile,
                @"iSize": [NSString stringWithFormat:@"%d", oFile.iSize]
        };
        NSDictionary *oReturn = [IFConnection PostRequest:EndpointUploadAssetFile oParams:[oDic mutableCopy]];
        return oReturn;
    } @catch (NSException *ex) {
        
    }
    
    return nil;
}

+(NSDictionary *)SI_UploadFile_ConfirmFileID:(int)fileID {
    @try {
        NSDictionary *oReturn = [IFConnection PostRequest:EndpointUploadAssetFileSuccess oParams:[@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"iSFileID": [NSString stringWithFormat:@"%d", fileID]} mutableCopy]];
        return oReturn;
    } @catch (NSException *ex) {

    }
    return nil;
}

+(NSDictionary *)SI_UploadFile_ConfirmFile:(O_File *)oFile {
   return [self SI_UploadFile_ConfirmFileID: oFile.iSFileID];
}

+(NSDictionary *)SI_GetFile_DownloadURL:(int)iFileID {
    @try {
        NSDictionary *oReturn;
        NSDictionary *params = @{
                @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
                @"sToken": [CommonUser currentToken],
                @"iFileID": [NSString stringWithFormat:@"%d", iFileID]
        };
        oReturn = [IFConnection PostRequest:EndpointGetAssetFile oParams:[params mutableCopy]];
        if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]) {
            return oReturn;
        }
        return nil;
    } @catch (NSException *ex) {

    }
    return nil;
}

+ (BOOL)uploadFile:(O_File *)oFile {
    if ([CommonHelper IF_FileExist:oFile.sFile]) {
        NSDictionary *oReturn = [CommonRequest SI_UploadFile:oFile];
        NSString *sUploadURL;
        if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]) {
            sUploadURL = [oReturn valueForKey:@"sURL"];
            oFile.iSFileID = [[oReturn[@"oFile"] valueForKey:@"iFileID"] intValue];
            [db_Media UpdateFile:oFile];
        } else {
            NSDictionary *oReturn1 = [CommonRequest SI_UploadFile:oFile];
            if (oReturn1 != nil && [[oReturn1 valueForKey:@"success"] boolValue]) {
                sUploadURL = [oReturn1 valueForKey:@"sURL"];

                oFile.iSFileID = [[oReturn[@"oFile"] valueForKey:@"iFileID"] intValue];
                [db_Media UpdateFile:oFile];
            } else {
                //bSyncSuccess = false;
                //sMessage = [NSString stringWithFormat:@"%@ %@", sMessage, @"106"];
                return false;
            }
        }
        if (sUploadURL != nil && [sUploadURL length] > 10) {
            if (![CommonS3 UploadFileToS3:oFile.sFile sURL:sUploadURL]) {
                if (![CommonS3 UploadFileToS3:oFile.sFile sURL:sUploadURL]) {
                    //bSyncSuccess = false;
                    //sMessage = [NSString stringWithFormat:@"%@ %@", sMessage, @"107"];
                    return false;
                } else {
                    NSDictionary *confirmReturn = [CommonRequest SI_UploadFile_ConfirmFileID: oFile.iSFileID];
                    if (confirmReturn == nil || [[confirmReturn valueForKey:@"success"] boolValue] == NO) {
                        NSDictionary *oReturn1 = [CommonRequest SI_UploadFile_ConfirmFileID: oFile.iSFileID];
                        if (oReturn1 == nil || [[oReturn1 valueForKey:@"success"] boolValue] == NO) {
                            return false;
                        }
                    }
                    oFile.bUploaded = true;
                    //oFile.iSFileID =
                    [db_Media UpdateFile:oFile];
                }
            } else {
                NSDictionary *confirmReturn = [CommonRequest SI_UploadFile_ConfirmFileID: oFile.iSFileID];
                if (confirmReturn == nil || [[confirmReturn valueForKey:@"success"] boolValue] == NO) {
                    NSDictionary *oReturn1 = [CommonRequest SI_UploadFile_ConfirmFileID: oFile.iSFileID];
                    if (oReturn1 == nil || [[oReturn1 valueForKey:@"success"] boolValue] == NO) {
                        return false;
                    }
                }
                oFile.bUploaded = true;
                //oFile.iSFileID =
                [db_Media UpdateFile:oFile];
            }

        } else {
            return false;
        }
    }

    return true;
}
@end
