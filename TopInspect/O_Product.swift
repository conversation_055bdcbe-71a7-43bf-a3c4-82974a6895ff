//
//  O_Product.swift
//  SnapInspect3
//
//  Created by <PERSON> on 2024/7/9.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

import FMDB
import Foundation
import SwiftDate

// Default unit name
let kUnitName = "Unit"

struct O_Product: Equatable, Identifiable {
    var iProductID: Int
    var iSProductID: Int
    var sCheckListID: String
    var iCompanyID: Int
    var sSKU: String
    var sName: String
    var sModel: String
    var sDesp: String
    var sAssociateArea: String
    var sAssociateItem: String
    var dUnitCost: Double
    var bAllowEdit: Bool
    var bOneOffCost: Bool
    var sProductCategory: String
    var sUnitName: String
    var sURL: String
    var sImage: String
    var sCustom1: String
    var sCustom2: String
    var bArchive: Bool
    var bDeleted: Bool
    var iCreatedBy: Int
    var iUpdatedBy: Int
    var dtUpdate: Date
    var dtDateTime: Date
    
    var id: Int { iProductID }
    
    var raws: [Any] {
        [iSProductID, sCheckListID, iCompanyID, sSKU, sName, sModel, sDesp, sAssociateArea, sAssociateItem, dUnitCost,
         bAllowEdit, bOneOffCost, sProductCategory, sUnitName, sURL, sImage, sCustom1, sCustom2,
         bArchive, bDeleted, iCreatedBy, iUpdatedBy, dtUpdate, dtDateTime]
    }
    
    init(
        iProductID: Int,
        iSProductID: Int,
        sCheckListID: String,
        iCompanyID: Int,
        sSKU: String,
        sName: String,
        sModel: String,
        sDesp: String,
        sAssociateArea: String,
        sAssociateItem: String,
        dUnitCost: Double,
        bAllowEdit: Bool,
        bOneOffCost: Bool,
        sProductCategory: String,
        sUnitName: String,
        sURL: String,
        sImage: String,
        sCustom1: String,
        sCustom2: String,
        bArchive: Bool,
        bDeleted: Bool,
        iCreatedBy: Int,
        iUpdatedBy: Int,
        dtUpdate: Date,
        dtDateTime: Date
    ) {
        self.iProductID = iProductID
        self.iSProductID = iSProductID
        self.sCheckListID = sCheckListID
        self.iCompanyID = iCompanyID
        self.sSKU = sSKU
        self.sName = sName
        self.sModel = sModel
        self.sDesp = sDesp
        self.sAssociateArea = sAssociateArea
        self.sAssociateItem = sAssociateItem
        self.dUnitCost = dUnitCost
        self.bAllowEdit = bAllowEdit
        self.bOneOffCost = bOneOffCost
        self.sProductCategory = sProductCategory
        self.sUnitName = sUnitName
        self.sURL = sURL
        self.sImage = sImage
        self.sCustom1 = sCustom1
        self.sCustom2 = sCustom2
        self.bArchive = bArchive
        self.bDeleted = bDeleted
        self.iCreatedBy = iCreatedBy
        self.iUpdatedBy = iUpdatedBy
        self.dtUpdate = dtUpdate
        self.dtDateTime = dtDateTime
    }
    
    init(resultSet: FMResultSet) {
        iProductID = Int(resultSet.int(forColumn: "iProductID"))
        iSProductID = Int(resultSet.int(forColumn: "iSProductID"))
        sCheckListID = resultSet.string(forColumn: "sCheckListID") ?? ""
        iCompanyID = Int(resultSet.int(forColumn: "iCompanyID"))
        sSKU = resultSet.string(forColumn: "sSKU") ?? ""
        sName = resultSet.string(forColumn: "sName") ?? ""
        sModel = resultSet.string(forColumn: "sModel") ?? ""
        sDesp = resultSet.string(forColumn: "sDesp") ?? ""
        sAssociateArea = resultSet.string(forColumn: "sAssociateArea") ?? ""
        sAssociateItem = resultSet.string(forColumn: "sAssociateItem") ?? ""
        dUnitCost = resultSet.double(forColumn: "dUnitCost")
        bAllowEdit = resultSet.bool(forColumn: "bAllowEdit")
        bOneOffCost = resultSet.bool(forColumn: "bOneOffCost")
        sProductCategory = resultSet.string(forColumn: "sProductCategory") ?? ""
        sUnitName = resultSet.string(forColumn: "sUnitName") ?? ""
        sURL = resultSet.string(forColumn: "sURL") ?? ""
        sImage = resultSet.string(forColumn: "sImage") ?? ""
        sCustom1 = resultSet.string(forColumn: "sCustom1") ?? ""
        sCustom2 = resultSet.string(forColumn: "sCustom2") ?? ""
        bArchive = resultSet.bool(forColumn: "bArchive")
        bDeleted = resultSet.bool(forColumn: "bDeleted")
        iCreatedBy = Int(resultSet.int(forColumn: "iCreatedBy"))
        iUpdatedBy = Int(resultSet.int(forColumn: "iUpdatedBy"))
        dtUpdate = resultSet.date(forColumn: "dtUpdate") ?? .date1970
        dtDateTime = resultSet.date(forColumn: "dtDateTime") ?? .date1970
    }
    
    init(dictionary: [AnyHashable: Any]) {
        iProductID = 0
        iSProductID = dictionary["iProductID"] as? Int ?? 0
        sCheckListID = dictionary["sCheckListID"] as? String ?? ""
        iCompanyID = dictionary["iCompanyID"] as? Int ?? 0
        sSKU = dictionary["sSKU"] as? String ?? ""
        sName = dictionary["sName"] as? String ?? ""
        sModel = dictionary["sModel"] as? String ?? ""
        sDesp = dictionary["sDesp"] as? String ?? ""
        sAssociateArea = dictionary["sAssociateArea"] as? String ?? ""
        sAssociateItem = dictionary["sAssociateItem"] as? String ?? ""
        dUnitCost = dictionary["dUnitCost"] as? Double ?? 0
        bAllowEdit = dictionary["bAllowEdit"] as? Bool ?? false
        bOneOffCost = dictionary["bOneOffCost"] as? Bool ?? false
        sProductCategory = dictionary["sProductCategory"] as? String ?? ""
        sUnitName = dictionary["sUnitName"] as? String ?? ""
        sURL = dictionary["sURL"] as? String ?? ""
        sImage = dictionary["sImage"] as? String ?? ""
        sCustom1 = dictionary["sCustom1"] as? String ?? ""
        sCustom2 = dictionary["sCustom2"] as? String ?? ""
        bArchive = dictionary["bArchive"] as? Bool ?? false
        bDeleted = dictionary["bDeleted"] as? Bool ?? false
        iCreatedBy = dictionary["iCreatedBy"] as? Int ?? 0
        iUpdatedBy = dictionary["iUpdatedBy"] as? Int ?? 0
        dtUpdate = (dictionary["dtUpdate"] as? String)?.toDate(yyyyMMddHHmmss)?.date ?? .date1970
        dtDateTime = (dictionary["dtDateTime"] as? String)?.toDate(yyyyMMddHHmmss)?.date ?? .date1970
    }
    
    var debugDescription: String {
        """
        O_Product: {
             iProductID: \(iProductID),
             iSProductID: \(iSProductID),
             sSku: \(sSKU),
             sName: \(sName),
             sModel: \(sModel),
             sDescription: \(sDesp),
             sAssociateArea: \(sAssociateArea),
             sAssociateItem: \(sAssociateItem),
             dUnitCost: \(dUnitCost),
             bAllowEdit: \(bAllowEdit),
             bOneOffCost: \(bOneOffCost),
             sProductCategory: \(sProductCategory),
             sUnitName: \(sUnitName),
             sURL: \(sURL),
             sImage: \(sImage),
             sCustom1: \(sCustom1),
             sCustom2: \(sCustom2),
             bArchive: \(bArchive),
             bDeleted: \(bDeleted),
             iCreatedBy: \(iCreatedBy),
             iUpdatedBy: \(iUpdatedBy),
             dtUpdate: \(dtUpdate),
             dtDateTime: \(dtDateTime)
        }
        """
    }
    
    var fixedUnitName: String {
        sUnitName.ifEmpty(then: kUnitName)
    }
}

// MARK: - Extension

extension O_Product {
    var imageFileURL: URL? {
        // Check if image is empty or not exist
        guard !sImage.isEmpty, let fileName = URL(string: sImage)?.lastPathComponent else {
            return nil
        }
        
        // If image is exist, return image file URL
        let sFile = "\(Constants.kProductPhotoDir)/\(fileName)"
        guard var url = CommonHelper.getRootFileURL() else { return nil }
        if #available(iOS 16.0, *) {
            url.append(component: sFile, directoryHint: .inferFromPath)
        } else {
            url.appendPathComponent(sFile)
        }
        return url
    }
    
    var imageURL: URL? {
        if let fileURL = imageFileURL, FileManager.default.fileExists(atPath: fileURL.path) {
            return fileURL
        }
        
        // Check if image directory is exist and create if not
        validOrCreatesProductPhotoFolder()
        
        // otherwise, return image URL
        var urlComps = URLComponents(string: Endpoint.getProductPhoto.rawValue)
        urlComps?.queryItems = [
            .init(name: "iProductID", value: "\(iSProductID)"),
            .init(name: "iCustomerID", value: "\(CommonUser.iCustomerID)"),
            .init(name: "sToken", value: CommonUser.currentToken)
        ]
        return urlComps?.url
    }
    
    private func validOrCreatesProductPhotoFolder() {
        guard let url = CommonHelper.getRootFileURL()?.appendingPathComponent(Constants.kProductPhotoDir),
              !FileManager.default.fileExists(atPath: url.path) else {
            return
        }
        try? FileManager.default.createDirectory(at: url, withIntermediateDirectories: true, attributes: nil)
    }
}

extension O_Product {
    var associateItems: [String] {
        guard let items = CommonJson.oJsonString(toArray: sAssociateItem) as? [String] else {
            return []
        }
        return items
    }
}
