//
//  if_DisplayPhoto_2nd.swift
//  SnapInspect3
//
// Created by <PERSON> on 2020/3/11.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

import Extensions
import FontAwesome
import UIKit
import AppFeatures

final class if_DisplayPhoto_2nd: UIViewController {
    enum Notifications: String, NotificationName {
        case deletePhoto, updatePhoto, willDeleteTaskPhoto
    }

    private static let allPhotosID = "allPhotos"
    private static let commentLibraryID = "CommentLibrary"
    private static let annotationID = "if_imageannotation"
    
    @objc public var iPhotoID: Int = 0
    @objc public var photoArray: [String] = []
    
    @IBOutlet private var collectionView: UICollectionView!
    @IBOutlet private var trashButton: UIButton!
    @IBOutlet private var allPhotosButton: UIButton!
    @IBOutlet private var photoIndexLabel: UILabel!

    // If need to hidden the navigation bar, set this to true
    private var needHideNavigationBar = false
    
    private lazy var flowLayout: UICollectionViewFlowLayout = {
        let layout = UICollectionViewFlowLayout()
        layout.sectionInset = .zero
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 0.0
        layout.minimumInteritemSpacing = 0.0
        return layout
    }()
    
    private lazy var annotationItem: UIBarButtonItem = {
        let item = UIBarButtonItem(
            image: UIImage(named: "icon_annatation"), style: .plain,
            target: self, action: .annotationItemTapped
        )
        item.tintColor = .white
        return item
    }()
    
    private lazy var rotationItem: UIBarButtonItem = {
        let item = UIBarButtonItem(
            image: UIImage(named: "icon_rotate"), style: .plain,
            target: self, action: .rotationItemItemTapped
        )
        item.tintColor = .white
        return item
    }()

    private lazy var savePhotoCommentItem: UIBarButtonItem = {
        let item = UIBarButtonItem(
            barButtonSystemItem: .save,
            target: self, action: .savePhotoCommentItemTapped
        )
        item.tintColor = .white
        return item
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        navigationController?.applyBlueTheme()
        navigationItem.title = "Photo"
        
        allPhotosButton.setTitle("All Photos", for: .normal)
        allPhotosButton.setTitleColor(.color_053BFF(), for: .normal)
        allPhotosButton.titleLabel?.font = .sfCompactText_Semibold(14.0)
        allPhotosButton.addTarget(self, tapped: .allPhotosButtonTapped)
        
        trashButton.setImage(UIImage(named: "icon_trash_can_red"), for: .normal)
        trashButton.addTarget(self, tapped: .trashButtonTapped)
        
        photoIndexLabel.textColor = .color_4A4A4A()
        photoIndexLabel.font = .sfCompactText_Regular(14.0)

        collectionView.isPagingEnabled = true
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.register(cellType: DisplayPhotoViewCell.self)
        collectionView.collectionViewLayout = flowLayout
        collectionView.keyboardDismissMode = .onDrag

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
            self.scrollingToSelectedPhoto()
        }
        
        displayLeftCancelItemButtonIfNeeded()
        if isBeingPresentedAsRootController(), photoArray.count < 2 {
            navigationController?.applyBlueTheme()
            photoIndexLabel.isHidden = true
            allPhotosButton.isHidden = true
        }
        
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardDidHide), name: UIResponder.keyboardDidHideNotification, object: nil)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if let nav = navigationController, nav.isNavigationBarHidden {
            // if the previous view controller is hidden the navigation bar, we need to show it here
            // And remember to hide it when this view controller is popped
            needHideNavigationBar = true
            nav.isNavigationBarHidden = false
        }
        guard let photo = currentPhoto,
              let cell = collectionView.visibleCells.first as? DisplayPhotoViewCell else { return }
        cell.updatePhoto(photo)
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        if isBeingPopped(), needHideNavigationBar {
            navigationController?.isNavigationBarHidden = true
        }
    }
}

extension if_DisplayPhoto_2nd {
    @objc private func keyboardDidHide() {
        for cell in collectionView.visibleCells {
            cell.top = 0.0
        }
    }
}

extension if_DisplayPhoto_2nd: UICollectionViewDelegateFlowLayout {
    public func collectionView(
        _ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath
    ) -> CGSize {
        var bottomPadding: CGFloat = 0.0
        if #available(iOS 11.0, *) {
            bottomPadding = if_AppDelegate.shared()?.window.safeAreaInsets.bottom ?? 0
        }
        return .init(width: view.width, height: view.height - 49.0 - bottomPadding)
    }
}

extension if_DisplayPhoto_2nd: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return photoArray.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(for: indexPath) as DisplayPhotoViewCell
        cell.top = 0.0
        if let iPhotoID = Int32(photoArray[indexPath.row]), let photo = db_Media.getPhoto(iPhotoID) {
            cell.updatePhoto(photo)
        }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        guard let cell = cell as? DisplayPhotoViewCell,
              let iPhotoID = Int32(photoArray[indexPath.row]),
              let photo = db_Media.getPhoto(iPhotoID) else { return }
        
        let insItem = db_Inspection.getItem(photo.iInsItemID)
        let iPInsItemID = insItem?.iPInsItemID ?? 0
        CommonUI.showsCommentsLibrary(
            cell.tvComments, withInsId: photo.iInsID,
            insItemId: photo.iInsItemID, pInsItemId: iPInsItemID
        ) { [weak self] sComments in
            guard let id = self?.iPhotoID, let text = sComments
            else { return }

            guard let photo = db_Media.getPhoto(Int32(id)),
                  photo.sComments != text else { return }
            photo.sComments = text
            photo.sField1 = photo.hasUploadedWhenEditingIns ?
                CommonJson.addKeyValueString(PrefsKeys.kUpdatePhotoComment, sValue: "1", sJson: photo.sField1) :
                CommonJson.removeKey(PrefsKeys.kUpdatePhotoComment, sJson: photo.sField1)
            db_Media.update(photo)
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, didEndDisplaying cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        guard let cell = cell as? DisplayPhotoViewCell else { return }
        cell.tvComments.inputAccessoryView = nil
    }
   
    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if decelerate {
            debounce(perform: #selector(scrollingFinished))
        }
    }

    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        debounce(perform: #selector(scrollingFinished))
    }
}

extension if_DisplayPhoto_2nd {
    private var currentPage: Int {
        guard photoArray.count > 0 else { return 0 }
        return min(photoArray.count - 1, max(collectionView.currentPage, 0))
    }
    
    private var currentPhoto: O_Photo? {
        guard let photoId = Int(photoArray[currentPage]) else { return nil }
        return db_Media.getPhoto(Int32(photoId))
    }
    
    private func closeViewController() {
        if isBeingPresentedAsRootController() {
            navigationController?.dismiss(animated: true, completion: nil)
        } else {
            navigationController?.popViewController(animated: true)
        }
    }
    
    @objc fileprivate func allPhotosButtonTapped() {
        if let all = navigationController?.previousViewController() as? if_AllPhotos {
            navigationController?.popToViewController(all, animated: true)
        } else {
            guard let all = storyboard?.identifier(Self.allPhotosID) as? if_AllPhotos else {
                return
            }
            all.photoArray = photoArray
            all.viewPhoto = { [weak self] in
                guard let self = self else { return }
                self.iPhotoID = $0
                self.scrollingToSelectedPhoto()
                self.navigationController?.popToViewController(self, animated: true)
            }
            navigationController?.pushViewController(all, animated: true)
        }
    }
    
    @objc fileprivate func trashButtonTapped() {
        showAlert(
            "Attention", sMessage: "Are you sure to delete this photo?", cancelButton: "Cancel", doneButton: "OK"
        ) { [weak self] _ in
            guard let self = self, let photo = self.currentPhoto else { return }

            // will delete task photo with the server photo id
            NotificationCenter.default.post(name: Notifications.willDeleteTaskPhoto.name, object: photo.iSPhotoID)

            let isFinished = db_Media.deletePhoto(byId: photo.iPhotoID)
            if isFinished {
                db_Media.deletePhotoID(fromInsItem: photo)
                self.removePhotoID(photo.iPhotoID)
            }
            // delete photo from the local database
            NotificationCenter.default.post(name: Notifications.deletePhoto.name, object: photo.iPhotoID)

            CommonAnalytics.trackEvent("Delete photo", meta: ["photoID": photo.iPhotoID])
            if self.photoArray.isEmpty {
                self.closeViewController()
            } else {
                self.collectionView.reloadData()
                self.scrollingFinished()
            }
        }
    }
    
    @objc fileprivate func annotationItemTapped() {
        guard let photo = currentPhoto,
              let annotation = storyboard?.identifier(Self.annotationID) as? if_ImageAnnotation
        else { return }
        
        annotation.modalPresentationStyle = .fullScreen
        annotation.sPath = photo.sFile
        annotation.didSavePhoto = { [weak self] in
            NotificationCenter.default.post(
                name: Notifications.updatePhoto.name, object: self?.currentPhoto?.iPhotoID
            )
        }
        present(annotation, animated: true, completion: nil)
    }
    
    @objc fileprivate func rotationItemItemTapped() {
        guard let photo = currentPhoto else { return }
        
        DispatchQueue.global(qos: .utility).async {
            let filePath = CommonHelper.if_FilePath(photo.sFile), thumbPath = CommonHelper.if_FilePath(photo.sThumb)
            
            if let file = UIImage(contentsOfFile: filePath),
               let rotated = file.sd_rotatedImage(withAngle: -.pi / 2, fitSize: true),
               let data = rotated.sd_imageData()
            {
                do {
                    try data.write(to: URL(fileURLWithPath: filePath))
                    SDImageCache.shared.removeImage(forPath: filePath)
                } catch {}
            }
            
            if let thumb = UIImage(contentsOfFile: thumbPath),
               let rotated = thumb.sd_rotatedImage(withAngle: -.pi / 2, fitSize: true),
               let data = rotated.sd_imageData()
            {
                do {
                    try data.write(to: URL(fileURLWithPath: thumbPath))
                    SDImageCache.shared.removeImage(forPath: thumbPath)
                } catch {}
            }
            
            DispatchQueue.main.async {
                self.collectionView.reloadItems(at: [IndexPath(item: self.currentPage, section: 0)])
            }
        }
    }

    @objc fileprivate func savePhotoCommentItemTapped() {
        CommonUI.hideKeyboard()
        
        guard NetworkConnection.isNetworkReachable else {
            showAlert("Error", sMessage: "Please connect to internet to save comments.")
            return
        }
        
        guard let photo = currentPhoto,
              let cell = collectionView.visibleCells.first as? DisplayPhotoViewCell else { return }
        let sComments = cell.sComments ?? ""
        
        showLoading("Saving...")
        DispatchQueue.global(qos: .userInitiated).async {
            let result = IFConnection.postRequest(
                .savePhotoComments,
                oParams: [
                    "iCustomerID": "\(CommonUser.iCustomerID)",
                    "sToken": CommonUser.currentToken,
                    "iSPhotoID": "\(photo.iSPhotoID)",
                    "sComments": sComments
                ]
            )

            DispatchQueue.main.async {
                self.hidesLoading()
            }
            
            if let result, result["success"] as? Bool != true,
               let message = result["message"] as? String
            {
                self.showAlert("Error", sMessage: message)
            }
        }
    }

    @objc private func scrollingFinished() {
        photoIndexLabel.text = "\(currentPage + 1) / \(photoArray.count)"
        guard let photo = currentPhoto else { return }
        iPhotoID = Int(photo.iPhotoID)
        
        var items = [UIBarButtonItem]()
        if let file = photo.sFile, CommonHelper.if_FileExist(file), !photo.bUploaded {
            items.append(contentsOf: [annotationItem, rotationItem])
        } else if photo.iSPhotoID > 0, (photo.isTaskPhoto || photo.hasUploadedWhenEditingIns) {
            // if the photo is a task photo or has been uploaded when editing inspection
            // then should be able to save the photo comments
            items.append(contentsOf: [savePhotoCommentItem])
        }
        
        navigationItem.rightBarButtonItems = items
    }
    
    private func scrollingToSelectedPhoto() {
        let item = photoArray.enumerated().first(where: { Int($0.element) == iPhotoID })
        guard let index = item?.offset else { return }
        collectionView.scrollToIndex(index: index, animated: false)
        scrollingFinished()
    }

    private func removePhotoID(_ photoID: Int32) {
        guard let index = photoArray.firstIndex(of: String(photoID)) else { return }
        photoArray.remove(at: index)
    }
}

private extension Selector {
    static let allPhotosButtonTapped = #selector(if_DisplayPhoto_2nd.allPhotosButtonTapped)
    static let trashButtonTapped = #selector(if_DisplayPhoto_2nd.trashButtonTapped)
    static let annotationItemTapped = #selector(if_DisplayPhoto_2nd.annotationItemTapped)
    static let rotationItemItemTapped = #selector(if_DisplayPhoto_2nd.rotationItemItemTapped)
    static let savePhotoCommentItemTapped = #selector(if_DisplayPhoto_2nd.savePhotoCommentItemTapped)
}

extension SDImageCache {
    func removeImage(forPath path: String) {
        guard let cachedKey = SDWebImageManager.shared.cacheKey(for: URL(fileURLWithPath: path)) else {
            return
        }
        
        removeImage(forKey: cachedKey, withCompletion: nil)
    }
    
    func removeImage(forURL url: URL) {
        guard let cachedKey = SDWebImageManager.shared.cacheKey(for: url) else {
            return
        }
        
        removeImage(forKey: cachedKey, withCompletion: nil)
    }
}

extension O_Photo {
    @objc
    var hasUploadedWhenEditingIns: Bool {
        guard let ins = db_Inspection.getByInsID(iInsID) else { return false }
        return ins.iSInsID > 0 && bUploaded
    }

    var isTaskPhoto: Bool {
        guard let iSTaskID = Int(sField3),
              let task = db_Tasks.getTask(withSTaskID: iSTaskID)
        else { return false }
        
        return task.sPhotoURL.map(\.rowID).contains(Int(iSPhotoID))
    }

    var isEditingInspection: Bool {
        guard let insItem = db_InsItem.getInsItem(iInsItemID) else { return false }
        return insItem.iSInsItemID > 0
    }
}
