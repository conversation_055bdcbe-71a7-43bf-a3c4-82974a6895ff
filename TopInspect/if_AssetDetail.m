//
//  if_AssetDetail.m
//  InspectionFolio
//
//  Created by <PERSON> on 1/02/14.
//  Copyright (c) 2014 Cloudya. All rights reserved.
//

#import "if_AssetDetail.h"
#import "AssetAddressCell.h"
#import "AssetContactsCell.h"
#import "if_TextViewCell.h"
#import "if_EditTextViewCell.h"
#import "CommonRequest.h"
#import "if_EditCustomInfo.h"
#import "if_EditCustomInfo_ViewModel.h"
#import "NSArray+HighOrderFunction.h"
#import "AssetHeaderTitleCell.h"
#import "db_Delete.h"
@import Extensions;
@import AppFeatures;

typedef NS_ENUM(NSInteger, AssetDetailTab) {
    AssetDetailTabInfo = 0,
    AssetDetailTabContact,
    AssetDetailTabCustomInfo,
    AssetDetailTabFloorPlan,
};

static NSString *const kFloorPlanCellID = @"FloorPlanCell";
static NSString *const kFloorPlanEmptyDataCellID = @"FloorPlanEmptyDataCell";

static NSString *const kRowType = @"row-type";
static NSString *const kRowValue = @"row-value";
static NSString *const kRowAction = @"row-action";
static NSString *const kSLabel = @"s-label";
static NSString *const kSValue = @"s-value";
static NSString *const kSType = @"s-type";
static NSString *const kPTO = @"pto";

static CGFloat const kAddFloorPlanButtonHeight = 54.0;

typedef NS_ENUM(NSUInteger, RowType) {
    RowTypeAddress = 0,
    RowTypeContact,
    RowTypeFloorPlan,
    RowTypeDictionary,
    RowTypeNone,
};

@interface if_AssetDetail () <
        SISegmentedControlDelegate,
        if_EditTextViewCellDelegate,
        UITableViewDataSource,
        UITableViewDelegate,
        FloorPlanCellDelegate,
        ScannerCameraControllerDelegate,
        FloorPlanControllerDelegate
        >
@property (nonatomic, weak) IBOutlet UITableView *oTableView;
@property (nonatomic, weak) IBOutlet UIView *segView;
@property (nonatomic, strong) NSArray *customInfoItems, *sortedKeys, *floorPlans;
@property (nonatomic, readonly) NSArray *tabs;
@property (nonatomic, assign) AssetDetailTab selectedTab;
@property (nonatomic, readonly) NSUInteger selectedTabIndex;
@property (nonatomic, strong) NSDictionary *tableContents;
@property (nonatomic, strong) UIView *oFooterView;
@property (nonatomic, strong) O_Asset *oAsset;
@property (nonatomic, strong) MBProgressHUD *hud;
@property (nonatomic, strong) NSArray *arrContacts;
@property (nonatomic, strong) NSMutableArray *assetCustomInfos;
@property (nonatomic, strong) SISegmentedControl *segControl;
@property (nonatomic, strong) NSMutableArray *lsRows;
@property (nonatomic, strong) NSMutableDictionary *oAssetAttributeInfo;
@property (nonatomic, assign) BOOL isRefreshing;
@property (nonatomic, assign) BOOL hasUpdatedFloorPlans;
@property (nonatomic, strong) UIButton *addFloorPlanButton;
@end

@implementation if_AssetDetail

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if (self) {
        // Custom initialization
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];

    self.navigationItem.title = @"Asset Information";
    
    self.oAsset = [db_Asset GetPropertyBySAssetID:self.iSAssetID];
    self.arrContacts = [db_Asset GetContacts:self.iSAssetID];
    self.assetCustomInfos = [[NSMutableArray alloc] init];
    self.floorPlans = [db_FloorPlan getFloorPlansWithISAssetID:self.iSAssetID];

    self.hud = [[MBProgressHUD alloc] si_initWithView:self.view];
    self.oTableView.rowHeight = UITableViewAutomaticDimension;
    self.oTableView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
//    [self getAssetCustomInfo];

    [self.oTableView registerNib:[AssetAddressCell getCellNib] forCellReuseIdentifier:AssetAddressCellID];
    [self.oTableView registerNib:[AssetContactsCell getCellNib] forCellReuseIdentifier:AssetContactsCellID];
    [self.oTableView registerNib:[if_TextViewCell getCellNib] forCellReuseIdentifier:TextViewCellID];
    [self.oTableView registerNib:[if_EditTextViewCell getCellNib] forCellReuseIdentifier:EditTextViewCellID];
    [self.oTableView registerNib:[FloorPlanCell getCellNib] forCellReuseIdentifier:kFloorPlanCellID];
    [self.oTableView registerNib:[EmptyDataViewCell getCellNib] forCellReuseIdentifier:kFloorPlanEmptyDataCellID];
    [self.oTableView registerNibWithClass: AssetHeaderTitleCell.class];

    //Segmented Control
    NSArray *titles = [self.tabs map:^NSString *(NSNumber *tab) {
        return [self titleForTab:(AssetDetailTab) tab.integerValue];
    }];

    [self.view layoutIfNeeded];

    //UIColor *activeColor = [UIColor colorWithRed:62/255.0f green:107/255.0f blue:255/255.0f alpha:1];
    SISegmentedControl *segControl = [[SISegmentedControl alloc] initWithFrame:_segView.bounds];
    //segControl.borderColor = [UIColor clearColor];
    //segControl.deactiveColor = activeColor;
    //segControl.activeTextColor = activeColor;
    // segControl.activeColor = [UIColor whiteColor];
    segControl.roundCorner = 18;
    segControl.font = [UIFont systemFontOfSize:14];
    [segControl setNames:titles];
    segControl.selectedIndex = 0;
    segControl.delegate = self;
    [_segView addSubview:segControl];
    segControl.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    self.segControl = segControl;
    
    self.lsRows = [NSMutableArray new];
    
    // register for the custom info updated notification
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(updateCustomInfo:)
                                                 name:kNotificationCustomInfoUpdated
                                               object:nil];
}

- (NSArray *)tabs {
    NSArray *tabs = @[@(AssetDetailTabInfo), @(AssetDetailTabContact), @(AssetDetailTabCustomInfo)];
    // Add blueprint tab if enabled
    if ([CommonPermission bEnableFloorPlan]) {
        tabs = [tabs arrayByAddingObject:@(AssetDetailTabFloorPlan)];
    }
    return tabs;
}

- (NSString *)titleForTab:(AssetDetailTab)value {
    return @[@"Asset Info", @"Contact", @"Custom Info", @"Blueprints"][value];
}

- (NSString *)headerTitleForTab:(AssetDetailTab)value {
    return @[@"Asset Info", @"Contacts Info", @"Asset Custom Info", @"Asset Custom Info"][value];
}

- (UIRefreshControl *)refreshControlForTab:(AssetDetailTab)tab {
    if (tab == AssetDetailTabFloorPlan) {
        UIRefreshControl *refreshControl = [[UIRefreshControl alloc] init];
        [refreshControl addTarget:self action:@selector(loadAssetFloorPlans) forControlEvents:UIControlEventValueChanged];
        return refreshControl;
    }
    return nil;
}

- (void)setIsRefreshing:(BOOL)isRefreshing {
    _isRefreshing = isRefreshing;
    if (isRefreshing) {
        if (!self.oTableView.refreshControl.isRefreshing) {
            // Set the content offset to show the spinner
            [self.oTableView setContentOffset:CGPointMake(0, -self.oTableView.refreshControl.frame.size.height) animated:YES];
            [self.oTableView.refreshControl beginRefreshing];
        }
    } else {
        [self.oTableView.refreshControl endRefreshing];
        [self.oTableView setContentOffset:CGPointZero animated:YES];
    }
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
}

- (void)updateUI {
    [self buildRows];
}

- (void)getAssetCustomInfo {
    NSMutableDictionary *oReturn = @{
        @"iAssetID": [NSString stringWithFormat:@"%d", self.oAsset.iSAssetID],
        @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
        @"sToken": [CommonUser currentToken]
    }.mutableCopy;

    [self.view addSubview:self.hud];
    
    self.hud.labelText = @"Processing. Please wait ...";
    @weakify(self)
    [self.hud showAnimated:YES whileExecutingBlock:^{
        @strongify(self)
        NSDictionary *oResult = [IFConnection PostRequest:EndpointGetPropertyCustomInfo oParams:oReturn];
        if (oResult != nil && [[oResult valueForKey:@"success"] boolValue]) {
            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                [self processCustomInfoResult:oResult];
            }];
        } else {
            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                [self showAlertDialog:@"Error" Message:@"Please make sure you are connected to Internet." withCompletionHandler:nil];
            }];
        }
    } completionBlock:^{
        @strongify(self)
        [self.hud removeFromSuperview];
    }];

}

- (void)processCustomInfoResult: (NSDictionary *)oResult {
     DLog(@"result %@", oResult);
    
    NSArray *attributes = oResult[@"lsAssetAttribute"];
    NSMutableArray *customInfos = [[NSMutableArray alloc] init];
    
    for (NSDictionary *att in attributes) {
        O_AssetCustomInfo *customInfo = [O_AssetCustomInfo new];
        customInfo.abbributeId = [att[@"iAssetAttributeID"] intValue];
        customInfo.sLabel = att[@"sLabel"];
        customInfo.sType = att[@"sType"];
        
        [customInfos addObject:customInfo];
    }
    
    NSArray *assetVales = oResult[@"lsAssetValue"];
    
    for (O_AssetCustomInfo *info in customInfos) {
        for (NSDictionary *value in assetVales) {
            if ([value[@"iAssetAttributeID"] intValue] == info.abbributeId) {
                if (value[@"sValue"] != nil) {
                    info.sValue = value[@"sValue"];
                }
            }
        }
    }
    
    self.assetCustomInfos = customInfos;
    [self buildRows];
}

- (void)actionButtonTapped {
    switch (self.selectedTab) {
        case AssetDetailTabInfo:
            [self editProperty];
            break;
        case AssetDetailTabContact:
            [self addNewContact];
            break;
        case AssetDetailTabCustomInfo:
            [self editCustomInfo];
            break;
        case AssetDetailTabFloorPlan:
            [self addFloorPlanAction];
            break;
        default:
            break;
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [CommonAnalytics trackEvent:@"iOS Asset Details" meta:nil];
    [self.navigationController setNavigationBarShadowImageHidden: YES];

    [self buildRows];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self.navigationController setNavigationBarShadowImageHidden: NO];
}

- (BOOL)oAssetIsFound {
    O_Asset *oAsset = [db_Asset GetPropertyBySAssetID:self.iSAssetID];
    return oAsset != nil && oAsset.iSAssetID > 0;
}

- (void)buildRows {
    //Read and Set values
    self.oAsset = [db_Asset GetPropertyBySAssetID:self.iSAssetID];
    self.arrContacts = [db_Asset GetContacts:self.iSAssetID];
    self.floorPlans = [db_FloorPlan getFloorPlansWithISAssetID:self.iSAssetID];

    //Setup Row Array of tableview
    [self.lsRows removeAllObjects];

    //address tab
    NSMutableArray *lsAdd = [NSMutableArray new];
    NSMutableArray *lsCont = [NSMutableArray new];
    NSMutableArray *lsCustom = [NSMutableArray new];
    NSDictionary *rowD;
    
    if (self.oAssetIsFound) {
        NSString *address = [NSString stringWithFormat:@"%@ %@",
                self.oAsset.sAddress1 == nil ? @"" : self.oAsset.sAddress1, self.oAsset.sAddress2 == nil ? @"" : self.oAsset.sAddress2];
        rowD = @{kRowType: @(RowTypeAddress), kRowValue: address};
    } else {
        rowD = @{kRowType: @(RowTypeAddress), kRowValue: @"The asset is not available."};
    }
    [lsAdd addObject:rowD];
    [lsCont addObject:rowD];
    [lsCustom addObject:rowD];
    
    if (self.oAssetIsFound) {
        rowD = @{kRowType: @(RowTypeDictionary),
                kRowValue: [self createDictionary:@"References" with:self.oAsset.sCustom3]}; //TODO - set oAsset's references
        [lsAdd addObject:rowD];

        rowD = @{kRowType: @(RowTypeDictionary),
                kRowValue: [self createDictionary:@"Manager" with:[CommonJson GetPropertyManager:self.oAsset.iCustomerID]]};
        [lsAdd addObject:rowD];

        //TODO set Unit of Address

        if (self.oAsset.sKey != nil) {
            rowD = @{kRowType: @(RowTypeDictionary),
                    kRowValue: [self createDictionary:@"Key" with:self.oAsset.sKey]};
            [lsAdd addObject:rowD];
        }
        if (self.oAsset.sAlarm != nil) {
            rowD = @{kRowType: @(RowTypeDictionary),
                    kRowValue: [self createDictionary:@"Alarm" with:self.oAsset.sAlarm]};
            [lsAdd addObject:rowD];
        }
    }
    
    [self.lsRows addObject:lsAdd];
    

    //contact tab
    if (self.arrContacts != nil && self.arrContacts.count > 0) {
        for (O_Contact *contact in self.arrContacts) {
            rowD = @{kRowType: @(RowTypeContact), kRowValue: contact};
            [lsCont addObject:rowD];
        }
    } else {
        rowD = @{kRowType: @(RowTypeNone), kRowValue: @"You have no contacts added."};
        [lsCont addObject:rowD];
    }
    [self.lsRows addObject:lsCont];

    //custom info tab
    if (self.oAssetIsFound) {
        NSString *sCustomInfo = [CommonHelper IFGetPref:PrefsKeys.kAssetAttributes];
        if ([sCustomInfo length] > 0) {
            NSArray *arrCustomInfo = sCustomInfo.jsonValue;
            self.oAssetAttributeInfo = [NSMutableDictionary dictionaryWithDictionary:self.oAsset.sCustom1.jsonValue];
            NSMutableArray *lsCustomInfo = [NSMutableArray array];
            for (int i = 0; i < arrCustomInfo.count; i++) {
                if_CustomInfo_Item *infoItem = [[if_CustomInfo_Item alloc] initWithDictionary: arrCustomInfo[i]];
                if (infoItem.iAssetAttributeID <= 0) continue;
                if (infoItem.type != FormItemTypeUnknown) {
                    infoItem.sValue = self.oAssetAttributeInfo[infoItem.sActionKey];
                    [lsCustomInfo addObject: infoItem];

                    if (infoItem.type == FormItemTypeAttachment && ![self hasPTOValue: infoItem.sValue]) continue;

                    rowD = @{kRowType: @(RowTypeDictionary), kRowValue: infoItem };
                    [lsCustom addObject:rowD];
                    
                }
            }

            self.customInfoItems = [NSArray arrayWithArray: lsCustomInfo];
        }
    }
        
    [self.lsRows addObject:lsCustom];

    //blueprint tab
    NSArray *lsFloorPlan;
    if ([self.floorPlans count] > 0) {
        lsFloorPlan = [[self.floorPlans splitBy:FloorPlanCell.kNumberOfFloorPlanDisplayedOnCell] map:^id _Nonnull(id item) {
            FloorPlanPair *pair = [[FloorPlanPair alloc] initWithArray:item];
            return @{kRowType: @(RowTypeFloorPlan), kRowValue: pair};
        }];
    } else if (!self.isRefreshing) {
        lsFloorPlan = @[@{kRowType: @(RowTypeNone), kRowValue: @"No Blueprints on the device."}];
    } else {
        lsFloorPlan = @[];
    }
    
    [self.lsRows addObject:lsFloorPlan];

    // Reload table view
    [self reloadTableViewData];
    // end refresh control
    self.isRefreshing = NO;
}

- (BOOL)hasPTOValue:(NSString *)sValue {
    if (![sValue length]) return NO;
    
    id ptoValue = [sValue jsonValue];
    if ([ptoValue isKindOfClass: NSDictionary.class]) {
        return [ptoValue[@"iFileID"] intValue] > 0;
    }
    
    return [sValue intValue] > 0;
}

- (NSDictionary *)createDictionary:(NSString *)sLabel with:(NSString *)sValue {
    return [self createDictionary:sLabel withType:@"" value:sValue];
}

- (NSDictionary *)createDictionary:(NSString*)sLabel withType:(NSString *)sType value:(NSString*)sValue {
    if (sValue == nil) {
        sValue = @"";
    }
    return @{kSType: sType, kSLabel: sLabel, kSValue: sValue};
}
    
- (void)reloadTableViewData {
    [self.oTableView reloadData];
}

- (NSUInteger)selectedTabIndex {
    return [self.tabs indexOfObject:@(self.selectedTab)];
}

#pragma mark - UITableViewDelegates, UITableViewDataSources
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSArray *arr = [self.lsRows safe_objectAtIndex: self.selectedTabIndex];
    return arr.count;
}

- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    return 40;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.selectedTab == AssetDetailTabFloorPlan && self.floorPlans.isEmpty) {
        return tableView.height * 0.8;
    }
    return UITableViewAutomaticDimension;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 0.0001;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    @try {
        NSMutableArray *arr = self.lsRows[self.selectedTabIndex];
        NSDictionary *rowD = arr[indexPath.row];
        
        NSString *rowType = rowD[kRowType];
        id rowValue = rowD[kRowValue];
        
        @weakify(self)
        switch ([rowType integerValue]) {
            case RowTypeAddress: {
                // Contacts
                if (self.selectedTab == AssetDetailTabContact) {
                    AssetAddressCell *cell = [tableView dequeueReusableCellWithIdentifier:AssetAddressCellID forIndexPath: indexPath];
                    [cell displayCell:self.oAsset];
                    cell.btnAction.hidden = YES;
                    return cell;
                } else {
                    AssetHeaderTitleCell *cell = [tableView dequeueReusableCellWithClass: AssetHeaderTitleCell.class forIndexPath: indexPath];
                    // self.oAssetIsFound
                    [cell configureWithModel: rowValue];
                    
                    cell.infoName = [self headerTitleForTab:self.selectedTab];
                    
                    // Configure the new/edit button visibility
                    cell.showsAction = [CommonPermission bEnableEditAsset];
                    
                    if (self.oAssetIsFound && [CommonPermission bEnableEditAsset]) {
                        cell.actionName = @"Edit";
                        cell.action = ^{
                            @strongify(self)
                            [self actionButtonTapped];
                        };
                    } else {
                        cell.action = ^{ };
                        cell.actionName = @"";
                    }
                    
                    cell.isAvailableAsset = self.oAssetIsFound;
                    return cell;
                }
            }
                break;
            case RowTypeContact:
                if ([rowValue isKindOfClass:[O_Contact class]]) {
                    AssetContactsCell *cell = [tableView dequeueReusableCellWithIdentifier:AssetContactsCellID forIndexPath: indexPath];
                    O_Contact *contact = (O_Contact *) rowValue;
                    [cell displayContact:contact];
                    cell.accessoryType = [CommonPermission bEnableEditAsset]
                    ? UITableViewCellAccessoryDisclosureIndicator : UITableViewCellAccessoryNone;
                    return cell;
                }
                break;
            case RowTypeFloorPlan: {
                FloorPlanPair *arrFloorPlan = (FloorPlanPair *) rowValue;
                FloorPlanCell *cell = [tableView dequeueReusableCellWithIdentifier:kFloorPlanCellID forIndexPath:indexPath];
                cell.delegate = self;
                [cell configureWith:arrFloorPlan];
                return cell;
            }
                break;
            case RowTypeDictionary: {
                if_TextViewCell *cell = [tableView dequeueReusableCellWithIdentifier:TextViewCellID forIndexPath: indexPath];
                
                cell.btnAction.onTapped = nil;
                cell.sAction = nil;
                
                NSMutableAttributedString *texts = [[NSMutableAttributedString alloc] init];
                @weakify(self)
                if ([rowValue isKindOfClass:[NSDictionary class]]) {
                    NSDictionary *rowDict = (NSDictionary *) rowValue;
                    [texts appendAttributedString: [self attributedWithText:[NSString stringWithFormat:@"%@: ", rowDict[kSLabel]]
                                                                       font:[UIFont SanFranciscoText_Semibold: 16.0]
                                                                      color:UIColor.color_4A4A4A]];
                    [texts appendAttributedString: [self processCustomItemValue: rowDict[kSValue] withCell: cell textOffset: texts.length]];
                } else if ([rowValue isKindOfClass:[if_CustomInfo_Item class]]) {
                    if_CustomInfo_Item *item = (if_CustomInfo_Item *) rowValue;
                    [texts appendAttributedString: [self attributedWithText:[NSString stringWithFormat:@"%@: ", item.sLabel]
                                                                       font:[UIFont SanFranciscoText_Semibold: 16.0]
                                                                      color:UIColor.color_4A4A4A]];
                    if (item.type == FormItemTypeCheckBox) {
                        [texts appendAttributedString: [self attributedWithText: @"   "
                                                                           font:[UIFont SanFranciscoText_Semibold: 16.0]
                                                                          color:UIColor.color_4A4A4A]];
                        BOOL optionChecked = [item.sValue boolValue];
                        NSTextAttachment *textAttachment = [[NSTextAttachment alloc] init];
                        textAttachment.image = [UIImage imageNamed: optionChecked ? @"icon_option_checked" : @"icon_option_unchecked"];
                        textAttachment.bounds = CGRectMake(0, -3.0, 18, 18);
                        [texts appendAttributedString: [NSAttributedString attributedStringWithAttachment: textAttachment]];
                    } else if (item.type == FormItemTypeAttachment) {
                        id ptoValue = [item.sValue jsonValue];
                        NSString *sAction = nil;
                        int fileID = 0;
                        if ([ptoValue isKindOfClass:NSDictionary.class]) {
                            sAction = ptoValue[@"sName"];
                            fileID = [ptoValue[@"iFileID"] intValue];
                        } else {
                            fileID = [item.sValue intValue];
                            sAction = @"View File";
                        }
                        
                        cell.sAction = fileID > 0 ? sAction : nil;
                        cell.btnAction.onTapped = ^(id sender) {
                            [Navigator downloadAndViewFileWithID:fileID sTitle:sAction];
                        };
                    } else {
                        [texts appendAttributedString:[self processCustomItemValue:item.sValue ?: @"" withCell:cell textOffset: texts.length]];
                    }
                }
                
                cell.lblTextView.attributedText = texts;
                cell.accessoryType = UITableViewCellAccessoryNone;
                return cell;
            }
                break;
            case RowTypeNone: {
                UITableViewCell *cell;
                if (self.selectedTab == AssetDetailTabFloorPlan) {
                    EmptyDataViewCell *emptyCell = [tableView dequeueReusableCellWithIdentifier:kFloorPlanEmptyDataCellID forIndexPath:indexPath];
                    [emptyCell setAsFloorPlans];
                    @weakify(self)
                    emptyCell.learnMore = ^{
                        @strongify(self)
                        [self learnMoreFloorPlan];
                    };
                    cell = emptyCell;
                } else {
                    cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"NoneCell"];
                    cell.textLabel.text = rowValue;
                    cell.textLabel.font = [UIFont SanFranciscoText_Regular:12];
                    cell.selectionStyle = UITableViewCellSelectionStyleNone;
                    cell.accessoryType = UITableViewCellAccessoryNone;
                }
                return cell;
            }
                break;
        }
        
        // Empty cell if not found
        UITableViewCell *cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"cell"];
        cell.textLabel.text = @"Empty";
        cell.textLabel.font = [UIFont SanFranciscoText_Regular:12];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        cell.accessoryType = UITableViewCellAccessoryNone;
        return cell;
        
    } @catch (NSException *exception) {
        Log_Exception(exception)
    }
    
    return nil;
}

- (NSAttributedString *)processCustomItemValue:(NSString *)sValue withCell:(if_TextViewCell *)textViewCell textOffset:(NSUInteger)offset {
    if (sValue.isEmpty) return [[NSAttributedString alloc] init];

    NSMutableAttributedString *attributed = [[NSMutableAttributedString alloc] init];
    NSString *pattern = @"[-+]?([1-8]?\\d(\\.\\d+)?|90(\\.0+)?),\\s*[-+]?(180(\\.0+)?|((1[0-7]\\d)|([1-9]?\\d))(\\.\\d+)?)";

    NSArray<NSString *> *otherComponents = [sValue componentsWithSeparatedByPattern:pattern];
    if (otherComponents.count > 0) {
        NSArray<NSString *> *results = [sValue resultsWithPattern:pattern];
        for (int i = 0; i < otherComponents.count; i++) {
            [attributed appendAttributedString:
                    [self attributedWithText:otherComponents[i]
                                        font:[UIFont SanFranciscoText_Regular:16.0]
                                       color:UIColor.color_4A4A4A]
            ];

            NSString *matched = [results safe_objectAtIndex:i];
            if (matched == nil || matched.isEmpty) continue;

            [attributed appendAttributedString:
                    [self attributedWithText:matched
                                        font:[UIFont SanFranciscoText_Regular:16.0]
                                       color:UIColor.color_053BFF]
            ];
        }

        NSArray <NSTextCheckingResult *> *matches = [sValue matchesWithPattern:pattern];
        textViewCell.textDidTap = ^(NSInteger charIndex) {
            NSInteger rangeIndex = [matches firstIndex:^BOOL(NSTextCheckingResult *result) {
                NSInteger realIndex = charIndex - offset;
                return result.range.location <= realIndex && realIndex <= result.range.length + result.range.location;
            }];
            if (rangeIndex == NSNotFound) return;
            NSURL *coordinate = [NSURL URLWithString:
                    [NSString stringWithFormat: @"https://maps.apple.com/maps?daddr=%@", [[results safe_objectAtIndex:rangeIndex] urlEncoded]]
            ];
            [UIApplication.sharedApplication openURL: coordinate options:@{} completionHandler:nil];
        };
    }

    return attributed;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    @try {
        if (self.selectedTab == AssetDetailTabContact) {
            [tableView deselectRowAtIndexPath:indexPath animated:YES];
            
            NSMutableArray *arr = self.lsRows[self.selectedTabIndex];
            NSDictionary *rowD = arr[indexPath.row];
            if ([CommonPermission bEnableEditAsset]
                && [rowD[kRowType] isEqual:@(RowTypeContact)]
                && [rowD[kRowValue] isKindOfClass:[O_Contact class]]) {
                O_Contact *contact = (O_Contact *) rowD[kRowValue];
                [self editContact:contact];
            }
        }
    }
    @catch (NSException *exception) {
        Log_Exception(exception)
    }
}

#pragma mark - Define Actions

- (NSAttributedString *)attributedWithText: (NSString *)text font: (UIFont *)font color: (UIColor *)textColor {
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
    return [[NSAttributedString alloc] initWithString: text attributes: @{
                NSFontAttributeName: font,
                NSForegroundColorAttributeName: textColor,
                NSParagraphStyleAttributeName: paragraphStyle
            }];
}

- (void)editCustomInfo {
    NSArray<if_CustomInfo_Item *> *items = self.customInfoItems;
    if_EditCustomInfo_ViewModel *vm = [[if_EditCustomInfo_ViewModel alloc]
            initWithCustomInfos: items andAssetID: self.oAsset.iSAssetID sCustom1: self.oAsset.sCustom1];
    if_EditCustomInfo *editCustomInfo = [[if_EditCustomInfo alloc] initWithViewModel:vm];
    @weakify(self)
    editCustomInfo.didFinishEditingCustomInfo = ^{
        @strongify(self)
        [self buildRows];
    };
    UINavigationController *nav = [[UINavigationController alloc] initWithRootViewController: editCustomInfo];
    [self presentViewController:nav animated:true completion: nil];
}

- (void)addNewContact {
    [Navigator PushEditContactAssetId:self.oAsset.iSAssetID contactId:0];
}

- (void)editProperty {
    [Navigator PushEditAssetId:self.oAsset.iSAssetID pAssetId: 0];
}

- (void)editContact:(O_Contact*)contact {
    [Navigator PushEditContactAssetId:self.oAsset.iSAssetID contactId: contact.iSContactID];
}

- (void)addFloorPlanAction {
    [Navigator showScanViewController:self scannerType:ScannerTypeBlueprint scanViewDelete:self scanNavDelegate:nil];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}
    
#pragma mark - SISegmentedcontrolDelegate
- (void)segmentedControlValueChanged:(NSInteger)selectedIndex {
    AssetDetailTab selectedTab = (AssetDetailTab)[self.tabs[(NSUInteger) self.segControl.selectedIndex] integerValue];
    if (selectedTab == self.selectedTab) return;
    self.selectedTab = selectedTab;
    self.oTableView.refreshControl = [self refreshControlForTab: selectedTab];

    if (!self.hasUpdatedFloorPlans && selectedTab == AssetDetailTabFloorPlan) {
        dispatch_delay(0.5, ^{ [self loadAssetFloorPlans]; });
    }

    [self buildRows];
    [self updateActionButton];
}

#pragma Mark - if_EditTextViewCellDelegate
- (void)ScrollToTableView:(UITextView *)oTextField {
    CGSize keyboardSize;
    if ([if_AppDelegate biPad]) {
        keyboardSize = CGSizeMake(434, 350);
    } else {
        keyboardSize = CGSizeMake(314, 300);
    }


    UIEdgeInsets contentInsets;
    if (UIInterfaceOrientationIsPortrait([[UIApplication sharedApplication] statusBarOrientation])) {
        contentInsets = UIEdgeInsetsMake(0.0, 0.0, (keyboardSize.height), 0.0);
    } else {
        contentInsets = UIEdgeInsetsMake(0.0, 0.0, (keyboardSize.width), 0.0);
    }

    self.oTableView.contentInset = contentInsets;
    self.oTableView.scrollIndicatorInsets = contentInsets;

    NSIndexPath *indexPath = [NSIndexPath indexPathForRow:oTextField.tag inSection:0];
    [self.oTableView scrollToRowAtIndexPath:indexPath atScrollPosition:UITableViewScrollPositionBottom animated:true];
}

- (void)DidTextViewEndEditing:(UITextView *)oTextView withLabel:(NSString *)sLabel key:(NSString *)actionKey {
    NSMutableArray *arr = [self.lsRows safe_objectAtIndex: self.selectedTabIndex];
    if (arr.isEmpty) return;

    NSString *sValue = oTextView.text;
    NSDictionary *rowD = @{
        kRowType: @(RowTypeDictionary),
        kRowAction: actionKey,
        kRowValue: [self createDictionary:sLabel with: sValue]
    };
    self.oAssetAttributeInfo[actionKey] = sValue;
    arr[oTextView.tag] = rowD;
}

- (void)ScrollBackTableView:(UITextView *)oTextField {
    [self.oTableView setContentInset:UIEdgeInsetsMake(0, 0, 0, 0)];
}

- (void)updateCustomInfo:(NSNotification *)notification {
    [self buildRows];
}

- (void)updateActionButton {
    UIBarButtonItem *rightButton = nil;
    [self.addFloorPlanButton removeFromSuperview];
    if ([CommonPermission bEnableEditAsset]) {
        switch (self.selectedTab) {
            case AssetDetailTabContact:
                if ([CommonPermission bEnableEditAsset]) {
                    rightButton = [[UIBarButtonItem alloc]
                                   initWithBarButtonSystemItem:UIBarButtonSystemItemAdd
                                   target:self
                                   action:@selector(actionButtonTapped)];
                }
                break;
            case AssetDetailTabFloorPlan:
                if ([CommonPermission bEnableFloorPlan]) {
                    [self.view addSubview:self.addFloorPlanButton];
                    [self.addFloorPlanButton mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.right.equalTo(self.view).offset(-16);
                        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(-16);
                        make.width.height.equalTo(@(kAddFloorPlanButtonHeight));
                    }];
                }
                break;
                
            default:
                break;
        }
    }
    self.navigationItem.rightBarButtonItem = rightButton;
}

#pragma mark - Blueprints
- (void)loadAssetFloorPlans {
    if (self.isRefreshing) return;
    
    // Check network connection
    if (![NetworkConnection isNetworkReachable]) {
        self.isRefreshing = NO;
        [self ShowAlert: @"Error" sMessage:@"No network connection"];
        return;
    }
    // Load asset floor plan
    self.isRefreshing = YES;
    [CommonFloorPlan loadFloorPlansWithISAssetID:self.iSAssetID completion:^{
        self.hasUpdatedFloorPlans = YES;
        self.isRefreshing = NO;
        [self buildRows];
    }];
}

// MARK: - FloorPlanCellDelegate
- (void)floorPlanCell:(FloorPlanCell *)cell didSelect:(O_FloorPlan *)floorPlan {
    if_FloorPlanController *floorPlanVC = [
        [if_FloorPlanController alloc] initWithFloorPlan:floorPlan layerType:LayerTypeAsset insItem:nil isInitial:NO];
    [self.navigationController pushViewController:floorPlanVC animated:YES];
}

- (void)learnMoreFloorPlan {
    if_WebView *oWebView = [self.storyboard instantiateViewControllerWithIdentifier:@"WebView"];
    oWebView.sURL = Constants.kFloorPlanLearnMoreURL;
    [self.navigationController pushViewController:oWebView animated:YES];
}

// MARK: - Add Floor Plan
- (UIButton *)addFloorPlanButton {
    if (!_addFloorPlanButton) {
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        button.backgroundColor = [UIColor color_477BFF];
        
        button.frame = CGRectMake(0, 0, kAddFloorPlanButtonHeight, kAddFloorPlanButtonHeight);
        button.layer.cornerRadius = button.width / 2;

        // Add shadow
        button.layer.shadowColor = [UIColor color_3E6BFF].CGColor;
        button.layer.shadowOffset = CGSizeMake(0, 2);
        button.layer.shadowRadius = 2;
        button.layer.shadowOpacity = 0.2;
        
        // Set contentEdgeInsets if needed to give padding to the image
        button.contentEdgeInsets = UIEdgeInsetsMake(10, 10, 10, 10);
        [button setImage:[UIImage imageNamed:@"btn_add"] forState:UIControlStateNormal];
        [button addTarget:self action:@selector(actionButtonTapped) forControlEvents:UIControlEventTouchUpInside];
        _addFloorPlanButton = button;
    }
    return _addFloorPlanButton;
}

- (void)scannerCameraController:(SICameraScannerViewController *)controller
             postProcessPhotoID:(NSInteger)iPhotoID
                     iInsItemID:(NSInteger)iInsItemID
                       iParamID:(NSInteger)iParamID {
    // Upload blueprint image which is generated from the scanner
    @try {
        UIViewController *topMost = UIApplication.sharedApplication.topMost;
        O_Photo *oPhoto = [db_Media GetPhoto:(int)iPhotoID];
        if (oPhoto == nil || oPhoto.iPhotoID <= 0) {
            [topMost ShowAlert:@"Error" sMessage:@"Photo not found."];
            return;
        }
        
        O_FloorPlan *oFloorPlan = [[O_FloorPlan alloc] initWithDictionary:@{}];
        oFloorPlan.iFloorPlanID = 0; // temporary id
        oFloorPlan.iSAssetID = self.iSAssetID;
        
        // remove the existing floor plan file
        [NSFileManager.defaultManager removeItemAtURL:oFloorPlan.sPlanFile error:NULL];
        // Save the image to the floor plan file
        NSURL *sFileURL = [NSURL fileURLWithPath: [CommonHelper IF_FilePath: oPhoto.sFile]];
        [NSFileManager.defaultManager moveItemAtURL:sFileURL toURL:oFloorPlan.sPlanFile error:NULL];
        oFloorPlan.sPlanPath = oFloorPlan.sPlanFileName;
        
        if_FloorPlanController *floorPlanVC = [[if_FloorPlanController alloc]
                                               initWithFloorPlan:oFloorPlan layerType:LayerTypeAsset insItem:nil isInitial:YES];
        floorPlanVC.delegate = self;
        [topMost.navigationController pushViewController:floorPlanVC animated:YES];
        
        // Delete the photo and its thumbnail
        dispatch_background_async(^{
            [CommonHelper DeleteFile: oPhoto.sFile];
            [CommonHelper DeleteFile: oPhoto.sThumb];
            // Also mark the photo as deleted
            [db_Delete DeletePhoto_ByID:oPhoto.iPhotoID];
        });
    } @catch (NSException *exception) {
        Log_Exception(exception)
    }
}

#pragma mark - FloorPlanControllerDelegate
- (void)floorPlanController:(if_FloorPlanController *)controller didInitializeCanvas:(O_FloorPlan *)floorPlan {
    [self updateUI];
}
@end

