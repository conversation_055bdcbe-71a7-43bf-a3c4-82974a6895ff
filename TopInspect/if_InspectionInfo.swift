//
//  if_InspectionInfo.swift
//  SnapInspect3
//
//  Created by <PERSON> on 2021/1/21.
//  Copyright © 2021 SnapInspect. All rights reserved.
//

import UIKit
import CoreLocation
import Extensions

class if_InspectionInfo: BaseViewController {
    private var insId: Int!
    private var inspection: [AnyHashable: Any] = [:]
    
    private var displayCellTypesAndHeaderTitles: [(UITableViewCell.Type, String)] = []
    
    private lazy var tableView: UITableView = {
        let view = UITableView()
        view.register(headerFooterViewType: TableViewHeaderFooterView.self)
        view.register(InspectionDetailCell.self)
        view.register(InspectionSummaryCell.self)
        view.register(InspectionStatusCell.self)
        view.keyboardDismissMode = .onDrag
        view.separatorStyle = .none
        view.delegate = self
        view.dataSource = self
        view.hidesEmptyCells()
        return view
    }()
    
    convenience init(insId: Int) {
        self.init(nibName: nil, bundle: nil)
        self.insId = insId
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupViews()
        requestInspection()
    }
}

extension if_InspectionInfo {
    private func setupViews() {
        navigationItem.title = "Inspection Info"
        view.backgroundColor = .white
        tableView.fullFill(to: view)
    }
    
    private func requestInspection() {
        guard let insId = insId else { return }
        
        let params = [
            "iCustomerID": CommonHelper.ifGetPref("iCustomerID") ?? "",
            "sToken": CommonUser.currentToken ?? "",
            "iInspectionID": "\(insId)"
        ]
        showLoading()
        DispatchQueue.global(qos: .utility).async {
            let result = IFConnection.postRequest(.getInspection, oParams: params)
            DispatchQueue.main.async { [weak self] in
                self?.hidesLoading()
                self?.processResult(result)
                self?.tableView.reloadData()
            }
        }
    }

    private func processResult(_ result: [AnyHashable: Any]?) {
        guard
            let oInspection = result?["oInspection"] as? [AnyHashable: Any],
            result?["success"] as? Bool ?? false else {
            return
        }

        inspection = oInspection
        
        displayCellTypesAndHeaderTitles.removeAll()
        displayCellTypesAndHeaderTitles.append((InspectionDetailCell.self, "Inspection Details"))
        if geoLocation != nil {
            displayCellTypesAndHeaderTitles.append((InspectionSummaryCell.self, "Inspection Summary"))
        }
        displayCellTypesAndHeaderTitles.append((InspectionStatusCell.self, "Inspection Status"))

        DispatchQueue.global(qos: .background).async {
            self.saveInspection(oInspection)
        }
    }

    private var geoLocation: CLLocationCoordinate2D? {
        guard
            let custom1 = inspection["sCustom1"] as? [AnyHashable: Any],
            let lat = custom1["Lat"] as? String,
            let long = custom1["Long"] as? String,
            let latitude = Double(lat.trimAllSidesWhitespace),
            let longitude = Double(long.trimAllSidesWhitespace)
        else {
            return nil
        }
        return .init(latitude: latitude, longitude: longitude)
    }
    
    private func saveInspection(_ inspection: [AnyHashable: Any]) {
        guard
            let iSInsID = inspection["iInspectionID"] as? Int
        else { return }

        if let ins = db_Inspection.getBySInsID(Int32(iSInsID)) {
            // if have saved in local db, update the inspection.
            ins.update(withDict: inspection)
            db_Inspection.update(ins)
            db_Inspection.updateCustom(ins)
        } else {
            // if not saved, create a new record.
            CommonInspection.reconsolidateInspection(withServer: Int32(iSInsID), oDic: inspection)
        }
    }

    private func updateStatus(_ status: [AnyHashable: Any]) {
        guard
            let sName = status["sName"] as? String,
            let sColorCode = status["sColorCode"] as? String
        else {
            return
        }
        showLoading("Processing...")
        let params = [
            "iCustomerID": CommonHelper.ifGetPref("iCustomerID") ?? "",
            "sToken": CommonUser.currentToken ?? "",
            "iInspectionID": "\(insId!)",
            "sStatus": sName,
            "sColorCode": sColorCode
        ]

        DispatchQueue.global(qos: .utility).async {
            let result = IFConnection.postRequest(.updateInspectionStatusAPI, oParams: params)
            DispatchQueue.main.async {
                self.hidesLoading()
                if let oInspection = result?["oInspection"] as? [AnyHashable: Any],
                   let isSuccess = result?["success"] as? Bool, isSuccess {
                    self.saveInspection(oInspection)
                    self.requestInspection()
                } else if let message = result?["message"] as? String {
                    self.showAlert("Error", sMessage: message)
                }
            }
        }
    }
    
    private func configureCell(cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        guard let cell = cell as? if_Bindable else {
            return
        }
        cell.configure(withModel: inspection)
    }
}

extension if_InspectionInfo: UITableViewDataSource {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        displayCellTypesAndHeaderTitles.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        1
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let (type, _) = displayCellTypesAndHeaderTitles[indexPath.section]
        let cell = tableView.dequeueReusableCell(withIdentifier: String(describing: type), for: indexPath)
        configureCell(cell: cell, forRowAt: indexPath)
        return cell
    }
}

extension if_InspectionInfo: UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = tableView.dequeueReusableHeaderFooterView() as TableViewHeaderFooterView
        headerView.si_setBackgroundColor(.white)
        headerView.titleLabel.numberOfLines = 0
        headerView.titleLabel.lineBreakMode = .byWordWrapping
        headerView.titleLabel.textColor = .init(hex: 0x666666)
        headerView.titleLabel.font = UIFont.sfCompactText_Semibold(17.0)
        headerView.title = displayCellTypesAndHeaderTitles[section].1
        headerView.insets = .init(top: 10.0, left: 20.0, bottom: 10.0, right: 20.0)
        return headerView
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let (type, _) = displayCellTypesAndHeaderTitles[indexPath.section]
        if type == InspectionSummaryCell.self {
            guard
                let coordinate = geoLocation,
                let insTitle = inspection["sTitle"] as? String
            else { return }
            
            let nav = ExternalNavigation()
            let navigationApps = ExternalNavigationApp.allCases.filter(nav.isAvailable)
            let navigationAppNames = navigationApps.map(\.displayString)
            showOptions(
                navigationAppNames,
                selected: { _, index in
                    nav.navigate(navigationApps[index], coordinate, insTitle)
                },
                title: "Route To Destination",
                sMessage: nil,
                sourceView: tableView.cellForRow(at: indexPath) ,
                cancelButton: "Cancel"
            )
            
        } else if type == InspectionStatusCell.self {
            let arrStatus = CommonHelper.getStatusArray(0)
            let arrOptions = arrStatus.compactMap { status -> String? in
                guard let item = status as? [AnyHashable: Any] else { return nil }
                return item["sName"] as? String
            }
            showOptions(
                arrOptions,
                selected: { [weak self] _, index in
                    if let status = arrStatus[index] as? [AnyHashable: Any] {
                        self?.updateStatus(status)
                    }
                },
                title: "Update Status",
                sMessage: nil,
                sourceView: tableView.cellForRow(at: indexPath) ,
                cancelButton: "Cancel"
            )
        }
    }
}
