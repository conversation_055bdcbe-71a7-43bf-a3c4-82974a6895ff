//
// Created by <PERSON> on 2024/7/9.
// Copyright (c) 2024 SnapInspect. All rights reserved.
//

import Foundation
import MBProgressHUD
import AppFeatures

extension if_HomeScreen {
    // Sync the products for the inspection item
    @objc func syncInsItemProducts(hud: MBProgressHUD) {
        guard CommonProduct.hasEnabledProduct else {
            return
        }
        hud.setDetailsLabelText("Sync products...")
        let dtSyncProduct = NSString.ifEmpty(
            CommonHelper.ifGetPref(PrefsKeys.kSyncProductsDate), then: Constants.sSyncDateDefault
        )
        
        CommonProduct.syncAllProducts(from: dtSyncProduct) { [weak hud] total in
            hud?.setDetailsLabelText("Processed \(total) Products. Please Wait")
        }
    }

    // Sync the smart comments
    @objc func syncSmartComments(hud: MBProgressHUD) {
        let params: [AnyHashable: Any] = [
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken,
            "sDateTime": NSString.ifEmpty(CommonHelper.ifGetPref(PrefsKeys.sSCSync), then: "1980-1-1"),
            "sDeviceInfo": CommonHelper.getDeviceInfo()
        ]
        hud.setDetailsLabelText("Prepare Files...")
        let oReturn = IFConnection.postRequest(.syncSmartComments, oParams: params)
        if let oReturn {
            let sSyncDate = oReturn["SyncDate"] as? String ?? ""
            CommonHelper.ifSavePref(PrefsKeys.sSCSync, sValue: sSyncDate)
            if let lsReturnArry = oReturn["lsReturn"] as? [[String: Any]] {
                for dic in lsReturnArry {
                    if let sDownloadURL = dic["sDownloadURL"] as? String,
                       let sPath = dic["sS3Path"] as? String
                    {
                        downloadSmartComment(urlStr: sDownloadURL, fileName: sPath)
                    }
                }
            }
        }
    }

    // Sync the property layouts
    @objc func syncPropertyLayouts(hud: MBProgressHUD) {
        hud.setDetailsLabelText("Sync Asset Layouts...")

        let dtSyncProduct = NSString.ifEmpty(
            CommonHelper.ifGetPref(PrefsKeys.kSyncPropertyLayoutsDate), then: Constants.sSyncDateDefault
        )
        CommonRequest.syncAllPropertyLayouts(sDateTime: dtSyncProduct) { [weak hud] total in
            hud?.setDetailsLabelText("Processed \(total) Asset Layouts. Please Wait")
        }
    }
}

extension if_HomeScreen {
    private func downloadSmartComment(urlStr: String, fileName: String) {
        guard !NSString.isNullOrEmpty(fileName) else {
            return
        }

        DispatchQueue.global(qos: .default).async {
            let array = fileName.components(separatedBy: "/")

            if let fileN = array.count >= 1 ? array.last : fileName,
               let urlToDownload = URL(string: urlStr),
               let urlData = try? Data(contentsOf: urlToDownload),
               let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first
            {
                let filePath = documentsDirectory.appendingPathComponent(fileN)
                try? urlData.write(to: filePath)
                debugPrint("File Saved ! --- \(filePath)")
            }
        }
    }
}
