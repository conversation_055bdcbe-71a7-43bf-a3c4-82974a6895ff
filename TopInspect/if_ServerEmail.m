//
//  if_ServerEmail.m
//  SnapInspect3
//
//  Created by <PERSON> on 7/19/16.
//  Copyright © 2016 SnapInspect. All rights reserved.
//

#import "if_ServerEmail.h"
#import "EmailCell.h"
@import ZSSRichTextEditor;

#define SEPARATE_STR "; "

@interface if_ServerEmail () <UITextViewDelegate, UITableViewDelegate, UITableViewDataSource>
@property (nonatomic) UIDeviceOrientation currentDeviceOrientation;
@property (weak, nonatomic) IBOutlet UITableView *emailTableView;
@end

@implementation if_ServerEmail {
    NSInteger clickedCellRow;
   // UITextView *bodyTextView;
    //UITextView *subjectTextView;
    //UITextView *receiptTextView;
    NSString *receipt;
    NSString *subject;
    NSString *body;
  //  bool bodyclicked;
    //NSArray *receipts;
    int iInsID;
  //  NSString *hyperStr;
 //   NSURL *htmlLink;
    NSString *htmlLinkStr;
   // bool isHTML;
  // bool isPortrait;
}

- (void)viewDidLoad {
    [super viewDidLoad];

    clickedCellRow = -1;

    [self.emailTableView registerNibWithClass: EmailCell.class];
    [self.emailTableView registerNibWithClass: EmailBodyCell.class];
    
    self.emailTableView.allowsSelection = NO;
    self.emailTableView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
    [self.emailTableView hidesEmptyCells];

    UIBarButtonItem *btn_Send = [[UIBarButtonItem alloc]
            initWithTitle:@"Send" style:UIBarButtonItemStylePlain target:self action:@selector(send:)];
    self.navigationItem.rightBarButtonItem = btn_Send;
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)setBody:(NSString*)text isHTML:(BOOL)html {
   // [self convertHTML:text];
    body = text;
}

- (void)setSubject:(NSString*)text {
    subject = text;
    self.navigationItem.title = text;
}

- (void)setToRecipients:(NSString *)_tempReceipt {
    //receipts = array;
    //for (NSString *text in array) {
    //    receipt = [NSString stringWithFormat:@"%@, %@", receipt, text];
    // }
    receipt =_tempReceipt;
}

- (void)setInsID:(int)setInsID {
    iInsID = setInsID;
}

- (void)viewDidAppear:(BOOL)animated {
    self.navigationItem.title = subject;
    
  //  self.sendBtn.enabled = YES;
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
}

- (void)willRotateToInterfaceOrientation:(UIInterfaceOrientation)toInterfaceOrientation duration:(NSTimeInterval)duration {
    [_emailTableView reloadData];
}

/*-(NSString *)convertHTML:(NSString *)html {
    
    NSScanner *myScanner;
    NSString *text = nil;
    NSString *url = nil;
    NSString *hText = nil;
    NSString *urlStr = nil;
    myScanner = [NSScanner scannerWithString:html];
    
    if (urlStr == nil) {
        urlStr = [self getSubString:html sub1:@"<a " sub2:@"/a>"];
        url = [self getSubString:urlStr sub1:@"href=" sub2:@">"];
        htmlLinkStr = url;
        htmlLink = [[NSURL alloc] initWithString:url];
        hText = [self getSubString:urlStr sub1:@">" sub2:@"<"];
        hyperStr = hText;
    }
    
    while ([myScanner isAtEnd] == NO) {
        [myScanner scanUpToString:@"<" intoString:NULL];
        
        [myScanner scanUpToString:@">" intoString:&text];
        
        html = [html stringByReplacingOccurrencesOfString:[NSString stringWithFormat:@"%@>", text] withString:@""];
    }
    //
    html = [html stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    
    return html;
}*/

/*-(NSString*)encodeHTML:(NSString *)string {
    string = [NSString stringWithFormat:@"<p>%@</p>", string];
    string = [string stringByReplacingOccurrencesOfString:@"\r\n" withString:@"</p>\r\n<p>"];
    string = [string stringByReplacingOccurrencesOfString:hyperStr withString:[NSString stringWithFormat:@"<a href=%@>%@</a>", htmlLinkStr,hyperStr] ];
    return string;
}

- (void) setAttributedTextView : (UITextView*)textView string:(NSString*)string {
    NSRange htmlRange = [string rangeOfString: hyperStr];
    NSMutableAttributedString * link = [[NSMutableAttributedString alloc] initWithString:string];
    [link addAttribute: NSLinkAttributeName value: link range: NSMakeRange(htmlRange.location, htmlRange.length)];
    [link addAttribute: NSUnderlineStyleAttributeName value:[NSNumber numberWithInt:NSUnderlineStyleSingle] range:NSMakeRange(htmlRange.location, htmlRange.length)];
    [link addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:17.0] range:NSMakeRange(0, string.length)];
    [textView setAttributedText:link];
}

- (NSString *)getSubString:(NSString*)str sub1:(NSString*)sub1 sub2:(NSString*)sub2{
    NSRange r1 = [str rangeOfString:sub1];
    NSRange r2 = [str rangeOfString:sub2];
    NSRange rSub = NSMakeRange(r1.location + r1.length, r2.location - r1.location - r1.length);
    NSString *sub = [str substringWithRange:rSub];
    return sub;
    
}*/
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 3;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.row == 2) {
        UITableViewCell *receipt = [tableView cellForRowAtIndexPath: [NSIndexPath indexPathForRow:0 inSection:0]];
        UITableViewCell *subject = [tableView cellForRowAtIndexPath: [NSIndexPath indexPathForRow:1 inSection:0]];
        return tableView.height - receipt.height - subject.height;
    }
    return UITableViewAutomaticDimension;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {

    UITableViewCell *cell = nil;
    if (indexPath.row == 0 || indexPath.row == 1) {
        EmailCell *emailCell = [tableView dequeueReusableCellWithClass:EmailCell.class forIndexPath: indexPath];
        @weakify(self, emailCell)
        emailCell.reloadCellHeight = ^{
            @strongify(self, emailCell)
            [emailCell updateCellHeight];
            [self.emailTableView reloadWithoutAnimation];
        };
        
        emailCell.textViewBeginEditing = ^{
            @strongify(self)
            [self setHTMLToolbarHidden: YES];
        };
        
        emailCell.textViewEndEditing = ^ {
            @strongify(self)
            [self setHTMLToolbarHidden: NO];
        };

        if (indexPath.row == 0) {
            emailCell.cellLab.text = @"To:";
            emailCell.textView.textColor = UIColor.color_053BFF;
            [emailCell configureWithModel: receipt];
        } else {
            emailCell.cellLab.text = @"Subject:";
            emailCell.textView.textColor = UIColor.blackColor;
            [emailCell configureWithModel: subject];
        }

        cell = emailCell;

    } else if (indexPath.row == 2) {
        EmailBodyCell *bodyCell = [tableView dequeueReusableCellWithClass:EmailBodyCell.class forIndexPath:indexPath];
        cell = bodyCell;
    }

    return cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    if ([cell isKindOfClass: EmailBodyCell.class]) {
        EmailBodyCell *bodyCell = (EmailBodyCell *) cell;
        bodyCell.textEditor.baseURL = [NSURL URLWithString:htmlLinkStr];
        bodyCell.textEditor.shouldShowKeyboard = NO;
        bodyCell.textEditor.alwaysShowToolbar = NO;
        [bodyCell configureWithModel: body];
    }
}

- (BOOL)validateEmailWithString:(NSString*)email {
   // NSString *emailRegex = @"[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{3,4}";
   // NSPredicate *emailTest = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", emailRegex];
   // return [emailTest evaluateWithObject:email];
    return [CommonHelper IFValidateEmail:email];
}

//-(void)textViewDidBeginEditing:(UITextView *)textView
//{
    
//}
//- (BOOL)textView:(UITextView *)textView shouldInteractWithURL:(NSURL *)URL inRange:(NSRange)characterRange
//{
//    [[UIApplication sharedApplication] openURL:URL];
//    return NO;
//}
/*- (void)textViewDidChange:(UITextView *)textView
{
    if (textView == receiptTextView) {
        NSString *receiptStr = textView.text;
        NSArray *myWords = [receiptStr componentsSeparatedByString:@"; "];
        NSString *tmp = [myWords lastObject];
        if ([self validateEmailWithString:tmp]) {
            
            receiptStr = [receiptStr stringByAppendingString:@"; "];
            receiptTextView.text = receiptStr;
        }
        receipt = receiptTextView.text;
    } else if (textView == subjectTextView) {
        subject = textView.text;
    }
    
    if (![receipt  isEqualToString: @""]) {
        self.sendBtn.enabled = YES;
    } else {
        self.sendBtn.enabled = NO;
    }
}*/
//- (void)addContactAddress:(UIButton*)sender{
//    DLog(@"add Contact");
    
//}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    if (clickedCellRow == 1) {
        if (indexPath.row > 2) {
            clickedCellRow = indexPath.row - 1;
            [self.emailTableView reloadData];
        } else if (indexPath.row == 1 || indexPath.row == 2) {
            //nothing
        } else {
            clickedCellRow = indexPath.row;
            [self.emailTableView reloadData];
        }
    } else {
        clickedCellRow = indexPath.row;
        [self.emailTableView reloadData];
    }
}

- (void)sendEmail: (NSString*)html {
    MBProgressHUD *hud = [[MBProgressHUD alloc] si_initWithView:self.view];
    [self.view addSubview:hud];
    hud.labelText = @"Connecting to Server";

    //NSArray *addArry = [receipt componentsSeparatedByString:@"; "];
    //NSString * address = [addArry objectAtIndex:0];
    //DLog(@"add -------> %@", address);
    if (![receipt hasSuffix:@";"]) {
        receipt = [NSString stringWithFormat:@"%@;", receipt];
    }

    [hud showAnimated:YES whileExecutingBlock:^{
        NSDictionary *oReturn = [IFConnection PostRequest:EndpointSendEmail oParams:[@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"sRecipient": [NSString stringWithFormat:@"%@", receipt], @"sSubject": [NSString stringWithFormat:@"%@", subject], @"sBody": [CommonHelper EscapeAmp:[NSString stringWithFormat:@"%@", html]], @"iInsID": [NSString stringWithFormat:@"%d", iInsID], @"sType": @"i"} mutableCopy]];
        [hud setDetailsLabelText:@"Processing Request"];

        NSMutableDictionary *dic = [@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken], @"sRecipient": [NSString stringWithFormat:@"%@", receipt], @"sSubject": [NSString stringWithFormat:@"%@", subject], @"sBody": [NSString stringWithFormat:@"%@", html], @"iInsID": [NSString stringWithFormat:@"%d", iInsID], @"sType": @"i"} mutableCopy];

        DLog(@"dic  %@", dic);
        if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]) {
            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                [self ShowSuccessAlert:@"Success!" sMessage:[oReturn valueForKey:@"message"]];
            }];
        } else {
            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                [self ShowAlert:@"Error" sMessage:[oReturn valueForKey:@"message"]];
            }];
        }
    } completionBlock:^{
        [hud removeFromSuperview];
    }];
}

- (IBAction)send:(id)sender {
    EmailBodyCell *bodyCell = [self.emailTableView cellForRowAtIndexPath: [NSIndexPath indexPathForRow:2 inSection:0]];
    @weakify(self)
    [bodyCell.textEditor getHTML:^(NSString *html, NSError *error) {
        @strongify(self)
        [self validateEmailAndSendEmail: html];
    }];
}

- (void)validateEmailAndSendEmail: (NSString*)html {
    if (html == nil || [html length] == 0) {
        [self ShowAlert:@"Error" sMessage:@"Please enter email content."];
        return;
    }
    
    EmailCell *oEmailCell = [self.emailTableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:0]];
    NSString *sTempReceipt = oEmailCell.textView.text;
    NSMutableArray *arrTemp_1 = [NSMutableArray arrayWithArray:[[[sTempReceipt stringByReplacingOccurrencesOfString:@"," withString:@";"] stringByReplacingOccurrencesOfString:@" " withString:@";"] componentsSeparatedByString:@";"]];
    bool bValidate = true;
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"length > 0"];
    NSArray *arrTemp = [arrTemp_1 filteredArrayUsingPredicate:predicate];
    for (NSString *cc in arrTemp) {
        if (![CommonHelper IFValidateEmail:cc]) {
            bValidate = false;
            break;
        }
    }
    if ((!bValidate) || (arrTemp == nil || [arrTemp count] == 0)) {
        [self ShowAlert:@"Error" sMessage:@"Invalid email. Please seperate emails use , or ; or empty space."];
        return;
    }
    receipt = [arrTemp componentsJoinedByString:@";"];
    //Test Here
    
    EmailCell *oEmailCell_1 = [self.emailTableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:1 inSection:0]];
    NSString *sTempSubject = [oEmailCell_1.textView.text trimAllSidesWhitespace];
    if ([sTempSubject length] == 0) {
        [self ShowAlert:@"Error" sMessage:@"Subject can not be empty."];
        return;
    }
    subject = sTempSubject;
    
    [self.view endEditing:YES];
    if (receipt != nil && [receipt length] > 0) {
        [self sendEmail:html];
    } else {
        [self ShowAlert:@"Error" sMessage:@"Please enter email!"];
    }
}

- (void)ShowSuccessAlert:(NSString *)sTitle sMessage:(NSString *)sMessage {
    @weakify(self)
    [self ShowAlert:sTitle sMessage:sMessage cancelButton:@"OK" cancelAction:^(UIAlertAction *_Nonnull action) {
        @strongify(self)
        [self.navigationController popViewControllerAnimated:YES];
    }];
}

- (void)setHTMLToolbarHidden: (BOOL)isHidden {
    EmailBodyCell *bodyCell = [self.emailTableView cellForRowAtIndexPath: [NSIndexPath indexPathForRow:2 inSection:0]];
    UIView *toolbarHolder = (UIView *)[bodyCell.textEditor performSelector: NSSelectorFromString(@"toolbarHolder")];
    if (toolbarHolder != nil) {
        toolbarHolder.hidden = isHidden;
    }
}

@end
