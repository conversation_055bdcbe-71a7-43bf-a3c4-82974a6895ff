//
//  if_EditCustomInfo_ViewModel.m
//  SnapInspect3
//
// Created by <PERSON> on 2020/4/22.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

#import "if_EditCustomInfo_ViewModel.h"
#import "NSArray+HighOrderFunction.h"
#import "if_FormItemTextInputCell.h"
#import "if_FormItemCheckboxCell.h"
#import "if_FormItemTextAreaCell.h"
#import "if_FormItemAttachmentCell.h"
#import "NSURL+Extensions.h"
#import "CommonRequest.h"
#import "CommonS3.h"
#import "O_Asset.h"
#import "db_Asset.h"
#import "CommonUpload.h"
NSString *const assetAttributePrefix = @"CV_";

@implementation if_CustomInfo_Item

- (instancetype)initWithDictionary: (NSDictionary *)result {
    self = [self init];
    if (self) {
        _iAssetAttributeID = [result[@"iAssetAttributeID"] intValue];
        _sLabel = result[@"sLabel"];
        _sActionKey = [NSString stringWithFormat:@"%@%d", assetAttributePrefix, _iAssetAttributeID];
        _type = [self processCustomInfoType: result[@"sType"]];
    }
    return self;
}

- (FormItemType)processCustomInfoType:(NSString *)type {
    NSNumber *formItem = self.typeMapping[type];
    return formItem != nil ? (FormItemType)formItem.integerValue : FormItemTypeUnknown;
}

- (NSDictionary<NSString *, NSNumber *> *)typeMapping {
    static NSDictionary<NSString *, NSNumber *> *typeMapping;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        typeMapping = @{
            @"text" : @(FormItemTypeTextField),
            @"check" : @(FormItemTypeCheckBox),
            @"textarea" : @(FormItemTypeTextView),
            @"datepicker" : @(FormItemTypeTextSelect),
            @"pto" : @(FormItemTypeAttachment)
        };
    });
    return typeMapping;
}

@end

@interface if_EditCustomInfo_ViewModel()
@property (readwrite, nonatomic) NSArray<if_FormItemModel *> * formDataSets;
@property (readwrite, nonatomic) NSArray <Class> *formItemCellClasses;

@property (weak, nonatomic) NSArray <if_CustomInfo_Item *> * customInfoItems;
@property (nonatomic) int iAssetID;
@property (copy, nonatomic) NSString *sCustom1;
@property (copy, nonatomic) NSMutableDictionary *fileIDMap;
@end

@implementation if_EditCustomInfo_ViewModel

- (instancetype)initWithCustomInfos: (NSArray <if_CustomInfo_Item *> *)items
                         andAssetID: (int)assetId
                           sCustom1: (NSString *)sCustom1 {
    self = [super init];
    if (self) {
        _iAssetID = assetId;
        _sCustom1 = sCustom1;
        _customInfoItems = items;
        _fileIDMap = NSMutableDictionary.dictionary;
    }
    return self;
}

- (NSString *)navigationTitle {
    return @"Edit Custom Info";
}

- (NSString *)bottomButtonTitle {
    return @"Save Changes";
}

- (NSArray<if_FormItemModel *> *)formDataSets {
    if (_formDataSets == nil) {
        _formDataSets = [self.customInfoItems map:^if_FormItemModel *(if_CustomInfo_Item *item) {
            if_FormItemModel *itemModel = [[if_FormItemModel alloc] init];
            itemModel.itemType = item.type;
            itemModel.identifier = item.sActionKey;
            itemModel.title = item.sLabel;
            itemModel.value = item.sValue;
            return itemModel;
        }];
    }
    return _formDataSets;
}

- (NSArray<Class> *)formItemCellClasses {
    if (_formItemCellClasses == nil) {
        _formItemCellClasses = [self.formDataSets compactMap:^id (if_FormItemModel *item) {
            switch (item.itemType) {
                case FormItemTypeTextField:
                case FormItemTypeTextSelect:
                    return if_FormItemTextInputCell.class;
                case FormItemTypeCheckBox:
                    return if_FormItemCheckboxCell.class;
                case FormItemTypeTextView:
                    return if_FormItemTextAreaCell.class;
                case FormItemTypeAttachment:
                    return if_FormItemAttachmentCell.class;
                default:
                    return nil;
            }
        }];
    }
    return _formItemCellClasses;
}

- (NSString *)validateFormData {
    [self.formDataSets forEach: ^ (if_FormItemModel *itemModel) {
        itemModel.validateResult = ValidateResultNone;
    }];

    return nil;
}

- (NSDictionary *)formParams {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    
    NSMutableDictionary *customDict = [NSMutableDictionary dictionary];
    [self.formDataSets forEach: ^ (if_FormItemModel *itemModel) {
        if ([itemModel.identifier hasPrefix: assetAttributePrefix]) {
            NSString *value = nil;
            NSDictionary *fileField = self.fileIDMap[itemModel.identifier];
            if (itemModel.itemType == FormItemTypeAttachment) {
                if ([fileField count] == 0) return;
                customDict[itemModel.identifier] = fileField;
            } else if (itemModel.value != nil && [itemModel.value isKindOfClass: NSString.class]) {
                value = [itemModel.value trimAllSidesWhitespace];
                customDict[itemModel.identifier] = value.length > 0 ? value : @"";
            }
        } else if (itemModel.value != nil && [itemModel.value isKindOfClass: NSString.class]) {
            params[itemModel.identifier] = [itemModel.value trimAllSidesWhitespace];
        }
    }];
    params[@"sCustom1"] = customDict.toJson;
    
    return [NSDictionary dictionaryWithDictionary: params];
}

- (void)requestSubmitData:(void (^)(BOOL success, NSString *sMessage))completion {
    dispatch_background_async(^{
        BOOL success = NO;
        NSString *message = [self validateFormData];
        if (message == nil) {
            ///  iCustomerID, sToken, iAssetID
            NSMutableDictionary *allParams = [NSMutableDictionary dictionaryWithDictionary:self.formParams];
            allParams[@"iCustomerID"] = [CommonHelper IFGetPref:@"iCustomerID"] ?: @"";
            allParams[@"sToken"] = [CommonUser currentToken] ?: @"";
            allParams[@"iAssetID"] = [@(self.iAssetID) stringValue];

            NSDictionary *oResult = [IFConnection PostRequest:EndpointUpdateAssetCustomInfo oParams:allParams];
            if (oResult != nil && [[oResult valueForKey:@"success"] boolValue]) {
                success = YES;
                NSDictionary *oProperty = oResult[@"oProperty"];
                int iServer_AssetID = [[oProperty valueForKey:@"iPropertyID"] intValue];
                if (iServer_AssetID > 0) {
                    [self ProcessResult:oResult];
                } else {
                    message = @"Failed to Update custom info, Please go to 'Settings' and 'Ask Support' for help.";
                }
            } else {
                success = false;
                message = oResult[@"message"];
            }
        }
        safe_dispatch_main_async(^{
            if (completion) completion(success, message);
        });
    });
}

- (void)ProcessResult: (NSDictionary *)oDic {
    if (oDic == nil || ![oDic[@"success"] boolValue]) return;

    NSDictionary *oProperty = oDic[@"oProperty"];
    int iServer_AssetID = [[oProperty valueForKey:@"iPropertyID"] intValue];
    O_Asset *oValidateAsset = [db_Asset GetPropertyBySAssetID:iServer_AssetID];
    if (oValidateAsset == nil || oValidateAsset.iAssetID == 0) {
        oValidateAsset = [[O_Asset alloc] init];
        oValidateAsset.iSAssetID = iServer_AssetID;
    }

    @try {
        id iSPropertyID = oProperty[@"iSPropertyID"];
        oValidateAsset.iSPAssetID =  iSPropertyID == [NSNull null] ? 0 : [iSPropertyID intValue];
        oValidateAsset.sCustom1 = oProperty[@"sCustom1"];
    } @catch(NSException *ex) {
        oValidateAsset.iSPAssetID = 0;
    }

    [db_Asset UpdateProperty:oValidateAsset];
}

- (void)uploadAttachmentAt:(NSURL *)documentURL forItemIdentifier:(NSString *)itemIdentifier {
    [CommonUpload uploadAttachmentAt: documentURL
                            iAssetID: self.iAssetID
                             success:^(NSInteger iFileID, NSString *sName) {
        NSMutableDictionary *field = [NSMutableDictionary dictionary];
        field[@"iFileID"] = @(iFileID);
        field[@"sName"] = sName;
        self.fileIDMap[itemIdentifier] = [NSDictionary dictionaryWithDictionary: field];
    }
                          completion:^{
        [self saveChanges];
    }];
}

- (void)uploadImage:(UIImage *)image withFileName:(NSString *)fileName forItemIdentifier: (NSString *)itemIdentifier {
    [CommonUpload uploadImage: image
                    withFileName: fileName
                      forAssetID: self.iAssetID
                       success:^(NSInteger iFileID, NSString *sName) {
        NSMutableDictionary *field = [NSMutableDictionary dictionary];
        field[@"iFileID"] = @(iFileID);
        field[@"sName"] = sName;
        self.fileIDMap[itemIdentifier] = [NSDictionary dictionaryWithDictionary: field];
    } completion:^{
        [self saveChanges];
    }];
}

- (void)saveChanges {
    UIViewController *topMost = UIApplication.sharedApplication.topMost;
    [topMost showLoading: @"Processing. Please wait ..."];
    @weakify(topMost)
    [self requestSubmitData: ^(BOOL success, NSString *sMessage) {
        @strongify(topMost)
        [topMost hidesLoading];
        if (sMessage.length > 0) {
            [topMost ShowAlert: @"Error" sMessage: sMessage];
        } else if (!success) {
            [topMost ShowAlert:@"Error" sMessage:@"To create or update asset information, please make sure you are connected to Internet."];
        } else {
            [NSNotificationCenter.defaultCenter postNotificationName: kNotificationCustomInfoUpdated object: nil];
        }
    }];
}

@end
