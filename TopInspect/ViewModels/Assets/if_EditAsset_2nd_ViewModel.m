//
//  if_EditAsset_2nd_ViewModel.m
//  SnapInspect3
//
//  Created by <PERSON> on 2019/12/23.
//  Copyright © 2019 SnapInspect. All rights reserved.
//

#import "if_EditAsset_2nd_ViewModel.h"
#import "NSArray+HighOrderFunction.h"
#import "if_FormItemModel.h"
#import "if_FormItemTextInputCell.h"
#import "if_FormItemCheckboxCell.h"
#import "db_Asset.h"
#import "v_Asset.h"

@interface if_EditAsset_2nd_ViewModel()
@property (readwrite, nonatomic) NSArray<if_FormItemModel *> * formDataSets;
@property (readwrite, nonatomic) NSArray<Class> *formItemCellClasses;

@property (strong, nonatomic) v_Asset *vAsset;
@property (strong, nonatomic) O_Asset *oAsset;
@end

@implementation if_EditAsset_2nd_ViewModel

- (instancetype)initWithAssetId:(int)iSAssetId pAssetId:(int)iSpAssetId {
    self = [self init];
    if (self) {
        _iSAssetID = iSAssetId;
        _iSPAssetID = iSpAssetId;

        if (self.isNewAsset) {
            _oAsset = [[O_Asset alloc] init];
            _oAsset.iSPAssetID = iSpAssetId;
            _oEditType = EditAssetTypeAsset;
            if (iSpAssetId > 0) {
                v_Asset *pAsset = [db_Asset GetAssetByID: iSpAssetId];
                if (pAsset.iSAssetID > 0 && pAsset.iPAssetID > 0) {
                    _oEditType = EditAssetTypeRoom;
                } else if (pAsset.iSAssetID > 0) {
                    _oEditType = EditAssetTypeUnit;
                }
            }
        } else {
            _oAsset = [db_Asset GetPropertyBySAssetID: iSAssetId];
            _iSPAssetID = _oAsset.iSPAssetID;
            _vAsset = [db_Asset GetAssetByID: iSAssetId];

            if (_vAsset.iSAssetID > 0 && _vAsset.iPAssetID > 0 && _vAsset.iPPAssetID > 0) {
                _oEditType = EditAssetTypeRoom;
            } else if (_vAsset.iSAssetID > 0 && _vAsset.iPAssetID > 0) {
                _oEditType = EditAssetTypeUnit;
            } else if (_vAsset.iSAssetID > 0) {
                _oEditType = EditAssetTypeAsset;
            }
        }


    }
    return self;
}

- (BOOL)isNewAsset {
    return self.iSAssetID <= 0;
}

- (NSString *)navigationTitle {
    return self.isNewAsset ? [NSString stringWithFormat:@"New %@", self.nameFromEditAssetType] : @"Edit Asset";
}

- (NSString *)nameFromEditAssetType {
    if (self.oEditType == EditAssetTypeAsset)
        return @"Asset";
    if (self.oEditType == EditAssetTypeRoom)
        return @"Room";
    if (self.oEditType == EditAssetTypeUnit)
        return @"Unit";
    return @"";
}

- (NSString *)topInfoTitle {
    if (self.oEditType == EditAssetTypeRoom || self.oEditType == EditAssetTypeUnit)
        return self.vAsset.sBuildingAddress;
    return nil;
}

- (NSString *)bottomButtonTitle {
    return [NSString stringWithFormat:self.isNewAsset ? @"Create %@" : @"Save %@", self.nameFromEditAssetType];
}

- (BOOL)showsMarkAsMultiFamilyBuilding {
    if (!self.isNewAsset || self.oEditType == EditAssetTypeRoom) return NO;
    return (CommonHelper.bMultiFamilyEnabled && self.oEditType == EditAssetTypeAsset)
    || (CommonHelper.bRoomEnabled && self.oEditType == EditAssetTypeUnit);
}

- (NSArray<Class> *)formItemCellClasses {
    if (_formItemCellClasses == nil) {
        NSMutableArray *identifiers = [NSMutableArray array];
        [identifiers addObjectsFromArray: [self.formDataSets compactMap:^id (if_FormItemModel *item) {
            switch (item.itemType) {
                case FormItemTypeTextField:
                    return if_FormItemTextInputCell.class;
                case FormItemTypeCheckBox:
                    return if_FormItemCheckboxCell.class;
                default:
                    return nil;
            }
        }]];

        _formItemCellClasses = identifiers;
    }
    return _formItemCellClasses;
}

- (NSArray<if_FormItemModel *> *)formDataSets {
    if (_formDataSets == nil) {
        NSMutableArray *result = [NSMutableArray array];

        if_FormItemModel *assetLine1 =
            [if_FormItemModel itemWithIdentifier: FormItemIdentifierAddressLine1 type: FormItemTypeTextField];
        assetLine1.title = @"Asset Line 1";
        assetLine1.isRequired = YES;
        assetLine1.requiredText = @"Line 1 required";
        assetLine1.value = self.oAsset.sAddress1;
        [result addObject:assetLine1];

        if_FormItemModel *assetLine2 =
            [if_FormItemModel itemWithIdentifier: FormItemIdentifierAddressLine2 type: FormItemTypeTextField];
        assetLine2.title = @"Asset Line 2";
        assetLine2.value = self.oAsset.sAddress2;
        [result addObject:assetLine2];

        if_FormItemModel *reference =
            [if_FormItemModel itemWithIdentifier: FormItemIdentifierReference type: FormItemTypeTextField];
        reference.title = @"Reference";
        reference.value = self.oAsset.sRef;
        [result addObject:reference];

        if_FormItemModel *keys =
            [if_FormItemModel itemWithIdentifier: FormItemIdentifierKeys type: FormItemTypeTextField];
        keys.title = @"Keys";
        keys.value = self.oAsset.sKey;
        [result addObject:keys];

        if_FormItemModel *alarms =
            [if_FormItemModel itemWithIdentifier: FormItemIdentifierAlarms type: FormItemTypeTextField];
        alarms.title = @"Alarms";
        alarms.value = self.oAsset.sAlarm;
        [result addObject:alarms];

        if (self.showsMarkAsMultiFamilyBuilding) {
            if_FormItemModel *markAsMultiFamilyBuilding =
                [if_FormItemModel itemWithIdentifier:@"" type:FormItemTypeCheckBox];
            markAsMultiFamilyBuilding.title = [self apartmentText];
            markAsMultiFamilyBuilding.value = self.bApartment ? @"true" : @"false";
            [result addObject:markAsMultiFamilyBuilding];
        }

        _formDataSets = [NSArray arrayWithArray:result];
    }
    return _formDataSets;
}

- (NSDictionary *)formParams {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [self.formDataSets forEach: ^ (if_FormItemModel *itemModel) {
        if (itemModel.value != nil && [itemModel.value isKindOfClass: NSString.class]) {
            NSString *value = [itemModel.value trimAllSidesWhitespace];
            params[itemModel.identifier] = value.length > 0 ? value : @"";
        }
    }];

    return [NSDictionary dictionaryWithDictionary: params];
}

- (NSString *)validateFormData {
    [self.formDataSets forEach: ^ (if_FormItemModel *itemModel) {
        itemModel.validateResult = ValidateResultNone;
    }];
    
    NSArray *requiredItems = [self.formDataSets filter: ^BOOL(if_FormItemModel *itemModel) {
        return itemModel.isRequired;
    }];
    if (requiredItems.isNotEmpty) {
        if_FormItemModel *line1 = [self.formDataSets filter:^BOOL(if_FormItemModel *itemModel) {
            return [itemModel.identifier isEqualToString: FormItemIdentifierAddressLine1];
        }].firstObject;

        NSString *sValue1 = [line1.value trimAllSidesWhitespace];
        if (sValue1.length == 0) {
            line1.validateResult = ValidateResultFailed;
            return @"Address Line 1 can not be empty.";
        }
    }
    return nil;
}

- (void)requestSubmitData:(void (^)(BOOL success, NSString *sMessage))completion {
    dispatch_background_async(^{
        BOOL success = NO;
        NSString *message = [self validateFormData];
        if (message == nil) {
            NSMutableDictionary *allParams = [NSMutableDictionary dictionaryWithDictionary:self.formParams];
            BOOL bApartment = NO;
            
            if (self.isNewAsset)
                bApartment = self.showsMarkAsMultiFamilyBuilding ? self.bApartment : NO;
            else
                bApartment = self.oAsset.bPush;

            allParams[@"bApartment"] = bApartment ? @"true" : @"false";
            allParams[@"sInsDue"] = self.oAsset.dtInsDue ?: @"";
            allParams[@"sNotes"] = self.oAsset.sNotes ?: @"";
            allParams[@"iPropertyID"] = [@(self.oAsset.iSAssetID) stringValue];
            allParams[@"iSPropertyID"] = [@(self.iSPAssetID) stringValue];
            allParams[@"iCustomerID"] = [CommonHelper IFGetPref:@"iCustomerID"] ?: @"";
            allParams[@"sToken"] = [CommonUser currentToken] ?: @"";

            NSDictionary *oResult = [IFConnection PostRequest:EndpointUpdateProperty oParams:allParams];
            if (oResult != nil && [[oResult valueForKey:@"success"] boolValue]) {
                success = YES;
                NSDictionary *oProperty = oResult[@"oProperty"];
                int iServer_AssetID = [[oProperty valueForKey:@"iPropertyID"] intValue];
                if (iServer_AssetID > 0) {
                    [self ProcessResult:oResult];
                } else {
                    message = @"Failed to create new Asset, Please go to 'Settings' and 'Ask Support' for help.";
                }
            }
            else{
                success= false;
                message = [oResult valueForKey:@"message"];
            }
        }
        safe_dispatch_main_async(^{
            if (completion) completion(success, message);
        });
    });
}

- (void)ProcessResult: (NSDictionary *)oDic {
    NSDictionary *oProperty = oDic[@"oProperty"];
    int iServer_AssetID = [[oProperty valueForKey:@"iPropertyID"] intValue];
    O_Asset *oValidateAsset = [db_Asset GetPropertyBySAssetID:iServer_AssetID];
    if (oValidateAsset == nil || oValidateAsset.iAssetID == 0) {
        oValidateAsset = [[O_Asset alloc] init];
        oValidateAsset.iSAssetID = iServer_AssetID;
    }

    @try {
        id iSPropertyID = oProperty[@"iSPropertyID"];
        oValidateAsset.iSPAssetID =  iSPropertyID == [NSNull null] ? 0 : [iSPropertyID intValue];
    } @catch(NSException *ex) {
        oValidateAsset.iSPAssetID = 0;
    }

    @try {
        oValidateAsset.iSAssetID = [oProperty[@"iPropertyID"] intValue];
        oValidateAsset.sAddress1 = oProperty[@"sAddress1"];
        oValidateAsset.sAddress2 = oProperty[@"sAddress2"];
        oValidateAsset.sKey = oProperty[@"sKey"];
        oValidateAsset.sAlarm = oProperty[@"sAlarm"];
        oValidateAsset.sCustom3 = oProperty[@"sRef"];

        NSString *due = oProperty[@"dtDue"];
        oValidateAsset.dtInsDue = ([due isEqual: [NSNull null]] || [due length] < 11) ? @"" : [due substringToIndex:12];;
        oValidateAsset.sFilter = [CommonHelper FilterString: self.oAsset.sAddress1 sAddress2: self.oAsset.sAddress2];
        oValidateAsset.iPLVerID = 0;
        oValidateAsset.bPush = [oProperty[@"bApartment"] boolValue];
    } @catch(NSException *ex) {
        Log_Exception(ex);
    }
    
    [db_Asset UpdateProperty:oValidateAsset];
}

- (NSString *)apartmentText {
    return self.oEditType == EditAssetTypeAsset ? @"Mark asset as Multi-Family Building" : @"Allow additional Rooms/Assets";
}

@end
