//
//  if_EditContact_2nd_ViewModel.m
//  SnapInspect3
//
//  Created by <PERSON> on 2020/2/13.
//  Copyright © 2020 SnapInspect. All rights reserved.
//

#import "if_EditContact_2nd_ViewModel.h"
#import "O_Contact.h"
#import "db_Asset.h"
#import "if_FormItemTextInputCell.h"
#import "if_FormItemCheckboxCell.h"
#import "if_FormItemDateRangeCell.h"
#import "if_FormItemModel.h"
#import "NSArray+HighOrderFunction.h"

@interface if_EditContact_2nd_ViewModel ()
@property (readwrite, nonatomic) NSArray<if_FormItemModel *> * formDataSets;
@property (readwrite, nonatomic) NSArray <Class> *formItemCellClasses;

@property (assign, nonatomic) int iSAssetID;
@property (strong, nonatomic) O_Contact *oContact;
@end

@implementation if_EditContact_2nd_ViewModel

- (instancetype)initWithAssetId:(int)iSAssetID contactId:(int)iSContactId {
    self = [self init];
    if (self) {
        _iSAssetID = iSAssetID;
        if (iSContactId > 0) {
            _oContact = [db_Asset GetContact: iSContactId];
            _iSAssetID = _oContact.iSAssetID;
            self.selectedContactType = _oContact.contactType;
        } else {
            _oContact = [[O_Contact alloc] init];
        }
    }
    return self;
}

- (BOOL)isNewContact {
    return self.oContact.iSContactID <= 0;
}

- (NSString *)navigationTitle {
    return self.isNewContact ? @"Add New Contact" : @"Edit Contact";
}

- (NSString *)bottomButtonTitle {
    return [NSString stringWithFormat:self.isNewContact ? @"Create %@" : @"Save %@", @"Contact"];
}

- (NSArray<Class> *)formItemCellClasses {
    if (!_formItemCellClasses) {
        NSMutableArray *identifiers = [NSMutableArray array];
        [identifiers addObjectsFromArray:[self.formDataSets compactMap:^id(if_FormItemModel *item) {
            switch (item.itemType) {
                case FormItemTypeTextField:
                case FormItemTypeTextSelect:
                    return if_FormItemTextInputCell.class;
                case FormItemTypeCheckBox:
                    return if_FormItemCheckboxCell.class;
                case FormItemTypeDateRange:
                    return if_FormItemDateRangeCell.class;
                default:
                    return nil;
            }
        }]];
        _formItemCellClasses = identifiers;
    }
    return _formItemCellClasses;
}

- (NSArray<if_FormItemModel *> *)formDataSets {
    if (!_formDataSets) {
        NSMutableArray *result = [NSMutableArray array];

        if_FormItemModel *firstName =
                [if_FormItemModel itemWithIdentifier:FormItemIdentifierFirstName type:FormItemTypeTextField];
        firstName.title = @"First Name";
        firstName.isRequired = YES;
        firstName.requiredText = @"Required";
        firstName.value = self.oContact.sFirstName;
        [result addObject:firstName];

        if_FormItemModel *lastName =
                [if_FormItemModel itemWithIdentifier:FormItemIdentifierLastName type:FormItemTypeTextField];
        lastName.title = @"Last Name";
        lastName.value = self.oContact.sLastName;
        [result addObject:lastName];

        if_FormItemModel *email =
                [if_FormItemModel itemWithIdentifier:FormItemIdentifierEmail type:FormItemTypeTextField];
        email.title = @"Email";
        email.isRequired = YES;
        email.keyboardType = UIKeyboardTypeEmailAddress;
        email.value = self.oContact.sEmail;
        [result addObject:email];

        if_FormItemModel *phone =
                [if_FormItemModel itemWithIdentifier:FormItemIdentifierPhone type:FormItemTypeTextField];
        phone.title = @"Phone";
        phone.keyboardType = UIKeyboardTypePhonePad;
        phone.value = self.oContact.sPhone;
        [result addObject:phone];

        if_FormItemModel *mobile =
                [if_FormItemModel itemWithIdentifier:FormItemIdentifierMobile type:FormItemTypeTextField];
        mobile.title = @"Mobile";
        mobile.keyboardType = UIKeyboardTypePhonePad;
        mobile.value = self.oContact.sMobile;
        [result addObject:mobile];

        if_FormItemModel *type =
                [if_FormItemModel itemWithIdentifier:FormItemIdentifierContactType type:FormItemTypeTextSelect];
        type.title = @"Type";
        type.isRequired = YES;
        type.value = self.oContact.sTag;
        [result addObject:type];

        _formDataSets = [NSArray arrayWithArray:result];
    }

    return _formDataSets;
}

- (NSDictionary *)formParams {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [self.formDataSets forEach: ^ (if_FormItemModel *itemModel) {
        if (itemModel.itemType == FormItemTypeDateRange) {
            params[@"sFrom"] = [itemModel value];
            params[@"sTo"] = [itemModel value2];
        } else if (itemModel.value != nil && [itemModel.value isKindOfClass: NSString.class]) {
            NSString *value = [itemModel.value trimAllSidesWhitespace];
            if (value.length == 0) return;
            params[itemModel.identifier] = value;
        }
    }];

    return [NSDictionary dictionaryWithDictionary: params];
}

- (NSString *)validateFormData {
    [self.formDataSets forEach: ^ (if_FormItemModel *itemModel) {
        itemModel.validateResult = ValidateResultNone;
    }];

    NSArray *requiredItems = [self.formDataSets filter: ^BOOL(if_FormItemModel *itemModel) {
        return itemModel.isRequired;
    }];

    if (requiredItems.isEmpty) return nil;

    if_FormItemModel *firstName = [self.formDataSets filter:^BOOL(if_FormItemModel *itemModel) {
        return [itemModel.identifier isEqualToString: FormItemIdentifierFirstName];
    }].firstObject;
    NSString *sValue1 = [firstName.value trimAllSidesWhitespace];
    if (sValue1.length == 0) {
        firstName.validateResult = ValidateResultFailed;
        return @"First Name can not be empty.";
    }

    if_FormItemModel *email = [self.formDataSets filter:^BOOL(if_FormItemModel *itemModel) {
        return [itemModel.identifier isEqualToString: FormItemIdentifierEmail];
    }].firstObject;
    NSString *sValue2 = [email.value trimAllSidesWhitespace];
    if (sValue2.length > 0 && ![CommonHelper IFValidateEmail: sValue2]) {
        email.validateResult = ValidateResultFailed;
        return @"Invalid Email Address";
    }

    if_FormItemModel *type = [self.formDataSets filter:^BOOL(if_FormItemModel *itemModel) {
        return [itemModel.identifier isEqualToString: FormItemIdentifierContactType];
    }].firstObject;
    NSString *sValue3 = [type.value trimAllSidesWhitespace];
    if (sValue3.length == 0) {
        type.validateResult = ValidateResultFailed;
        return @"Please select a Contact Type";
    }

    return nil;
}

- (void)requestSubmitData:(void (^)(BOOL success, NSString *sMessage))completion {
    dispatch_background_async(^{
        BOOL success = NO;
        NSString *message = [self validateFormData];
        if (message == nil) {
            NSMutableDictionary *allParams = [NSMutableDictionary dictionaryWithDictionary:self.formParams];
            allParams[@"iContactID"] = [@(self.oContact.iSContactID) stringValue];
            allParams[@"iPropertyID"] = [@(self.iSAssetID) stringValue];
            allParams[@"iCustomerID"] = [CommonHelper IFGetPref:@"iCustomerID"];
            allParams[@"sToken"] = [CommonUser currentToken];

            NSDictionary *oResult = [IFConnection PostRequest:EndpointUpdateContact oParams:allParams];
            if (oResult != nil && [[oResult valueForKey:@"success"] boolValue]) {
                success = YES;
                [self ProcessResult:oResult];
            }
        }
        safe_dispatch_main_async(^{
            if (completion) completion(success, message);
        });
    });
}

- (void)requestDeleteContact:(void (^)(BOOL success, NSString *sMessage))completion {
    dispatch_background_async(^{
        BOOL success = NO;
        NSString *message = nil;
        if (self.oContact.iSContactID > 0) {
            NSMutableDictionary *allParams = [NSMutableDictionary dictionary];
            allParams[@"iContactID"] = [@(self.oContact.iSContactID) stringValue];
            allParams[@"iPropertyID"] = [@(self.iSAssetID) stringValue];
            allParams[@"iCustomerID"] = [CommonHelper IFGetPref:@"iCustomerID"];
            allParams[@"sToken"] = [CommonUser currentToken];

            NSDictionary *oResult = [IFConnection PostRequest:EndpointDeleteContact oParams:allParams];
            if (oResult != nil && [[oResult valueForKey:@"success"] boolValue]) {
                success = YES;
                self.oContact.bDeleted = YES;
                [db_Asset UpdateContact:self.oContact];
            } else {
                message = ![NSString isNullOrEmpty:oResult[@"message"]] ? oResult[@"message"] : @"Failed to delete contact.";
            }
        }
        safe_dispatch_main_async(^{
            if (completion) completion(success, message);
        });
    });
}


- (void)ProcessResult: (NSDictionary *)oDic {
    NSDictionary *oContactDict = oDic[@"oContact"];
    O_Contact *oTempContact = [[O_Contact alloc] initWithResponse:oContactDict];
    [db_Asset UpdateContact:oTempContact];
}

- (NSArray *)arrContactType {
    return [CommonHelper getContactTypes];
}

- (void)setSelectedContactType:(O_ContactType *)selectedContactType {
    _selectedContactType = selectedContactType;

    // Update the form data sets based on the selected contact type
    // Remove the "Primary" and "Date Range" items
    NSMutableArray *result = [NSMutableArray arrayWithArray:
            [self.formDataSets filter:^BOOL(if_FormItemModel *itemModel) {
                return ![@[FormItemIdentifierIsPrimary, FormItemIdentifierDateRange] containsObject:itemModel.identifier];
            }]];
    // Add the "Primary" and "Date Range" items if needed
    if (_selectedContactType.bPrimary) {
        if_FormItemModel *isPrimaryItem =
                [if_FormItemModel itemWithIdentifier:FormItemIdentifierIsPrimary type:FormItemTypeCheckBox];
        isPrimaryItem.title = @"Primary";
        isPrimaryItem.value = self.oContact.bIsPrimary ? @"true" : @"false";
        [result addObject:isPrimaryItem];
    }

    if (_selectedContactType.bDateRange) {
        if_FormItemModel *dateRangeItem =
                [if_FormItemModel itemWithIdentifier:FormItemIdentifierDateRange type:FormItemTypeDateRange];
        dateRangeItem.title = @"Occupancy Period";
        dateRangeItem.value = self.oContact.dtFrom;
        dateRangeItem.value2 = self.oContact.dtTo;
        [result addObject:dateRangeItem];
    }
    self.formDataSets = [NSArray arrayWithArray:result];

    _formItemCellClasses = nil;
}

@end

