//
//  DB_Upgrade.m
//  TopInspect
//
//  Created by Terry<PERSON><PERSON> on 24/08/15.
//  Copyright (c) 2015 Cloudya. All rights reserved.
//

#import "DB_Upgrade.h"
#import "FMDatabaseAdditions.h"
#import "db_Delete.h"
#import "db_Sync.h"
#import "NSArray+HighOrderFunction.h"

NSString *const sCreateTableProduct = @"CREATE TABLE IF NOT EXISTS ai_Product (\n"
"    iProductID INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,\n"
"    iSProductID INTEGER,\n"
"    sCheckListID TEXT,\n"
"    iCompanyID INTEGER,\n"
"    sSKU TEXT,\n"
"    sName TEXT,\n"
"    sModel TEXT,\n"
"    sDesp TEXT,\n"
"    sAssociateArea TEXT,\n"
"    sAssociateItem TEXT,\n"
"    dUnitCost DOUBLE,\n"
"    bAllowEdit BOOLEAN,\n"
"    bOneOffCost BOOLEAN,\n"
"    sProductCategory TEXT,\n"
"    sUnitName TEXT,\n"
"    sURL TEXT,\n"
"    sImage TEXT,\n"
"    sCustom1 TEXT,\n"
"    sCustom2 TEXT,\n"
"    bArchive BOOLEAN,\n"
"    bDeleted BOOLEAN,\n"
"    iCreatedBy INTEGER,\n"
"    iUpdatedBy INTEGER,\n"
"    dtUpdate TEXT,\n"
"    dtDateTime TEXT\n"
" );";

NSString *const sCreateTableProductCost = @"CREATE TABLE IF NOT EXISTS ai_ProductCost (\n"
"    iCostingID INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,\n"
"    iSCostingID INTEGER,\n"
"    iAssetID INTEGER NOT NULL,\n"
"    iInspectionID INTEGER,\n"
"    iInsItemID INTEGER,\n"
"    iPInsItemID INTEGER,\n"
"    iTaskID INTEGER,\n"
"    iProductID INTEGER NOT NULL,\n"
"    dUnit DOUBLE NOT NULL,\n"
"    dUnitCost DOUBLE NOT NULL,\n"
"    dTotalCost DOUBLE NOT NULL,\n"
"    sNotes TEXT,\n"
"    sCustom1 TEXT,\n"
"    sCustom2 TEXT,\n"
"    sCustom3 TEXT,\n"
"    bDeleted BOOLEAN NOT NULL,\n"
"    iCreatedBy INTEGER,\n"
"    iUpdatedBy INTEGER,\n"
"    dtUpdate TEXT,\n"
"    dtDateTime TEXT\n"
");";

NSString *const sCreateTableTask = @"CREATE TABLE IF NOT EXISTS ai_Task (\n"
"    iNotificationID INTEGER PRIMARY KEY AUTOINCREMENT,\n"
"    iSNotificationID INTEGER,\n"
"    iSubmitCustomerID INTEGER,\n"
"    iFollowUpCustomerID INTEGER NOT NULL,\n"
"    iPropertyID INTEGER,\n"
"    iCompanyID INTEGER,\n"
"    iInsItemID INTEGER,\n"
"    iInspectionID INTEGER,\n"
"    sCategory TEXT,\n"
"    iPriority INTEGER NOT NULL,\n"
"    sCode TEXT,\n"
"    sTitle TEXT NOT NULL,\n"
"    sDescription TEXT,\n"
"    sPhotoURL TEXT,\n"
"    sVideoURL TEXT,\n"
"    dtDateDue DATE,\n"
"    sCustom1 TEXT,\n"
"    sCustom2 TEXT,\n"
"    bClosed BOOLEAN NOT NULL,\n"
"    bDeleted BOOLEAN NOT NULL,\n"
"    dtDateTime DATE NOT NULL,\n"
"    dtComplete DATE,\n"
"    dtUpdate DATE,\n"
"    iPTaskID INTEGER,\n"
"    arrMember TEXT,\n"
"    sStatus TEXT,\n"
"    iCategoryID INTEGER,\n"
"    dtUpdate_Msg DATE,\n"
"    dCost DOUBLE\n"
");";

NSString *const sCreateTableUpdateAssetTask = @"CREATE TABLE IF NOT EXISTS ai_UpdateAssetTask (\n"
"   iID INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,\n"
"   iAssetID INTEGER NOT NULL,\n"
"   dtLastSync TEXT,\n"
"   sCustom1 TEXT,\n"
"   sCustom2 TEXT,\n"
"   bDeleted BOOLEAN NOT NULL,\n"
"   dtUpdate DATE,\n"
"   dtDateTime TEXT\n"
");";

NSString *const sCreateInspectionView = @"CREATE VIEW IF NOT EXISTS  v_inspection (iInspectionID, iSInsID, iSAssetID, sTitle, sInsTitle, dtStartDate, dtEndDate, bComplete, bSynced, sCustom1, sCustom2, iSScheduleID, iPPAssetID, iPAssetID, sRef, sBuildingAddress, sUnitAddress, sRoomAddress, iGroupID, bApartment, sSearchTerm) AS SELECT c.iInspectionID, c.iSInsID, c.iSAssetID, c.sTitle,  c.sInsTitle, c.dtStartDate, c.dtEndDate, c.bComplete, c.bSynced, c.sCustom1, c.sCustom2, c.iSScheduleID, d.iPPAssetID, d.iPAssetID, d.sRef, d.sBuildingAddress, d.sUnitAddress, d.sRoomAddress, d.iGroupID, d.bApartment, IFNULL(c.sTitle, '') || ' ' || IFNULL(sInsTitle, '') || ' ' || IFNULL(dtEndDate, '') ||  ' ' || IFNULL(d.sSearchTerm, '') FROM ai_Inspection c left join v_asset d on c.iSAssetID = d.iSAssetID where c.bDeleted = 0";
NSString *const sCreatePropertyView = @"CREATE VIEW IF NOT EXISTS v_asset (iAssetID, iSAssetID, iPAssetID, iPPAssetID, sRef, sSearchTerm, sBuildingAddress, sUnitAddress, sRoomAddress, iCustomerID, iGroupID, bApartment, sCustom1, sCustom2) AS SELECT c.iAssetID, c.iSAssetID, d.iSAssetID, e.iSAssetID, c.sField3 as sRef, ((CASE WHEN d.iSPAssetID > 0 then ' ' || e.sAddress1 || ' ' || e.sAddress2 || ' | ' ELSE '' END) || (CASE WHEN c.iSPAssetID > 0 THEN ' ' || d.sAddress1 || ' ' || d.sAddress2 || ' | ' ELSE '' END) || ' ' || c.sAddress1 || ' ' || c.sAddress2), (CASE WHEN d.iSPAssetID > 0 THEN e.sAddress1 || ', ' || e.sAddress2 WHEN c.iSPAssetID > 0 THEN d.sAddress1 || ', ' || d.sAddress2 ELSE c.sAddress1 || ', ' || c.sAddress2 END), (CASE WHEN d.iSPAssetID > 0 THEN d.sAddress1 || ', ' || d.sAddress2 WHEN c.iSPAssetID > 0 THEN c.sAddress1 || ', ' || c.sAddress2 ELSE '' END), (CASE WHEN (c.iSPAssetID > 0 and d.iSPAssetID > 0) THEN c.sAddress1 || ', ' || c.sAddress2 ELSE '' END), c.iCustomerID, c.iGroupID, c.bPush, c.sField1, c.sField2  from ai_Assets c left join ai_assets d on c.iSPAssetID = d.iSAssetID LEFT JOIN ai_Assets e on d.iSPAssetID = e.iSAssetID where c.bDeleted = 0";

NSString *const sCreateScheduleView = @"CREATE VIEW IF NOT EXISTS v_schedule (iScheduleID, iSScheduleID, iSInsTypeID, sPTC, sInsTitle, sDateTime, iUnixTime, sCustom1, sCustom2, sType, iPPAssetID, iPAssetID, iCustomerID, sRef, sBuildingAddress, sUnitAddress, sRoomAddress, iGroupID, bApartment, iAssetID, iSAssetID, sSearchTerm, sAddress1, sAddress2, sRRule, sEXRule) AS SELECT c.iScheduleID, c.iSScheduleID, c.iSInsTypeID, c.sPTC,  c.sInsTitle, c.dtDateTime, c.dtUnixTime,  c.sCustom1, c.sCustom2, c.sType, d.iPPAssetID, d.iPAssetID, d.iCustomerID, d.sRef, d.sBuildingAddress, d.sUnitAddress, d.sRoomAddress, d.iGroupID, d.bApartment, d.iAssetID, d.iSAssetID, (IFNULL(c.dtDateTime, '') || ' ' || IFNULL(d.sSearchTerm, (c.sAddress1 || ' ' || c.sAddress2))), c.sAddress1, c.sAddress2, c.sRRule, c.sEXRule FROM ai_Schedule c left join v_asset d on c.iSAssetID = d.iSAssetID";

@implementation DB_Upgrade

+(void)DB_Upgrade_1_0{
    @autoreleasepool {
        @try {
            
            FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
            //  NSArray *oArray = [[NSArray alloc] init];
            //  NSDictionary *oDic1  = [oArray objectAtIndex:10];
            NSString *sTableName = @"ai_Notification";
            NSString *sTableName1 = @"ai_NoticeCategory";
            if ([oDB open]){
                
                if (![oDB tableExists:sTableName]){
                    NSString *sQuery = @"CREATE TABLE `ai_Notification` (`iNotificationID`    INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,`sTitle`    TEXT,`sDescription`    TEXT,`iInsItemID`    INTEGER NOT NULL,`sPhotoURL`    TEXT,`iVideoID`    INTEGER,`sCustom1`    TEXT,`sCustom2`    TEXT,`bDeleted`    INTEGER NOT NULL,`dtDateTime`    TEXT NOT NULL)";
                    [oDB executeUpdate:sQuery];
                    
                }

                
                if (![oDB tableExists:sTableName1]){
                    NSString *sQuery1 = @"CREATE TABLE `ai_NoticeCategory` (`iNoticeCategoryID`    INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,`iSNoticeCategoryID` INTEGER,`sName`    TEXT NOT NULL,`sDescription`    TEXT,`bDeleted`    INTEGER DEFAULT 0)";
                    [oDB executeUpdate:sQuery1];
                    
                }

                [oDB close];
            }
            [oDB close];
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }
    }

}
+(void)DB_Upgrade_1_1{
    
    FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
    //  NSArray *oArray = [[NSArray alloc] init];
    //  NSDictionary *oDic1  = [oArray objectAtIndex:10];
    NSString *sTableName = @"ai_InsItem";
    NSString *sTableName1 = @"ai_Inspection";
    NSString *sTableName2 = @"ai_Schedule";
    NSString *sColumnName1 = @"sCustom1";
    NSString *sColumnName2 = @"sCustom2";
    if ([oDB open]){
        if (![oDB columnExists:sColumnName1 inTableWithName:sTableName]){
            NSString *sQuery1 =[NSString stringWithFormat: @"ALTER TABLE %@ ADD COLUMN %@ TEXT NOT NULL DEFAULT ('')", sTableName, sColumnName1];
             NSString *sQuery2 =[NSString stringWithFormat: @"ALTER TABLE %@ ADD COLUMN %@ TEXT NOT NULL DEFAULT ('')", sTableName, sColumnName2];
            [oDB executeUpdate:sQuery1];
            [oDB executeUpdate:sQuery2];
        }
        if (![oDB columnExists:sColumnName1 inTableWithName:sTableName1]){
            NSString *sQuery1 =[NSString stringWithFormat: @"ALTER TABLE %@ ADD COLUMN %@ TEXT NOT NULL DEFAULT ('')", sTableName1, sColumnName1];
            NSString *sQuery2 =[NSString stringWithFormat: @"ALTER TABLE %@ ADD COLUMN %@ TEXT NOT NULL DEFAULT ('')", sTableName1, sColumnName2];
            [oDB executeUpdate:sQuery1];
            [oDB executeUpdate:sQuery2];
        }
        if (![oDB columnExists:sColumnName1 inTableWithName:sTableName2]){
            NSString *sQuery1 =[NSString stringWithFormat: @"ALTER TABLE %@ ADD COLUMN %@ TEXT NOT NULL DEFAULT ('')", sTableName2, sColumnName1];
            NSString *sQuery2 =[NSString stringWithFormat: @"ALTER TABLE %@ ADD COLUMN %@ TEXT NOT NULL DEFAULT ('')", sTableName2, sColumnName2];
            [oDB executeUpdate:sQuery1];
            [oDB executeUpdate:sQuery2];
        }
    }
    [oDB close];
    
}
+(void)DB_Upgrade_1_5{
    NSString *sTableName = @"ai_File";
    NSString *sColumnName1 = @"iSFileID";
     FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
    @try{
        if ([oDB open]){
            if (![oDB columnExists:@"iSFileID" inTableWithName:sTableName]){
                NSString *sQuery1 =[NSString stringWithFormat: @"ALTER TABLE %@ ADD COLUMN %@ INTEGER NOT NULL DEFAULT 0", sTableName, sColumnName1];
                [oDB executeUpdate:sQuery1];
            }
        }
        [oDB close];
        
    }@catch(NSException *ex){
        @try{
             [oDB close];
        }@catch(NSException *eee){
            
        }
       
    }
}

+(void)DB_Upgrade_1_6{
    NSString *sTableName = @"ai_Assets";
    NSString *sColumnName1 = @"iGroupID";
    FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
    @try{
        if ([oDB open]){
            if (![oDB columnExists:sColumnName1 inTableWithName:sTableName]){
                NSString *sQuery1 =[NSString stringWithFormat: @"ALTER TABLE %@ ADD COLUMN %@ INTEGER NOT NULL DEFAULT 0", sTableName, sColumnName1];
                [oDB executeUpdate:sQuery1];
            }
        }
        [oDB close];
        
    }@catch(NSException *ex){
        @try{
            [oDB close];
        }@catch(NSException *eee){
            
        }
        
    }
}
+(void)DB_Upgrade_1_7{
    NSString *sTableName = @"ai_Tasks";
    FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
    @try{
        if ([oDB open]){
            if (![oDB tableExists:@"ai_Tasks"]){
               // NSDate *oDate = [NSDate new];
               // [oDate timeIntervalSince1970];
                
                NSString *sQuery1 = @"CREATE TABLE `ai_Tasks` ( `iTaskID` INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT, `iSTaskID` INTEGER NOT NULL, `iSubmitCustomerID` INTEGER NOT NULL, `iFolllowUpCustomerID` INTEGER, `iSAssetID` INTEGER NOT NULL, `iInsItemID` INTEGER, `iInspectionID` INTEGER, `sCategory` TEXT, `iPriority` INTEGER, `sCode` TEXT, `sTitle` TEXT, `sDescription` TEXT, `sPhotoURL` TEXT, `sVideoURL` TEXT, `dtDateDue` TEXT, `sCustom1` TEXT, `sCustom2` TEXT, `bClosed` INTEGER, `bDeleted` INTEGER, `dtComplete` INTEGER, `dtUpdate` INTEGER, `iPTaskID` INTEGER )";
                [oDB executeUpdate:sQuery1];
            }
          //  [oDB executeUpdate:sCreateInspectionView];

        }
        [oDB close];
        
    }@catch(NSException *ex){
        @try{
            [oDB close];
        }@catch(NSException *eee){
            
        }
        
    }
}

+(void)DB_Upgrade_1_8 {
    NSString *sTableName = @"ai_Schedule";
    NSString *sColumnName1 = @"sRRule", *sColumnName2 = @"sEXRule";
    FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
    @try {
        if ([oDB open]) {

            if (![oDB columnExists:sColumnName1 inTableWithName:sTableName]) {
                NSString *sQuery1 = [NSString stringWithFormat:
                        @"ALTER TABLE %@ ADD COLUMN %@ TEXT NOT NULL DEFAULT ('')",
                        sTableName, sColumnName1
                ];
                [oDB executeUpdate:sQuery1];
            }
            if (![oDB columnExists:sColumnName2 inTableWithName:sTableName]) {
                NSString *sQuery2 = [NSString stringWithFormat:
                        @"ALTER TABLE %@ ADD COLUMN %@ TEXT NOT NULL DEFAULT ('')",
                        sTableName, sColumnName2
                ];
                [oDB executeUpdate:sQuery2];
            }

            // will recreate view `v_schedule`
            [db_Delete DeleteSchedulesView];
        }
        [oDB close];
    } @catch (NSException *ex) {
        @try {
            [oDB close];
        } @catch (NSException *eee) {

        }
    }
}

+(void)DB_Upgrade_1_9 {
    @try {
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            @try {
                NSDictionary *oReturn = [IFConnection PostRequest:EndpointInsTypeUpgrade_3_3_1 oParams:[@{@"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"], @"sToken": [CommonUser currentToken]} mutableCopy]];
                if (oReturn != nil && ![[oReturn valueForKey:@"bSuccess"] boolValue]) {
                    NSArray *arrInsType = [oReturn valueForKey:@"lsInsType"];
                    for (int i = 0; i < arrInsType.count; i++) {
                        @try {
                            NSDictionary *oDic = [arrInsType safe_objectAtIndex: i];
                            int iSInsTypeID = [[oDic valueForKey:@"iInsTypeID"] intValue];
                            NSString *sCustom1 = [oDic valueForKey:@"sCustom1"];
                            O_InsType *oInsType = [db_Sync GetInsType:iSInsTypeID];
                            if (oInsType != nil && oInsType.iSInsTypeID > 0) {
                                [db_Sync UpdateInsType_WithCustom:oInsType.sPTC sInsTitle:oInsType.sInsTitle sType:oInsType.sType iSInsTypeID:oInsType.iSInsTypeID sCustom1:sCustom1];
                            } else {
                                if ([oDic valueForKey:@"bDeleted"] != nil && (![[oDic valueForKey:@"bDeleted"] boolValue])) {
                                    [db_Sync InsertInsType_WithCustom1:[oDic valueForKey:@"sPTC"] sInsTitle:[oDic valueForKey:@"sTitle"] sType:[oDic valueForKey:@"sType"] iSInsTypeID:[[oDic valueForKey:@"iInsTypeID"] intValue] sCustom1:[oDic valueForKey:@"sCustom1"]];
                                }
                            }
                        } @catch (NSException *eex) {
                        }
                    }
                }
            } @catch (NSException *ece) {
            }

            //For Shane to complete  oReturn should get a list of all the instype, this is to upgrade existing DB to download sCustom1 field,   will return a list of instype, please compare local DB with server via iSInsTypeID, if exist, then upgrade sCustom1 field only, otherwise insert new record to local DB.

            //use db_Sync.UpdateInsType_WithCustom to update InsType if needed..
        });

    } @catch (NSException *eeee) {}
}

+ (void)DB_Upgrade_2_0 {
    NSString *sTableName = @"ai_Photo";
    NSString *sColumnName1 = @"sField1", *sColumnName2 = @"sField2", *sColumnName3 = @"sField3";
    @try {
        FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
        if ([oDB open]) {
            [@[sColumnName1, sColumnName2, sColumnName3] forEach: ^(NSString *columnName) {
                if (![oDB columnExists:columnName inTableWithName:sTableName]) {
                    [oDB executeUpdate:[NSString stringWithFormat:
                            @"ALTER TABLE %@ ADD COLUMN %@ TEXT NOT NULL DEFAULT ('')", sTableName, columnName]];
                }
            }];
        }
        [oDB close];
    } @catch (NSException *ex) {
        //
    }
}

+ (void)DB_Upgrade_2_1 {
    @try {
        FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
        if ([oDB open]) {
            // Add the Table `ai_Project`
            NSString *sProjectTableName = @"ai_Project";
            if (![oDB tableExists:sProjectTableName]) {
                [oDB executeUpdate:[NSString stringWithFormat:
                        @"CREATE TABLE %@ (\
                        iProjectID INTEGER PRIMARY KEY AUTOINCREMENT,\
                        iSProjectID INTEGER NOT NULL DEFAULT 0,\
                        iCreatorID INTEGER NOT NULL DEFAULT 0,\
                        iCompanyID INTEGER NOT NULL DEFAULT 0,\
                        sReference TEXT,\
                        sName TEXT,\
                        sDescription TEXT,\
                        sCustomField TEXT,\
                        iGroupID INTEGER NOT NULL DEFAULT 0,\
                        iTotalIns INTEGER NOT NULL DEFAULT 0,\
                        iCompletedIns INTEGER NOT NULL DEFAULT 0,\
                        sStatus TEXT,\
                        sStatusCode TEXT,\
                        sCustom1 TEXT,\
                        sCustom2 TEXT,\
                        bActive INTEGER NOT NULL DEFAULT 0,\
                        bDeleted INTEGER NOT NULL DEFAULT 0,\
                        dtUpdate DATE, \
                        dtDateTime DATE, \
                        arrInspector TEXT, \
                        iManagerID INTEGER NOT NULL DEFAULT 0)", sProjectTableName]];
            }

            NSString *sProjectInspectionsTableName = @"ai_ProjectInspection";
            if (![oDB tableExists:sProjectInspectionsTableName]) {
                [oDB executeUpdate:[NSString stringWithFormat:
                        @"CREATE TABLE %@ (\
                        iProjectAssetInsTypeID INTEGER PRIMARY KEY AUTOINCREMENT,\
                        iSProjectAssetInsTypeID INTEGER NOT NULL DEFAULT 0,\
                        iProjectID INTEGER NOT NULL DEFAULT 0,\
                        iCompanyID INTEGER NOT NULL DEFAULT 0,\
                        iAssetID INTEGER NOT NULL DEFAULT 0,\
                        iInsTypeID INTEGER NOT NULL DEFAULT 0,\
                        iInspectionID INTEGER NOT NULL DEFAULT 0,\
                        iSInspectionID INTEGER NOT NULL DEFAULT 0,\
                        iInspectorID INTEGER NOT NULL DEFAULT 0,\
                        dtStart DATE,\
                        dtEnd DATE,\
                        sCustom TEXT,\
                        bDeleted INTEGER NOT NULL DEFAULT 0,\
                        dtUpdate DATE, \
                        dtDateTime DATE)", sProjectInspectionsTableName]];
            }
            [oDB close];
        }
    } @catch (NSException *ex) {
        //
    }
}

/*

+ (void)DB_Upgrade_1_8 {
    NSString *sTableName = @"ai_Schedule";
    NSString *sColumnName1 = @"sRRule", *sColumnName2 = @"sEXRule";
    FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getRootFilePath]];
    @try{
        if ([oDB open]){
            if (![oDB columnExists: sColumnName1 inTableWithName:sTableName]){
                NSString *sQuery1 = [NSString stringWithFormat:
                        @"ALTER TABLE %@ ADD COLUMN %@ TEXT NOT NULL DEFAULT ('')",
                        sTableName, sColumnName1
                ];
                [oDB executeUpdate:sQuery1];
            }
            if (![oDB columnExists: sColumnName2 inTableWithName:sTableName]){
                NSString *sQuery2 = [NSString stringWithFormat:
                        @"ALTER TABLE %@ ADD COLUMN %@ TEXT NOT NULL DEFAULT ('')",
                        sTableName, sColumnName2
                ];
                [oDB executeUpdate: sQuery2];
            }
        }
        [oDB close];
    } @catch (NSException *ex) {
        @try {
            [oDB close];
        } @catch(NSException *eee) {

        }
    }
}
*/

+ (void)DB_CreateInspectionView {
    FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
    @try {
        if ([oDB open]) {
            [oDB executeUpdate:sCreatePropertyView];
            //NSError *oError = [oDB lastError];
            [oDB executeUpdate:sCreateInspectionView];
        }
    } @catch (NSException *ex) {
    } @finally {
        [oDB close];
    }
}

+ (void)DB_CreateScheduleView {
    FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
    @try {
        if ([oDB open]) {
            [oDB executeUpdate:sCreatePropertyView];
            //NSError *oError = [oDB l astError];
            [oDB executeUpdate:sCreateScheduleView];
        }
    } @catch (NSException *ex) {
    } @finally {
        [oDB close];
    }
}

+ (void)DB_Upgrade_2_2 {
    // Add the Table `ai_AssetView`
    NSString *sAssetViewTableName = @"ai_AssetView";
    FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
    @try {
        if ([oDB open]) {
            [oDB executeUpdate:[NSString stringWithFormat:
                    @"CREATE TABLE IF NOT EXISTS %@ (\
                        iAssetViewID INTEGER PRIMARY KEY AUTOINCREMENT,\
                        iSAssetViewID INTEGER NOT NULL DEFAULT 0,\
                        iCustomerID INTEGER NOT NULL DEFAULT 0,\
                        iCompanyID INTEGER NOT NULL DEFAULT 0,\
                        iGroupID INTEGER NOT NULL DEFAULT 0,\
                        sName TEXT,\
                        sDescription TEXT,\
                        sCustom1 TEXT,\
                        sCustom2 TEXT,\
                        bArchived INTEGER NOT NULL DEFAULT 0,\
                        bDeleted INTEGER NOT NULL DEFAULT 0,\
                        dtUpdate DATE,\
                        arrMem TEXT)", sAssetViewTableName]];
        }
    } @catch (NSException *ex) {
        DLog(@"DB_Upgrade_2_2: %@", ex);
    } @finally {
        [oDB close];
    }

}

// Add the Table `ai_FloorPlan`
+ (void)DB_Upgrade_2_3 {
    FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
    @try {
        if ([oDB open]) {
            [oDB executeUpdate:@"CREATE TABLE IF NOT EXISTS ai_FloorPlan (\n"
                               "    iFloorPlanID INTEGER PRIMARY KEY,\n"
                               "    iSFloorPlanID INTEGER,\n"
                               "    iSAssetID INTEGER,\n"
                               "    iCreateCustomerID INTEGER,\n"
                               "    sMember TEXT,\n"
                               "    sTitle TEXT,\n"
                               "    sDesp TEXT,\n"
                               "    sPlanPath TEXT,\n"
                               "    sImagePath TEXT,\n"
                               "    sMarks TEXT,\n"
                               "    sCustom1 TEXT,\n"
                               "    sCustom2 TEXT,\n"
                               "    sCustom3 TEXT,\n"
                               "    bArchive BOOLEAN,\n"
                               "    bDeleted BOOLEAN,\n"
                               "    dtUpdate DATE,\n"
                               "    dtDateTime DATE\n"
                               ");"];
        }
    } @catch (NSException *ex) {
        DLog(@"DB_Upgrade_2_3: %@", ex);
    } @finally {
        [oDB close];
    }
}

+ (void)DB_Upgrade_2_4 {
    // drop view v_schedule
    [db_Delete DeleteSchedulesView];
}

+ (void)DB_Upgrade_2_5 {
    // Add the Table `ai_Product`
    FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
    @try {
        if ([oDB open]) {
            [oDB executeUpdate:sCreateTableProduct];
            [oDB executeUpdate:sCreateTableProductCost];
        }
    } @catch (NSException *ex) {
        DLog(@"DB_Upgrade_2_5: %@", ex);
    } @finally {
        [oDB close];
    }
}

+ (void)DB_Upgrade_2_6 {
    FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
    @try {
        if ([oDB open]) {
            // create the tables `ai_Task` and `ai_Update_AssetTask`
            [oDB executeUpdate:sCreateTableTask];
            [oDB executeUpdate:sCreateTableUpdateAssetTask];

            // add the new columns `iPNoteCategoryID`, `dtUpdate`, `dtDateTime`, `sCustom1`, `sCustom2` to the table `ai_NoticeCategory`
            [oDB executeUpdate:@"ALTER TABLE ai_NoticeCategory ADD COLUMN iPNoteCategoryID INTEGER NOT NULL DEFAULT 0"];
            [oDB executeUpdate:@"ALTER TABLE ai_NoticeCategory ADD COLUMN dtUpdate DATE"];
            [oDB executeUpdate:@"ALTER TABLE ai_NoticeCategory ADD COLUMN dtDateTime DATE"];
            [oDB executeUpdate:@"ALTER TABLE ai_NoticeCategory ADD COLUMN sCustom1 TEXT"];
            [oDB executeUpdate:@"ALTER TABLE ai_NoticeCategory ADD COLUMN sCustom2 TEXT"];
        }
    } @catch (NSException *ex) {
        DLog(@"DB_Upgrade_2_6: %@", ex);
    } @finally {
        [oDB close];
    }
}

+ (void)DB_Upgrade_2_7 {
    // Add the Table `ai_PropertyLayout`
    FMDatabase *oDB = [FMDatabase databaseWithPath:[CommonHelper getDatabaseFilePath]];
    @try {
        if ([oDB open]) {
            [oDB executeUpdate:@"CREATE TABLE IF NOT EXISTS ai_PropertyLayout (\n"
                               "    iPropertyLayoutID INTEGER PRIMARY KEY AUTOINCREMENT,\n"
                               "    iSPropertyLayoutID INTEGER NOT NULL DEFAULT 0,\n"
                               "    iCompanyID INTEGER NOT NULL DEFAULT 0,\n"
                               "    iPropertyID INTEGER NOT NULL DEFAULT 0,\n"
                               "    sPTC TEXT NOT NULL DEFAULT '',\n"
                               "    arrLayout TEXT NOT NULL DEFAULT '[]',\n"
                               "    sCustom1 TEXT,\n"
                               "    sCustom2 TEXT,\n"
                               "    bDeleted BOOLEAN NOT NULL DEFAULT 0,\n"
                               "    dtUpdate DATE,\n"
                               "    dtDateTime DATE\n"
                               ");"];
        }
    } @catch (NSException *ex) {
        DLog(@"DB_Upgrade_2_7: %@", ex);
    } @finally {
        [oDB close];
    }
}

@end
