//
//  CommonProduct.swift
//  SnapInspect3
//
//  Created by <PERSON> on 2024/7/9.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

import Foundation
import AppFeatures

class CommonProduct: NSObject {
    public static var hasEnabledProduct: Bool {
        CommonHelper.ifGetPref_Bool(PrefsKeys.kEnableProduct)
    }
    
    @discardableResult
    public static func syncAllProducts(
        from sDateTime: String, 
        iLength: Int = 1000,
        progress: @escaping (Int) -> Void
    ) -> [O_Product] {
        var lsProduct = [O_Product]()
        var iStartIndex = 0
        var newProducts: [O_Product]
        
        repeat {
            newProducts = syncProduct(sDateTime: sDateTime, iStartIndex: iStartIndex, iLength: iLength)
            db_Product.save(products: newProducts)
            lsProduct.append(contentsOf: newProducts)
            iStartIndex += iLength
            progress(lsProduct.count)
        } while newProducts.count >= iLength
        
        return lsProduct
    }
}

extension CommonProduct {
    private static func syncProduct(
        sDateTime: String,
        iStartIndex: Int,
        iLength: Int = 1000
    ) -> [O_Product] {
        let params = [
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken,
            "sDateTime": sDateTime,
            "iStartIndex": "\(iStartIndex)",
            "iLength": "\(iLength)"
        ]
        
        let response = IFConnection.postRequest(.syncProduct, oParams: params)
        guard let result = response, result["success"] as? Bool == true,
              let lsResult = result["lsProduct"] as? [[AnyHashable: Any]]
        else {
            let message = response?["message"] as? String ?? "Please try again later."
            DispatchQueue.main.async {
                UIApplication.shared.topMost?.showAlert("Error", sMessage: message)
            }
            return []
        }
        
        // Save sync date
        if iStartIndex == 0,
            let dtSyncDate = result["dtSyncDate"] as? String {
            CommonHelper.ifSavePref(PrefsKeys.kSyncProductsDate, sValue: dtSyncDate)
        }
        
        return lsResult.map(O_Product.init(dictionary:))
    }
}
