//
//  CommonRequest+Layout.swift
//  SnapInspect3
//
//  Created by <PERSON> on 2/17/25.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

import Foundation
import Extensions
import AppFeatures

extension CommonRequest {
    private typealias PropertyLayoutResponse = (layouts: [O_PropertyLayout], dtSync: String?)
    
    @discardableResult
    static func syncAllPropertyLayouts(
        sDateTime: String, 
        pageSize: Int = 1000, 
        progress: @escaping (Int) -> Void
    ) -> [O_PropertyLayout] {
        var lsPropertyLayouts = [O_PropertyLayout]()
        var iStartIndex = 0
        var newPropertyLayouts: [O_PropertyLayout]
        var dtSync: String?
        
        repeat {
            let response = syncPropertyLayouts(
                sDateTime: sDateTime, 
                iStartIndex: iStartIndex, 
                iLength: pageSize
            )
            newPropertyLayouts = response.layouts
            lsPropertyLayouts.append(contentsOf: newPropertyLayouts)
            
            // Save property layouts
            db_Common.save(propertyLayouts: newPropertyLayouts)
            
            // Assign dtSync when first batch was received
            if iStartIndex == 0 {
                dtSync = response.dtSync
            }
            
            // Increment start index
            iStartIndex += pageSize
            progress(lsPropertyLayouts.count)
        } while newPropertyLayouts.count >= pageSize
        
        // Save the latest dtSync
        if let dtSync, let dtSyncDate = dtSync.dateValue?.toFormat(yyyyMMddHHmmss) {
            CommonHelper.ifSavePref(PrefsKeys.kSyncPropertyLayoutsDate, sValue: dtSyncDate)
        }
        
        return lsPropertyLayouts
    }
    
    private static func syncPropertyLayouts(
        sDateTime: String,
        iStartIndex: Int,
        iLength: Int
    ) -> PropertyLayoutResponse {
        let params = [
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken,
            "sDateTime": sDateTime,
            "iStartIndex": "\(iStartIndex)",
            "iLength": "\(iLength)"
        ]
        
        let response = IFConnection.postRequest(.propertyLayoutSync, oParams: params, httpBodyFormat: .JSON)
        guard let result = response, result["success"] as? Bool == true,
              let lsResult = result["lsPropertyLayout"] as? [[AnyHashable: Any]]
        else {
            let message = response?["message"] as? String ?? "Please try again later."
            DispatchQueue.main.async {
                UIApplication.shared.topMost?.showAlert("Error", sMessage: message)
            }
            return (layouts: [], dtSync: nil)
        }
        
        return (
            layouts: lsResult.map(O_PropertyLayout.init(dictionary:)),
            dtSync: result["dtSync"] as? String
        )
    }
    
    @objc static func getAssetLayoutV3(
        iSAssetID: Int, 
        iSInsTypeID: Int,
        sPTC: String,
        completion: @escaping ([O_Layout], String?) -> Void
    ) {
        guard NetworkConnection.isNetworkReachable else {
            completion([], "No network connection")
            return
        }
        
        let params = [
            "iCustomerID": "\(CommonUser.iCustomerID)",
            "sToken": CommonUser.currentToken,
            "iAssetID": "\(iSAssetID)",
            "iInsTypeID": "\(iSInsTypeID)"
        ]
        
        DispatchQueue.global(qos: .userInitiated).async {
            let response = IFConnection.postRequest(.getAssetLayoutV3, oParams: params)
            if let result = response, result["success"] as? Bool == true,
               let sArrLayoutJson = result["arrLayout"] as? String {
                let layouts = CommonJson.oJsonString(toArray: sArrLayoutJson) as? [[AnyHashable: Any]] ?? []
                let aiLayouts = layouts
                    .map(O_PropertyLayout.LayoutItem.init(dictionary:))
                    .enumerated()
                    .map { index, item in
                        let layout = db_Common.getLayoutWithLayoutItem(item, sPTC: sPTC)
                        layout.sName = item.layoutName ?? ""
                        layout.iOrder = index
                        layout.iCount = 1
                        return layout
                    }
                DispatchQueue.main.async {
                    if !aiLayouts.isEmpty {
                        completion(aiLayouts, nil)
                    } else {
                        completion([], "No layouts found")
                    }
                }
            } else {
                DispatchQueue.main.async {
                    completion([], response?["message"] as? String ?? "Please try again later.")
                }
            }
        }
    }
}
