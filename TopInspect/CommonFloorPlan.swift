//
// Created by <PERSON> on 2023/11/7.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

import Foundation
import Extensions

/// A class responsible for handling floor plan related operations.
class CommonFloorPlan: NSObject {

    /// Loads floor plans for a given inspection item ID.
    /// - Parameters:
    ///   - iSInsItemID: The inspection item ID.
    ///   - completion: A closure to be executed once the load operation is complete.
    @objc static func loadFloorPlans(iSInsItemID: Int, completion: @escaping () -> Void) {
        guard iSInsItemID > 0, let iCustomerID = CommonHelper.ifGetPref("iCustomerID"),
              let sToken = CommonUser.currentToken else {
            completion()
            return
        }

        let params = [
            "iInsItemID": "\(iSInsItemID)", "iCustomerID": "\(iCustomerID)", "sToken": sToken
        ]

        guard NetworkConnection.isNetworkReachable else {
            notifyNetworkError()
            completion()
            return
        }

        DispatchQueue.global(qos: .background).async {
            let response = IFConnection.postRequest(.getItemFloorPlans, oParams: params)
            DispatchQueue.main.async {
                guard let result = response, result["success"] as? Bool == true,
                      let lsResult = result["lsResult"] as? [[AnyHashable: Any]], !lsResult.isEmpty else {
                    let message = response?["message"] as? String ?? "Please try again later."
                    UIApplication.shared.showAlert("Error", sMessage: message)
                    completion()
                    return
                }
                completion()
            }
        }
    }

    /// Loads floor plans for a given asset ID.
    /// - Parameters:
    ///   - iSAssetID: The asset ID.
    ///   - completion: A closure to be executed once the load operation is complete.
    @objc static func loadFloorPlans(iSAssetID: Int, completion: @escaping () -> Void) {
        guard iSAssetID > 0, let iCustomerID = CommonHelper.ifGetPref("iCustomerID"),
              let sToken = CommonUser.currentToken else {
            completion()
            return
        }

        let params = [
            "iAssetID": "\(iSAssetID)", "iCustomerID": "\(iCustomerID)", "sToken": sToken,
        ]

        guard NetworkConnection.isNetworkReachable else {
            notifyNetworkError()
            completion()
            return
        }

        DispatchQueue.global(qos: .background).async {
            let response = IFConnection.postRequest(.getAssetFloorPlans, oParams: params)
            DispatchQueue.main.async {
                guard let result = response, result["success"] as? Bool == true,
                      let lsFloorPlan = result["lsFloorPlan"] as? [[AnyHashable: Any]] else {
                    let message = response?["message"] as? String ?? "Please try again later."
                    UIApplication.shared.showAlert("Error", sMessage: message)
                    completion()
                    return
                }

                let floorPlans = lsFloorPlan.map(O_FloorPlan.init(dictionary:))
                let existingFloorPlans = db_FloorPlan.getFloorPlans(iSAssetID: iSAssetID)
                let floorPlansUpdated = floorPlans.filter { floorPlan in
                    guard let existingFloorPlan = existingFloorPlans.first(
                            where: { floorPlan.iSFloorPlanID == $0.iSFloorPlanID }) else {
                        return true
                    }
                    return floorPlan.dtUpdate > existingFloorPlan.dtUpdate
                }

                let floorPlansRemoved = existingFloorPlans.filter { existingFloorPlan in
                    !floorPlans.contains { $0.iSFloorPlanID == existingFloorPlan.iSFloorPlanID }
                }
                floorPlansRemoved.map(\.iSFloorPlanID).forEach(db_FloorPlan.removeFloorPlan(iSFloorPlanID:))

                if !floorPlans.isEmpty {
                    if !floorPlansUpdated.isEmpty {
                        db_FloorPlan.save(floorPlans: floorPlansUpdated)
                        CommonMedia.downloadFloorPlanImages(floorPlansUpdated, completion: completion)
                    } else {
                        completion()
                    }
                } else {
                    db_FloorPlan.removeFloorPlans(iSAssetID: iSAssetID)
                    completion()
                }
            }
        }
    }

    /// Uploads a floor plan image for a given asset ID.
    /// - Parameters:
    ///   - sPhotoFileURL: The URL of the photo file to be uploaded.
    ///   - iSAssetID: The asset ID.
    ///   - completion: A closure to be executed once the upload operation is complete, with the floor plan ID and success status.
    @objc static func uploadFloorPlan(sPhotoFileURL: URL, iSAssetID: Int, completion: @escaping (Int, Bool) -> Void) {
        guard let iCustomerID = CommonHelper.ifGetPref("iCustomerID"),
              let sToken = CommonUser.currentToken else {
            completion(-1, false)
            return
        }

        let params = [
            "iCustomerID": "\(iCustomerID)", 
            "sToken": sToken,
            "iAssetID": "\(iSAssetID)"
        ]

        guard NetworkConnection.isNetworkReachable else {
            notifyNetworkError()
            completion(-1, false)
            return
        }

        DispatchQueue.global(qos: .background).async {
            let response = IFConnection.postRequest(
                withFile: .initFloorPlan,
                oParams: params,
                sPath: sPhotoFileURL.path,
                sFileFieldName: Constants.kUploadFormFileName
            )
            DispatchQueue.main.async {
                if let oReturn = response, oReturn["success"] as? Bool == true {
                    let result = O_FloorPlan(dictionary: oReturn["oFloorPlan"] as? [AnyHashable: Any] ?? [:])
                    if let destination = result.sPlanFile {
                        try? FileManager.default.moveItem(at: sPhotoFileURL, to: destination)
                        result.sPlanPath = result.sPlanFileName
                        result.sImagePath = result.sImageFileName
                    }
                    db_FloorPlan.save(floorPlans: [result])
                    completion(result.iSFloorPlanID, true)
                } else {
                    let message = response?["message"] as? String ?? "Please try again later."
                    UIApplication.shared.showAlert("Error", sMessage: message)
                    completion(-1, false)
                }
            }
        }
    }
    
    /// Uploads floor plan marks for a given floor plan ID.
    /// - Parameters:
    ///   - sMarks: The marks to be uploaded.
    ///   - iSFloorPlanID: The floor plan ID.
    ///   - sThumbnailPath: The path to the thumbnail image.
    ///   - completion: A closure to be executed once the upload operation is complete, with the updated floor plan object.
    @objc static func uploadFloorPlanMarks(_ sMarks: String, iSFloorPlanID: Int, sThumbnailPath: String?, completion: @escaping (O_FloorPlan?) -> Void) {
        guard let iCustomerID = CommonHelper.ifGetPref("iCustomerID"),
              let sToken = CommonUser.currentToken else {
            completion(nil)
            return
        }
    
        let oParams: [String: Any] = [
            "iCustomerID": iCustomerID,
            "sToken": sToken,
            "iFloorPlanID": "\(iSFloorPlanID)",
            "sMarks": sMarks
        ]

        guard NetworkConnection.isNetworkReachable else {
            notifyNetworkError()
            completion(nil)
            return
        }

        DispatchQueue.global(qos: .background).async {
            let oReturn = IFConnection.postRequest(
                withFile: .uploadFloorPlanMarks, 
                oParams: oParams,
                sPath: sThumbnailPath,
                sFileFieldName: Constants.kUploadFormFileName
            )
            DispatchQueue.main.async {
                if let oReturn = oReturn, let success = oReturn["success"] as? Bool, success {
                    let result = O_FloorPlan(dictionary: oReturn["oFloorPlan"] as? [AnyHashable: Any] ?? [:])
                    db_FloorPlan.save(floorPlans: [result])
                    completion(result)
                } else {
                    let sMessage = oReturn?["message"] as? String ?? "Failed to save blueprint marks."
                    UIApplication.shared.showAlert("Error", sMessage: sMessage)
                    completion(nil)
                }
            }
        }
    }
}

extension CommonFloorPlan {
    /// Notifies the user of a network error (no internet connection).
    private static func notifyNetworkError() {
        UIApplication.shared.showAlert("Error", sMessage: "Please make sure you are connected to Internet.")
    }
}

extension UIApplication {
    /// Displays an alert with the given title and message.
    /// - Parameters:
    ///   - title: The title of the alert.
    ///   - sMessage: The message of the alert.
    fileprivate func showAlert(_ title: String, sMessage: String) {
        Self.shared.topMost?.showAlert(title, sMessage: sMessage)
    }
}
