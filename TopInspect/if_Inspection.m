//
//  if_Inspection.m
//  InspectionFolio
//
//  Created by <PERSON> on 18/01/14.
//  Copyright (c) 2014 Cloudya. All rights reserved.
//

#import "if_Inspection.h"
#import "db_Common.h"
#import "if_NewAsset.h"
#import "db_Upload.h"
//#import "SALQuickTutorialViewController.h"
#import "if_ServerEmail.h"
#import <MessageUI/MFMailComposeViewController.h>

#import "UI_StatusButton.h"
#import "InspectCell.h"
#import "SISegmentedControl.h"
#import "if_InspectionComment.h"
#import "NSArray+HighOrderFunction.h"

@interface if_Inspection () <SISegmentedControlDelegate>
@property (weak, nonatomic) IBOutlet UIView *segView;
@property (weak, nonatomic) IBOutlet UIButton *btnAllUpload;
@property (weak, nonatomic) IBOutlet UILabel *lblNone;
@property (weak, nonatomic) IBOutlet UITextField *searchbar;
@property (weak, nonatomic) IBOutlet UIView *viewAllUpload;
@property (weak, nonatomic) IBOutlet UIView *deadView;
@property (weak, nonatomic) IBOutlet UITableView *oTableView;
@property (weak, nonatomic) IBOutlet UIButton *btn_Setting;
@property (weak, nonatomic) IBOutlet UIButton *btn_AddInspection;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *btnAddInspectionWidth;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *btnAddInspectionLeading;
@property (strong, nonatomic) MBProgressHUD *hud;
@end

@implementation if_Inspection {
    int iWidth;
    NSArray *arrInsType;
    NSMutableArray *arrCompletedIns;
    NSMutableArray *arrCurrentIns;
    NSMutableArray *arrUploadedIns;
   // NSMutableArray *tmpCompletedIns;
   // NSMutableArray *tmpCurrentIns;
  //  NSMutableArray *tmpUploadedIns;
    SISegmentedControl *segControl;
    NSInteger selectedTabIndex;
  //  int iCurrentCursor;
   // int iSearchCloudLength;
}

- (id)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil
{
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if (self) {
        // Custom initialization
    }
    
  //  iCurrentCursor = 0;
  //  iSearchCloudLength = 50;
    return self;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];

    // [DB_Upgrade DB_CreateInspectionView];
    [CommonAnalytics trackEvent:@"iOS View Inspections" meta:nil];
    [db_Log InsertLog:@"Event" sMessage:@"Inspection List View"];

    CGSize oSize = [[UIScreen mainScreen] bounds].size;
    iWidth = (int) oSize.width;
    if ([CommonHelper bKioskMode]) {
        [_btn_Setting setHidden:YES];
        [_btnSync setHidden:YES];
        // [btn_Add setHidden:YES];
        [_viewAllUpload setHidden:YES];
    }

    [if_AppDelegate ShowTutorial:@"TUT_INS_LIST_HEAD" sMessage:@"TUT_INS_LIST_BODY"];
    [self ReloadTable:true sTerm:@""];
    // Permission Edit/Add Asset to prevent start new inspection icon on top right on the inspection tab.
    [self setShowsAddInspectionButton:[self canNewInspection]];
}

- (BOOL)canNewInspection {
    return [CommonPermission bEnableNewInspection] && [CommonPermission bEnableEditAsset];
}

#pragma mark -- define Actions

- (IBAction)settingAction:(id)sender {
    [_oHomeScreenDelegate GoToSetting];
}
- (IBAction)syncAction:(id)sender {
    [_oHomeScreenDelegate Sync];
}

- (IBAction)addInspectAction:(UIButton*)sender {
    [self btn_AddInspectionAction];
}

- (IBAction)addNewInspect:(id)sender {
    [self btn_AddInspectionAction];
}

- (IBAction)uploadAll:(id)sender {
    @weakify(self)
    [self.oHomeScreenDelegate Upload_Action:0 completion:^{
        @strongify(self)
        if ([[db_Upload ReadyToUploadInspection: 0] count] > 0) {
            [self ReloadTable:true sTerm:[self.searchbar text]];
        } else {
            [self selectUploadedSegment];
        }
    }];
}

-(void) willRotateToInterfaceOrientation:(UIInterfaceOrientation)toInterfaceOrientation duration:(NSTimeInterval)duration {
    if (toInterfaceOrientation == UIInterfaceOrientationLandscapeLeft || toInterfaceOrientation == UIInterfaceOrientationLandscapeRight) {

            CGSize oSize = [[UIScreen mainScreen] bounds].size;
            iWidth = oSize.height;
        
    } else if (toInterfaceOrientation == UIInterfaceOrientationPortrait || toInterfaceOrientation == UIInterfaceOrientationPortraitUpsideDown) {

            CGSize oSize = [[UIScreen mainScreen] bounds].size;
            iWidth = oSize.height;
    }
}

- (void) ReloadTable:(bool)bReloadData {
    [self ReloadTable:YES sTerm:@""];
}

- (void) ReloadTable:(bool)bReloadData sTerm:(NSString *)sTerm{
    
    if (bReloadData){
        if (segControl.selectedIndex == 0){
            arrCurrentIns =[db_Inspection GetInspectionsBySearch:sTerm  bCompleted:false bSynced:false];
            _viewAllUpload.hidden = YES;
        }
        else if (segControl.selectedIndex == 1){
            arrCompletedIns = [db_Inspection GetInspectionsBySearch:sTerm bCompleted:true  bSynced:false];
            _viewAllUpload.hidden = NO;
        }
        else if (segControl.selectedIndex == 2){
            arrUploadedIns = [db_Inspection GetInspectionsBySearch:sTerm bCompleted:true bSynced:true];
            _viewAllUpload.hidden = YES;
        }
    }

    [self setShowsDeadView: NO];
    if (arrCompletedIns == nil || [arrCompletedIns count] == 0){
        arrCompletedIns = [[NSMutableArray alloc] init];
        if (selectedTabIndex == 1) {
            _viewAllUpload.hidden = YES;
            [self setShowsDeadView: YES];
        }
    }
    
    if (arrCurrentIns == nil || [arrCurrentIns count] == 0){
        arrCurrentIns = [[NSMutableArray alloc] init];
        if (selectedTabIndex == 0) {
            [self setShowsDeadView: YES];
            if ([CommonHelper bKioskMode]){
                self.lblNone.text =  @"Go to 'Schedule' tab to start the inspection.";
            }
        }
    }
    if (arrUploadedIns == nil || [arrUploadedIns count] == 0){
        arrUploadedIns = [[NSMutableArray alloc] init];
        if (selectedTabIndex == 2) {
            [self setShowsDeadView: NO];
        }
    }
    
    self.tableContents = @{
            @"Inspection in progress": arrCurrentIns,
            @"Completed Inspection": arrCompletedIns,
            @"Uploaded Inspections": arrUploadedIns
    };
    self.sortedKeys = @[
            @"Inspection in progress",
            @"Completed Inspection",
            @"Uploaded Inspections"
    ];
    
    [self.oTableView reloadData];
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    [self.navigationController applyBlueTheme];
    [self.navigationController setNavigationBarHidden:YES];
    
    self.edgesForExtendedLayout = UIRectEdgeAll;
    self.oTableView.contentInset = UIEdgeInsetsMake(0.0f, 0.0f, CGRectGetHeight(self.tabBarController.tabBar.frame), 0.0f);
    self.oTableView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
    self.oTableView.rowHeight = UITableViewAutomaticDimension;
    
    self.longPressRecognizer = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(recognizerFired:)];
    self.longPressRecognizer.delegate = self;
    [self.tabBarController.tabBar addGestureRecognizer:self.longPressRecognizer];
    
    //Segmented Control
    NSArray * titles = @[@"In Progress", @"Completed", @"Uploaded"];
    
    //UIColor *activeColor = [UIColor colorWithRed:62/255.0f green:107/255.0f blue:255/255.0f alpha:1];
    segControl = [[SISegmentedControl alloc] initWithFrame:_segView.bounds];
    //segControl.borderColor = [UIColor clearColor];
    //segControl.deactiveColor = activeColor;
    //segControl.activeTextColor = activeColor;
   // segControl.activeColor = [UIColor whiteColor];
    segControl.roundCorner = 18;
    segControl.font = [UIFont systemFontOfSize:14];
    [segControl setNames:titles];
    segControl.selectedIndex = 0;
    segControl.delegate = self;
    [_segView addSubview:segControl];
    segControl.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
 
    _searchbar.delegate = self;
    
    UIColor *shadowColor = UIColorFromRGB(0xDFDFDF);
    
    [CommonHelper applySketchShadow:_viewAllUpload.layer color:shadowColor alpha:0.9 x:1 y:7 blur:9 spread:0];
    _viewAllUpload.layer.cornerRadius = 21;

    self.btnSync.hidden = YES;
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
   
}

- (void)recognizerFired:(UILongPressGestureRecognizer *) recognizer {

    if (recognizer.state == UIGestureRecognizerStateBegan) {
        if ([CommonHelper bKioskMode]){
            UITabBar *tabBar = ((UITabBar* )recognizer.view);
            
            if (tabBar.selectedItem == self.tabBarItem) {
                UIAlertController * alert=[UIAlertController alertControllerWithTitle:@"Submit Data"
                                                                              message:@"Please do not press Yes unless received the instruction from your Administrator. Thank you!"
                                                                       preferredStyle:UIAlertControllerStyleAlert];
                
                UIAlertAction* yesButton = [UIAlertAction actionWithTitle:@"Yes, please"
                                                                    style:UIAlertActionStyleDefault
                                                                  handler:^(UIAlertAction * action)
                {
                    [self.oHomeScreenDelegate ExternalRecover];
                }];
                
                UIAlertAction* noButton = [UIAlertAction actionWithTitle:@"No, thanks"
                                                                   style:UIAlertActionStyleDefault
                                                                 handler:^(UIAlertAction * action)
                {
                }];
                
                [alert addAction:yesButton];
                [alert addAction:noButton];
                
                [self presentViewController:alert animated:YES completion:nil];
                
            }
        }
    }
    
}
-(void)btn_Upload:(UIBarButtonItem *)myButton {
    @weakify(self)
    [self.oHomeScreenDelegate Upload_Action:0 completion:^{
        @strongify(self)
        if ([[db_Upload ReadyToUploadInspection: 0] count] > 0) {
            [self ReloadTable:true sTerm:[self.searchbar text]];
        } else {
            [self selectUploadedSegment];
        }
    }];
}

-(void)btn_Upload_1:(UIButton *)sender {
    @weakify(self)
    [self.oHomeScreenDelegate Upload_Action:(int)sender.tag completion:^{
        @strongify(self)
        if ([[db_Upload ReadyToUploadInspection: 0] count] > 0) {
            [self ReloadTable:true sTerm:[self.searchbar text]];
        } else {
            [self selectUploadedSegment];
        }
    }];
}

- (void)selectUploadedSegment {
    self->segControl.selectedIndex = 2;
}

- (void)selectCompletedSegment {
    self->segControl.selectedIndex = 1;
}

- (void)btn_AddInspectionAction {
    @try {
        if (![self validateNewInspection]) {
            return;
        }
        // oInsTypeView = nil;
        if (arrInsType){
            arrInsType = nil;
        }
        arrInsType = [db_Common GetIntTypes];
        @weakify(self)
        [InsTypeSelectionView show:arrInsType didSelectOption:^(O_InsType * _Nonnull oInsType) {
            @strongify(self)
            if_NewAsset *oNewAsset = [[if_NewAsset alloc] init];
            oNewAsset.sPTC = oInsType.sPTC;
            oNewAsset.iSInsTypeID = oInsType.iSInsTypeID;
            oNewAsset.sType = oInsType.sType;
            [db_Log InsertLog:@"Prepare Inspection" sMessage:[NSString stringWithFormat:@"Start New (%@) Inspection For New Asset", oInsType.sInsTitle]];
            [self.navigationController pushViewController:oNewAsset animated:YES];
        }];
    }
    @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
}


- (NSString *)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component {
    O_InsType *oInsType = [arrInsType objectAtIndex:row];
	return oInsType.sInsTitle;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return 1;
}

- (NSInteger)tableView:(UITableView *)table numberOfRowsInSection:(NSInteger)section {
    NSArray *listData = self.tableContents[self.sortedKeys[selectedTabIndex]];
  //NSString *cc = [_searchbar text] ;
    //if (selectedTabIndex == 2 && [[_searchbar text] length] > 0 && (![CommonHelper bKioskMode])){
  //      return [listData count] + 1;
   // }
    return [listData count];
}
- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    return 55;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    @autoreleasepool {
        @try {
            static NSString *SimpleTableIdentifier = @"InspectCell";
            
            InspectCell *cell = (InspectCell *)[tableView dequeueReusableCellWithIdentifier:SimpleTableIdentifier];
            if (cell == nil)
            {
                NSArray *nib = [[NSBundle mainBundle] loadNibNamed:@"InspectCell" owner:self options:nil];
                cell = [nib objectAtIndex:0];
            }
        //    int cc = [indexPath row];
        //    int dd = [tableView numberOfRowsInSection:0];
          /*  if (selectedTabIndex == 2 && [[_searchbar text] length] > 0 && ([arrUploadedIns count] == [indexPath row]))
            {
                UITableViewCell *oTempCell = [tableView dequeueReusableCellWithIdentifier:@"InsSearchCell"];
                if (oTempCell == nil)
                {
                    oTempCell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"InsSearchCell"];
                }
                oTempCell.textLabel.text = @"Click to Search Cloud ... ";
                oTempCell.textLabel.font = [UIFont italicSystemFontOfSize:15];
                oTempCell.accessoryView = nil;
                oTempCell.textLabel.textAlignment = NSTextAlignmentCenter;
               //oTempCell.imageView = [UIImage imageNamed:@""]
                return oTempCell;
            }*/
           // int iTemp1 =selectedTabIndex;
           // int iTemp2 = indexPath.row;

            O_InspectionTemp *oInsTemp = [self.tableContents[self.sortedKeys[selectedTabIndex]] objectAtIndex:[indexPath row]];
            
            cell.lblName.text = (oInsTemp.sAddress == nil || [oInsTemp.sAddress isEqualToString:@""]) ? oInsTemp.sTitle : oInsTemp.sAddress;
            cell.ivInspect.hidden = YES;
            cell.lblDetails.text = oInsTemp.bEmpty ? @"" : [NSString stringWithFormat:@"%@ on %@", oInsTemp.sInsTitle, oInsTemp.dtEndDate == nil || [oInsTemp.dtEndDate isEqualToString:@""]  ? (oInsTemp.dtStartDate == nil || [oInsTemp.dtStartDate isEqualToString:@""] ? @"In Progressing" : oInsTemp.dtStartDate) : oInsTemp.dtEndDate];
           
            //TODO - Set O_InspectionTemp's References
            if (oInsTemp.sRef != nil && [oInsTemp.sRef length] > 0){
                cell.lblRef.text = oInsTemp.sRef;//oInsTemp.sRef;   //Seems it is empty
                [cell.lblRef setHidden:NO];
            }
            else{
                cell.lblRef.text = @"";//oInsTemp.sRef;   //Seems it is empty
                [cell.lblRef setHidden:YES];
            }
            
            
            cell.statusView.hidden = YES;
            cell.lblType.hidden = NO;
            cell.btnAction.hidden = NO;
            cell.btnAction.enabled = YES;
            cell.lblType.text = oInsTemp.sTag;
            cell.typeView.backgroundColor = [oInsTemp getTypeColor];
            cell.ivInspect.hidden = YES;
             cell.titleLeftMargin.constant = SMALL_INSPECT_CELL_LEFT_MARGIN;
            cell.btnAction.userInteractionEnabled = NO;
            cell.accessoryView = nil;
            if (oInsTemp.bEmpty){
                [cell.lblName setTextColor:[UIColor lightGrayColor]];
                cell.btnAction.hidden = YES;
            }
            else{
                [cell.lblName setTextColor:[UIColor blackColor]];
                cell.btnAction.hidden = NO;
                
                if (selectedTabIndex == 0){
                    cell.ivInspect.hidden = YES;
                    cell.titleLeftMargin.constant = SMALL_INSPECT_CELL_LEFT_MARGIN;
                    [cell.btnAction setImage:[UIImage imageNamed:@"icon_more"] forState:UIControlStateNormal];
                    //[cell.btnAction addTarget:self action:@selector(btn_GoToInspection:) forControlEvents:UIControlEventTouchUpInside];
                    [cell.btnAction setTag:oInsTemp.iInsID];
                    
                    
                }
                else if (selectedTabIndex == 1){
                    [cell.btnAction setTag:oInsTemp.iInsID];
                    cell.ivInspect.hidden = YES;
                    cell.titleLeftMargin.constant = SMALL_INSPECT_CELL_LEFT_MARGIN;
                    cell.btnAction.userInteractionEnabled = YES;
                    [cell.btnAction setImage:[UIImage imageNamed:@"icon_upload"] forState:UIControlStateNormal];
                    [cell.btnAction addTarget:self action:@selector(btn_Upload_1:) forControlEvents:UIControlEventTouchUpInside];
                }
                else{
                    
                    if (!oInsTemp.bEmpty){

                        [cell.btnAction setTag:oInsTemp.iSInsID];
                       // cell.titleLeftMargin.constant = FULL_INSPECT_CELL_LEFT_MARGIN;
                        //cell.ivInspect.hidden = NO;
                        //cell.ivInspect.image = [oInsTemp getTypeImage];
                        cell.statusView.hidden = [oInsTemp.sStatus length] <= 0;
                        cell.btnAction.hidden = [oInsTemp.sStatus length] > 0;
                        
                        if ([oInsTemp.sStatus length] > 0){
                            cell.accessoryView=  [[UI_StatusButton alloc] initWithFrame:CGRectMake(0, 0, 40, 40) sText:oInsTemp.sStatus
                                    oColor:[UIColor colorFromHexString:oInsTemp.sStatus_Color]];
                        }
                        else{
                            [cell.btnAction setImage:[UIImage imageNamed:@"icon_more"] forState:UIControlStateNormal];
                        }
                    }
                    
                }
            }
            
            return cell;
        }
        @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
        }
    }

    return nil;
}

-(void)btn_GoToInspection:(UIButton *)sender{
    int iInsID = (int)sender.tag;
    
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Action" message:nil preferredStyle:UIAlertControllerStyleActionSheet];
    
    UIAlertAction *oEdit = [UIAlertAction actionWithTitle:@"Edit" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
        [self PushInspection:iInsID];
    }];
    [alert addAction:oEdit];
    
    UIAlertAction *oClose = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil];
    [alert addAction:oClose];

    alert.modalPresentationStyle = UIModalPresentationFullScreen;
    alert.popoverPresentationController.sourceView = sender;
    alert.popoverPresentationController.sourceRect = sender.bounds;
    [self presentViewController:alert animated:YES completion:nil];
}


- (void)tableView:(UITableView *)tableView
didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    @try {
        [tableView deselectRowAtIndexPath:indexPath animated:YES];
        if (selectedTabIndex == 0){
            O_InspectionTemp *oTemp = [arrCurrentIns objectAtIndex:[indexPath row]];
            if (oTemp.iInsID > 0){
               // [db_Log InsertLog:@"Inspection" sMessage:[NSString stringWithFormat:@"View Report %d", iSInsID]];
                [self PushInspection:oTemp.iInsID];
            }
        }
        else if (selectedTabIndex == 1){
            O_InspectionTemp *oTemp = [arrCompletedIns objectAtIndex:[indexPath row]];
            if (oTemp.iInsID > 0){
                [self PushInspection:oTemp.iInsID];
            }
        }
        else if (selectedTabIndex == 2){
            //if ([arrUploadedIns count]  == [indexPath row]){
            //    [self SearchCloud];
           // }
           // else{
                O_InspectionTemp *oTemp = [arrUploadedIns objectAtIndex:[indexPath row]];
                if (oTemp.iSInsID > 0){
                    [self ViewReport:oTemp.iSInsID sTitle:oTemp.sTitle sInsTitle:oTemp.sInsTitle oRect:[[tableView cellForRowAtIndexPath:indexPath] bounds]  oView:[tableView cellForRowAtIndexPath:indexPath] iSAssetID:oTemp.iSAssetID];
                }
            //}
        }
    }
    @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
}

-(void)ViewReport:(int)iInsID sTitle:(NSString *)sTitle sInsTitle:(NSString *)sInsTitle oRect:(CGRect)oRect oView:(UIView *)oView iSAssetID:(int)iSAssetID{
    if ([self.navigationController.visibleViewController isKindOfClass:[UIAlertController class]]) {
        return;
    }
    MBProgressHUD *hud = self.hud;
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:sTitle message:@"" preferredStyle:UIAlertControllerStyleActionSheet];
    
        UIAlertAction *oViewPDF = [UIAlertAction actionWithTitle:@"View PDF" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
                        [CommonHelper_SendReport ViewReport_Internal:hud oVC:self iInsID:iInsID sTitle:sTitle sType:@"P"];
        }];
        [alert addAction:oViewPDF];
    

    if (![CommonHelper bKioskMode]){
        UIAlertAction *oSendPDF = [UIAlertAction actionWithTitle:@"Send PDF" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
            [CommonHelper_SendReport SendReport_Internal:hud oVC:self iInsID:iInsID sTitle:sTitle sType:@"P" iSAssetID:iSAssetID];
            
        }];
        [alert addAction:oSendPDF];
        if ([CommonUI bEnableWord]){
                UIAlertAction *oSendWord = [UIAlertAction actionWithTitle:@"Send Word Doc" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
                    [CommonHelper_SendReport SendReport_Internal:hud oVC:self iInsID:iInsID sTitle:sTitle sType:@"D" iSAssetID:iSAssetID];
                    
                }];
                [alert addAction:oSendWord];
        }
        O_InspectionTemp *oInsTemp = [self GetUploadedInspectionBySInsID:iInsID];
        if (oInsTemp != nil) {
            if ([CommonPermission bEnableEditInspection]) {
                UIAlertAction *oEditInspection = [UIAlertAction actionWithTitle:@"Edit Inspection" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
                    if (![if_AppDelegate.shared bInternet_WithMessage:@"Please connect to Internet."]) {
                        return;
                    }
                    [self EditInspection:oInsTemp];
                }];
                [alert addAction:oEditInspection];
            }
        }
        if ([CommonPermission bEnableEditInspection] && [CommonHelper bStatus]){
            UIAlertAction *oCopyInspection= [UIAlertAction actionWithTitle:@"Update Status" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
            
                if (![if_AppDelegate.shared bInternet_WithMessage:@"Please connect to Internet."]){
                    
                    return;
                }
                [self UpdateStatusAlert:oView oRect:oRect oInsTemp:(O_InspectionTemp *)oInsTemp];
                
            }];
            [alert addAction:oCopyInspection];
        }
        UIAlertAction *oComment = [UIAlertAction actionWithTitle:@"Comments" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            if (![if_AppDelegate.shared bInternet_WithMessage:@"Please connect to Internet."]){
                
                return;
            }
            [if_InspectionComment push:self.storyboard fromController:self data:iInsID];
        }];
        [alert addAction:oComment];
    }

    
    
    UIAlertAction *oClose = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil];
    [alert addAction:oClose];
    
    if ([if_AppDelegate biPad]){
        [alert setModalPresentationStyle:UIModalPresentationFullScreen];
        UIPopoverPresentationController *popPresenter = [alert
                                                         popoverPresentationController];
       // popPresenter.sourceView = button;
        popPresenter.sourceView = oView;
        popPresenter.sourceRect = oRect;

        [self presentViewController:alert animated:YES completion:nil];
    }
    else{
        [self presentViewController:alert animated:YES completion:nil];
    }
    
}
-(void)UpdateStatusAlert:(UIView *)oView oRect:(CGRect) oRect oInsTemp:(O_InspectionTemp *)oInsTemp{
    
    UIAlertController *oChildAlert = [UIAlertController alertControllerWithTitle:@"Update Status" message:@"" preferredStyle:UIAlertControllerStyleActionSheet];
    NSArray *arrStatus = [CommonHelper GetStatusArray:0];
    
    for (NSDictionary *oTemp in arrStatus){
        
        
        UIAlertAction *oAlertStatus = [UIAlertAction actionWithTitle:[oTemp valueForKey:@"sName"] style:UIAlertActionStyleDefault handler:^(UIAlertAction *action){
            @try {
                if (![if_AppDelegate.shared bInternet:false]){
                    // [self ShowAlert:@"Error" sMessage:@"Please connect to Internet to proceed."];
                    return;
                }
                MBProgressHUD *hud = [[MBProgressHUD alloc] si_initWithView:self.view];
                [self.view addSubview:hud];
                self.hud = hud;
                
                hud.labelText = @"Processing...";
                
                [hud showAnimated:YES whileExecutingBlock:^{
                    NSMutableDictionary *oRefreshToken = [@{
                            @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
                            @"sToken": [CommonUser currentToken],
                            @"iInspectionID": [NSString stringWithFormat:@"%d", oInsTemp.iSInsID],
                            @"sStatus": [oTemp valueForKey:@"sName"],
                            @"sColorCode": [oTemp valueForKey:@"sColorCode"]
                    } mutableCopy];
                    NSDictionary *oReturn = [[IFConnection PostRequest:EndpointUpdateInspectionStatusAPI oParams:oRefreshToken] mutableCopy];
                    if (oReturn != nil && [[oReturn valueForKey:@"success"] boolValue]){
                        O_Inspection *oIns = [db_Inspection GetInspectionBySInsID:oInsTemp.iSInsID];
                        
                        oIns.sCustom1 = [CommonHelper DictionaryToJson: [oReturn[@"oInspection"] objectForKey:@"sCustom1"]];
                        [db_Inspection UpdateInspectionCustom:oIns];
                        oInsTemp.sStatus =[oTemp valueForKey:@"sName"];
                        oInsTemp.sStatus_Color =[oTemp valueForKey:@"sColorCode"];
                        [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                            arrUploadedIns = [db_Inspection GetInspectionsBySearch:@"" bCompleted:true bSynced:true];
                            self.tableContents = @{
                                    @"Inspection in progress": arrCurrentIns,
                                    @"Completed Inspection": arrCompletedIns,
                                    @"Uploaded Inspections": arrUploadedIns
                            };
                            [self.oTableView reloadData];
                        }];
                    }
                    else if (oReturn != nil && (![[oReturn valueForKey:@"success"] boolValue])){
                        [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                            [self ShowAlert:@"Error" sMessage:[oReturn valueForKey:@"message"]];
                        }];
                    }
                    
                    
                    
                } completionBlock:^{
                    [hud removeFromSuperview];
                }];
                
                

            }
            @catch (NSException *exception) {
                [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
            }
            
        }];
        [oChildAlert addAction:oAlertStatus];
    }
    
    
    
    
    UIAlertAction *oChildClose = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil];
    [oChildAlert addAction:oChildClose];
    if ([if_AppDelegate biPad]){
        [oChildAlert setModalPresentationStyle:UIModalPresentationFullScreen];
        UIPopoverPresentationController *popPresenter = [oChildAlert
                                                         popoverPresentationController];
        // popPresenter.sourceView = button;
        popPresenter.sourceView = oView;
        popPresenter.sourceRect = oRect;
        
        [self presentViewController:oChildAlert animated:YES completion:nil];
    }
    else{
        [self presentViewController:oChildAlert animated:YES completion:nil];
    }
}


-(O_InspectionTemp *)GetUploadedInspectionBySInsID:(int)iSInsID{
    for (int i=0; i<[arrUploadedIns count]; i++){
        O_Inspection *oTemp = [arrUploadedIns objectAtIndex:i];
        if (oTemp.iSInsID == iSInsID){
            return (O_InspectionTemp *)oTemp;
        }
    }
    return nil;

}

-(void)EditInspection:(O_InspectionTemp *)oInsTemp {
    @try {
        [CommonInspection downloadInspectionAndContinueWithISInsID:(int)oInsTemp.iSInsID bComplete:NO bSynced:NO fromViewController:self completion:nil];
    } @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
}

-(void)SendSeverEmail:(NSString *)to withSubject:(NSString *)subject withBody:(NSString *)body insID:(int)iInsID{
    @try {
        if_ServerEmail *vc = [self.storyboard instantiateViewControllerWithIdentifier:@"if_ServerEmail"];
        [vc setToRecipients:to];
        //[mailController setToRecipients:[[NSArray alloc] initWithObjects:@"<EMAIL>", nil] ];
        [vc setSubject:subject];
        [vc setBody:body  isHTML:YES];
        [vc setInsID:iInsID];
        //[self p:vc animated:YES completion:nil];
        [self.navigationController pushViewController:vc animated:YES];
    }
    @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
    
    
}
-(void)SendEmail:(NSString *)to withSubject:(NSString *)subject withBody:(NSString *)body{
    @try {
        UINavigationBar.appearance.barTintColor =  [UIColor colorWithRed:78/255.0f green:105/255.0f blue:255/255.0f alpha:1];
        MFMailComposeViewController *mailController = [[MFMailComposeViewController alloc] init];
        
        [mailController setToRecipients:[to componentsSeparatedByString:@";"]];
        //[mailController setToRecipients:[[NSArray alloc] initWithObjects:@"<EMAIL>", nil] ];
        [mailController setSubject:subject];
        [mailController setMessageBody:body isHTML:YES];
        
        mailController.mailComposeDelegate = (id)self;

        mailController.navigationBar.titleTextAttributes = [NSDictionary dictionaryWithObject:[UIColor whiteColor] forKey: NSForegroundColorAttributeName];
//        [mailController.navigationBar setTintColor:[UIColor blackColor]];
//        [mailController.navigationItem.leftBarButtonItem setTintColor:[UIColor blackColor]];
//        mailController.navigationController.navigationItem.l
        //[mailController.navigationBar.items[0]]
        
        // UINavigationController *myNavController = [self navigationController];
        
        if ( mailController != nil ) {
            if ([MFMailComposeViewController canSendMail]){
                [self.navigationController presentViewController:mailController animated:YES completion:nil];
            }
        }
    }
    @catch (NSException *exception) {
            [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
    
    
}
-(void)mailComposeController:(MFMailComposeViewController*)controller didFinishWithResult:(MFMailComposeResult)result error:(NSError*)error {
    // [self dismissModalViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)PushInspection:(int)iInsID {
    [Navigator PushInspection:iInsID];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)segmentedControlValueChanged:(NSInteger)selectedIndex {
    DLog(@"segmentedControlValueChanged  %ld", selectedIndex);
    selectedTabIndex = selectedIndex;
    
    _viewAllUpload.hidden = (selectedIndex == 1) ? NO : YES;
    [self ReloadTable:true sTerm:[_searchbar text]];
}

- (BOOL)textFieldShouldClear:(UITextField *)textField {
    [self performSelector:@selector(hideKeyboardWithSearchBar:) withObject:textField afterDelay:0];
    return YES;
}

- (void)hideKeyboardWithSearchBar:(UITextField *)searchBar{
    [self searchInspection:@""];
    [_searchbar resignFirstResponder];
}


-(BOOL)textFieldShouldReturn:(UITextField *)textField {
    [textField resignFirstResponder];
    [self searchInspection:textField.text];
    return YES;
    
}

-(void)searchInspection:(NSString*)searchStr {
    [self ReloadTable:true sTerm:searchStr];
}

- (void)setShowsAddInspectionButton:(BOOL)showAddInspectionButton {
    self.btn_AddInspection.hidden = !showAddInspectionButton;
    self.btnAddInspectionWidth.constant = showAddInspectionButton ? 28 : 0;
    self.btnAddInspectionLeading.constant = showAddInspectionButton ? 9 : 0;
}

- (void)setShowsDeadView:(BOOL)showsDeadView {
    if (![self canNewInspection]) {
        self.deadView.hidden = YES;
        return;
    }
    self.deadView.hidden = !showsDeadView;
    if (showsDeadView) [self.view bringSubviewToFront:self.deadView];
    else [self.view sendSubviewToBack:self.deadView];
}

@end
