//
//  TaskCategoryCell.swift
//  SnapInspect3
//
//  Created by <PERSON> on 2020/9/4.
//  Copyright © 2020 SnapInspect. All rights reserved.
//

import AppFeatures
import Extensions
import UIKit

class TaskCategoryCell: UITableViewCell {
    public var arrCategory: [O_NoticeCategory]? {
        didSet {
            updateTagsView()
        }
    }
    
    public var didSelectCategory: (O_NoticeCategory) -> Void = { _ in }
    public var didReloadData: () -> Void = {}
    
    private var itemModel: if_FormItemModel = .init()
    
    @IBOutlet private var lbsTitle: UILabel!
    @IBOutlet private var bottomLineView: UIView!
    @IBOutlet private var tagsContainerView: UIView!
    
    private var tagsView: TagsView<TaskCategoryItemCell>!
    
    override func awakeFromNib() {
        super.awakeFromNib()
        
        selectionStyle = .none
        lbsTitle.font = .sanFranciscoText_Semibold(14.0)
        lbsTitle.textColor = .init(hex: 0xB3B4B4)
        
        setupTagsView()
    }

    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
    }
    
    private func setupTagsView() {
        tagsView = TagsView(
            frame: .zero,
            tags: tags,
            cellType: TaskCategoryItemCell.self,
            configureCell: { cell, tag, _ in
                cell.configure(withModel: tag)
            },
            didSelectTag: { [weak self] _, index in
                guard let category = self?.arrCategory?[safe: index] else { return }
                self?.didSelectCategory(category)
            },
            didReloadData: { [weak self] in
                self?.didReloadData()
            }
        )
        
        tagsContainerView.addSubview(tagsView)
        tagsView.snp.makeConstraints { make in
            make.top.equalTo(lbsTitle.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
        }
        tagsContainerView.snp.makeConstraints { make in
            make.height.equalTo(tagsView)
        }
    }
    
    private var tags: [String] {
        guard let categories = arrCategory else { return [] }
        return categories.compactMap { category in
            let name = category.sName
            return NSString.isNullOrEmpty(name) ? nil : name
        }
    }
    
    private func updateTagsView() {
        tagsView.setTags(tags)
        tagsView.reloadData { [weak self] in
            guard
                let sCategory = self?.itemModel.value as? String,
                !sCategory.isEmpty,
                let index = self?.arrCategory?.firstIndex(where: { $0.sName == sCategory })
            else { return }
            
            self?.tagsView.selectItem(
                at: .init(row: index, section: 0),
                animated: false,
                scrollPosition: .centeredHorizontally
            )
        }
    }
}

extension TaskCategoryCell: NibReusable {}

extension TaskCategoryCell: if_Bindable {
    func configure(withModel model: Any!) {
        guard let itemModel = model as? if_FormItemModel else { return }
        lbsTitle.text = itemModel.title
        self.itemModel = itemModel
    }
}
