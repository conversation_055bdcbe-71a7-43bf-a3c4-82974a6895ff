//
//  CommonInspection.m
//  SnapInspect3
//
//  Created by <PERSON> on 27/06/17.
//  Copyright © 2017 SnapInspect. All rights reserved.
//

#import "NSArray+HighOrderFunction.h"
@import Extensions;

@implementation CommonInspection
+ (NSArray *)GetInsTypeIDs_CustomerPermission {
    NSArray<O_InsType *> *arrInsType = [db_Common GetIntTypes];
    return [arrInsType compactMap:^id(O_InsType *oInsType) {
        return ([CommonInspection bDisplayInsType:oInsType]) ? nil : @(oInsType.iSInsTypeID);
    }];
}

+ (BOOL)bDisplayInsType:(O_InsType *)oInsType {
    @try {
        if ([CommonHelper IFGetPref:@"sRole"] == nil ||
                ![[CommonHelper IFGetPref:@"sRole"] isEqualToString:Constants.kUserRoleCompanyManager]) {
            NSString *sID = [CommonJson GetJsonKeyValue:@"_iHide" sJson:oInsType.sCustom1];
            NSArray *items = [sID componentsSeparatedByString:@","];
            NSString *iCustomerID = [CommonHelper IFGetPref:@"iCustomerID"];
            if ([items containsObject:iCustomerID]) {
                return NO;
            }
        }

        // Check if the inspection type is in the customer permission list
        if ([[CommonHelper getHiddenInsTypeIDs] containsObject:@(oInsType.iSInsTypeID)]) {
            return NO;
        }

    } @catch (NSException *exx) {

    }
    return YES;
}

+ (BOOL)bPassLayout:(O_Layout *)oLayout withInsType:(O_InsType *)oInsType {
    @try{
        if (oInsType == nil || oInsType.sCustom1 == nil || oLayout == nil) {
            return false;
        }
        // [222,333,{"asfd":"adsff"}]
        NSArray *byPassItems = [CommonJson GetJsonArray:@"_iPass" sJson:oInsType.sCustom1];
        return [byPassItems containsObject:@(oLayout.iSLayoutID)];
    }@catch(NSException *ex){
        return false;
    }

}

+ (SI_Inspection_Type)GetInspectionType:(O_Inspection *)oIns {
    return [oIns.sType.lowercaseString isEqualToString:@"s"] ? SI_SimpleInspection : SI_FullInspection;
}

+ (SI_Question_Type)GetInsQuestionType:(O_InsItem *)oInsItem {
    if ([oInsItem.sQType.uppercaseString isEqualToString:@"C"]) {
        return SI_Question_C;
    } else if ([oInsItem.sQType.uppercaseString isEqualToString:@"G"]) {
        return SI_Question_G;
    } else if ([oInsItem.sQType.uppercaseString isEqualToString:@"A"]) {
        return SI_Question_A;
    } else if ([oInsItem.sQType.uppercaseString isEqualToString:@"S"]) {
        return SI_Question_S;
    } else if ([oInsItem.sQType.uppercaseString isEqualToString:@"V"]) {
        return SI_Question_V;
    } else if ([oInsItem.sQType.uppercaseString isEqualToString:@"P"]) {
        return SI_Question_P;
    } else {
        return SI_Question_C;
    }
}

+ (SI_Control_Type)GetControlType:(NSString *)sConfig {
    return [O_Control getControlTypeWithConfig:sConfig];
}

+ (void)ReconsolidateInspectionWithServer:(int)iSInsID oDic:(NSDictionary *)oDic {
    @try {
        O_Inspection *oIns = [db_Inspection GetInspectionBySInsID:iSInsID];
        if (oIns == nil || oIns.iInsID == 0 || (oIns.bSynced && oIns.bComplete)) {
            if (oIns == nil) {
                oIns = [[O_Inspection alloc] init];
                oIns.iInsID = 0;
            }
            oIns.iSInsID = iSInsID;
            [oIns updateWithDict:oDic];
            [db_Inspection UpdateInspection:oIns];
            [db_Inspection UpdateInspection:oIns];
            [db_Inspection UpdateInspectionCustom:oIns];
        }
    } @catch (NSException *ex) {

    }
}

+ (void)CheckAll_V3:(O_InsItem *)oInsItem oControlType:(SI_Control_Type)oControlType iConfigNumber:(int)iConfigNumber iOrderNumber:(int)iOrderNumber sRating:(NSString *)sRating {
    @try {
        NSMutableArray *arrInsItems = [db_Inspection GetItems:oInsItem.iPInsItemID iInsID:oInsItem.iInsID];
        NSString *sCheckValue = sRating;
        for (O_Item *oTemp in arrInsItems) {
            if ([oTemp.sQType isEqualToString:oInsItem.sQType]) {
                NSString *sCompareValue = [self GetInsItemValue:oTemp iConfigNumber:iConfigNumber];
                NSMutableArray *arrCompareValues = (NSMutableArray *) [sCompareValue componentsSeparatedByString:@"|"];

                if (oControlType == SI_Control_SCHK) {
                    for (int i = 0; i < arrCompareValues.count; i++) {
                        arrCompareValues[i] = @"A";
                    }
                }
                if ([arrCompareValues count] == 1) {
                    arrCompareValues = [@[sCheckValue] mutableCopy];
                } else {
                    arrCompareValues[iOrderNumber] = sCheckValue;
                }

                [self SetInsItemValue:oTemp iConfigNumber:iConfigNumber sValue:[arrCompareValues componentsJoinedByString:@"|"]];
                [db_InsItem UpdateInsItemValue:oTemp];
            }
        }
    } @catch (NSException *ex) {
        Log_Exception(ex)
    }
}

+ (void)CheckAll_V2:(O_InsItem *)oInsItem iConfigNumber:(int)iConfigNumber iOrderNumber:(int)iOrderNumber {
    NSMutableArray *arrInsItems = [db_Inspection GetInsItems:oInsItem.iPInsItemID iInsID:oInsItem.iInsID];
    O_InsItem *oPInsItem = [db_InsItem GetInsItem:oInsItem.iPInsItemID];
    NSString *sConfig = [CommonInspection GetInsItemConfig:([oInsItem.sQType isEqualToString:@"P"] ? oInsItem : oPInsItem) iConfigNumber:iConfigNumber];

    NSString *sValue = [self GetInsItemValue:oInsItem iConfigNumber:iConfigNumber];
    NSMutableArray *arrValues = (NSMutableArray *) [sValue componentsSeparatedByString:@"|"];
    NSString *sCheckValue = arrValues[iOrderNumber];
    if (sCheckValue == nil || [sCheckValue isEqualToString:@""]) {
        sCheckValue = @"A";
    }
    for (O_InsItem *oTemp in arrInsItems) {
        if ([oTemp.sQType isEqualToString:oInsItem.sQType]) {
            if ([oInsItem.sQType isEqualToString:@"C"]) {
                NSString *sCompareValue = [self GetInsItemValue:oTemp iConfigNumber:iConfigNumber];
                NSMutableArray *arrCompareValues = (NSMutableArray *) [sCompareValue componentsSeparatedByString:@"|"];

                SI_Control_Type controlType = [O_Control getControlTypeWithConfig:sConfig];
                if (controlType == SI_Control_SCHK) {
                    for (int i = 0; i < arrCompareValues.count; i++) {
                        arrCompareValues[i] = @"A";
                    }

                }
                if ([arrCompareValues count] == 1) {
                    arrCompareValues = [@[sCheckValue] mutableCopy];
                } else {
                    arrCompareValues[iOrderNumber] = sCheckValue;
                }

                [self SetInsItemValue:oTemp iConfigNumber:iConfigNumber sValue:[arrCompareValues componentsJoinedByString:@"|"]];
                [db_InsItem UpdateInsItemValue:oTemp];
            } else if ([oInsItem.sQType isEqualToString:@"P"]) {
                NSString *sCompareConfig = [CommonInspection GetInsItemConfig:oInsItem iConfigNumber:iConfigNumber];
                if ([sConfig isEqualToString:sCompareConfig]) {
                    NSString *sCompareValue = [self GetInsItemValue:oTemp iConfigNumber:iConfigNumber];
                    NSMutableArray *arrCompareValues = (NSMutableArray *) [sCompareValue componentsSeparatedByString:@"|"];

                    SI_Control_Type controlType = [O_Control getControlTypeWithConfig:sConfig];
                    if (controlType == SI_Control_SCHK) {
                        for (int i = 0; i < arrCompareValues.count; i++) {
                            arrCompareValues[i] = @"A";
                        }
                    }
                    if ([arrCompareValues count] == 1) {
                        arrCompareValues = [@[sCheckValue] mutableCopy];
                    } else {
                        arrCompareValues[iOrderNumber] = sCheckValue;
                    }

                    [self SetInsItemValue:oTemp iConfigNumber:iConfigNumber sValue:[arrCompareValues componentsJoinedByString:@"|"]];
                    [db_InsItem UpdateInsItemValue:oTemp];
                }
            }
        }
    }
}

+ (void)SetInsItemValue:(O_InsItem *)oInsItem iConfigNumber:(int)iConfigNumber sValue:(NSString *)sValue {
    if (iConfigNumber == 1) {
        oInsItem.sValue1 = sValue;
    } else if (iConfigNumber == 2) {
        oInsItem.sValue2 = sValue;
    } else if (iConfigNumber == 3) {
        oInsItem.sValue3 = sValue;
    } else if (iConfigNumber == 4) {
        oInsItem.sValue4 = sValue;
    } else if (iConfigNumber == 5) {
        oInsItem.sValue5 = sValue;
    } else if (iConfigNumber == 6) {
        oInsItem.sValue6 = sValue;
    }
}

+ (O_Control *)GetInsItemControl:(O_Item *)oItem iConfigNumber:(int)iConfigNumber {
    if (iConfigNumber == 1) {
        return oItem.oControl1;
    } else if (iConfigNumber == 2) {
        return oItem.oControl2;
    } else if (iConfigNumber == 3) {
        return oItem.oControl3;
    } else if (iConfigNumber == 4) {
        return oItem.oControl4;
    } else if (iConfigNumber == 5) {
        return oItem.oControl5;
    } else if (iConfigNumber == 6) {
        return oItem.oControl6;
    }
    return nil;
}

+ (NSString *)GetInsItemConfig:(O_InsItem *)oInsItem iConfigNumber:(int)iConfigNumber {
    if (iConfigNumber == 1) {
        return oInsItem.sConfig1;
    } else if (iConfigNumber == 2) {
        return oInsItem.sConfig2;
    } else if (iConfigNumber == 3) {
        return oInsItem.sConfig3;
    } else if (iConfigNumber == 4) {
        return oInsItem.sConfig4;
    } else if (iConfigNumber == 5) {
        return oInsItem.sConfig5;
    } else if (iConfigNumber == 6) {
        return oInsItem.sConfig6;
    }
    return @"";
}

+ (NSString *)GetInsItemValue:(O_InsItem *)oInsItem iConfigNumber:(int)iConfigNumber {
    if (iConfigNumber == 1) {
        return oInsItem.sValue1;
    } else if (iConfigNumber == 2) {
        return oInsItem.sValue2;
    } else if (iConfigNumber == 3) {
        return oInsItem.sValue3;
    } else if (iConfigNumber == 4) {
        return oInsItem.sValue4;
    } else if (iConfigNumber == 5) {
        return oInsItem.sValue5;
    } else if (iConfigNumber == 6) {
        return oInsItem.sValue6;
    }
    return @"";
}

+ (void)getAssetLayoutsWithID: (NSInteger)iSAssetID
                   insTypeID:(NSInteger)iSInsTypeID
                  completion:(void (^)(NSArray<O_Layout *> *oAssetLayouts, NSString *errorMessage))completion {
    if (![NetworkConnection isNetworkReachable]) {
        safe_dispatch_main_async(^{
            completion(nil, @"No network connection");
        });
        return;
    }
    dispatch_background_async(^{
        NSMutableDictionary *params = [NSMutableDictionary new];
        params[@"iCustomerID"] = [CommonHelper IFGetPref:@"iCustomerID"];
        params[@"sToken"] = [CommonUser currentToken];
        params[@"iAssetID"] = [@(iSAssetID) stringValue];
        params[@"iInsTypeID"] = [@(iSInsTypeID) stringValue];
        NSDictionary *response = [IFConnection PostRequest:EndpointGetAssetLayoutV2 oParams:params];
        safe_dispatch_main_async(^{
            if ([response[@"success"] boolValue]) {
                NSArray *layouts = response[@"lsAssetLayout"];
                NSArray *aiLayouts = [[layouts map:^id _Nonnull(NSDictionary * _Nonnull obj) {
                    O_Layout *layout = [[O_Layout alloc] initWithDictionary:obj];
                    layout.iCount = 1;
                    return layout;
                }] sortedArrayUsingComparator:^NSComparisonResult(O_Layout *layout1, O_Layout *layout2) {
                    return [@(layout1.iOrder) compare:@(layout2.iOrder)];
                }];
                if (aiLayouts.isNotEmpty) {
                    DLog(@"aiLayouts: %@", aiLayouts);
                    completion(aiLayouts, nil);
                } else {
                    completion(nil, @"No layouts found");
                }
            } else {
                completion(nil, response[@"message"]);
            }
        });
    });
}

+ (void)downloadInspectionAndContinueWithISInsID:(int)iSInsID
                                     bComplete:(BOOL)bComplete
                                       bSynced:(BOOL)bSynced
                            fromViewController:(UIViewController *)fromViewController
                                    completion:(void (^)(int iInsID))completion {
    O_Inspection *oInspection = [db_Inspection GetInspectionBySInsID:iSInsID];
    if (oInspection != nil && oInspection.iInsID > 0 && !oInspection.bSynced) {
        [Navigator PushInspection:oInspection.iInsID];
        completion(oInspection.iInsID);
        return;
    }

    [fromViewController showLoading:@"Processing..." detailMessage:@"Connecting to server..."];
    dispatch_background_async(^{
        int insID = [db_SetupInspection editInspectionWithServerInsID:iSInsID];
        if (insID > 0) {
            O_Inspection *oTemp = [db_Inspection GetInspectionByInsID:insID];
            oTemp.bSynced = bSynced;
            oTemp.bComplete = bComplete;
            [db_Inspection UpdateInspection:oTemp];
        }
        safe_dispatch_main_async(^{
            if (completion) completion(insID);
            [fromViewController hidesLoading];
            [Navigator PushInspection:insID];
        });
    });
}
@end
