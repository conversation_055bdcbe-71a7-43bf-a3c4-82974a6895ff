name: Xcode - Build and Analyze

on:
  push:
    branches: [ "*" ]
  pull_request:
    branches: [ "master" ]
  workflow_dispatch:

jobs:
  build-and-test:
    runs-on: macos-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Install xcpretty
        run: gem install xcpretty
        
      - name: Build and analyze
        run: |
          xcodebuild test \
          -workspace SnapInspect3.xcworkspace \
          -scheme "SnapInspect3" \
          -destination 'platform=iOS Simulator,name=iPhone 16 Pro' \
          -skipMacroValidation \
          -skipPackagePluginValidation \
          CODE_SIGN_IDENTITY="" \
          CODE_SIGNING_REQUIRED=NO \
          ONLY_ACTIVE_ARCH=YES \
          | xcpretty 