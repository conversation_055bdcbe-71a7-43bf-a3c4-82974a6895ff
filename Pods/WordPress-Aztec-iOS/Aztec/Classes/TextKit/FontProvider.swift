import Foundation
import UIKit

public class Font<PERSON>rovider {

    private init() {

    }

    public static var shared = FontProvider()

    public lazy var monospaceFont: UIFont = {
        let baseFont = UIFont(descriptor:UIFontDescriptor(name: "<PERSON><PERSON>", size: 14), size:14)
        let font: UIFont
        font = UIFontMetrics.default.scaledFont(for: baseFont)
        return font
    }()

    public lazy var defaultFont: UIFont = {
        let baseFont = UIFont.systemFont(ofSize: 14)
        let font: UIFont
        font = UIFontMetrics.default.scaledFont(for: baseFont)        
        return font
    }()
}
