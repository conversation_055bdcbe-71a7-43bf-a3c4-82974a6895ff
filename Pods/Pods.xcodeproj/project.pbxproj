// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 63;
	objects = {

/* Begin PBXBuildFile section */
		000B7CF92F1A0FC654C3FD59E8D83952 /* IQPreviousNextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0F2BD190F1AF129F491C83B157B82BF6 /* IQPreviousNextView.m */; };
		00AAB7239E9937CA81595C36F7CD6340 /* AWSSTSResources.m in Sources */ = {isa = PBXBuildFile; fileRef = CFE6C1F0ACD53B397DC9998112E1C7EC /* AWSSTSResources.m */; };
		00B4CA3A6CCF5C9F25A5D1B4F980665C /* AWSURLResponseSerialization.m in Sources */ = {isa = PBXBuildFile; fileRef = 20AF0B625EEB1A41F012EAAB78307DB2 /* AWSURLResponseSerialization.m */; };
		014A3D103E37C91B0CF8E2D25F6EF5DE /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7FB463E08CB491D770D1C136C8C1263E /* Foundation.framework */; };
		01DC449236DDCEF360B520DA88817B21 /* HTMLAttachmentToElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = EAE9C430705DCB6836D4E92429017830 /* HTMLAttachmentToElementConverter.swift */; };
		01DED22B5FF4908B3932E63CDB95DE5D /* Fabric.h in Headers */ = {isa = PBXBuildFile; fileRef = E5A10819C69BFCFC90328EE0CA5F6342 /* Fabric.h */; settings = {ATTRIBUTES = (Private, ); }; };
		01F8F2EA8144B8587E3691C78F1C60F4 /* AWSTMCacheBackgroundTaskManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F6A5EDE45053CCB5AB388186F1D7645 /* AWSTMCacheBackgroundTaskManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		030637F260D9D0D4973D0A98579919FB /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = A918900FAA168905AA5694B24E77B979 /* <EMAIL> */; };
		0339FAEF99C33FC0808EFD62475558FA /* NSAttributedString+Lists.swift in Sources */ = {isa = PBXBuildFile; fileRef = 393B080CD824A0BD742E121DC2D659EE /* NSAttributedString+Lists.swift */; };
		034CD848D2155FCE0CAB851C48C6AAA3 /* Element.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98151BE1EB7039A64A7B4C3B74E7F59D /* Element.swift */; };
		038D589E80BCA3B48827FA2431CE98A5 /* AWSSTSService.m in Sources */ = {isa = PBXBuildFile; fileRef = F97BCD847DC357F3DCC362F08C9F3B97 /* AWSSTSService.m */; };
		04651828EBE0C82C8F16C615DCF315EC /* Gutenblock.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C3C646B55D8159F3245C63650B4CC41 /* Gutenblock.swift */; };
		0482E5528F3925EF665500E39BB1C0A6 /* ZSSh6.png in Resources */ = {isa = PBXBuildFile; fileRef = 2484BDB79E84DB4E30C811523EA5AC75 /* ZSSh6.png */; };
		04945BCD44194BE55B2A418B712C4BF2 /* FormatBarItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = C5D43DBF89C84CEF2712E850CD1FA4DC /* FormatBarItem.swift */; };
		04EFFC931E41041F65D51D7DFD2FDBB9 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7FB463E08CB491D770D1C136C8C1263E /* Foundation.framework */; };
		04F5ECB5E38E86CEB03051ED472D7A1C /* ZSSlink.png in Resources */ = {isa = PBXBuildFile; fileRef = 1E73B3D82EF8948B09B08E7989CCA8E6 /* ZSSlink.png */; };
		05751DF8BF5E831964692A625FE68B3B /* html_colors.json in Resources */ = {isa = PBXBuildFile; fileRef = B86D8491319968018CDF9D79B418C700 /* html_colors.json */; };
		058EB1DE8B45EDF0210443E276AF4DD0 /* Figcaption.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E2EECDA23F704DD9AE43511878D3CA4 /* Figcaption.swift */; };
		05A4AD131328F352F02B30FF4C3FC70E /* AWSMTLJSONAdapter.h in Headers */ = {isa = PBXBuildFile; fileRef = EFA116471150875E379A82E2A8C98597 /* AWSMTLJSONAdapter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		05E2B7C1DB7528A0BBEA1521BE0DBAF1 /* MASViewAttribute.h in Headers */ = {isa = PBXBuildFile; fileRef = DB2A8C3E7BD092724C9D127FB2EC2647 /* MASViewAttribute.h */; settings = {ATTRIBUTES = (Public, ); }; };
		06116B9D867C77EAE024448AEDBD8419 /* String+RegEx.swift in Sources */ = {isa = PBXBuildFile; fileRef = B7F581F8772EBEEC50F4C0F8F3D1DBF9 /* String+RegEx.swift */; };
		0632BA4B6FC7B9A0061AFCF4A03E63A6 /* GalleryShortcodeInputProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8034E723B38DF6B087138D044FD8D296 /* GalleryShortcodeInputProcessor.swift */; };
		069D75321C432E6823ECF8F2948A4864 /* AWSCategory.m in Sources */ = {isa = PBXBuildFile; fileRef = 76BBF9674059B06F4ABF0CB90EDE2BAE /* AWSCategory.m */; };
		06A4D6EF860B9C6C1C08F31D98679C8C /* AWSFMDatabase.h in Headers */ = {isa = PBXBuildFile; fileRef = D7E3B4F930F504C5E1706A478ED2942C /* AWSFMDatabase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		070E042675A16B066CF5CAD507D02422 /* GutenpackAttachment.swift in Sources */ = {isa = PBXBuildFile; fileRef = C8F6CBB1764E09A11791565EA0D7FC7A /* GutenpackAttachment.swift */; };
		0744FF91BD34CBF21804DD175227A3D0 /* IQUITextFieldView+Additions.m in Sources */ = {isa = PBXBuildFile; fileRef = 29CE6563281AD32FC9E4DCFAB9976742 /* IQUITextFieldView+Additions.m */; };
		09018F6A45DF1AA778CC2BF150961B41 /* MediaAttachment+WordPress.swift in Sources */ = {isa = PBXBuildFile; fileRef = 093DAD6CB8CBFCC6E95A59F369AF655A /* MediaAttachment+WordPress.swift */; };
		09583B75374F627C545A7DE7E018A68B /* Header.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58C45A4A7AAAC50FBEDA41BD860D23EC /* Header.swift */; };
		099D041DA665DD316A654B5E03EBC1C6 /* LinkFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = E66718A179DA93FA5F965387502D0DCD /* LinkFormatter.swift */; };
		0A173B6C6A84D15CF06A6F375AC7E003 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 0B5610F6E07B1A7AEA1AE3A2F7969DDC /* <EMAIL> */; };
		0A38B0E0A5B9A395F5DD5EBAD5E782BF /* ParagraphProperty.swift in Sources */ = {isa = PBXBuildFile; fileRef = 073011D63121F15BAB242E48C9DD55F4 /* ParagraphProperty.swift */; };
		0A732B35B3EF8F5CD4ADB67E331EA746 /* ZSSfonts.png in Resources */ = {isa = PBXBuildFile; fileRef = 2B661495505FAA6317D0B5324CD7AD7B /* ZSSfonts.png */; };
		0ABB381D036C9E9FBB0FF69B2EC0B09D /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 73304FC323A42EDC9BBB24BEF29EB36E /* <EMAIL> */; };
		0B62E7CFFED5AB95373F9C1B0D7ED79B /* NSValueTransformer+AWSMTLPredefinedTransformerAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = A20AE295FDEB3E4FB7D13B49A9E8F530 /* NSValueTransformer+AWSMTLPredefinedTransformerAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0B6EAE3F11A8E293D8E6FB74888D3F05 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B7B72B9BFA15BA7916D51B6BAF4931ED /* <EMAIL> */; };
		0BC3A2CF6ADD87DC7C8FFC0A395A3A7A /* Dictionary+AttributedStringKey.swift in Sources */ = {isa = PBXBuildFile; fileRef = 02DC8FB325DEDC74EA982677F1587231 /* Dictionary+AttributedStringKey.swift */; };
		0BE5B1A7E0E9DEACB4FC73BA9ECB1261 /* NSBundle+AztecBundle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 039BFD41CC88B7E9A28C9AC2A4E3A37A /* NSBundle+AztecBundle.swift */; };
		0C6912494A9947FCA4178CC755E8385C /* Media.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 42E7FFC595DDA2DA6367E0BD1322713B /* Media.xcassets */; };
		0CEBAFB8F0A4ED507209D6A7CE97E34F /* ZSScenterjustify.png in Resources */ = {isa = PBXBuildFile; fileRef = 7DE33AC5AF743E2B5FA4B20250E3063E /* ZSScenterjustify.png */; };
		0D11CB16C46BB1A8D75C2A84C138A7FE /* ZSSCustomButtonsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = BEC2A893C33575FC31B14A52454E06E7 /* ZSSCustomButtonsViewController.m */; };
		0D51D8E37835CEE44502FC03B1DAB29C /* UnderlineCSSAttributeMatcher.swift in Sources */ = {isa = PBXBuildFile; fileRef = 35B87544670F4E671FA17F93E1E53671 /* UnderlineCSSAttributeMatcher.swift */; };
		0D560328328A83B262378B1C6C0AD513 /* GutenbergAttributeNames.swift in Sources */ = {isa = PBXBuildFile; fileRef = F96508A77801158BFFDF20B1046D8011 /* GutenbergAttributeNames.swift */; };
		0F35A3A3F485DF5DC51E0B380F8FEDC2 /* AWSKSReachability.m in Sources */ = {isa = PBXBuildFile; fileRef = 18C050F7D65590348644F2EFCF1BBE20 /* AWSKSReachability.m */; };
		1004D81C7EA9ADDECBFAFEA1A4EFD5FC /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 0EFC9290CDB3BCDABF661E53BCC38C49 /* <EMAIL> */; };
		10D9C4C0CBD0BAD49E6F9F00372794F9 /* HTMLAttachmentRenderer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 818F5E6B4DBD91C42E8C9380DE4D5018 /* HTMLAttachmentRenderer.swift */; };
		110B7DB758908362F74A9BDDD338E040 /* CodeFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = CF292F57BA682294ED768150923FB82F /* CodeFormatter.swift */; };
		11A30E7E9BC41582363F71CAE1B4EC19 /* ZSSColorViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 51C38653C21585E26F09E0FBFEB57DCB /* ZSSColorViewController.m */; };
		11F1B3548B4DF50398011DD4C43F4CA6 /* TextList.swift in Sources */ = {isa = PBXBuildFile; fileRef = 70CF970A0876BD6863CCA882372A8855 /* TextList.swift */; };
		12485E8698801B7D1D6E86EE6F281147 /* HTMLStorage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 120A7EB69C81F1A7EF1F516A8EDBD0EB /* HTMLStorage.swift */; };
		13F10E57BCFADEDB7ED1A5FB3B0499F2 /* HTMLParser.swift in Sources */ = {isa = PBXBuildFile; fileRef = CA4B55324018AE6F3874D130E483F4BB /* HTMLParser.swift */; };
		141AA3770501900BBCD270C6FC43A958 /* AWSCognitoIdentityModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DD71838E5EDBD9BBE5B20E7963C4B22 /* AWSCognitoIdentityModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		15B6F25A01F1FF8F3308D6BB5CC0FCA1 /* Pods-SnapInspect3-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = DB11D3FD6BC33D77BB6D82183398392E /* Pods-SnapInspect3-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		164991A2E2D34C0F2C523F0E2C71A3D9 /* FigureFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B0A29AA8A0CC441B625B7CD0347D9D0 /* FigureFormatter.swift */; };
		183C1181055ABA6B4C0D2390CCB50328 /* WordPressPasteboardDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = DC95FC2B3239D664802460A2BAD3E045 /* WordPressPasteboardDelegate.swift */; };
		186B93994773FC73AAF4384EAAAAE7ED /* AWSDDAbstractDatabaseLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = 365B2B752EA07985E0214D7560CD16BA /* AWSDDAbstractDatabaseLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		193A04DEB07B3F650348A077D40137A1 /* AWSCancellationToken.m in Sources */ = {isa = PBXBuildFile; fileRef = 97A83F9E5D08C4AFF2DB0AE534D85A9C /* AWSCancellationToken.m */; };
		19F75BF66A04D712F9390236D00A1623 /* AWSS3TransferUtilityTasks.h in Headers */ = {isa = PBXBuildFile; fileRef = 139FA8BD67D5FBBEF0E0632A2BCF2CDD /* AWSS3TransferUtilityTasks.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1A46F83233F438F9784FBA3597A9ABCC /* AWSFMResultSet.h in Headers */ = {isa = PBXBuildFile; fileRef = DB1DD27CB50948A64DB1B8372CC800B5 /* AWSFMResultSet.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1A672ED193BC9C15B6D2C834DE290C7F /* AWSSimpleDBResources.h in Headers */ = {isa = PBXBuildFile; fileRef = A116A72EC0E1FAE72337F8394B1C38E2 /* AWSSimpleDBResources.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1AAD31BFE81567EB979CB63D802CECDB /* ConditionalItalicStringAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03B6F41E23C27BC816E8FE9F8D0269DF /* ConditionalItalicStringAttributeConverter.swift */; };
		1AECE3C34F7B9484EC41D92B2E1015D0 /* AWSCancellationTokenSource.m in Sources */ = {isa = PBXBuildFile; fileRef = E465246DA8193C8320E29AA4CDB2BC23 /* AWSCancellationTokenSource.m */; };
		1B18CBE55D207EAF26CC2E64ED51F2D4 /* ZSSundo.png in Resources */ = {isa = PBXBuildFile; fileRef = D38D8B63299065FBB13B66182BCB12B2 /* ZSSundo.png */; };
		1B4BE98867AB5C6C1727863627501724 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 4B518F9BCE8A9F7586E3D8C692D21E3C /* <EMAIL> */; };
		1B51ADD2EDB1E350C06D929B5B1E6854 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 2C827CBA650FA0C93DDF2ECD2E3CB064 /* <EMAIL> */; };
		1B9D8386A8FFB1D6F356D2CF34C963C8 /* IQUIView+IQKeyboardToolbar.h in Headers */ = {isa = PBXBuildFile; fileRef = 1BA9A17680202558687A37AA159DCB2D /* IQUIView+IQKeyboardToolbar.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1D00181E61A74B66B145897150943E83 /* IQUIView+Hierarchy.h in Headers */ = {isa = PBXBuildFile; fileRef = 5D8F3D63AAA227811B98BAAE24389EC1 /* IQUIView+Hierarchy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1D033AA874891F26C74B612FCECAAC62 /* FABAttributes.h in Headers */ = {isa = PBXBuildFile; fileRef = 064EDF41254270D4B8DF6B0160E321A3 /* FABAttributes.h */; settings = {ATTRIBUTES = (Private, ); }; };
		1D7B7EA80CCAE79F4D5AEAA5D1F959D2 /* FormatterElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 64208D4FE2A83D087C8B5A7F78FBF7CD /* FormatterElementConverter.swift */; };
		1D8946CC661CF711BD7A131BAA2C87FB /* ParagraphPropertyConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5681CE57343F77FFD7C18484BE9D91F8 /* ParagraphPropertyConverter.swift */; };
		1E63FA190EEB23418E740047316B21B7 /* GutenbergOutputHTMLTreeProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = D7748D5F8C9CABE568A6A1AAB5F7FF5E /* GutenbergOutputHTMLTreeProcessor.swift */; };
		1E68CACCD0ECEE396BAA327A5723F54D /* InAttributesConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5E7C6C9BC56B01D4B66D50C4A4939C5E /* InAttributesConverter.swift */; };
		1E6DD16D765A5A22706F411ECD0CE2E2 /* AWSS3TransferUtility+HeaderHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 0110D202E8D83129762D378FDDF143D4 /* AWSS3TransferUtility+HeaderHelper.m */; };
		1E859AE3EB53C31F1986BF63404729B1 /* AWSNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = 97ADBE32A4F1B909C5E451F8D7903634 /* AWSNetworking.m */; };
		1FD498A9CD395BB6B0AF553D09A9DE07 /* AWSSynchronizedMutableDictionary.h in Headers */ = {isa = PBXBuildFile; fileRef = D36AE816EC31BEAC9AB9DB9EA2B49AFE /* AWSSynchronizedMutableDictionary.h */; settings = {ATTRIBUTES = (Public, ); }; };
		20137A91484861CBE35A7D29B3E36171 /* AWSXMLWriter.m in Sources */ = {isa = PBXBuildFile; fileRef = 772044D1CBAEA66DF6DF7A7AD8B94FAD /* AWSXMLWriter.m */; };
		2074386B87C7099A9D09D9C6A1BDE570 /* GalleryElementToTagConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 09D5992C26C541102E2F4D3CEA3ABFB2 /* GalleryElementToTagConverter.swift */; };
		20F1A81AF4194F3CE3307103FCA04942 /* ZSSLargeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1E448A42284A180CEFC211B3EDCC37A1 /* ZSSLargeViewController.m */; };
		210FE6E33C9634152F2FAE9F835CC846 /* ForegroundColorCSSAttributeMatcher.swift in Sources */ = {isa = PBXBuildFile; fileRef = 36DCD2F5E26EAE3DA08826FA45FE9ECF /* ForegroundColorCSSAttributeMatcher.swift */; };
		213DC578F2EE16517BEF36C4B95E5092 /* AWSInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 413BE7E313C4F81889A39015E63FEF62 /* AWSInfo.m */; };
		21A03B6C8F7D979F8D3E5CB19408DF17 /* PluginManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 850083AE3F6ED8106C86F0A76D3331BA /* PluginManager.swift */; };
		21B73FE17E887AFF2291B8C84DD3FCE6 /* CommentAttachmentRenderer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2CE77014456596A4717032B476C7E555 /* CommentAttachmentRenderer.swift */; };
		220B1ECDDAF00DEC680813C69EE90CFA /* AWSMantle.h in Headers */ = {isa = PBXBuildFile; fileRef = A0903D0074BBBD56225535B98AFE5B90 /* AWSMantle.h */; settings = {ATTRIBUTES = (Public, ); }; };
		22E4919703896BB1E502DDF2F29CCC1E /* AWSModel.h in Headers */ = {isa = PBXBuildFile; fileRef = AC84E94C514DA4AF02D2DF649F69B650 /* AWSModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		240202AD1C01F1C9F0E89B816F9972CA /* IQKeyboardManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FCAE400E9F687984A707BDDCCC6578F2 /* IQKeyboardManager.m */; };
		241E50AE70329797FA02B2824B1F4483 /* AWSInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 847EF218D80B5A990D6646B162B88F86 /* AWSInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		24B21140F5975144219214CED0A1B677 /* AWSNetworkingHelpers.h in Headers */ = {isa = PBXBuildFile; fileRef = 72499EE078AD8003B33D48EAADE71CF6 /* AWSNetworkingHelpers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		24D5D985D8325FAC4927DBBD2E167EF8 /* AWSDDOSLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = B5C3AAD1FF47242B09A33D914CE9A172 /* AWSDDOSLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		24EB215D83D4B29CD4C759752B1E552D /* AWSS3TransferUtility.m in Sources */ = {isa = PBXBuildFile; fileRef = 4BAB18119B32D5084251313A4A4A0E64 /* AWSS3TransferUtility.m */; };
		259F6DC8E50BDC0347C06EC6B6B08426 /* CommentAttachmentToElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A4E718B7D0E0B58A336D2D0FDE7C9FDA /* CommentAttachmentToElementConverter.swift */; };
		27458707BD64D8AB517D236B670BF054 /* AWSCategory.h in Headers */ = {isa = PBXBuildFile; fileRef = 146F47E00215443494348808DD1AC419 /* AWSCategory.h */; settings = {ATTRIBUTES = (Public, ); }; };
		278DD16D9413D3BBB24AF62C8FDE2557 /* AWSNetworkingHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E06E4832B4FB41B2D9633BB589906A /* AWSNetworkingHelpers.m */; };
		27BF49265939C1A43E8BFE8881927B85 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = D04C1B1DB16ACC78AC9EE2605D85D852 /* <EMAIL> */; };
		27F69C6688D86564B0A02D52835EF86E /* IQBarButtonItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 8E85B120A0979EE2ED5F1E5784756B04 /* IQBarButtonItem.m */; };
		299D896F2D9EC42D4F03EBED59E703E0 /* AWSS3Serializer.m in Sources */ = {isa = PBXBuildFile; fileRef = 44779C7C477FE3FAFE32447C23B1FF27 /* AWSS3Serializer.m */; };
		29A7C810544A8C1E17E7A7F426C653B7 /* Figure.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7F038CD9E930B4AD0D7E067D11EDBF54 /* Figure.swift */; };
		29C1C361BEA65B19EF2508CCBD0D0E05 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7FB463E08CB491D770D1C136C8C1263E /* Foundation.framework */; };
		29E5B0BA27995891088BC3EE80A3F210 /* GalleryAttachmentToElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = CF4ECA173328DA930216D26C29718794 /* GalleryAttachmentToElementConverter.swift */; };
		2A0ED9538B30D2FFE508DDE819D65064 /* Array+Attribute.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39BB99D3270473231795762345668826 /* Array+Attribute.swift */; };
		2A329296A4DC3BBA7E27EF8F0EAFC8BD /* IQKeyboardManager-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 55F36AD1259D4BB93C53F2402D2EBD95 /* IQKeyboardManager-dummy.m */; };
		2A8890C3CD6F19A439ACCDB9F2D61B47 /* ZSSRichTextEditor-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = D1450B441CC78E73C0A765A7E605DD4C /* ZSSRichTextEditor-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2B030BC09EFFEEC5146511D89702FD75 /* NSMutableAttributedString+ParagraphProperty.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8565124D9E31139351BC2015D6343353 /* NSMutableAttributedString+ParagraphProperty.swift */; };
		2B3CC3063E0D51FE6BFC168CC0EE78B9 /* AWSCognitoIdentity.h in Headers */ = {isa = PBXBuildFile; fileRef = 46D31E2B8E7446A5E4DDB826A5D70EDF /* AWSCognitoIdentity.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2B40B09A5D909798F8DE56E5DBEA9EE2 /* AWSMTLManagedObjectAdapter.h in Headers */ = {isa = PBXBuildFile; fileRef = B08508F0601023DE71C82068EDE7DF1B /* AWSMTLManagedObjectAdapter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2B6BF126C9593B7268BCEB5DE91E8991 /* NSTextingResult+Helpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC16870CD931C70EED97EFDEB5C7135F /* NSTextingResult+Helpers.swift */; };
		2BF47C5182157A41F63F7E09A7A7DA00 /* AWSURLSessionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 411D9C55F97896E1AFD3DCF13A8E0E53 /* AWSURLSessionManager.m */; };
		2C34E61BB8B0B039156E9D34E916997C /* VideoShortcodeProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = AB5FD88DF442EF74E7ADC537ED171E54 /* VideoShortcodeProcessor.swift */; };
		2C58B4897C0EF6AFBD8E2123F45A3674 /* ElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 75332399D9905747E1517151D5D8869F /* ElementConverter.swift */; };
		2D220977FEC0B146B32A9C3C728D6E63 /* AWSCognitoIdentityResources.m in Sources */ = {isa = PBXBuildFile; fileRef = 96165BF12398A6C2770E17940E8CC6AD /* AWSCognitoIdentityResources.m */; };
		2D2433756EB19A309DBBD3BA73D15AC4 /* UILayoutPriority+Swift4.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28C9E10CDA6BF017DD893E3A2B5DCF66 /* UILayoutPriority+Swift4.swift */; };
		2D2C84E3A1976DED48341393B35E1BC6 /* IQUIView+Hierarchy.m in Sources */ = {isa = PBXBuildFile; fileRef = D77752F458417B3BEDD1EF059DEB14E1 /* IQUIView+Hierarchy.m */; };
		2D51AB19413B1498C6D19BD8056346E4 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 2EEDE148BA200B1BF0D05E7008289C8A /* <EMAIL> */; };
		2E1D8B0F196A7DA6FB4FAB9C8D61C4DA /* NSAttributedString+CharacterName.swift in Sources */ = {isa = PBXBuildFile; fileRef = C7A329294BBA948213A73A6CDFB4EC53 /* NSAttributedString+CharacterName.swift */; };
		2E900991B442CF013FE5FDEB52BDF01F /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 7FD88E10375476175A0144CCC33D86B5 /* <EMAIL> */; };
		2F595B210C812A369B0AF981070CB8ED /* HTMLTreeProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = A13EF83225F8280989C1B4644F4C0201 /* HTMLTreeProcessor.swift */; };
		303BEF8D208D1A4FF76F8A124C365AD5 /* NSObject+AWSMTLComparisonAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = D9B1729EEBD0ADC4FF2C2C1769353F34 /* NSObject+AWSMTLComparisonAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		303C1DD88B45BBD8CD7DA1AA6AC67AD9 /* ZSSCustomButtonsViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 5ECC8B54AC81C10B88EB37406DEB8E31 /* ZSSCustomButtonsViewController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		310661ECEAA2BB615E330BA92C07DA83 /* ZSSunlink.png in Resources */ = {isa = PBXBuildFile; fileRef = B575BD8E16AAAB8FE3CE326F713BAD0B /* ZSSunlink.png */; };
		312AE7AE9A6E3EB30FD084F241146810 /* IQTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = E5285A404596A0935B0BA6597DB3A47D /* IQTextView.m */; };
		31624D1655D06E52FD7F2264215DAC9D /* CSSAttributeType.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD7C9D5AB40262B95BB3FB53FAC06428 /* CSSAttributeType.swift */; };
		31F304FC20613FEFD839E34047F449C6 /* ShortcodeAttributeParser.swift in Sources */ = {isa = PBXBuildFile; fileRef = 13B1EDA7EC9AE069B4BB6D3A22118DCC /* ShortcodeAttributeParser.swift */; };
		322384803FDF05A58830635062175F0F /* NSRange+Helpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA9F7CAF04778BAF43B8AEAB75680FA0 /* NSRange+Helpers.swift */; };
		32889E620480C1E5F756E0024E9F2C28 /* AttributedStringSerializer.swift in Sources */ = {isa = PBXBuildFile; fileRef = EB057E231D1002C7FB222DF42A01C3DC /* AttributedStringSerializer.swift */; };
		328F038CA4B20997FF19A8C670DCBF0D /* AWSCognitoIdentityResources.h in Headers */ = {isa = PBXBuildFile; fileRef = 45555B9376A64C0156962C778ED629DC /* AWSCognitoIdentityResources.h */; settings = {ATTRIBUTES = (Public, ); }; };
		337428F0C302779F354FB3795BFDD14C /* ZSSoutdent.png in Resources */ = {isa = PBXBuildFile; fileRef = 3F617F83325E31A07FD8EB179E208CC8 /* ZSSoutdent.png */; };
		33794D097908172BBA7B92E1F9516D9E /* SpecialTagAttachmentRenderer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 93A3ECEAA852629251E567DD02905F1E /* SpecialTagAttachmentRenderer.swift */; };
		34F12F259EFE456D9AB87F623C2DFE86 /* ZSSTextView.h in Headers */ = {isa = PBXBuildFile; fileRef = 349DEC503349A25288631757FD8339AD /* ZSSTextView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		35A281E7977D414DC43845F9B1CCB9EE /* CSSParser.swift in Sources */ = {isa = PBXBuildFile; fileRef = F2081AE1A1284F945259F1A770E34889 /* CSSParser.swift */; };
		369469F63D4702E7EB246DB0634F4F94 /* FABKitProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = C9A8EB2E724C516C03CF1440E39D450D /* FABKitProtocol.h */; settings = {ATTRIBUTES = (Private, ); }; };
		36D41C33D818887857ADDDB0E3719A00 /* AWSSerialization.m in Sources */ = {isa = PBXBuildFile; fileRef = 195D12B4CE31F09A47E018BD02E15C5F /* AWSSerialization.m */; };
		37505B5201C5E07923545B7BD57DA15C /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 0C8D2038A9D99F02D343E6D2F7779FA5 /* <EMAIL> */; };
		37B890ABDC7DD441E6AA662325D412E6 /* MASConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 9AD41CE3DD9D13779F236141AB923239 /* MASConstraint.h */; settings = {ATTRIBUTES = (Public, ); }; };
		37F4A6A58892C4E564DD54EB0E6F2BB9 /* WordPress-Editor-iOS-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = A105034077CFBB0E2A112BF033BAD528 /* WordPress-Editor-iOS-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3866F0220817D2128801820110DA7F3F /* UITextView+Undoable.swift in Sources */ = {isa = PBXBuildFile; fileRef = AC1EC3852FAA899F11800775A2A52D3C /* UITextView+Undoable.swift */; };
		38ED8E58A5DD0B04D6186FF129475A98 /* ZSSkeyboard.png in Resources */ = {isa = PBXBuildFile; fileRef = 930FF5049DD63C81F91352537AD13FC5 /* ZSSkeyboard.png */; };
		39A80900512EA85A514A7135C391EAA8 /* AWSS3TransferUtility+Validation.m in Sources */ = {isa = PBXBuildFile; fileRef = D819C408A8ADCF07B67904A8D572502E /* AWSS3TransferUtility+Validation.m */; };
		39E0497C7835224C88697B39AC00321F /* AWSTMMemoryCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 8111EBB5048CB52A29C0402C6C90E7A4 /* AWSTMMemoryCache.m */; };
		3A9B4A5BDD7C5D398E87C541A32AE6C5 /* AWSGZIP.h in Headers */ = {isa = PBXBuildFile; fileRef = C23576EE4D80E4F01F94FF878DBA4BF2 /* AWSGZIP.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3B7DDA8AC1B0A07757E9C51E10F6CF68 /* FigcaptionFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 30B0C0D2B2CAD08CAE85660EA39786E8 /* FigcaptionFormatter.swift */; };
		3BAA42836EC51CF1AF92923E22AB8B4D /* ZSSh5.png in Resources */ = {isa = PBXBuildFile; fileRef = B1679FC776EDCED632CADABF467AA512 /* ZSSh5.png */; };
		3C0D5BD1C5607ECDA844921623290CCB /* Plugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = CF70592D5CE0FE6B21AF87DDB648F76E /* Plugin.swift */; };
		3C4AEE2C983897CC79457CD04B4B39F7 /* AWSLogging.h in Headers */ = {isa = PBXBuildFile; fileRef = EEF7C1919374A21C682A1FBC0CA362EA /* AWSLogging.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3C760C3FE2AC9D793C214631FCBE2C61 /* FormatBarDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 829A43B8A8EEFE789AE6D7115BD89ACB /* FormatBarDelegate.swift */; };
		3DABBFDABC03A66B4329A30A0C23BF44 /* IQToolbar.h in Headers */ = {isa = PBXBuildFile; fileRef = CBE29738EE0127A981F04E41AAF8DAC9 /* IQToolbar.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3DD36E03DC267312472F4AFEEB4F48B5 /* NSLayoutManager+Attachments.swift in Sources */ = {isa = PBXBuildFile; fileRef = BCF06F1A4556E293158A2D3427D56AAB /* NSLayoutManager+Attachments.swift */; };
		3E3249FFCF66B83AFDD9990FCC189DAA /* NSAttributedString+FontTraits.swift in Sources */ = {isa = PBXBuildFile; fileRef = 04712930695581C893D0FA730F42AF22 /* NSAttributedString+FontTraits.swift */; };
		3E50B8AB03669BEDC2C702F974119837 /* AWSDDFileLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = 32A480ECEAD05019B93201344BA19161 /* AWSDDFileLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3E893A0B7968D6ED2691A1461B50C184 /* DocumentType+Swift4.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8C9BCEA2B3640D180AFF3625E28AE130 /* DocumentType+Swift4.swift */; };
		3F38FD7AC1B97A972D40397A017A656E /* AWSUICKeyChainStore.h in Headers */ = {isa = PBXBuildFile; fileRef = 3926C62A6CA718012FE043321379A91A /* AWSUICKeyChainStore.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3F85BA3939138B6C58A0C773F2131120 /* ZSSforcejustify.png in Resources */ = {isa = PBXBuildFile; fileRef = 89FFE461E69C6CE0E84EA6BFC9E5F32B /* ZSSforcejustify.png */; };
		3FB4B354C1F53E7A9308FBCA75C37115 /* AWSClientContext.m in Sources */ = {isa = PBXBuildFile; fileRef = 4968981B497BB3A8BABDD479977EC3EA /* AWSClientContext.m */; };
		4012858D3B6D619D1B744CE3DFF2FB91 /* AWSDDOSLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 6F47FA784DD33522432B2CC185BC118F /* AWSDDOSLogger.m */; };
		40C7F79E381710C696018B3708705129 /* HTMLParagraphFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E34300DCEEA53679C0878FF8D099A9 /* HTMLParagraphFormatter.swift */; };
		40C8F7152C7194608DBB8F0B5379CA92 /* AWSDDLegacyMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 5398C69045FD4FE203EDAF9757769D0D /* AWSDDLegacyMacros.h */; settings = {ATTRIBUTES = (Public, ); }; };
		41036CB95CF0F10B8E667204DE604E44 /* AWSDDTTYLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = B897E25B523117B1B4F2B60DF273045F /* AWSDDTTYLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4158FA2AF7DDD63B5D4C2D07803EC9F0 /* PluginOutputCustomizer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 356905AB70772357AA910F0C5CE07A98 /* PluginOutputCustomizer.swift */; };
		41812F4826E8FE8B1DF49C9618765B8E /* StringAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 122A1FDB2A5EA16088E24D982A4137D7 /* StringAttributeConverter.swift */; };
		4244E57DDB307CC9DF3504127F11AB25 /* InNodesConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8DB57B22242369D57372EBC7E718895 /* InNodesConverter.swift */; };
		43002A8C6B0C044DD1EDE22B69C94EF9 /* String+CharacterName.swift in Sources */ = {isa = PBXBuildFile; fileRef = 215CF00CC0D2E2F90D8FC85852A8CEF1 /* String+CharacterName.swift */; };
		43D59F9203A119ADA3D29829BE4227B3 /* AWSDDMultiFormatter.h in Headers */ = {isa = PBXBuildFile; fileRef = 83FA287D7A85D8517864E61A69BB2EC4 /* AWSDDMultiFormatter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		44339C00EEAC1F42F4BD9F5FC1C93FA0 /* IQPreviousNextView.h in Headers */ = {isa = PBXBuildFile; fileRef = 1FD9C5127E16DDB5B0698CC793BF066B /* IQPreviousNextView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		445DBF3AFB999F2CEAC8A17BD815BA4B /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3E244762E12B8DF439FC4306B59E9232 /* <EMAIL> */; };
		4571A0EA37DC84F39E3830D38A1531AB /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE5BBF965B3752D8FF960CACBBF77D02 /* UIKit.framework */; };
		46285DB9A9659B075E62101B92A2056D /* ShortcodeAttribute.swift in Sources */ = {isa = PBXBuildFile; fileRef = 80E9B08FED6EB8F3A7CD09807B717871 /* ShortcodeAttribute.swift */; };
		46BB28F62F6521E1CA1BB3A729629041 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FE9C3C3DCCAF9CE6C6A54DA80A656949 /* QuartzCore.framework */; };
		475103394C72DD39AC32E1436F7B27F9 /* FormattingIdentifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5EF7253309C5E66A79B9F7A0FC1DD411 /* FormattingIdentifier.swift */; };
		48A76FE1B092845BF44F304C02FB0203 /* AWSFMDB.h in Headers */ = {isa = PBXBuildFile; fileRef = E97BCF1D169A5A1D63095CD780C88282 /* AWSFMDB.h */; settings = {ATTRIBUTES = (Public, ); }; };
		48D75E24074D80B0BA803FCA10775BFA /* AWSCancellationTokenRegistration.m in Sources */ = {isa = PBXBuildFile; fileRef = 845B34CC7B2DAD2ED03AEBCF5DB4601C /* AWSCancellationTokenRegistration.m */; };
		4A1715498623DEF75C4A64811C4B107E /* ColorFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B4321EC5D63E49AC4FDC1BE57FF1D3F /* ColorFormatter.swift */; };
		4A340A4E33D5D579C58D05B9C79AFD34 /* UIPasteboard+Helpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0C08AE8BA5A4F1D594434167187487E /* UIPasteboard+Helpers.swift */; };
		4A4A77BD2E94D2ABDAE9CD7030F234E4 /* GutenpackConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = C5198901E04E501A833F0FF49F1EAFAE /* GutenpackConverter.swift */; };
		4A997E78B54E039EF0FF48C2DEE3BDEC /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7FB463E08CB491D770D1C136C8C1263E /* Foundation.framework */; };
		4AA5CC38B853C31501C69D7D4FD6CA99 /* AWSExecutor.h in Headers */ = {isa = PBXBuildFile; fileRef = 6728180B89FD8D473A33467EEF87CE5E /* AWSExecutor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4ACB6A663BCFB08A0A8158F6463CF563 /* IQUIScrollView+Additions.h in Headers */ = {isa = PBXBuildFile; fileRef = 457CCD794442C7C27C23936EA4CB0B17 /* IQUIScrollView+Additions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4B6F8EFA53E31ED8C40A21FD14A6A91F /* NSError+AWSMTLModelException.m in Sources */ = {isa = PBXBuildFile; fileRef = 6BBD635FA05A2D7526B765997F06D40E /* NSError+AWSMTLModelException.m */; };
		4C88652FD272955B4A627F4503D82617 /* AWSSTS.h in Headers */ = {isa = PBXBuildFile; fileRef = 623C2F459B9B84527E06BCD6444BD3F2 /* AWSSTS.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4CC111CA5180A2E971D283026D7E5FE1 /* AWSTMDiskCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 99C4601DBF9EA72727271589E9B79F3A /* AWSTMDiskCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4D328F05AFEECC3B3637A7A986ABC5DE /* AttachmentElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BF9ADCE97343827849DC20CFDCA4F04D /* AttachmentElementConverter.swift */; };
		4D3A3222F4A8BEB1BC5367976AF846CA /* ZSSbgcolor.png in Resources */ = {isa = PBXBuildFile; fileRef = C76A3AEEC5AE69CF8633AE1E1ADFCB68 /* ZSSbgcolor.png */; };
		4EC9586C6C8A1895D5AE8B31206DB829 /* BoldElementAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9E8368C3580161A5C1D43904197F5581 /* BoldElementAttributeConverter.swift */; };
		4F300B14FA0C4375225BEFCD3DEC4657 /* ZSSinsertkeyword.png in Resources */ = {isa = PBXBuildFile; fileRef = 1F273EC6E6AC1D4C91422AE42A0A40D2 /* ZSSinsertkeyword.png */; };
		4F57D3D03FC1432F435E44621D37DF83 /* GalleryElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = B85C045D7629FC0E880202F650B37877 /* GalleryElementConverter.swift */; };
		4F58029FAF77F861BF22EF08B2076DE9 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = D6081B9B734C533F2D1A836FEE841432 /* <EMAIL> */; };
		4F6B73DA488D67FE1627D308791355FA /* CaptionShortcodeInputProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = B9A442ED36519EB4C4BAF837A19B5A49 /* CaptionShortcodeInputProcessor.swift */; };
		4FF321DBF88514A1D22A74BBDE8110F8 /* AWSS3RequestRetryHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = DF5C99563404E05B7CED21B339F1E03F /* AWSS3RequestRetryHandler.m */; };
		5005561A2A6042A85A801C8281692F57 /* MarkStringAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = E230B3086A0FF4B1388CDFEFEDE3532B /* MarkStringAttributeConverter.swift */; };
		5072430BCA1A650B644E622032451698 /* AWSCore-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 6166EFFEC336B3F993255148D94A61D5 /* AWSCore-dummy.m */; };
		5094BFE7A4D11AEE41E02B05462C05A3 /* HRBrightnessCursor.m in Sources */ = {isa = PBXBuildFile; fileRef = 38F5F0F67785D719E9F71D6A7A00DB94 /* HRBrightnessCursor.m */; };
		50BCABADC6A5FC1B00A253D9E958B0E2 /* ZSSh1.png in Resources */ = {isa = PBXBuildFile; fileRef = 91CB469A5BDECC08AA507FED5FBF05E5 /* ZSSh1.png */; };
		51FBB691D503F5C8A063CF8B72631906 /* NSArray+AWSMTLManipulationAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = E00A223B033023252D3906B75835325B /* NSArray+AWSMTLManipulationAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		526F818D64DE833C1B5C976244F0695F /* ZSSclearstyle.png in Resources */ = {isa = PBXBuildFile; fileRef = 7C65FC3A7A72534371AC6444DF4914AE /* ZSSclearstyle.png */; };
		53123E10EED954475D585D32A00BF4D2 /* AWSMTLModel.h in Headers */ = {isa = PBXBuildFile; fileRef = B391CB9E214DA15C37037A217641F72F /* AWSMTLModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		536A941A1556BDE2F78BF1F29AF6DD3D /* String+Paragraph.swift in Sources */ = {isa = PBXBuildFile; fileRef = D636AA42F89F893F58FDECE85FC1FBFF /* String+Paragraph.swift */; };
		53EC4B257A2FC015D67073EBD6848286 /* RegexProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 455ADBDA6E35123297CA2CA977F1AC7C /* RegexProcessor.swift */; };
		542E4DBEEED286E4BDE43FD357469BB4 /* AWSS3PreSignedURL.m in Sources */ = {isa = PBXBuildFile; fileRef = CDD7A04EDE672FB8DF3E2F05A8272A51 /* AWSS3PreSignedURL.m */; };
		5433AC8EF416EE85DA1093791A436133 /* SuperscriptStringAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = D378FA8A15146EB2BD288F751FF479F0 /* SuperscriptStringAttributeConverter.swift */; };
		55258B9086E0153797111EE693C7FA9F /* HRCgUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 59B1D5E156E5017D65D84E40906457A1 /* HRCgUtil.m */; };
		552A1101F65922522B26A4CABC9EFF55 /* TextNode.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225A16318E4C53492B121D4E9E32EC64 /* TextNode.swift */; };
		5584F96BEF00BE7F9B5A115516C36B5A /* AWSCancellationTokenRegistration.h in Headers */ = {isa = PBXBuildFile; fileRef = 8216011AB3DBE55EAC3EC1E695892B37 /* AWSCancellationTokenRegistration.h */; settings = {ATTRIBUTES = (Public, ); }; };
		55D769D7645327B00D156902A545EFB1 /* CommentNode.swift in Sources */ = {isa = PBXBuildFile; fileRef = D8685D6C7C6BCE54476678671D6808F8 /* CommentNode.swift */; };
		55EAB8232F1416B795ACC9ABE8A8C60B /* AWSBolts.h in Headers */ = {isa = PBXBuildFile; fileRef = F81DA223794E75F47D653205636DE507 /* AWSBolts.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5650CCA6210351DD5AB497F94199D25E /* ZSSparagraph.png in Resources */ = {isa = PBXBuildFile; fileRef = F6156E84C45AB51D43FAC904D18A8A03 /* ZSSparagraph.png */; };
		5658F281FBBE3F8F1369030129EEA4D2 /* CustomIOSAlertView-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = ******************************** /* CustomIOSAlertView-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		56E800EB3B2BE8AE0BA45A30974D7920 /* Masonry-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 4FF37E11D6B53A3D4838C1F163CF57BF /* Masonry-dummy.m */; };
		56FE480583EF9BFE2399DD13AB373524 /* InNodeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A4D56EE434F98C7FC54FB7DAC79CF1CC /* InNodeConverter.swift */; };
		56FF76F3268C38BE9F6A4F9436D11E81 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 097ABECCD50A7F3C7D61D73B87E6226F /* <EMAIL> */; };
		57B14356117BD81BCCF624E1B9F3132A /* HTMLProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB93B472172D5A1221F4A68802398F34 /* HTMLProcessor.swift */; };
		57FD10F4B92DD290E280DE6132518574 /* AWSURLRequestSerialization.h in Headers */ = {isa = PBXBuildFile; fileRef = 49D71110221D757C0CB53D3F55D291F3 /* AWSURLRequestSerialization.h */; settings = {ATTRIBUTES = (Public, ); }; };
		585E0A795B394F93D75D408AFC66A684 /* AWSEXTKeyPathCoding.h in Headers */ = {isa = PBXBuildFile; fileRef = AD2B778079026D96FD5732ED78ACFFDF /* AWSEXTKeyPathCoding.h */; settings = {ATTRIBUTES = (Private, ); }; };
		58891050646DFF567142A2248F729593 /* ZSSorderedlist.png in Resources */ = {isa = PBXBuildFile; fileRef = 3E979FC80F584432AE1119072D70F554 /* ZSSorderedlist.png */; };
		58E78B2E98A60A4B97AFB535F3E19188 /* MainAttributesConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8019BE9C525449AC6777B20200813346 /* MainAttributesConverter.swift */; };
		59773F5FB73B0ACD94C54A8C0F0F6A51 /* HeaderFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 38E79041FE8351E3C2E42817B2AD2F19 /* HeaderFormatter.swift */; };
		59ACEFE4AF81DDAA750154064380E73F /* ZSSimageDevice.png in Resources */ = {isa = PBXBuildFile; fileRef = 78B19AEF248AFAF06A15F0F6DA9141EF /* ZSSimageDevice.png */; };
		59F0BBB962FB497BFE96925764E48A86 /* NSAttributedString+ReplaceOcurrences.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6BA5E53794CB1A7D5D1924A240439486 /* NSAttributedString+ReplaceOcurrences.swift */; };
		5A947F2B74BD4B630643D3AE235FB43A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 0A5FE2974BDB1311E15E603A6754B2CD /* <EMAIL> */; };
		5AA3BB263DE0333ADDB76512B16052BB /* NSMutableAttributedString+ReplaceAttributes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7B6CB535B781BF4B6B396F79FB55DF41 /* NSMutableAttributedString+ReplaceAttributes.swift */; };
		5ACDD551C2B54B08C7EE7B014B5D3313 /* AWSModel.m in Sources */ = {isa = PBXBuildFile; fileRef = B7E7238C0AEE25E2932705C5DB26BD5C /* AWSModel.m */; };
		5ADB604C209759FE6349991CA6D6095F /* AWSCognitoIdentity+Fabric.h in Headers */ = {isa = PBXBuildFile; fileRef = 37081F17CBAE1F2535B46A6B59DB619B /* AWSCognitoIdentity+Fabric.h */; settings = {ATTRIBUTES = (Private, ); }; };
		5B08596E856E4CC2F34A8A2372F9F764 /* NSArray+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1CA742AAA15F496FEC813F2CC9407159 /* NSArray+MASAdditions.m */; };
		5B8932C9DDF49E45E773E87920FA85D7 /* AWSS3PreSignedURL.h in Headers */ = {isa = PBXBuildFile; fileRef = DCCC8562DCFD1E3FCC9A183247D40ED8 /* AWSS3PreSignedURL.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5BDBD885C47A9B46BF960B5025D2AB46 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = E72D7893E3009E211340A2B9B5A03ADD /* <EMAIL> */; };
		5C48B14FC860F81F25F746255400C745 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 2DA7C0F3D813B48B010E47DAB01C295D /* <EMAIL> */; };
		5C88E1B78D6724C19D070FCCE10704CB /* IQKeyboardManager-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 17B3917ED068AB56D3810991CF31059A /* IQKeyboardManager-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5CEE042E3D4069FB5FC86C2A1564E357 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 95AAA80023A09D43DF2B01519622C519 /* <EMAIL> */; };
		5D5BAB5D75CC70996D46A73A54184DE8 /* AWSFMDatabaseQueue.h in Headers */ = {isa = PBXBuildFile; fileRef = 192CB99A1A3CABA30AA090348A75CC24 /* AWSFMDatabaseQueue.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5DAA92258EE62D8371641B30383D1149 /* AWSDDAssertMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 5651B66AE63DF3C9D28A23701727FBC4 /* AWSDDAssertMacros.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5DC8C4F0F23982E81CC41542FB030088 /* IQToolbar.m in Sources */ = {isa = PBXBuildFile; fileRef = 01B2F9115C1925D20200A33F7A44087B /* IQToolbar.m */; };
		5E5B93A19F7A6026A6A0A81CF86C0F07 /* GenericElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 64614B89BF46ED549F261F6168E38801 /* GenericElementConverter.swift */; };
		5E6525FB520A5CA2D5469D36082FFD7E /* TextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 55AE4E9798206559C3DCDCDBF725FECD /* TextView.swift */; };
		5E7EAC95F11E9BDC426E72B44C4AEEEB /* CYRTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = EC410C1A7CBE4A0BAF57504964554143 /* CYRTextView.m */; };
		5E9970A8DE3387A036E3B095BBE8C4EA /* ZSSTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 23CED6C054FF7BDBF12E90D288D9855A /* ZSSTextView.m */; };
		5F45735DF355530CC955066D3C007E19 /* MASViewConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 29E6ACF651319B9A26A854D6DA3CBB23 /* MASViewConstraint.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5FDAF7732974188E3D0B50A2F696FB76 /* editor.html in Resources */ = {isa = PBXBuildFile; fileRef = A8E5A062BC2C12D27FDAB47AB0DCD1EC /* editor.html */; };
		5FFB8A7F1C40843FD6389CDC60CCC56E /* AWSGZIP.m in Sources */ = {isa = PBXBuildFile; fileRef = F8341D1E0EBD0BCE67F8476689B03490 /* AWSGZIP.m */; };
		602BC21CD18C6BBCF019E2A1300CA969 /* AWSFMDatabase+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = FD649B02DEFCC16A5EBA3842C6C27CA0 /* AWSFMDatabase+Private.h */; settings = {ATTRIBUTES = (Private, ); }; };
		604947531E91D67BB34FEAFC013EC129 /* ZSSFontsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 22BCE032BBD33162D3E9275C9C97F41D /* ZSSFontsViewController.m */; };
		607B8A893A5D58B9CE8D3E9B0679742F /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE5BBF965B3752D8FF960CACBBF77D02 /* UIKit.framework */; };
		607E23B19FD8700ED4BA434C686CABED /* IQUIView+IQKeyboardToolbar.m in Sources */ = {isa = PBXBuildFile; fileRef = 4C35D6B72AF803FF46236DEBD9538A40 /* IQUIView+IQKeyboardToolbar.m */; };
		60F8F5D53A696B13D2E88CD5892A92CF /* IQTextView.h in Headers */ = {isa = PBXBuildFile; fileRef = BBF0B4C8483D374CF913D7A32C3B4F38 /* IQTextView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		61507E402F1F7C58BF119995A0479A22 /* NSArray+MASShorthandAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 50584B4D5004395480A752508693C842 /* NSArray+MASShorthandAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		622A271F4187B19DAF81A6F1D72F50E6 /* AWSCognitoIdentityModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A8D3BDE8DB11B9DE4D293810156F319 /* AWSCognitoIdentityModel.m */; };
		626401380ADC61520BB5627C76521683 /* IQKeyboardManager-IQKeyboardManager in Resources */ = {isa = PBXBuildFile; fileRef = A5B2838E4866923F3DA717B058EBFF5D /* IQKeyboardManager-IQKeyboardManager */; };
		62E7550568A5A4FDF0CB9E5387909E75 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7FB463E08CB491D770D1C136C8C1263E /* Foundation.framework */; };
		62FCD4A967D743DE07DA80DACF5B14DB /* IQUIViewController+Additions.m in Sources */ = {isa = PBXBuildFile; fileRef = D78E6A5586279CEE4056F8FEA4754460 /* IQUIViewController+Additions.m */; };
		63AF101F0F9330BD239E2DC04388EDB9 /* HTMLPre.swift in Sources */ = {isa = PBXBuildFile; fileRef = C209BE1B2AC0B2E433FBC807B789B8D7 /* HTMLPre.swift */; };
		64168D7B65F965A5049DDDFD0F135EF9 /* StringUTF16+RangeConversion.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6142C216EF73A86D30B9F2ED8AAA7DF1 /* StringUTF16+RangeConversion.swift */; };
		64BC2B856E9972A9D1CB23B5C50D9A81 /* AWSTaskCompletionSource.h in Headers */ = {isa = PBXBuildFile; fileRef = 6DEB0307CE0537D3DB877865D38ABD00 /* AWSTaskCompletionSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		650A547EE1D74EB78102C879C0E36062 /* ZSSredo.png in Resources */ = {isa = PBXBuildFile; fileRef = C9AFB7272C30CA1C4B4A415161C2D87E /* ZSSredo.png */; };
		6569A960A49775D6B8318AFD3F8B90C6 /* TextStorage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3F68214465A80C238205DC7D43435219 /* TextStorage.swift */; };
		65A2BA2675C3CA9E080E1C343FC7F069 /* AttributeType.swift in Sources */ = {isa = PBXBuildFile; fileRef = EB490F0B68D427E72F1F760FEA34DFCA /* AttributeType.swift */; };
		65DB6D055AAD5F89EFB33C554CA7F53B /* IQNSArray+Sort.h in Headers */ = {isa = PBXBuildFile; fileRef = BA2197EC871A6323D44BB9675BFF43ED /* IQNSArray+Sort.h */; settings = {ATTRIBUTES = (Project, ); }; };
		664D3EB5DF5505F5023301FE1F5E7110 /* UITextView+Placeholder-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 8FAB08C499E17F0B00712D1A24584AD9 /* UITextView+Placeholder-dummy.m */; };
		66D1162BD854BC15E3200936A304D01C /* IQTitleBarButtonItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 42C84C6628B8E4B28D6E3DFE65D31341 /* IQTitleBarButtonItem.m */; };
		66EB7E0A586898E120B786D39F994AED /* AWSDDAbstractDatabaseLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 93FB0C3238BA760211FE768FFF134809 /* AWSDDAbstractDatabaseLogger.m */; };
		6786E92B2BF6586958D89CA1D3253747 /* AWSMTLModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 76CFE2037447BF1F045097EEBA4417EB /* AWSMTLModel.m */; };
		686D0A4F22E81F1DCC0479F291DF0BDC /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE5BBF965B3752D8FF960CACBBF77D02 /* UIKit.framework */; };
		6930BE61268C1F6272DFACF4E52A3A1D /* AWSEXTRuntimeExtensions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B776A3E797E4B8EEDA61EF1F11B6FBE /* AWSEXTRuntimeExtensions.m */; };
		6955DF9A8A7817124BCEA8A80B064C55 /* ZSSBarButtonItem.h in Headers */ = {isa = PBXBuildFile; fileRef = 6ADF3CAA4370C2E24C0B1CD33BD8BCAE /* ZSSBarButtonItem.h */; settings = {ATTRIBUTES = (Public, ); }; };
		69DC58C397AFC977E33BBA462052A429 /* HRCgUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = ******************************** /* HRCgUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6A25226823CB61420042CF3CD3F0624A /* ParagraphStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7222D67D962ED7B8792514D58F2B8FBF /* ParagraphStyle.swift */; };
		6A4CDFA9DAEE59CB9A11C961F299DE23 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 52075A5E43EA9D657F0C3D72D9E3723C /* PrivacyInfo.xcprivacy */; };
		6AB59672EB46D8DBEF528068DDA1E650 /* ImageAttachment.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7225EB57D56745BF550C02D1730AD91C /* ImageAttachment.swift */; };
		6AC6DB566533BB3C51FA22C7133A4977 /* IQNSArray+Sort.m in Sources */ = {isa = PBXBuildFile; fileRef = 49EC90BD7B2EF167EEF9CAC88CDC6F67 /* IQNSArray+Sort.m */; };
		6AD3E37014DE84F78EF20D20679FBFFA /* AWSS3TransferUtility.h in Headers */ = {isa = PBXBuildFile; fileRef = 61050C66A590F543EEF7CE93E4D0C371 /* AWSS3TransferUtility.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6AE3A8CE0601F3F782B6AD38C5880515 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FCEB3C046E5E247626A1DA623B152DF0 /* <EMAIL> */; };
		6B01DFACAE3F722287F0EEEA44F55CC0 /* NSAttributedString+ParagraphRange.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9428216B36ED69D2AAEAB5BF7B64E3C9 /* NSAttributedString+ParagraphRange.swift */; };
		6B7E7C48122CAB38B392E8CDB6A52616 /* AWSMTLJSONAdapter.m in Sources */ = {isa = PBXBuildFile; fileRef = EC8E359082632DD368135B02AAF7E8DA /* AWSMTLJSONAdapter.m */; };
		6C106C8524251E4416501C098E59DC44 /* Character+Name.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1EA243511A78898E9242B57B682A48AC /* Character+Name.swift */; };
		6C464AEBFE8E85A9FFDE4B09C107C788 /* AWSS3Model.h in Headers */ = {isa = PBXBuildFile; fileRef = 02134B9202DD57A98E9EE85B2EA527AE /* AWSS3Model.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6D2CD6AF47A76D19B82D3D1197C012A1 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = F4A5BFA29695EAA8C8F0BB2BCC5A29E9 /* <EMAIL> */; };
		6DB1D70618CF8A0F33E75E7FD397C3AD /* AWSDDASLLogCapture.h in Headers */ = {isa = PBXBuildFile; fileRef = 99F56AF89A82A83979896B07F61C4F55 /* AWSDDASLLogCapture.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6E39A4EB58A31F35447212C65DD3869C /* AWSBolts.m in Sources */ = {isa = PBXBuildFile; fileRef = 211A0E041B95372827B5C3CD90B67C98 /* AWSBolts.m */; };
		6E40A3DB67C930B119788FD5A9D3DF43 /* AWSDDContextFilterLogFormatter.m in Sources */ = {isa = PBXBuildFile; fileRef = 638B59C57691081714DF94E1105414E8 /* AWSDDContextFilterLogFormatter.m */; };
		6E963F8B93F83C4C0C02060F74F1484F /* AWSTMMemoryCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 981C790AD43EC86299CCCA039FF11C34 /* AWSTMMemoryCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6EC1C79506E536FCDB8751B0CE79EFA4 /* FontFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = C375E2FE5A4C3C599F3B0482E623243B /* FontFormatter.swift */; };
		6EC7F112735AF0F25CFA074F07F98D1A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 5768CA66FC6D4F2A5A91F89AEC34999E /* <EMAIL> */; };
		6F1996C947A78940C56A417EDF23AB4C /* ZSSSelectiveViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = BD82597FFB703C1EBA39C5D90AA148D9 /* ZSSSelectiveViewController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6F50F24D94076A2CEC79A86DBAB070A4 /* AWSS3Resources.m in Sources */ = {isa = PBXBuildFile; fileRef = BED37574F7251385DF55FAB4D8BA7D1C /* AWSS3Resources.m */; };
		700BA5C6D7BFE8251E95B6AAC1AEADE7 /* IQTitleBarButtonItem.h in Headers */ = {isa = PBXBuildFile; fileRef = 144D7D8856C187E674643B1968669749 /* IQTitleBarButtonItem.h */; settings = {ATTRIBUTES = (Public, ); }; };
		701A6794B2E9008E30D65D449A20EA72 /* Fabric+FABKits.h in Headers */ = {isa = PBXBuildFile; fileRef = 1910B7E730B49715E0CC9D517563030F /* Fabric+FABKits.h */; settings = {ATTRIBUTES = (Private, ); }; };
		71BB0CD56FD004B6F58703DABDF8A22E /* Pods-SnapInspect3-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = A908D8227F1E42268F80EA280762725A /* Pods-SnapInspect3-dummy.m */; };
		71CD6579C3A6219C624227FE1F1CCF31 /* HTMLDiv.swift in Sources */ = {isa = PBXBuildFile; fileRef = 917E30E7AF36AE4FC09DB3946E54C9BC /* HTMLDiv.swift */; };
		72B80401465DECACC88FDA41632CFB29 /* TextViewPasteboardDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = EBCDCAE8871EBCE71CEA84C7A443F151 /* TextViewPasteboardDelegate.swift */; };
		7342C00FB2A7218BD66EE50A5BC803FB /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 28F526E648A3518F8638E381CD064A90 /* Security.framework */; };
		73857DCAC405D113628199EAEBA0D5B4 /* FigcaptionElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = DED96BA25C711FD0BF237D2D210D7FBE /* FigcaptionElementConverter.swift */; };
		738DB8BF34AB20E6434800B3F8C00819 /* UIImage+Resize.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6F2A2CA14F27AD32A1CD80270410FAE8 /* UIImage+Resize.swift */; };
		73A23EF5AC075A85768E9EE448C0539A /* AWSSimpleDB-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 4B0054E487BCEFC478BB837BB2D27E7A /* AWSSimpleDB-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		75AEF67E5F8AB55E76DFC131A7F8DB46 /* Node.swift in Sources */ = {isa = PBXBuildFile; fileRef = CF5D36114A8AB10D32B0D58F5713DEBA /* Node.swift */; };
		75C09A86FB5D2868ACF88226BED8A9DC /* UnsupportedHTML.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0F7010BE81BBA3DFE1A7DD01500D17D7 /* UnsupportedHTML.swift */; };
		75EB437F3246ECF525733A7A5E94A04C /* jQuery.js in Resources */ = {isa = PBXBuildFile; fileRef = 0CA0E7960986B255EB42D0BD04524C2C /* jQuery.js */; };
		76111D9BC887DCE9740F90BF364017B0 /* AWSIdentityProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = 3D3B05789398B7B6198AD5365927E91D /* AWSIdentityProvider.m */; };
		772CF8E9CD02ECA4275B6173E2110E80 /* View+MASShorthandAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 20DD5DD231841EB0CD054E9FD7A7BE96 /* View+MASShorthandAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		774AC97029AFA0549641698C225651DE /* NSArray+AWSMTLManipulationAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = E6D6733F786B5D3F02F4DFCBF4E02370 /* NSArray+AWSMTLManipulationAdditions.m */; };
		774EE7A080C432272A311B73A98B6EC0 /* AWSServiceEnum.h in Headers */ = {isa = PBXBuildFile; fileRef = 3EF8EAE788C771937E1415A893339F46 /* AWSServiceEnum.h */; settings = {ATTRIBUTES = (Public, ); }; };
		789B4B8A9289366110589815260C2C44 /* AWSEXTRuntimeExtensions.h in Headers */ = {isa = PBXBuildFile; fileRef = B0E0975267A737851C218C21DC21F058 /* AWSEXTRuntimeExtensions.h */; settings = {ATTRIBUTES = (Private, ); }; };
		78EE858C9438D7A57262E1D8C1C28279 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 826E8F3C56C8D0005C3D245652B575C1 /* <EMAIL> */; };
		795D3BEBAAC12C0563A31122B87745B3 /* ZSSrightjustify.png in Resources */ = {isa = PBXBuildFile; fileRef = 9D898348183AE73ADEE5C5707B0827E2 /* ZSSrightjustify.png */; };
		798C43E48CD423C85EB15603B8AF64E1 /* AWSSignature.h in Headers */ = {isa = PBXBuildFile; fileRef = 6CCAA421ED6A2394D5CC7E8FCFD5727D /* AWSSignature.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7A1E340800248BB4861CC0B0516D8AF0 /* Attribute.swift in Sources */ = {isa = PBXBuildFile; fileRef = 04D414B15ACD38A9FD5A3FD390B38AD8 /* Attribute.swift */; };
		7A27FADBDCF99241F59F265FF0CA9AD2 /* NSAttributedStringKey+Conversion.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5524DC621EC590F329D352C73F45E4CD /* NSAttributedStringKey+Conversion.swift */; };
		7AF84C725A959E87E2AE069CCB62087D /* AWSDDLog.h in Headers */ = {isa = PBXBuildFile; fileRef = 1EAB1389E7B7667B9767F2E6F81B20E6 /* AWSDDLog.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7BC2C981E201CEB0CD73B1F6D3F301FD /* AWSS3Service.m in Sources */ = {isa = PBXBuildFile; fileRef = 5298F51E389DB6ECECC55D425173061F /* AWSS3Service.m */; };
		7C12C640B065FFA719630032C644FAA5 /* NSAttributedString+Analyzers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 97AF1FAE6D3DA554621D5D56F7EBEDDF /* NSAttributedString+Analyzers.swift */; };
		7C5505A2D3F2A697A5F324787061F4B7 /* MASConstraint+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = DF7B55685454B1762A22CEC8D0310AE9 /* MASConstraint+Private.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7CA477E561782E082D0FED5A3C54BACA /* AWSSimpleDBResources.m in Sources */ = {isa = PBXBuildFile; fileRef = ADA5741F6BCC100592751FA0D4AA8C7C /* AWSSimpleDBResources.m */; };
		7CC2A6BBCBE24CD6D5E179BEED44252C /* AWSCredentialsProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = 3E8BA41A73F8121A015507BD2EDC7846 /* AWSCredentialsProvider.m */; };
		7D4AA1998E543A91DF981C207CFB149E /* AWSDDLog.m in Sources */ = {isa = PBXBuildFile; fileRef = 50EC52C80BB9351BD7FD3F77C1012B45 /* AWSDDLog.m */; };
		7D7576F49D628A82DBD1425A1157D106 /* AWSSTSService.h in Headers */ = {isa = PBXBuildFile; fileRef = C4C9485AEB65EFA224A8882C58E5D0F2 /* AWSSTSService.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7D94877A130FB7BD0DEBE3864D9DC34E /* ImageAttachmentToElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 738FAD0985222D7E507BE7FEDAC9895E /* ImageAttachmentToElementConverter.swift */; };
		7DC189DB314BF660CB5DB4140CCD7B07 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE5BBF965B3752D8FF960CACBBF77D02 /* UIKit.framework */; };
		7E474EAFA5E2F9EB78F8FEC9E38B02A8 /* AWSS3RequestRetryHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = 834D5D87CAFE0E36DDBBB77A63249B4A /* AWSS3RequestRetryHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7EA54D82094944541E95CB5041773FC4 /* IQKeyboardReturnKeyHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = B15FE37152D666D5C1D33395B4B78CF8 /* IQKeyboardReturnKeyHandler.m */; };
		803EF0A1FD80636406D149170416B003 /* ZSSRichTextEditor.m in Sources */ = {isa = PBXBuildFile; fileRef = 5775490B4D1B6EFF212E4736D032AA64 /* ZSSRichTextEditor.m */; };
		807833A26E72FBE293BE8C1D47663554 /* AWSTask.h in Headers */ = {isa = PBXBuildFile; fileRef = F459000FF37B6A661E5CCB88B45753C0 /* AWSTask.h */; settings = {ATTRIBUTES = (Public, ); }; };
		80BC5DE1CE16235D6166643E18874114 /* IQKeyboardManagerConstantsInternal.h in Headers */ = {isa = PBXBuildFile; fileRef = F92F459EC99C7CC257CEF2291D790715 /* IQKeyboardManagerConstantsInternal.h */; settings = {ATTRIBUTES = (Project, ); }; };
		813BE4C96A6D39C13EC50C6CD164F0AF /* MASConstraintMaker.h in Headers */ = {isa = PBXBuildFile; fileRef = 44CE3454C1307BA6FD69D3EF29B127D4 /* MASConstraintMaker.h */; settings = {ATTRIBUTES = (Public, ); }; };
		81A4A9DD649CC8732FFA2658F8BA556C /* NSDictionary+AWSMTLManipulationAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 7C49B484F3F261F33738AA0DD9AA4BB2 /* NSDictionary+AWSMTLManipulationAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		830CEDBF82E0BA859502785FF8220D17 /* WordPressInputCustomizer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D41FB80E50F514C1979F3200F232C77 /* WordPressInputCustomizer.swift */; };
		836F7C86B67D8B9DC07E92DA3454E86C /* IQKeyboardReturnKeyHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = 704368D961907C9D12638D4FA30555A9 /* IQKeyboardReturnKeyHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		83DEC5A25771F8D7A58E1A4F626C3C8F /* AWSDDDispatchQueueLogFormatter.h in Headers */ = {isa = PBXBuildFile; fileRef = D3B7410F56195D34BA6FA4341CD76FEA /* AWSDDDispatchQueueLogFormatter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		844431C020362D51B4434A2FC9739A40 /* Configuration.swift in Sources */ = {isa = PBXBuildFile; fileRef = E1A9BAB022FAC5F5CFF7C98848DABF20 /* Configuration.swift */; };
		845C507ABE2B03F5D11BB487BB222D92 /* AWSDDASLLogCapture.m in Sources */ = {isa = PBXBuildFile; fileRef = F5772291CE124DEDB5DDEE211525061C /* AWSDDASLLogCapture.m */; };
		8473CBA5295F8C35F24EB3F6A11ECC01 /* AWSValidation.h in Headers */ = {isa = PBXBuildFile; fileRef = 73573205F758A3C405D7A69AA503A9D0 /* AWSValidation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		84CED2C84465FBC7D02D260BFC7701E9 /* ZSStable.png in Resources */ = {isa = PBXBuildFile; fileRef = 8DF46159153BEF3ECE1FD6F070554176 /* ZSStable.png */; };
		85C86F48862DB2AB748C52A9A2F06D25 /* NSAttributedString+Attachments.swift in Sources */ = {isa = PBXBuildFile; fileRef = D51983E998BEB28DC943C1579C4948C9 /* NSAttributedString+Attachments.swift */; };
		85F1ABBC8FBB1CADB94E0BC5E8BCA9B0 /* LayoutManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8F5E8BED95199D4F7A100BE19F99C1D /* LayoutManager.swift */; };
		85F83BA89EB4818CE233B0F26C765699 /* NSMutableAttributedString+ReplaceOcurrences.swift in Sources */ = {isa = PBXBuildFile; fileRef = BB452189A6A2527BA2572E1FED9FD05F /* NSMutableAttributedString+ReplaceOcurrences.swift */; };
		85FDE1091AD58CF459CE9B76AA22F250 /* HRColorPickerView.h in Headers */ = {isa = PBXBuildFile; fileRef = 60594BCC19FB157FAE64A5765806EE3D /* HRColorPickerView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		867C76F0B293E910A28E6315CEDCEC79 /* LIElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = B32AEC38238F7C01AE5609556EA5957F /* LIElementConverter.swift */; };
		868B44496A68FFF6D364DD9548078DCC /* CYRToken.h in Headers */ = {isa = PBXBuildFile; fileRef = FB8D412F745D7E32C26BB00D411F1CFB /* CYRToken.h */; settings = {ATTRIBUTES = (Public, ); }; };
		86AB7E862B4735E0098CB3B42B861242 /* GutenbergAttributeEncoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC301C3181DDEFDAA75801B80996AB23 /* GutenbergAttributeEncoder.swift */; };
		86F16E50281882B6295EF4AD221BDD80 /* UIColor+Parsers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5E72AA636629923D4322311C823D9CD1 /* UIColor+Parsers.swift */; };
		872C8AEB1CCE54C7B4F28FD7917867AB /* NSError+AWSMTLModelException.h in Headers */ = {isa = PBXBuildFile; fileRef = 547EAB1BE3AA869CD15DBAB199FE5B54 /* NSError+AWSMTLModelException.h */; settings = {ATTRIBUTES = (Public, ); }; };
		87592BE5C65DF3C576CA265AB83EEE55 /* AWSDDDispatchQueueLogFormatter.m in Sources */ = {isa = PBXBuildFile; fileRef = 2DDF2C4F33FD6482C1AC71B6D294822B /* AWSDDDispatchQueueLogFormatter.m */; };
		876499F256D618D225A49F6F4540E84A /* HTMLSerializer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0D377072F910FDB79D7AE76CE72E0DEE /* HTMLSerializer.swift */; };
		87A7E8CB9968B54066F8C0DC8403A84D /* AWSSimpleDBModel.h in Headers */ = {isa = PBXBuildFile; fileRef = B9ED883D190A2A182BAEEBF81746DF9D /* AWSSimpleDBModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		87BD0ED1231289CC370D1BED587C2808 /* MediaAttachment.swift in Sources */ = {isa = PBXBuildFile; fileRef = 797C45CC75CDE85071DA2BAA7E18EFA1 /* MediaAttachment.swift */; };
		882947BB173441CE5D8B6164DFFB62DB /* Pods-SnapInspect3Tests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 8545602ABE422AEBDB226FBF2D2399BB /* Pods-SnapInspect3Tests-dummy.m */; };
		89062974ED32988F0BC23CC85B4CBE10 /* UITextView+Delegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 728CE07C37573C09974CDEBE06F36D58 /* UITextView+Delegate.swift */; };
		89E71DB5847F98EF0725D2640369D1C8 /* CommentNode+Gutenberg.swift in Sources */ = {isa = PBXBuildFile; fileRef = BF341CD0B675238BFEC0443733F1BBD9 /* CommentNode+Gutenberg.swift */; };
		8A2C82BF46159957EF14B3FAA9CAA31E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 617BEDD58C1F01349E22E8A7086FCB49 /* <EMAIL> */; };
		8A68E9A4D8CF213E9155F1E732FFDC32 /* CYRTextView.h in Headers */ = {isa = PBXBuildFile; fileRef = 1DD9D813F4256C2D550E728DF0B29480 /* CYRTextView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8AC0B8D04477D2A90C0569725A916DB2 /* Pods-SnapInspect3Tests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = E4EF326ED9DB7B756E1EBC7A70FE23E1 /* Pods-SnapInspect3Tests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8B0DC8B6CFB0EB49CE574C04FFBD2295 /* CiteStringAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9247040712D2E676ABE303146C08E865 /* CiteStringAttributeConverter.swift */; };
		8BB6E710E3B30F2A55F103A42DD819EA /* StandardAttributeFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4271FCF653EE523E6572B0CF14D0692B /* StandardAttributeFormatter.swift */; };
		8C2CD923B27029D3C602AF7638F02DF6 /* IQKeyboardManagerConstants.h in Headers */ = {isa = PBXBuildFile; fileRef = 555E8F030F772F41331EF1EEB47ED4FD /* IQKeyboardManagerConstants.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8C5293636315107327511C4706E02869 /* AWSSTSModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 4B9B211F59242354487828265CB3D189 /* AWSSTSModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8C66DF5DC308823B9AE925936C0EEDD1 /* UITextView+Placeholder-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 18629905972CCFFCFA7F9DA8F8EC371F /* UITextView+Placeholder-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8C6C7E25C5A24C936F81823978190E96 /* ViewController+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = F564312154567EEF4E7EC72E5C9F4573 /* ViewController+MASAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8C725CA50D6E6EF78F9621388F3C8E2F /* BlockquoteFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = FBF57D77F6B3B7389719A0285F52CD9D /* BlockquoteFormatter.swift */; };
		8CC5AE27ADEFA04C22B42387222CF565 /* AWSMTLManagedObjectAdapter.m in Sources */ = {isa = PBXBuildFile; fileRef = F43D81C739F6CFA1BC5B2E3863D24BFB /* AWSMTLManagedObjectAdapter.m */; };
		8D611CB96055154EC8015D5EB805C81B /* VideoElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = C9F5126C0EC1ED09C4BB52767F9799FF /* VideoElementConverter.swift */; };
		8E4031687D3A4C4918E33C83BF8F96FB /* AWSService.m in Sources */ = {isa = PBXBuildFile; fileRef = 85506C33D6ACBC73C840990C39D8C24B /* AWSService.m */; };
		8EBBD67CB954963E8656B0045977B23A /* UnderlineFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A0A756442DFB9E20738362928F051C6 /* UnderlineFormatter.swift */; };
		8EC6FCC5B184197D9CCC197D9C85D799 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 8C6CEAD6FF46BCFFC517DBF6D3AB88C5 /* <EMAIL> */; };
		8EDA722335D5E63EC02FEC6A7B6583FE /* AWSURLResponseSerialization.h in Headers */ = {isa = PBXBuildFile; fileRef = DFE9FB38AFE4807ADF098632397DFE68 /* AWSURLResponseSerialization.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8EE639DB09DA0F5D92EEFC116EB3ADE8 /* CSSAttributeMatcher.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB0A191EB4E8D3F2D8E3BEECDBB30F34 /* CSSAttributeMatcher.swift */; };
		8F5A68D306F8535EB095822EC9C481EC /* AWSCognitoIdentityService.h in Headers */ = {isa = PBXBuildFile; fileRef = FC605FFBB3DE7BA7F708C726F71575B8 /* AWSCognitoIdentityService.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8F724103188BCE82B3F2CDAE352EC7E7 /* OptionsTableViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = C33AD5851F6175CE30C9A55A7BEA503D /* OptionsTableViewController.swift */; };
		8FDF46C01937AD224D17B02749DCB60F /* AWSSTSResources.h in Headers */ = {isa = PBXBuildFile; fileRef = 40FE320447E269AEC1B033900A2F066D /* AWSSTSResources.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8FF7B6477BFA6E6ABA168E1417291D5F /* MASCompositeConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 09D07A5FD2DF28FD7A522BD3C53F2ECB /* MASCompositeConstraint.m */; };
		903DAF1F6469E0537564628DD141E653 /* AWSFMDatabaseAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 40277157CEE8D04B755EA40AE3B5B5F2 /* AWSFMDatabaseAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		90DA11ED2C482EBF858FC8849E6A793E /* ZSSFontsViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = BFA5D86CE146F831C977D29ADDAF27D9 /* ZSSFontsViewController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		90F2ACAAAFD2178B9624E59F2F98454B /* AWSEXTScope.h in Headers */ = {isa = PBXBuildFile; fileRef = 47B0721500312E98CDDB9B79CCE98012 /* AWSEXTScope.h */; settings = {ATTRIBUTES = (Private, ); }; };
		9115B521394E139980D44B53FB5E4648 /* ElementAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1275F9CFE4974B39A872C32F8D8BDD8C /* ElementAttributeConverter.swift */; };
		914D0554C48F665CDE79438831C55367 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = A7C002473DB627C553D0EE1423982BA7 /* <EMAIL> */; };
		92E9697A733031FDED1687E7F61C4AF4 /* AWSTMDiskCache.m in Sources */ = {isa = PBXBuildFile; fileRef = C18B489BE2B5ADD61B238F6DB866EA23 /* AWSTMDiskCache.m */; };
		92F6F3DD7E45E46652CB0073E3B59A72 /* BoldStringAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8C6D278652EA585D68FB13E2A840CCE0 /* BoldStringAttributeConverter.swift */; };
		9394131A1C5D08CE746B01BD86A82519 /* AWSMTLValueTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = 4F9194FC2F8BB5A93E35DA589D6DFA3A /* AWSMTLValueTransformer.m */; };
		93D7616E5D511399D50F717F8AA989E9 /* AWSS3-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = F969559232E7B06DB96ECC88B5291E47 /* AWSS3-dummy.m */; };
		9497C7563ABA0B6947FEAAF202FFEF37 /* AWSXMLDictionary.m in Sources */ = {isa = PBXBuildFile; fileRef = FBF10A99698398BB3B52F654632C0D19 /* AWSXMLDictionary.m */; };
		9525717DB59E3A40DF24083E89652C7D /* CYRTextStorage.h in Headers */ = {isa = PBXBuildFile; fileRef = 903C5C496D228289A15316CF176BA1C7 /* CYRTextStorage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		95301BC363DADC43EE7C9EE38536D802 /* AWSS3TransferUtilityDatabaseHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A0DD016CF6AEC8D5E8AA37B55DEB66A /* AWSS3TransferUtilityDatabaseHelper.m */; };
		9575B0742742FAA12CDA3BC1B376AB84 /* AttachmentToElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9968ABA13A812750759CBD89C6F4A0A4 /* AttachmentToElementConverter.swift */; };
		965F959DF0E9CFBDC150FC23AC864756 /* ZSSh2.png in Resources */ = {isa = PBXBuildFile; fileRef = 181214419C4F510ED3AE893D1A4145A1 /* ZSSh2.png */; };
		971BB790BC554E0B09D724EDFCC2D505 /* MarkFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 06851E661D2C17A43BC27D5A4D344DB3 /* MarkFormatter.swift */; };
		9728EE5EE2E026019110143CBCEDF46F /* GutenbergInputHTMLTreeProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = D9A2F0252AC9448237DEEFA677853A89 /* GutenbergInputHTMLTreeProcessor.swift */; };
		9737FEFD384873E344D44FDA23E57B4A /* CYRLayoutManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 89FE963973B0B1859068323549AD1F27 /* CYRLayoutManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		97468BBAC22E7CA577EE9C82C4E91D62 /* ZSShorizontalrule.png in Resources */ = {isa = PBXBuildFile; fileRef = E41F24F034D5ECC74CC512127BB37461 /* ZSShorizontalrule.png */; };
		978E98D633D142FA5B824D51522F947A /* FontProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8C0DC31065D92ABF0464048E46D47057 /* FontProvider.swift */; };
		97C43A8009B9B2FCD3C9C2FE128CF299 /* HTMLAttachment.swift in Sources */ = {isa = PBXBuildFile; fileRef = F59A5756411A47B93814FAFDDA43BE59 /* HTMLAttachment.swift */; };
		982A7960ED18DF6C5AE1B2EBD213BE92 /* PreFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1298B59F12519D51BC957A3A0386CC99 /* PreFormatter.swift */; };
		9867966D2470B7B58A082B9F8B6CA516 /* ZSSindent.png in Resources */ = {isa = PBXBuildFile; fileRef = DBAB142ECACF1C5340488046EBD3E9E6 /* ZSSindent.png */; };
		991641A92E2DC37CF52C2C24100150A7 /* Converter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 876ABF628D9DE72E7646E9ED6545047C /* Converter.swift */; };
		99B897E377C14B9DA2919F83AE1D7B15 /* BoldCSSAttributeMatcher.swift in Sources */ = {isa = PBXBuildFile; fileRef = 12BC206C8845BC6D96492ABA143BF767 /* BoldCSSAttributeMatcher.swift */; };
		99EDAE39FF008B799A3F4F69D055A1AD /* AWSSimpleDBModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 527DF69633C91FA79917BC7E6431D4AE /* AWSSimpleDBModel.m */; };
		9A2C74A50EADDE69ACCE0F8D2AC1F6CB /* ZSSitalic.png in Resources */ = {isa = PBXBuildFile; fileRef = FD2E72D0432611CE3DBB82A7118D6C2B /* ZSSitalic.png */; };
		9A4E8B8F7B384E1024551E4B2956DED7 /* Metrics.swift in Sources */ = {isa = PBXBuildFile; fileRef = AADF95D6950248D1E241198FCF2ABE15 /* Metrics.swift */; };
		9A7D153A7D4F200B9779B1084601AD4C /* AWSDDASLLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = CC14EA37003C9F9EDDFAB2B4D8522780 /* AWSDDASLLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9B01FCF4AF533D61C2ACB289D60B6457 /* AWSFMResultSet.m in Sources */ = {isa = PBXBuildFile; fileRef = DE3C256959ECE4A37854A454867350F5 /* AWSFMResultSet.m */; };
		9B2E19FB0E083B2DF5BCEE7AEA7D23B7 /* IQUIScrollView+Additions.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC41F38A7F148F05A397CA73A7D9390 /* IQUIScrollView+Additions.m */; };
		9B5350E66C4080EF67A8D3B0798CBB8C /* AWSIdentityProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = D221ABA24362788B04C38371DC7CBC09 /* AWSIdentityProvider.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9BECE8C7E24EAB4217E61A71C059C354 /* LineAttachment.swift in Sources */ = {isa = PBXBuildFile; fileRef = 639A1130FC5C740DFC065F2FEAD27266 /* LineAttachment.swift */; };
		9C0CB58BFF96BC49B2FC80F028DBD640 /* AWSTMCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 17E1A3BC2775421D2C200F672365778D /* AWSTMCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9C4E9B21BB983ABE8D5D0EA33A25F2E3 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7FB463E08CB491D770D1C136C8C1263E /* Foundation.framework */; };
		9CAA66D83679BF07317C824F8D96EB2A /* BoldWithShadowForHeadingFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = E65FB0FDBC463303A34DD96661351FA1 /* BoldWithShadowForHeadingFormatter.swift */; };
		9D703DC51FCC257F549BDB2995678CB1 /* ItalicCSSAttributeMatcher.swift in Sources */ = {isa = PBXBuildFile; fileRef = BFFFC89A31DE183078A5145DB51BFE05 /* ItalicCSSAttributeMatcher.swift */; };
		9D9D4CB9606AD0AD8A1478E0D0E58E07 /* AWSFMDatabaseQueue.m in Sources */ = {isa = PBXBuildFile; fileRef = 405F1C37FB06DB35C92A64437575FF27 /* AWSFMDatabaseQueue.m */; };
		9E1E85FB0A5EC9B4C5388FFC076BEEE9 /* ShortcodeProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6BE43D121C2414F242B3AC1077A2B67B /* ShortcodeProcessor.swift */; };
		9EFB2F3288942DAB2EEF71B94F9D40D2 /* HRColorCursor.m in Sources */ = {isa = PBXBuildFile; fileRef = 550E5C69144C26B309C8ED92E96C07B9 /* HRColorCursor.m */; };
		9F125786F14008560614A87EA6D0972E /* RemovePProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0A580597DC2DF8CA22F274EBF1A56C78 /* RemovePProcessor.swift */; };
		9FE8DB8B14D951A4B62248AC137C7B42 /* AWSS3TransferUtilityDatabaseHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 877AF6C35718C06CF18D7634D6655A04 /* AWSS3TransferUtilityDatabaseHelper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A0A1E1303751DCE2387A20EAABBA5F8A /* HTMLRepresentation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8C5B1742EBD4FABEA46AA50116D42214 /* HTMLRepresentation.swift */; };
		A1E24C792F92594B3F3BF116F1C02BC2 /* AWSS3TransferUtility+HeaderHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = E5B3325338CB083CF942BBE997C46E02 /* AWSS3TransferUtility+HeaderHelper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A20D034B03C106C7D38E0FFB8CD9A604 /* UIFont+Traits.swift in Sources */ = {isa = PBXBuildFile; fileRef = ED742648F42A0FF1C7C3BFD356543434 /* UIFont+Traits.swift */; };
		A26DB9A491AD68A6EC98D92E8807E093 /* AWSFMDB+AWSHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = A1C522ACEAE3E35517A873AB77C7B09D /* AWSFMDB+AWSHelpers.m */; };
		A313478E310288704A456C9CD331A80D /* AWSS3.h in Headers */ = {isa = PBXBuildFile; fileRef = A59475F444CA3AC4FDC597B6C0C671CC /* AWSS3.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A32243824F1BF0E26ADD06F1E96C6299 /* FigureElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 84F85EEF0E7442B2B1EE9396B766DCA2 /* FigureElementConverter.swift */; };
		A32E6E097C189BC3B7592AF1C021E136 /* AWSDDMultiFormatter.m in Sources */ = {isa = PBXBuildFile; fileRef = 4F05A80F68B8D260112B398C93C53D8B /* AWSDDMultiFormatter.m */; };
		A345B1A04A58BA0633FCA36EB93C6E12 /* HRColorCursor.h in Headers */ = {isa = PBXBuildFile; fileRef = 96CD570ACBFACC29A9BE9C8DDE8A7F92 /* HRColorCursor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A45D3B525173E3BF3D1874FFBD38FAA1 /* AWSKSReachability.h in Headers */ = {isa = PBXBuildFile; fileRef = 6E2903F26479939D102361A3562C2479 /* AWSKSReachability.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A51C2B3362B81EFC72BC857DF4A3639B /* PipelineProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1AD819682E18D1A33EC662C4228B03A8 /* PipelineProcessor.swift */; };
		A6B72FFA55B83340E0C13A4A80A7BE42 /* NSAttributedStringKey+Aztec.swift in Sources */ = {isa = PBXBuildFile; fileRef = DAC39BB747BE315892883EC173D22E09 /* NSAttributedStringKey+Aztec.swift */; };
		A6D37161CA529498C758D7DF2F2BBF43 /* AWSDDLog+LOGV.h in Headers */ = {isa = PBXBuildFile; fileRef = A2371E8F49DCD048BF18D94BFD29DC4C /* AWSDDLog+LOGV.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A6E0BD27A057E2413926A6D4946BA265 /* NSValueTransformer+AWSMTLPredefinedTransformerAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = E2982D83493A9251C7C6BE0F4CACEBE6 /* NSValueTransformer+AWSMTLPredefinedTransformerAdditions.m */; };
		A75C33AF3EDFAF211341C4A13216F061 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B489F756CBB488250AC94B05B84D66C6 /* <EMAIL> */; };
		A789484945AC0B27E45F315880D28FB3 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7FB463E08CB491D770D1C136C8C1263E /* Foundation.framework */; };
		A7CD8657A6BA24F11EE4D69E1B109183 /* UITextView+Placeholder.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DDBECEED799A7A43F80F855AA433018 /* UITextView+Placeholder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A7E75FD5F699882C1263AFC6D122BE8C /* AWSCognitoIdentityService.m in Sources */ = {isa = PBXBuildFile; fileRef = A8DD60E2084C5DE8ACF1B2C6583E1EF2 /* AWSCognitoIdentityService.m */; };
		A7E9F40DFD86B9C7C7C613573FA6E8E5 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = CDFB470C5D11C6BC6069B4E0C3C07CF3 /* <EMAIL> */; };
		A82BF9C0FFB4FD595F04EE3FEBB40608 /* ZSSPlaceholderViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5F415AE0B84D32167555ABCC9005E6D5 /* ZSSPlaceholderViewController.m */; };
		A9381834B25FCD33A5E5D39D0E0E7D99 /* AWSS3TransferManager.h in Headers */ = {isa = PBXBuildFile; fileRef = AD7FFD189A8598CD3AAF4E564F7ACBED /* AWSS3TransferManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A9B2027CC35F5B532BE2E2525E3E9964 /* Array+ShortcodeAttribute.swift in Sources */ = {isa = PBXBuildFile; fileRef = 150FCA1A936738D5AFEC87092DE8AFF4 /* Array+ShortcodeAttribute.swift */; };
		ABE9B46FE6152C2ACA00C28C0DE09203 /* NSObject+AWSMTLComparisonAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = C55E09C0A9B0FD2471683DEAF395BBBB /* NSObject+AWSMTLComparisonAdditions.m */; };
		AC1172EDD1E0A78AD513726266B67444 /* AWSDDASLLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 5CFD3A2AEAB2229824D502647EAB5E23 /* AWSDDASLLogger.m */; };
		AC4EEC43C635548767A9CF8FE52335AB /* AWSCore.h in Headers */ = {isa = PBXBuildFile; fileRef = 1BA06FC2FBB82A646E0B8E92DBC41C2F /* AWSCore.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AC5A88137B59BE44BCA4BC33A3066BB1 /* ZSSsubscript.png in Resources */ = {isa = PBXBuildFile; fileRef = 5FE37BF832A23CF06031E09547E0B46B /* ZSSsubscript.png */; };
		AC704E2E012388ABB6E37F82BA957247 /* SubscriptFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 12769C1EC854AE0D071FBA8D0F088AC5 /* SubscriptFormatter.swift */; };
		AD420933AFCC3FE6222431010C769FDA /* ShortcodeAttributeSerializer.swift in Sources */ = {isa = PBXBuildFile; fileRef = ******************************** /* ShortcodeAttributeSerializer.swift */; };
		ADD8499452068F0DB9943E8D514FCDD2 /* CiteElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51CD230427EA642242F58513F7356C59 /* CiteElementConverter.swift */; };
		AE5AB9DB70374552A957C7537FE17F3B /* ZSSRichTextEditor.h in Headers */ = {isa = PBXBuildFile; fileRef = 730FBDA9EF3D89251EC5F6784A86D3D2 /* ZSSRichTextEditor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AE7B02645B8F769CA5F215EE8F7CC5B0 /* View+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = BD036081E355A0A78E5C83461E07BACA /* View+MASAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AEBDA1DC1ABCC7597027680352E71ECE /* String+EndOfLine.swift in Sources */ = {isa = PBXBuildFile; fileRef = F10A4882050CF12A800FCBE09E4A11FD /* String+EndOfLine.swift */; };
		AEDAE00D2AB83EAC7A02BC87F7AE2833 /* AWSS3Resources.h in Headers */ = {isa = PBXBuildFile; fileRef = 530A85936D44B96232C8AE5E4FA9A7CF /* AWSS3Resources.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AF272FD4749D8EEE9E1CB04DEF2B8D05 /* UIStackView+Helpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0937A48B91842E46510D876C7A64338E /* UIStackView+Helpers.swift */; };
		AFEE6655A6C465107D2148D3BD592589 /* AWSS3Model.m in Sources */ = {isa = PBXBuildFile; fileRef = E1318762F8470F95594D57AD21E65A92 /* AWSS3Model.m */; };
		B0393F89A2B7C009A2485180C33FF531 /* AWSNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = C77AD6683490ADE50CAFE73BC1BB0995 /* AWSNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B2CEAB042B37AECB6B71FBCD06FDA088 /* AWSExecutor.m in Sources */ = {isa = PBXBuildFile; fileRef = 297C04CB8ED04EB643395E8C060F0666 /* AWSExecutor.m */; };
		B32A180FD32A261DA68D1FD79F156BDB /* UIFont+Emoji.swift in Sources */ = {isa = PBXBuildFile; fileRef = 940BEA802B685570975157FD009EE6ED /* UIFont+Emoji.swift */; };
		B34EE475E952BBC64EB55AFA53C4597E /* WordPressPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5CA57AAD93FB7DA49B17A49D9BA1F5FE /* WordPressPlugin.swift */; };
		B353A64CF634761A870BE48726245D48 /* HRColorUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 03D44C2546E374AFC6D8F598130AC58D /* HRColorUtil.m */; };
		B35A5CD3A3A68D33BBB78CDBE06BD7A2 /* AWSCredentialsProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = 8C83C48284F8921E0632E8AA48CA43BB /* AWSCredentialsProvider.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B36C32A1DC9FA7F1BBE5747ADF19C476 /* AWSMTLModel+NSCoding.m in Sources */ = {isa = PBXBuildFile; fileRef = 1281A45D13B60DFC43269917F71F7BF5 /* AWSMTLModel+NSCoding.m */; };
		B3EBB552619D153A3F7151BA722979AE /* JSBeautifier.js in Resources */ = {isa = PBXBuildFile; fileRef = 698A8CF09E49986092016E5DE49A4E95 /* JSBeautifier.js */; };
		B44332DF9791582EB5D01FBC3869C580 /* AWSSimpleDBService.m in Sources */ = {isa = PBXBuildFile; fileRef = 0E4DF85BAF486D03F07290BA1EAAE4F3 /* AWSSimpleDBService.m */; };
		B59E60FBC9665FC1061B88B8E6FD9FAF /* Masonry-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = E75FEFF63044BB7DA3F65090089C66AA /* Masonry-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B5C718CE631C4DA647770FD7EDF0752E /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7FB463E08CB491D770D1C136C8C1263E /* Foundation.framework */; };
		B5D50443C405878F94FD61CF8EF15BAB /* GutenblockConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = D7B8CB609AB33A42406F276FE6C1D1A9 /* GutenblockConverter.swift */; };
		B680C2604BD8BC9644AE7C67BC46B9BB /* MASLayoutConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 5BF5EECD1547D846386284170DC1D1E6 /* MASLayoutConstraint.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B68DDB2E7B4B989FB73F34A327464621 /* ZSSleftjustify.png in Resources */ = {isa = PBXBuildFile; fileRef = 77BB5EE6ED2EDECBD411F1CD24F81615 /* ZSSleftjustify.png */; };
		B6CE5CB8D1E8F6BA89981E381A15C9D3 /* AWSSimpleDB.h in Headers */ = {isa = PBXBuildFile; fileRef = F888173F09CEEAB4493DD473DB3B7465 /* AWSSimpleDB.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B7672C65570BD20FFA8701121D7940C9 /* WordPress-Editor-iOS-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 7E984FB8DCF638BB61DCE418C8F67B05 /* WordPress-Editor-iOS-dummy.m */; };
		B76D424D237C9FABBC186BE713212404 /* AWSTaskCompletionSource.m in Sources */ = {isa = PBXBuildFile; fileRef = 6D59136F1388507489BB0A938C86C0CA /* AWSTaskCompletionSource.m */; };
		B7FB71BE01B35CB7365714B8295ADCA7 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 5B8CFB2B9F5E012D390A8ACDF2102A27 /* <EMAIL> */; };
		B80408732932015A5FCCA0D7E240E50A /* AWSDDLogMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 50A25FDB1EF5F62CC4CFF4831358D64A /* AWSDDLogMacros.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B80B5829186C6B4752D705434E044903 /* DemoModalViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D83C5CC3DA7D07A83C6E0DEFF2103F3 /* DemoModalViewController.m */; };
		B9200DA853AF98730F23F73058CD490F /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 07431828B67BD39967B508F00CE4DBAF /* CoreGraphics.framework */; };
		B9367C57A23B8343D653FFEC56CD8080 /* AWSURLRequestRetryHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 8459F05ECCAAD575CF7CB6C028E43445 /* AWSURLRequestRetryHandler.m */; };
		B949ADDC04FED68131A8AC7032AAD9DA /* ItalicElementAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E20CDF0898283444674C3CA432257AD /* ItalicElementAttributeConverter.swift */; };
		B9DF825AA587B24F869F613287E77526 /* ZSSquicklink.png in Resources */ = {isa = PBXBuildFile; fileRef = 86082A689D2C66A48D5AAF2AEA2E0094 /* ZSSquicklink.png */; };
		BA6E99D5CC61856BD39E9695D3F99631 /* ForegroundColorElementAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8DCAD714A8E80AAAC7528B3FED4DA767 /* ForegroundColorElementAttributeConverter.swift */; };
		BA904ABA8ED36CC4E5EB2B2004CA1F18 /* MASCompositeConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 038253724BF4626A000CFC2A50C6DE08 /* MASCompositeConstraint.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BABED818770BC4AE6A92AC75F75D1E41 /* ZSSunorderedlist.png in Resources */ = {isa = PBXBuildFile; fileRef = E77ABE55BFAE11F4FC7355018A66637A /* ZSSunorderedlist.png */; };
		BBC736908D19B1C1EADCF4FCA61A799A /* WordPress-Aztec-iOS-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 1C10EA016C6C7A70C2A4D952793CEABB /* WordPress-Aztec-iOS-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BBE8416060003393BE06B4399A7250E7 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = F2B1DE607CE9A45D4DBF9A57065CF354 /* <EMAIL> */; };
		BC27F264D7813C128E1000996D1637D3 /* AWSS3TransferUtilityTasks.m in Sources */ = {isa = PBXBuildFile; fileRef = 1B8F6DF217C03167DEA1195A385C6E4D /* AWSS3TransferUtilityTasks.m */; };
		BC91A11F62D0440D2125FD242E05DF61 /* BRElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 555D9149D58E5FFD50F69F51EAB980C6 /* BRElementConverter.swift */; };
		BC9682FB2976A1213BD81037D2D927D7 /* HTMLStyleToggler.swift in Sources */ = {isa = PBXBuildFile; fileRef = BF848E21E7446262B39EC796C68C40E6 /* HTMLStyleToggler.swift */; };
		BCC2052AA36DDA1A437094153C40173D /* AWSService.h in Headers */ = {isa = PBXBuildFile; fileRef = 0E1141B7368A68052DC4226DCD2F35A6 /* AWSService.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BCE64D07A736DC4115877184105A6359 /* AWSFMDB+AWSHelpers.h in Headers */ = {isa = PBXBuildFile; fileRef = 36E9F5AB776534B247915CE96EFD9212 /* AWSFMDB+AWSHelpers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BD2FE1DBB66E37D35547112691C48523 /* ZSSh3.png in Resources */ = {isa = PBXBuildFile; fileRef = BDF2A6850655F9CC6A63DB78241F3978 /* ZSSh3.png */; };
		BDB32861714959A4E6EFB3678C325BD4 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 4017BF898984538205277618E23E83EB /* <EMAIL> */; };
		BE07851962EBF324076FE46BE140C369 /* ZSSColorViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 9082B30DFDF81429C19E87E82407B053 /* ZSSColorViewController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BE6A8F2FD75CF2EC56E66254578255D7 /* CaptionShortcodeOutputProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 68DBF6A14B2F3292CB8AE931B00D4AA7 /* CaptionShortcodeOutputProcessor.swift */; };
		BE842CEEDABE17213E6F91148CF71E99 /* ZSSh4.png in Resources */ = {isa = PBXBuildFile; fileRef = 3B402823BA5F31C7192627F11AB9939B /* ZSSh4.png */; };
		BE8DE890E953564D5486DD6CBED46FA8 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7FB463E08CB491D770D1C136C8C1263E /* Foundation.framework */; };
		BEDD2C53CFCFD37B2C804A8A61FA14F9 /* ZSSSelectiveViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DD3DE6D2733041CEF845E8314E85E29 /* ZSSSelectiveViewController.m */; };
		BEF52BF81ED0BA3209B667B5BBDE54A5 /* ZSSLargeViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 28E59BCC5C47ACAA00937C7DCB09380E /* ZSSLargeViewController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BF22D137EF6324675FA50080C5D93C00 /* NSArray+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 66A9CCEAF64BB0063D1A97C737417736 /* NSArray+MASAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BF61A9638C4046244F7504489D6627BB /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 07431828B67BD39967B508F00CE4DBAF /* CoreGraphics.framework */; };
		BF93E5E0D4D22220CAA619BD3D6A2469 /* AWSCore-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = C4EBDCB812DE082CC1D8A443279C1006 /* AWSCore-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C02E1CD7E9187FEB26192768E0CD6C6C /* VideoAttachment.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6F95A12AE839B6405F69619B00BCC5BE /* VideoAttachment.swift */; };
		C17E23EA7CB94CF9D81CA6A2199170A4 /* AWSClientContext.h in Headers */ = {isa = PBXBuildFile; fileRef = C5C7E5DECF3D9295CDBE42349FB9366E /* AWSClientContext.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C1DFF7ADC039E3DCB58901C70783F5B4 /* AWSS3-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = B02164EA8470858C577C423F5D967E89 /* AWSS3-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C2068AEACC2D9C7F1FFE41AA25B12A68 /* MASUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = E26EACC90EF23460C230D65A5D55CE9E /* MASUtilities.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C2FE60A10C792613E45031AE6E851ECB /* MASViewConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = BAA9AAE0FE730578FA248BAC637AC620 /* MASViewConstraint.m */; };
		C3E4B5284CE32F2A906C3FDB050BB10F /* AWSMTLModel+NSCoding.h in Headers */ = {isa = PBXBuildFile; fileRef = 22887F82031644416E84A7204E5F160F /* AWSMTLModel+NSCoding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C48C4B93E5B299C99086570EEE83D72C /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = E7F57C8FBBEA23A5D7F81300162CCB70 /* <EMAIL> */; };
		C4D098CE7C4639568056A9158326CAA8 /* ZSSPlaceholderViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 958E826129D5AA9DEC5074C9F89BC3AC /* ZSSPlaceholderViewController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C4E861F81827FDA1C7C9A40C825170AA /* HRColorPickerViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = F847C9C848B73C5FF8634045B6DB557B /* HRColorPickerViewController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C4E8F0B8D79C3CB823FA4134E0395C6F /* AWSMTLReflection.h in Headers */ = {isa = PBXBuildFile; fileRef = 1EF07C441AD75BE9D582C89492B7F35E /* AWSMTLReflection.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C55D53CAC027E68AF48FF45532B14D0A /* AttributeFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63309F31146AB4C903CC7A3196078A5F /* AttributeFormatter.swift */; };
		C637103F229839E3C7653AD690A38F77 /* InAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9AFCE0ECCC017ECBA8B0157C0A9F326F /* InAttributeConverter.swift */; };
		C739921A2B30A851A2BD1853502E0AC1 /* HTMLDivFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = D47AE08537AEA4D6E9057597D2BB8FC7 /* HTMLDivFormatter.swift */; };
		C857B8D2D0BAA5A8A764F9E1C4B85807 /* ViewController+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = E70598D921E3E2ECC5DAC54D91C51FB3 /* ViewController+MASAdditions.m */; };
		C8E9DD78F3BCEFFC1F6567FA456AA399 /* AWSMTLReflection.m in Sources */ = {isa = PBXBuildFile; fileRef = 1E704FCBEDE3AFDF36B4835141628046 /* AWSMTLReflection.m */; };
		C8EC35DFB0945DBE2F2FF9ECFE6D9711 /* NSLayoutConstraint+MASDebugAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = D4ECEE9567271A5B94C7B83FE70ABF5F /* NSLayoutConstraint+MASDebugAdditions.m */; };
		C90B079736EAE092E00B2CA32B85E098 /* AWSFMDatabaseAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = A64EDFF3CE34949D3A965A50D0060C8A /* AWSFMDatabaseAdditions.m */; };
		C943C0B1691AF7849173FA461B61988E /* GallerySupportedAttribute.swift in Sources */ = {isa = PBXBuildFile; fileRef = 423A40F8AF0C61C887E96EEE9820403F /* GallerySupportedAttribute.swift */; };
		C943FF49078B48077D8C18CCF95EF92F /* HRColorUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = AAD320CE6517A51936DFD2C6A8017043 /* HRColorUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C9E19D164C26414115CC969ED9A303C1 /* MASLayoutConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 27BC91EDEA8EF9984D9F9A1E5B2FF42C /* MASLayoutConstraint.m */; };
		CA2D80A774CA224E57F8E2B29D55B46E /* ImageElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 32F27D73078787B366DFFB831C91CCE4 /* ImageElementConverter.swift */; };
		CBA4E86157FC8473E597F0D7050B5E4C /* IQBarButtonItem.h in Headers */ = {isa = PBXBuildFile; fileRef = 7BAE45001CE72B3C620CCAC0A8BE196A /* IQBarButtonItem.h */; settings = {ATTRIBUTES = (Public, ); }; };
		CC778579A2260EE731A4021117AFC713 /* WordPress-Aztec-iOS-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 8686E3BD6A3C230B9041630D692A0943 /* WordPress-Aztec-iOS-dummy.m */; };
		CD11E21BE5B056E5E8FA14A13A6D783A /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7FB463E08CB491D770D1C136C8C1263E /* Foundation.framework */; };
		CD48256D9CECAE4321B1822C075CDB83 /* GalleryAttachment.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD9025ADA9F348524356867F4D7C2AF2 /* GalleryAttachment.swift */; };
		CDAEEECAAEE3C96F1AEFBFC25D853B3A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = A9420D27C675E14B58E526325816B5F3 /* <EMAIL> */; };
		CDAF632A690DD9EEF2AB046DD584E8B1 /* ZSSimage.png in Resources */ = {isa = PBXBuildFile; fileRef = 0A242436B71B27FC1E4A5244459D73F5 /* ZSSimage.png */; };
		CE2E2CF660578E6E00E9F1A4473A9933 /* AWSSignature.m in Sources */ = {isa = PBXBuildFile; fileRef = FA7B0C8D1975D6F2E9C29EBBA0284F1E /* AWSSignature.m */; };
		CE86F407A7227375840D78435FC62703 /* UnderlineStringAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = D93F207F5E4A55F70E904D3350E47A2C /* UnderlineStringAttributeConverter.swift */; };
		CEAA9762AE07F50730E944B5D920DDE4 /* SubscriptStringAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 34CF0B5321B52651C350F3ABA3093B72 /* SubscriptStringAttributeConverter.swift */; };
		CF1222F500945786CCFCDA54C1E99D00 /* NSAttributedString+Archive.swift in Sources */ = {isa = PBXBuildFile; fileRef = AB3B7A57EAF5A49169480418B2C4B56F /* NSAttributedString+Archive.swift */; };
		CF29255890AA94D02FF0C561D9F15828 /* AWSDDFileLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = D1732181FE21DAEBF85DD3ADB8B88188 /* AWSDDFileLogger.m */; };
		D0D1BD33A1A80BDDD3780CD504DB6B8D /* SuperscriptFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 151F4074C4E3C4C4B13BD19991E61B04 /* SuperscriptFormatter.swift */; };
		D11351DF62441C422DF2701BE35E70CC /* WordPressOutputCustomizer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3661F17A73E95C0DE5265638C5025A94 /* WordPressOutputCustomizer.swift */; };
		D1961090D2875CF29600806A9E68E3F0 /* StrikethroughFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3B63EC20DCE9FF02443F11E6996DB297 /* StrikethroughFormatter.swift */; };
		D239C23EA20929C3E3CDC4F6B4390FBD /* HRColorPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 41333B3E10C40F0F6C451FFB07D501B9 /* HRColorPickerView.m */; };
		D26D4ACE75E28BFD688A7FC34E7E2724 /* NSValueTransformer+AWSMTLInversionAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 8B8CCEA8562FB0CAA855E9892319AE81 /* NSValueTransformer+AWSMTLInversionAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D363DF6224959D3F83B5F0BB683C6802 /* ZSSsuperscript.png in Resources */ = {isa = PBXBuildFile; fileRef = 105D8A2CE972CC478BA50EBE9A4D6999 /* ZSSsuperscript.png */; };
		D463CB7B759689BEA93954BCAF7C04A6 /* ZSStextcolor.png in Resources */ = {isa = PBXBuildFile; fileRef = CF54D9BACDC044506D6F68DBBC66BA63 /* ZSStextcolor.png */; };
		D476043570D7A008BC1942D342B0DF52 /* String+HTML.swift in Sources */ = {isa = PBXBuildFile; fileRef = 44D25AFA0D910C603722D8A644685A3A /* String+HTML.swift */; };
		D4B9CF817B3CFDF6BA6043F2992749C6 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B16CD00475BF1284F5E2CF445243F49E /* <EMAIL> */; };
		D5100C41EC22C42FE239C5300F212254 /* AWSS3Service.h in Headers */ = {isa = PBXBuildFile; fileRef = 290B681D6D19B19A2AB302958798CEFE /* AWSS3Service.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D6284116E531C0CBC1D105F2FE889482 /* HRBrightnessCursor.h in Headers */ = {isa = PBXBuildFile; fileRef = 357EDED1CD149FA5D8556F07EA9F61CE /* HRBrightnessCursor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D663837F4347AF58660EE6F7FD426ECE /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7FB463E08CB491D770D1C136C8C1263E /* Foundation.framework */; };
		D788BA4B9E8186271BA75CA52B30502C /* View+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = AD3B9EC43358F4BF419BB354BD9D0534 /* View+MASAdditions.m */; };
		D81624EF7B3F4CBB6805A746D28276A0 /* AWSSerialization.h in Headers */ = {isa = PBXBuildFile; fileRef = 911FFA8275F5C2BCA0C06B955E7C61B7 /* AWSSerialization.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D81CC2C58DA7A0F9364DA9A5F3F883DC /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 07431828B67BD39967B508F00CE4DBAF /* CoreGraphics.framework */; };
		D8BF432D975194053857506253DC9CF9 /* CiteFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE3149C5151BA104A29FEE081968A56B /* CiteFormatter.swift */; };
		D92A610A227F6A9EBA216425314B6871 /* CustomIOSAlertView-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C80B7E9AF74FA513E183FBE290E28F7 /* CustomIOSAlertView-dummy.m */; };
		D98C9F46D8E0D313A05D5425BF4D1F17 /* WordPress-Aztec-iOS-WordPress-Aztec-iOS in Resources */ = {isa = PBXBuildFile; fileRef = C417EB24EA443F14B2EF5F35E63FA887 /* WordPress-Aztec-iOS-WordPress-Aztec-iOS */; };
		D9FAC810A4A1400D2440E63E86FF711E /* UnderlineElementAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA2704F94C3FCAC9249EDFDC5E3FA21F /* UnderlineElementAttributeConverter.swift */; };
		DA15A3FFA27B101B8818A8808BAC5E40 /* EmbedURLProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = F975BDE7BFE498FFEAD0E12DEC01670B /* EmbedURLProcessor.swift */; };
		DAF8078CFCC5E871992E33695057E109 /* ColorProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B397F1E81751913B882870C417D2559 /* ColorProvider.swift */; };
		DBA9500CBBA5FF6FCBBA115AE4D12152 /* NSLayoutConstraint+MASDebugAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = DCC664C99DDBB480FFEC29AB06B29BA8 /* NSLayoutConstraint+MASDebugAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DBD304ABFB503805597E751AD69C2237 /* CYRTextStorage.m in Sources */ = {isa = PBXBuildFile; fileRef = 4EDC65361B2AB402DED6311C02F43B49 /* CYRTextStorage.m */; };
		DC603CBCE47B96B6AA145E256889F552 /* AWSSynchronizedMutableDictionary.m in Sources */ = {isa = PBXBuildFile; fileRef = 7EDECE18E660C72B4D9FA7CB4582081F /* AWSSynchronizedMutableDictionary.m */; };
		DC767EFA03E7E8E90612A00D6ABD8409 /* AWSCancellationTokenSource.h in Headers */ = {isa = PBXBuildFile; fileRef = 313D9F017893EC5D1D39DC041EA291E1 /* AWSCancellationTokenSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DD198D339C0F331BA234521737C0C75A /* AWSDDTTYLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 548E4579A2F41FC491D679739625C312 /* AWSDDTTYLogger.m */; };
		DD2E2E77E29589A3D40BE1ABCAEB3E1B /* AWSmetamacros.h in Headers */ = {isa = PBXBuildFile; fileRef = FC785106A32817B914FFF26BDEF1DB3F /* AWSmetamacros.h */; settings = {ATTRIBUTES = (Private, ); }; };
		DDDEE42EF767C17D9C31D99EE8399431 /* AWSFMDatabasePool.h in Headers */ = {isa = PBXBuildFile; fileRef = B34595B0BEDC3B2D63D1FC080E1668E3 /* AWSFMDatabasePool.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DE871EA5386975267F95C22DF8951FE2 /* HRElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 599E6006DA6A07FEB01679A64B6BA616 /* HRElementConverter.swift */; };
		DE8A00F3E5149DA3D5D36CBCA08B748C /* CSSAttribute.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8F5DE4B797E346FDB6C6F6704B8AD042 /* CSSAttribute.swift */; };
		DF2B15402CE105F5A8CE48BBDCFFD5DD /* MASConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = B34B5E2F0FAD624DFA7AEDE2C00F9750 /* MASConstraint.m */; };
		DF7B35F9570D6662374B19ACC42A8677 /* GenericElementToTagConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = D73296DC00FB984B0D4CCBA98FEEB245 /* GenericElementToTagConverter.swift */; };
		DF8E74A399AD6822FD419B2876338BB6 /* CommentAttachment.swift in Sources */ = {isa = PBXBuildFile; fileRef = ABC4C62B6B80178C30FB33C7D08D1839 /* CommentAttachment.swift */; };
		DF9A356185FA86299967F25E5F804380 /* GutenpackAttachmentToElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 166C2FDE683B09A9E70C7CBDB25649B6 /* GutenpackAttachmentToElementConverter.swift */; };
		E0142D33A5DD2015DEE26C7FF21F9E79 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 37EB391AF9C0460BD4DF10F4B6251888 /* <EMAIL> */; };
		E08AEE1EA3BD7FFDAD6C4EB77DFED690 /* AWSURLSessionManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 5059651588EA83D193D76041BA6F064A /* AWSURLSessionManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E1619B51610316C723561ECE1AE8EF02 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 671BBB6BC5C0AAE909871444BDC0AE52 /* SystemConfiguration.framework */; };
		E17DF855124E38FB6414775AF4AFF8B7 /* CYRToken.m in Sources */ = {isa = PBXBuildFile; fileRef = B9DEA711B1F797284E5A9708F277020F /* CYRToken.m */; };
		E20C0FD106A9ACB4550067DBA0136B5C /* ParagraphAttributeFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9053F06430FDBF219FFE8F6EA3348170 /* ParagraphAttributeFormatter.swift */; };
		E2269D51AC0E34E1E8BCE30DA1C58CF1 /* HRColorPickerMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 34ECFF26C3283CF927CEC068076A8230 /* HRColorPickerMacros.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E2CDB512511CC1F09A5423A8FA8F89B9 /* TextListFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03B4854CBE49FA43EED2306959C2C939 /* TextListFormatter.swift */; };
		E36D96643CA562A360678779E886DE84 /* VideoAttachment+WordPress.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0D3DA541B91685DAC2E73BCF60E667CD /* VideoAttachment+WordPress.swift */; };
		E3793B5E3EEFBCC94978DBF0D24BDC5D /* Blockquote.swift in Sources */ = {isa = PBXBuildFile; fileRef = 274699B1D2C22F0EB55A6F7BD9C6FA21 /* Blockquote.swift */; };
		E39471067B9E8F3C1EB91B5C891D3EEE /* ZSSRichTextEditor.js in Resources */ = {isa = PBXBuildFile; fileRef = ******************************** /* ZSSRichTextEditor.js */; };
		E3CAC3EB76F64237998894723BE79D4A /* PluginInputCustomizer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 38E87BDB93453DDF0BD1522C6E2842AB /* PluginInputCustomizer.swift */; };
		E3DC44EF91CB364EFFBD74B9F7A2EF41 /* CoreText.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9D15296881BFAB381C59EFC4966C6D01 /* CoreText.framework */; };
		E48B8286E7F64AD30F2D57ECA6E24DCC /* ZSSBarButtonItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 5A38E9FE9EB858D2A5F68995D1C7B8A8 /* ZSSBarButtonItem.m */; };
		E49C49F4C1B0B978559155B7C188AC3F /* FormatBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2C01276638E91D57CCD0895AC56A75C0 /* FormatBar.swift */; };
		E4D864469B4B100A1D4367FB4F7D4744 /* CustomIOSAlertView.h in Headers */ = {isa = PBXBuildFile; fileRef = 489C7F9CA1EBAD893492F836A60B3984 /* CustomIOSAlertView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E50EF621D72D2DB7BCEC579674BEBAD5 /* Assets.swift in Sources */ = {isa = PBXBuildFile; fileRef = EBD4716179247B892B0408640A886585 /* Assets.swift */; };
		E5B4E349010EE3231276EDA4D80203F4 /* AWSXMLDictionary.h in Headers */ = {isa = PBXBuildFile; fileRef = DA37F4A41BB30CADBA20FCB9F226E9EE /* AWSXMLDictionary.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E6C856B08B5E0F06F0814B3F1FD16754 /* ZSSviewsource.png in Resources */ = {isa = PBXBuildFile; fileRef = FAC0EB99E32B88789CC8AAF1824B23E8 /* ZSSviewsource.png */; };
		E6FC855D8ADEF6D46159054E5D90BA33 /* aztec.png in Resources */ = {isa = PBXBuildFile; fileRef = AF4069A14D9C01FB0A3E774A00E6D15C /* aztec.png */; };
		E70423B9FFC8CFBB53D07C6DC6A1B344 /* Array+Helpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FBCF675096DCEFB635C9BFCBFF69500 /* Array+Helpers.swift */; };
		E71721C3F30059935DA983CABFB184D0 /* ZSSunderline.png in Resources */ = {isa = PBXBuildFile; fileRef = 21C04AD0E8C825BF988BCACBEE9C1B22 /* ZSSunderline.png */; };
		E72E0788A8229863460334C38C36122A /* HTMLParagraph.swift in Sources */ = {isa = PBXBuildFile; fileRef = 660C00ECF2F488914D5AA9E4701F664C /* HTMLParagraph.swift */; };
		E75E4792B035CB5BE30A1CD3FB452753 /* BoldFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = E12AF7C977258FF6635EF3A52CD63EE3 /* BoldFormatter.swift */; };
		E84E42135263CCD3C82E86A9644C5402 /* ElementNode.swift in Sources */ = {isa = PBXBuildFile; fileRef = 49EDD161022F1226ABBF8FA40729A345 /* ElementNode.swift */; };
		E8AB268A35E2DD8014E090056A07637D /* UITextView+Placeholder.m in Sources */ = {isa = PBXBuildFile; fileRef = 8A9C455940ED58B87DB93AE3AF77AB40 /* UITextView+Placeholder.m */; };
		E8C1D0E835CDF2A13512B19D15E6CAEF /* ItalicStringAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 125EA11C82FA2EC02252CFE3A1472000 /* ItalicStringAttributeConverter.swift */; };
		E8CE8DBDEDBD5568227076DAD6F5BA48 /* VideoAttachmentToElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = FEC8C967B96E7DF9B00007197E3426B9 /* VideoAttachmentToElementConverter.swift */; };
		E8D76196E54CD3234F5DF7D9AA461A5E /* ZSSstrikethrough.png in Resources */ = {isa = PBXBuildFile; fileRef = 15C8C9E0261563ABE5CF764B84881735 /* ZSSstrikethrough.png */; };
		E8E58C13CE260EB592F8515E95B3DD91 /* AWSLogging.m in Sources */ = {isa = PBXBuildFile; fileRef = 758D76535C24029633B4CB5525FD6370 /* AWSLogging.m */; };
		E930A5612DC6D120BE040AD17C6D1BCD /* MASViewAttribute.m in Sources */ = {isa = PBXBuildFile; fileRef = 9DA3DE632D60541EB7D834021067DA80 /* MASViewAttribute.m */; };
		EAAA3B37B65F7EA48AD636418393FFCD /* ConditionalStringAttributeConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9E02BD2CFC8A70D922DDE96CA66A46A1 /* ConditionalStringAttributeConverter.swift */; };
		EB11A8EFA070117F8E6AF08345772E43 /* LineAttachmentToElementConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BCD43C2F7D1E824EADF092422238B70C /* LineAttachmentToElementConverter.swift */; };
		EBA5FCF696B3C5D8597B8CA750372464 /* CYRLayoutManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B558202ADA4407D1A64B290FF504C72 /* CYRLayoutManager.m */; };
		EC565C6DB1038E063EE2F9BD1A4A40EC /* String+RangeConversion.swift in Sources */ = {isa = PBXBuildFile; fileRef = CF23F6F37818B43A7807B1850D91DB37 /* String+RangeConversion.swift */; };
		EC73F4B2541F0E9810745D6AE9CC46CD /* AutoPProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = FF81762FE99A9978A8B94631E05653D7 /* AutoPProcessor.swift */; };
		EC9B34262AED632D7EFB49804337648E /* Masonry.h in Headers */ = {isa = PBXBuildFile; fileRef = A7B73D648F0D281C558AF0E3CA4195E2 /* Masonry.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ECD2F320A154DB0933465C19D5DD6308 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = F18CFB4577F1D7DFE58D9743FE984C62 /* <EMAIL> */; };
		ED4C5B96740B71F0350D9F4A45A91F67 /* AWSS3TransferManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 97178E4CD9890DD8945143B4F35552F6 /* AWSS3TransferManager.m */; };
		EDAD4950F0CAEFE4E5B225A25DB7E1D2 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7FB463E08CB491D770D1C136C8C1263E /* Foundation.framework */; };
		EDB309CB9B92BEA6210FB7636B775F1B /* ZSSpicker.png in Resources */ = {isa = PBXBuildFile; fileRef = FA93A6B40EC8EE06F9D01F4581D01807 /* ZSSpicker.png */; };
		EDC30424370FA6E922920A3E8C9A1F85 /* EditorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = EF9A7B350CD6915D385FC0B851BEB0F8 /* EditorView.swift */; };
		F06AF8BF57F20FEFC9E2546548DFB075 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 363FEAFF429B76667F1B0502D1873343 /* <EMAIL> */; };
		F07D340DEDD5F2378761E235B3963348 /* IQUITextFieldView+Additions.h in Headers */ = {isa = PBXBuildFile; fileRef = 75398A29EDFDFA22F9212F8A8FD5923F /* IQUITextFieldView+Additions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F0988B5B3762B8B65EC2E3EF3F05F969 /* AWSS3Serializer.h in Headers */ = {isa = PBXBuildFile; fileRef = B11BE74E22A12A4098F043BF3B407E34 /* AWSS3Serializer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F0F8EE59EDB86336E29CCA58CA100EDE /* IQKeyboardManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 24CD46B7BC4C45C484CC6D328B4A9063 /* IQKeyboardManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F18AB87ACDA15E30A7E211C4201FA1B1 /* NSValueTransformer+AWSMTLInversionAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 5A821347CD1D8A20D645FE673B89478C /* NSValueTransformer+AWSMTLInversionAdditions.m */; };
		F198043A3A9295E676BABBA41232222A /* AWSUICKeyChainStore.m in Sources */ = {isa = PBXBuildFile; fileRef = 6B0C62E41EEB1F8AF6B5E729C5A800DD /* AWSUICKeyChainStore.m */; };
		F1FF83938200DE7CA5CEA7473B5391EF /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 16E9830A962849892F6E7F367F0F579D /* <EMAIL> */; };
		F24267E4F85DF7F637AFD9501DA7451D /* DemoModalViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 0711EA3F47547EAEFCC09F6FDB0EA1CB /* DemoModalViewController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F294B393CC184A77803BB708DF749E23 /* CLinkedListToArrayConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = B89995A7AC4D608EA58571EA22CCBEAE /* CLinkedListToArrayConverter.swift */; };
		F2C7366946814C115C9156674B8A2F0A /* OptionsTablePresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D3E0AA31BFC20022F1FD3C1A73CD590 /* OptionsTablePresenter.swift */; };
		F33CF65B2495C391B5A4C42E10621CEF /* AWSValidation.m in Sources */ = {isa = PBXBuildFile; fileRef = 796C1D4D0E02C7E49C871D77A15B055D /* AWSValidation.m */; };
		F34C7DA40E83F15A848792C487BE9AC0 /* AWSCancellationToken.h in Headers */ = {isa = PBXBuildFile; fileRef = D99737B2BC8F7D0892BF7F1DA1F200B7 /* AWSCancellationToken.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F3C4DCDD47543BCD85C33E2BFB94017C /* AWSSimpleDBService.h in Headers */ = {isa = PBXBuildFile; fileRef = 515991B90E307156A5BC561574050B72 /* AWSSimpleDBService.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F3CEB7F204897849B5F99DB240AE4531 /* GutenbergAttributeDecoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 42BE4BB4FA24D3AB56CF7DFD6A2A1E50 /* GutenbergAttributeDecoder.swift */; };
		F460B8D9827F2F2D722A47C28B2D4C95 /* IQUIViewController+Additions.h in Headers */ = {isa = PBXBuildFile; fileRef = F5DC4F3600A2A49E4A906226A7A5E292 /* IQUIViewController+Additions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F4ABE5F340FD7875EFED790228E19913 /* AWSTask.m in Sources */ = {isa = PBXBuildFile; fileRef = 08FAAE69093894648B755A8AB25E808E /* AWSTask.m */; };
		F5593A9079FCE83FF3D1497FC3CB3E50 /* AWSMTLValueTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = 56E0F20EEBDFA49113C6F76E28AF022E /* AWSMTLValueTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F56F2A1CDFC69C3EF4D3F2BB364684A4 /* ElementToStringConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1E0AD4D0ECB835EC6ACBC70FEE7A14 /* ElementToStringConverter.swift */; };
		F5A5A99010BC92F28398F2C2991F7292 /* ImageAttachment+WordPress.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6DBE0C1F3C2495032E38C84CD46909A7 /* ImageAttachment+WordPress.swift */; };
		F5AF0B57618D1FCEF95B63EC56B32B31 /* ZSSbold.png in Resources */ = {isa = PBXBuildFile; fileRef = DA54B32D41AF298D6558E6BDAB0C33D0 /* ZSSbold.png */; };
		F64235CF66746719F1801082B7C681E8 /* AWSXMLWriter.h in Headers */ = {isa = PBXBuildFile; fileRef = A824636F156CD9FB741A732835E18402 /* AWSXMLWriter.h */; settings = {ATTRIBUTES = (Private, ); }; };
		F658ADF464810FEB16192B131870012F /* AWSSimpleDB-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = E24AE983ACB78BFC1BD2C78449EC4489 /* AWSSimpleDB-dummy.m */; };
		F6B55C9AF3EE2C05B7CB9B789DEBE693 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 0AA8C6968875E0578A0D46F4B1FC0A98 /* <EMAIL> */; };
		F6D1C960368EB1E067ABD0BFF707FC56 /* MASConstraintMaker.m in Sources */ = {isa = PBXBuildFile; fileRef = 40E30DA9C3BD6250D6D306AEC513E69F /* MASConstraintMaker.m */; };
		F6D83EFB94ADE1AA7FFAF7FC5762BC31 /* HTMLConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 577A82C3784C61980A348C3339C38EC3 /* HTMLConverter.swift */; };
		F6DEEAB7965DA6574F2C54C63E696543 /* AWSCognitoIdentity+Fabric.m in Sources */ = {isa = PBXBuildFile; fileRef = 62FF9C38311EA366D40408B6FDBEA95D /* AWSCognitoIdentity+Fabric.m */; };
		F80FEA299E2688461BDE931344E97373 /* AWSURLRequestSerialization.m in Sources */ = {isa = PBXBuildFile; fileRef = 8CBE5939B36C709164984BFB030ABEB7 /* AWSURLRequestSerialization.m */; };
		F81563CBB9710F03F6F0598528ED1242 /* CustomIOSAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = B17D712E52732810E6EBB6A84BDEC604 /* CustomIOSAlertView.m */; settings = {COMPILER_FLAGS = "-DOS_OBJECT_USE_OBJC=0"; }; };
		F884345D750741F026D26BFEAD4775BA /* HTMLLi.swift in Sources */ = {isa = PBXBuildFile; fileRef = 61B1580831F5EE4C99337A1E56114053 /* HTMLLi.swift */; };
		F8D47FB527D982DA43E870A088504A5A /* AWSDDContextFilterLogFormatter.h in Headers */ = {isa = PBXBuildFile; fileRef = AAAABF29488FA39F76C549E9F8D0CDE6 /* AWSDDContextFilterLogFormatter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F928B02E247F56A754330690C9DF2D4E /* NSDictionary+AWSMTLManipulationAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = B15DCBE8E89EA8C8E0E845B756A805F5 /* NSDictionary+AWSMTLManipulationAdditions.m */; };
		FA5C5008DE4C7939DAFB39D9C4CA3263 /* AWSFMDatabasePool.m in Sources */ = {isa = PBXBuildFile; fileRef = DAD1927B144306FD16FE78AB6F83CFA9 /* AWSFMDatabasePool.m */; };
		FB3DE04B6D6287AC80225D779E96F283 /* AWSFMDatabase.m in Sources */ = {isa = PBXBuildFile; fileRef = 9CEC9BED47F9A9090F913FA2EE294605 /* AWSFMDatabase.m */; };
		FB43CBE2CA4DFCB42306090CA0351B87 /* AWSGeneric.h in Headers */ = {isa = PBXBuildFile; fileRef = 627559E1B38D7FA374DD781638070008 /* AWSGeneric.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FB9CA60B34A59B4B706A43DB45584D48 /* RenderableAttachment.swift in Sources */ = {isa = PBXBuildFile; fileRef = 06326BD3E7AAF964B86655D35A00CAAF /* RenderableAttachment.swift */; };
		FBC75C96033593C07244089F619A5DC6 /* AWSEXTScope.m in Sources */ = {isa = PBXBuildFile; fileRef = 6EDC72D8701ADAEE54C666BB207C79F1 /* AWSEXTScope.m */; };
		FC6BCE4E74D68EF40D222DF549423FEF /* AWSSTSModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 521B7C312C31BA26032799373DFB8DB2 /* AWSSTSModel.m */; };
		FD4DBB5BE5BAC3F0410DFF70172CC90B /* AWSCocoaLumberjack.h in Headers */ = {isa = PBXBuildFile; fileRef = E432BD589A2FCE4F28C251DD1FD55074 /* AWSCocoaLumberjack.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FDE46805D0553B751AFEC3AB4987D805 /* AttributedStringParser.swift in Sources */ = {isa = PBXBuildFile; fileRef = 641112455CC4BEB1F70702889FBDB738 /* AttributedStringParser.swift */; };
		FE13C0BF9DD3F59EC7AFC6C7ED0F653D /* Processor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 251BF72F713401D3B45B731343FF621F /* Processor.swift */; };
		FE3F696FDD39748DB6A6F6444990413F /* GutenpackAttachmentRenderer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7240A1FFFCBE40594C39FA603F44590E /* GutenpackAttachmentRenderer.swift */; };
		FE4599BC32C0FED7C1836929AD278DCA /* ZSSRichTextEditor-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 681D312522EFBDBCC7C7C5D24188044D /* ZSSRichTextEditor-dummy.m */; };
		FE777530D958BFCDC7DD5296F501D71D /* HRColorPickerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9FE94E5220B1B4EDEB0DDF7CC3954033 /* HRColorPickerViewController.m */; };
		FF1E08069DCD8B9DBEE1BC6CB8AB24B9 /* AWSURLRequestRetryHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = 1201C4BCEBE15F5A9A9A6B9C31AC2F53 /* AWSURLRequestRetryHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FF4AC76147DBA55CEAB7D3D3FB274145 /* ItalicFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BF42115721F9D66BBA24B92B2CC207E8 /* ItalicFormatter.swift */; };
		FF639263CDF715D5B9DFD37C3119BF2E /* LiFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = F9C7BD95E4E51F2DD088F0C2452EFFF5 /* LiFormatter.swift */; };
		FFA7F53A0DE854AC8C3368A1C27A2EAE /* DocumentReadingOptionKey+Swift4.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5720D83C765870F489179B2733A34E8F /* DocumentReadingOptionKey+Swift4.swift */; };
		FFFEFCA8AA921F2536E85C88BEB4EAE8 /* AWSTMCache.m in Sources */ = {isa = PBXBuildFile; fileRef = ABFB8288FEC824AEC757558B8201837D /* AWSTMCache.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		115CDEC6E289AEF158614FDC959B431F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6AC97A319172C716659A6283A2D82401;
			remoteInfo = "WordPress-Aztec-iOS";
		};
		202A1847EA969B6AC48BBE01CF14F148 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DFF29EDE35ED89B0D385581C6035677E;
			remoteInfo = "Pods-SnapInspect3";
		};
		221C9093E65AAEC78475F3AD83B7CC1A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 94DBE1F02563D90F416A7DB9558489B2;
			remoteInfo = "UITextView+Placeholder";
		};
		229E2A4F1CD5CE5EDEB127D39BAF579E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9B172FACE90046AA5E100E650B6109DD;
			remoteInfo = AWSCore;
		};
		337C12EA4FD5F8D96550CEC40F65B3A4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 52036133E4B9C2D1B79378F2A9B7BFE4;
			remoteInfo = "WordPress-Editor-iOS";
		};
		4AD33A56E73F81E17394FBB41AAD2464 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D7940BE184F37BA203D5586A4D2F87BF;
			remoteInfo = CustomIOSAlertView;
		};
		50E8815F47855252EB1614194FEA34CD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 73305835456A90447978F6D2F459C81A;
			remoteInfo = AWSSimpleDB;
		};
		525CB0F3309F31002B26932423EC966E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 55AF53E6C77A10ED4985E04D74A8878E;
			remoteInfo = Masonry;
		};
		5BA3EE8DE43395370A8CB5DCF31CE249 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6AC97A319172C716659A6283A2D82401;
			remoteInfo = "WordPress-Aztec-iOS";
		};
		9511534A1F67CB2BF0D6A087ED88F6E3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 31F0084E2E60CA68AAF7E3224C77C86E;
			remoteInfo = AWSS3;
		};
		95681C64DB782E8C1132D164068E0448 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 05B2A835D60F78761395189914B88047;
			remoteInfo = "IQKeyboardManager-IQKeyboardManager";
		};
		B217EB9DA02506F3FE9FC58A87ACAA01 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = FBA456CB50E371584C11231929A0971E;
			remoteInfo = IQKeyboardManager;
		};
		D22D5E51179B8DF8191151BF0E60C6B3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9B172FACE90046AA5E100E650B6109DD;
			remoteInfo = AWSCore;
		};
		DCC8A206ED064479F1D5D878F593A827 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EBF557DDD39B493EA318729B2036BAF5;
			remoteInfo = ZSSRichTextEditor;
		};
		E9933646FA10D2631CF1FEF0324B8891 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A8A26D58F4832E00EBE5951F0425C233;
			remoteInfo = "WordPress-Aztec-iOS-WordPress-Aztec-iOS";
		};
		EE4254DC3B35AFE25782BECA702A8E3B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9B172FACE90046AA5E100E650B6109DD;
			remoteInfo = AWSCore;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		004E2DA89A9EDD6F56D27B9A8FE55A9A /* WordPress-Editor-iOS-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "WordPress-Editor-iOS-Info.plist"; sourceTree = "<group>"; };
		0090612116EAD6CFDF6527ED6C0A7615 /* Masonry-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Masonry-prefix.pch"; sourceTree = "<group>"; };
		00E06E4832B4FB41B2D9633BB589906A /* AWSNetworkingHelpers.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSNetworkingHelpers.m; path = AWSCore/Networking/AWSNetworkingHelpers.m; sourceTree = "<group>"; };
		0110D202E8D83129762D378FDDF143D4 /* AWSS3TransferUtility+HeaderHelper.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "AWSS3TransferUtility+HeaderHelper.m"; path = "AWSS3/AWSS3TransferUtility+HeaderHelper.m"; sourceTree = "<group>"; };
		01B2F9115C1925D20200A33F7A44087B /* IQToolbar.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = IQToolbar.m; path = IQKeyboardManager/IQToolbar/IQToolbar.m; sourceTree = "<group>"; };
		02134B9202DD57A98E9EE85B2EA527AE /* AWSS3Model.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSS3Model.h; path = AWSS3/AWSS3Model.h; sourceTree = "<group>"; };
		02186B0D32F4092812EC9CECB2859997 /* CustomIOSAlertView-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "CustomIOSAlertView-Info.plist"; sourceTree = "<group>"; };
		02DC8FB325DEDC74EA982677F1587231 /* Dictionary+AttributedStringKey.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Dictionary+AttributedStringKey.swift"; path = "Aztec/Classes/Extensions/Dictionary+AttributedStringKey.swift"; sourceTree = "<group>"; };
		038253724BF4626A000CFC2A50C6DE08 /* MASCompositeConstraint.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MASCompositeConstraint.h; path = Masonry/MASCompositeConstraint.h; sourceTree = "<group>"; };
		039BFD41CC88B7E9A28C9AC2A4E3A37A /* NSBundle+AztecBundle.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSBundle+AztecBundle.swift"; path = "Aztec/Classes/Extensions/NSBundle+AztecBundle.swift"; sourceTree = "<group>"; };
		03B4854CBE49FA43EED2306959C2C939 /* TextListFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TextListFormatter.swift; path = Aztec/Classes/Formatters/Implementations/TextListFormatter.swift; sourceTree = "<group>"; };
		03B6F41E23C27BC816E8FE9F8D0269DF /* ConditionalItalicStringAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConditionalItalicStringAttributeConverter.swift; path = Aztec/Classes/Converters/StringAttributesToAttributes/ConditionalConverters/ConditionalItalicStringAttributeConverter.swift; sourceTree = "<group>"; };
		03D44C2546E374AFC6D8F598130AC58D /* HRColorUtil.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = HRColorUtil.m; path = "ZSSRichTextEditor/Source/Third Party/HRColorUtil.m"; sourceTree = "<group>"; };
		04712930695581C893D0FA730F42AF22 /* NSAttributedString+FontTraits.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSAttributedString+FontTraits.swift"; path = "Aztec/Classes/Extensions/NSAttributedString+FontTraits.swift"; sourceTree = "<group>"; };
		04D414B15ACD38A9FD5A3FD390B38AD8 /* Attribute.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Attribute.swift; path = Aztec/Classes/Libxml2/DOM/Data/Attribute.swift; sourceTree = "<group>"; };
		06326BD3E7AAF964B86655D35A00CAAF /* RenderableAttachment.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RenderableAttachment.swift; path = Aztec/Classes/TextKit/RenderableAttachment.swift; sourceTree = "<group>"; };
		063F586E7A8CB9B81ACDB8D916030C1B /* Pods-SnapInspect3 */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-SnapInspect3"; path = Pods_SnapInspect3.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		064EDF41254270D4B8DF6B0160E321A3 /* FABAttributes.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FABAttributes.h; path = AWSCore/Fabric/FABAttributes.h; sourceTree = "<group>"; };
		06851E661D2C17A43BC27D5A4D344DB3 /* MarkFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MarkFormatter.swift; path = Aztec/Classes/Formatters/Implementations/MarkFormatter.swift; sourceTree = "<group>"; };
		0711EA3F47547EAEFCC09F6FDB0EA1CB /* DemoModalViewController.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = DemoModalViewController.h; path = ZSSRichTextEditor/DemoModalViewController.h; sourceTree = "<group>"; };
		073011D63121F15BAB242E48C9DD55F4 /* ParagraphProperty.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParagraphProperty.swift; path = Aztec/Classes/TextKit/ParagraphProperty/ParagraphProperty.swift; sourceTree = "<group>"; };
		07431828B67BD39967B508F00CE4DBAF /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreGraphics.framework; sourceTree = DEVELOPER_DIR; };
		08FAAE69093894648B755A8AB25E808E /* AWSTask.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSTask.m; path = AWSCore/Bolts/AWSTask.m; sourceTree = "<group>"; };
		0937A48B91842E46510D876C7A64338E /* UIStackView+Helpers.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIStackView+Helpers.swift"; path = "Aztec/Classes/Extensions/UIStackView+Helpers.swift"; sourceTree = "<group>"; };
		093DAD6CB8CBFCC6E95A59F369AF655A /* MediaAttachment+WordPress.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "MediaAttachment+WordPress.swift"; path = "WordPressEditor/WordPressEditor/Classes/Extensions/MediaAttachment+WordPress.swift"; sourceTree = "<group>"; };
		097ABECCD50A7F3C7D61D73B87E6226F /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		09D07A5FD2DF28FD7A522BD3C53F2ECB /* MASCompositeConstraint.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MASCompositeConstraint.m; path = Masonry/MASCompositeConstraint.m; sourceTree = "<group>"; };
		09D5992C26C541102E2F4D3CEA3ABFB2 /* GalleryElementToTagConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GalleryElementToTagConverter.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Calypso/GalleryShortcode/GalleryElementToTagConverter.swift; sourceTree = "<group>"; };
		0A242436B71B27FC1E4A5244459D73F5 /* ZSSimage.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSimage.png; path = ZSSRichTextEditor/Source/Images/ZSSimage.png; sourceTree = "<group>"; };
		0A580597DC2DF8CA22F274EBF1A56C78 /* RemovePProcessor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RemovePProcessor.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Calypso/AutopRemovep/RemovePProcessor.swift; sourceTree = "<group>"; };
		0A5FE2974BDB1311E15E603A6754B2CD /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		0AA8C6968875E0578A0D46F4B1FC0A98 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		0AD9939574ACB24611ED895616022021 /* UITextView+Placeholder.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "UITextView+Placeholder.release.xcconfig"; sourceTree = "<group>"; };
		0B5610F6E07B1A7AEA1AE3A2F7969DDC /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		0C8D2038A9D99F02D343E6D2F7779FA5 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		0CA0E7960986B255EB42D0BD04524C2C /* jQuery.js */ = {isa = PBXFileReference; includeInIndex = 1; name = jQuery.js; path = ZSSRichTextEditor/Source/jQuery.js; sourceTree = "<group>"; };
		0D377072F910FDB79D7AE76CE72E0DEE /* HTMLSerializer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLSerializer.swift; path = Aztec/Classes/Libxml2/Converters/Out/HTMLSerializer.swift; sourceTree = "<group>"; };
		0D3DA541B91685DAC2E73BCF60E667CD /* VideoAttachment+WordPress.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "VideoAttachment+WordPress.swift"; path = "WordPressEditor/WordPressEditor/Classes/Extensions/VideoAttachment+WordPress.swift"; sourceTree = "<group>"; };
		0D83C5CC3DA7D07A83C6E0DEFF2103F3 /* DemoModalViewController.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = DemoModalViewController.m; path = ZSSRichTextEditor/DemoModalViewController.m; sourceTree = "<group>"; };
		0E1141B7368A68052DC4226DCD2F35A6 /* AWSService.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSService.h; path = AWSCore/Service/AWSService.h; sourceTree = "<group>"; };
		0E4DF85BAF486D03F07290BA1EAAE4F3 /* AWSSimpleDBService.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSSimpleDBService.m; path = AWSSimpleDB/AWSSimpleDBService.m; sourceTree = "<group>"; };
		0EFC9290CDB3BCDABF661E53BCC38C49 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		0F2BD190F1AF129F491C83B157B82BF6 /* IQPreviousNextView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = IQPreviousNextView.m; path = IQKeyboardManager/IQToolbar/IQPreviousNextView.m; sourceTree = "<group>"; };
		0F30CB740A9D0D16D82A2970D27FD906 /* Pods-SnapInspect3Tests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-SnapInspect3Tests-acknowledgements.plist"; sourceTree = "<group>"; };
		0F7010BE81BBA3DFE1A7DD01500D17D7 /* UnsupportedHTML.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = UnsupportedHTML.swift; path = Aztec/Classes/NSAttributedString/Attributes/UnsupportedHTML.swift; sourceTree = "<group>"; };
		0FB4F3CB2AFC04DB3FD5AEC3485CC221 /* WordPress-Aztec-iOS-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "WordPress-Aztec-iOS-Info.plist"; sourceTree = "<group>"; };
		105D8A2CE972CC478BA50EBE9A4D6999 /* ZSSsuperscript.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSsuperscript.png; path = ZSSRichTextEditor/Source/Images/ZSSsuperscript.png; sourceTree = "<group>"; };
		1201C4BCEBE15F5A9A9A6B9C31AC2F53 /* AWSURLRequestRetryHandler.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSURLRequestRetryHandler.h; path = AWSCore/Serialization/AWSURLRequestRetryHandler.h; sourceTree = "<group>"; };
		1205509CA36681694DC1CE169F5A56E8 /* WordPress-Aztec-iOS-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "WordPress-Aztec-iOS-prefix.pch"; sourceTree = "<group>"; };
		120A7EB69C81F1A7EF1F516A8EDBD0EB /* HTMLStorage.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLStorage.swift; path = Aztec/Classes/TextKit/HTMLStorage.swift; sourceTree = "<group>"; };
		122A1FDB2A5EA16088E24D982A4137D7 /* StringAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = StringAttributeConverter.swift; path = Aztec/Classes/Converters/StringAttributesToAttributes/Base/StringAttributeConverter.swift; sourceTree = "<group>"; };
		125EA11C82FA2EC02252CFE3A1472000 /* ItalicStringAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ItalicStringAttributeConverter.swift; path = Aztec/Classes/Converters/StringAttributesToAttributes/Implementations/ItalicStringAttributeConverter.swift; sourceTree = "<group>"; };
		1275F9CFE4974B39A872C32F8D8BDD8C /* ElementAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ElementAttributeConverter.swift; path = Aztec/Classes/Converters/AttributesToStringAttributes/Base/ElementAttributeConverter.swift; sourceTree = "<group>"; };
		12769C1EC854AE0D071FBA8D0F088AC5 /* SubscriptFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SubscriptFormatter.swift; path = Aztec/Classes/Formatters/Implementations/SubscriptFormatter.swift; sourceTree = "<group>"; };
		1281A45D13B60DFC43269917F71F7BF5 /* AWSMTLModel+NSCoding.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "AWSMTLModel+NSCoding.m"; path = "AWSCore/Mantle/AWSMTLModel+NSCoding.m"; sourceTree = "<group>"; };
		1298B59F12519D51BC957A3A0386CC99 /* PreFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PreFormatter.swift; path = Aztec/Classes/Formatters/Implementations/PreFormatter.swift; sourceTree = "<group>"; };
		12BC206C8845BC6D96492ABA143BF767 /* BoldCSSAttributeMatcher.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BoldCSSAttributeMatcher.swift; path = Aztec/Classes/Libxml2/DOM/Logic/CSS/BoldCSSAttributeMatcher.swift; sourceTree = "<group>"; };
		13966B8E431CFA857A9DCDB2ACBCC530 /* AWSSimpleDB.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = AWSSimpleDB.modulemap; sourceTree = "<group>"; };
		139FA8BD67D5FBBEF0E0632A2BCF2CDD /* AWSS3TransferUtilityTasks.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSS3TransferUtilityTasks.h; path = AWSS3/AWSS3TransferUtilityTasks.h; sourceTree = "<group>"; };
		13B1EDA7EC9AE069B4BB6D3A22118DCC /* ShortcodeAttributeParser.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ShortcodeAttributeParser.swift; path = Aztec/Classes/Processor/ShortcodeAttributeParser.swift; sourceTree = "<group>"; };
		144D7D8856C187E674643B1968669749 /* IQTitleBarButtonItem.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = IQTitleBarButtonItem.h; path = IQKeyboardManager/IQToolbar/IQTitleBarButtonItem.h; sourceTree = "<group>"; };
		146F47E00215443494348808DD1AC419 /* AWSCategory.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSCategory.h; path = AWSCore/Utility/AWSCategory.h; sourceTree = "<group>"; };
		150FCA1A936738D5AFEC87092DE8AFF4 /* Array+ShortcodeAttribute.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Array+ShortcodeAttribute.swift"; path = "Aztec/Classes/Extensions/Array+ShortcodeAttribute.swift"; sourceTree = "<group>"; };
		151F4074C4E3C4C4B13BD19991E61B04 /* SuperscriptFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SuperscriptFormatter.swift; path = Aztec/Classes/Formatters/Implementations/SuperscriptFormatter.swift; sourceTree = "<group>"; };
		15C8C9E0261563ABE5CF764B84881735 /* ZSSstrikethrough.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSstrikethrough.png; path = ZSSRichTextEditor/Source/Images/ZSSstrikethrough.png; sourceTree = "<group>"; };
		******************************** /* CustomIOSAlertView-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "CustomIOSAlertView-umbrella.h"; sourceTree = "<group>"; };
		166C2FDE683B09A9E70C7CBDB25649B6 /* GutenpackAttachmentToElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GutenpackAttachmentToElementConverter.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Gutenberg/GutenpackAttachmentToElementConverter.swift; sourceTree = "<group>"; };
		16E9830A962849892F6E7F367F0F579D /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		******************************** /* HRCgUtil.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = HRCgUtil.h; path = "ZSSRichTextEditor/Source/Third Party/HRCgUtil.h"; sourceTree = "<group>"; };
		17B3917ED068AB56D3810991CF31059A /* IQKeyboardManager-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardManager-umbrella.h"; sourceTree = "<group>"; };
		17E1A3BC2775421D2C200F672365778D /* AWSTMCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSTMCache.h; path = AWSCore/TMCache/AWSTMCache.h; sourceTree = "<group>"; };
		181214419C4F510ED3AE893D1A4145A1 /* ZSSh2.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSh2.png; path = ZSSRichTextEditor/Source/Images/ZSSh2.png; sourceTree = "<group>"; };
		18629905972CCFFCFA7F9DA8F8EC371F /* UITextView+Placeholder-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "UITextView+Placeholder-umbrella.h"; sourceTree = "<group>"; };
		18C050F7D65590348644F2EFCF1BBE20 /* AWSKSReachability.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSKSReachability.m; path = AWSCore/KSReachability/AWSKSReachability.m; sourceTree = "<group>"; };
		1910B7E730B49715E0CC9D517563030F /* Fabric+FABKits.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "Fabric+FABKits.h"; path = "AWSCore/Fabric/Fabric+FABKits.h"; sourceTree = "<group>"; };
		192CB99A1A3CABA30AA090348A75CC24 /* AWSFMDatabaseQueue.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSFMDatabaseQueue.h; path = AWSCore/FMDB/AWSFMDatabaseQueue.h; sourceTree = "<group>"; };
		195D12B4CE31F09A47E018BD02E15C5F /* AWSSerialization.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSSerialization.m; path = AWSCore/Serialization/AWSSerialization.m; sourceTree = "<group>"; };
		1A8D3BDE8DB11B9DE4D293810156F319 /* AWSCognitoIdentityModel.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSCognitoIdentityModel.m; path = AWSCore/CognitoIdentity/AWSCognitoIdentityModel.m; sourceTree = "<group>"; };
		1AD819682E18D1A33EC662C4228B03A8 /* PipelineProcessor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PipelineProcessor.swift; path = Aztec/Classes/Processor/PipelineProcessor.swift; sourceTree = "<group>"; };
		1AE09A7EF3D9A2E1A93E931D04D34824 /* WordPress-Editor-iOS-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "WordPress-Editor-iOS-prefix.pch"; sourceTree = "<group>"; };
		1B397F1E81751913B882870C417D2559 /* ColorProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ColorProvider.swift; path = Aztec/Classes/TextKit/ColorProvider.swift; sourceTree = "<group>"; };
		1B8F6DF217C03167DEA1195A385C6E4D /* AWSS3TransferUtilityTasks.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSS3TransferUtilityTasks.m; path = AWSS3/AWSS3TransferUtilityTasks.m; sourceTree = "<group>"; };
		1BA06FC2FBB82A646E0B8E92DBC41C2F /* AWSCore.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSCore.h; path = AWSCore/AWSCore.h; sourceTree = "<group>"; };
		1BA9A17680202558687A37AA159DCB2D /* IQUIView+IQKeyboardToolbar.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "IQUIView+IQKeyboardToolbar.h"; path = "IQKeyboardManager/IQToolbar/IQUIView+IQKeyboardToolbar.h"; sourceTree = "<group>"; };
		1C10EA016C6C7A70C2A4D952793CEABB /* WordPress-Aztec-iOS-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "WordPress-Aztec-iOS-umbrella.h"; sourceTree = "<group>"; };
		1C3C646B55D8159F3245C63650B4CC41 /* Gutenblock.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Gutenblock.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Gutenberg/Gutenblock.swift; sourceTree = "<group>"; };
		1CA742AAA15F496FEC813F2CC9407159 /* NSArray+MASAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSArray+MASAdditions.m"; path = "Masonry/NSArray+MASAdditions.m"; sourceTree = "<group>"; };
		1D216CE4A8B6F50D603960D17EE072C6 /* AWSS3 */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = AWSS3; path = AWSS3.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1DD9D813F4256C2D550E728DF0B29480 /* CYRTextView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CYRTextView.h; path = "ZSSRichTextEditor/Source/Third Party/CYRTextView.h"; sourceTree = "<group>"; };
		1E448A42284A180CEFC211B3EDCC37A1 /* ZSSLargeViewController.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ZSSLargeViewController.m; path = ZSSRichTextEditor/Classes/ZSSLargeViewController.m; sourceTree = "<group>"; };
		1E704FCBEDE3AFDF36B4835141628046 /* AWSMTLReflection.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSMTLReflection.m; path = AWSCore/Mantle/AWSMTLReflection.m; sourceTree = "<group>"; };
		1E73B3D82EF8948B09B08E7989CCA8E6 /* ZSSlink.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSlink.png; path = ZSSRichTextEditor/Source/Images/ZSSlink.png; sourceTree = "<group>"; };
		1E74A73E3762665118306365F95453FC /* IQKeyboardManager.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardManager.debug.xcconfig; sourceTree = "<group>"; };
		1EA243511A78898E9242B57B682A48AC /* Character+Name.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Character+Name.swift"; path = "Aztec/Classes/Extensions/Character+Name.swift"; sourceTree = "<group>"; };
		******************************** /* ZSSRichTextEditor.js */ = {isa = PBXFileReference; includeInIndex = 1; name = ZSSRichTextEditor.js; path = ZSSRichTextEditor/Source/ZSSRichTextEditor.js; sourceTree = "<group>"; };
		1EAB1389E7B7667B9767F2E6F81B20E6 /* AWSDDLog.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSDDLog.h; path = AWSCore/Logging/AWSDDLog.h; sourceTree = "<group>"; };
		1EF07C441AD75BE9D582C89492B7F35E /* AWSMTLReflection.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSMTLReflection.h; path = AWSCore/Mantle/AWSMTLReflection.h; sourceTree = "<group>"; };
		1F273EC6E6AC1D4C91422AE42A0A40D2 /* ZSSinsertkeyword.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSinsertkeyword.png; path = ZSSRichTextEditor/Source/Images/ZSSinsertkeyword.png; sourceTree = "<group>"; };
		1FD9C5127E16DDB5B0698CC793BF066B /* IQPreviousNextView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = IQPreviousNextView.h; path = IQKeyboardManager/IQToolbar/IQPreviousNextView.h; sourceTree = "<group>"; };
		1FFED36A657123030ABB700256D73F15 /* Masonry */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = Masonry; path = Masonry.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		20AF0B625EEB1A41F012EAAB78307DB2 /* AWSURLResponseSerialization.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSURLResponseSerialization.m; path = AWSCore/Serialization/AWSURLResponseSerialization.m; sourceTree = "<group>"; };
		20DD5DD231841EB0CD054E9FD7A7BE96 /* View+MASShorthandAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "View+MASShorthandAdditions.h"; path = "Masonry/View+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		211A0E041B95372827B5C3CD90B67C98 /* AWSBolts.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSBolts.m; path = AWSCore/Bolts/AWSBolts.m; sourceTree = "<group>"; };
		21210BFF7AB19CAD62B5789683A3EF46 /* Masonry-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Masonry-Info.plist"; sourceTree = "<group>"; };
		215CF00CC0D2E2F90D8FC85852A8CEF1 /* String+CharacterName.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "String+CharacterName.swift"; path = "Aztec/Classes/Extensions/String+CharacterName.swift"; sourceTree = "<group>"; };
		21C04AD0E8C825BF988BCACBEE9C1B22 /* ZSSunderline.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSunderline.png; path = ZSSRichTextEditor/Source/Images/ZSSunderline.png; sourceTree = "<group>"; };
		225A16318E4C53492B121D4E9E32EC64 /* TextNode.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TextNode.swift; path = Aztec/Classes/Libxml2/DOM/Data/TextNode.swift; sourceTree = "<group>"; };
		22887F82031644416E84A7204E5F160F /* AWSMTLModel+NSCoding.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "AWSMTLModel+NSCoding.h"; path = "AWSCore/Mantle/AWSMTLModel+NSCoding.h"; sourceTree = "<group>"; };
		22BCE032BBD33162D3E9275C9C97F41D /* ZSSFontsViewController.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ZSSFontsViewController.m; path = ZSSRichTextEditor/Source/ZSSFontsViewController.m; sourceTree = "<group>"; };
		23CED6C054FF7BDBF12E90D288D9855A /* ZSSTextView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ZSSTextView.m; path = ZSSRichTextEditor/Source/ZSSTextView.m; sourceTree = "<group>"; };
		2484BDB79E84DB4E30C811523EA5AC75 /* ZSSh6.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSh6.png; path = ZSSRichTextEditor/Source/Images/ZSSh6.png; sourceTree = "<group>"; };
		24CD46B7BC4C45C484CC6D328B4A9063 /* IQKeyboardManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = IQKeyboardManager.h; path = IQKeyboardManager/IQKeyboardManager.h; sourceTree = "<group>"; };
		251BF72F713401D3B45B731343FF621F /* Processor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Processor.swift; path = Aztec/Classes/Processor/Processor.swift; sourceTree = "<group>"; };
		2719DFEA52EB04A9F9238C185EB5ED5F /* IQKeyboardManager-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "IQKeyboardManager-Info.plist"; sourceTree = "<group>"; };
		274699B1D2C22F0EB55A6F7BD9C6FA21 /* Blockquote.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Blockquote.swift; path = Aztec/Classes/TextKit/ParagraphProperty/Blockquote.swift; sourceTree = "<group>"; };
		27BC91EDEA8EF9984D9F9A1E5B2FF42C /* MASLayoutConstraint.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MASLayoutConstraint.m; path = Masonry/MASLayoutConstraint.m; sourceTree = "<group>"; };
		28C9E10CDA6BF017DD893E3A2B5DCF66 /* UILayoutPriority+Swift4.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UILayoutPriority+Swift4.swift"; path = "Aztec/Classes/Extensions/UILayoutPriority+Swift4.swift"; sourceTree = "<group>"; };
		28E59BCC5C47ACAA00937C7DCB09380E /* ZSSLargeViewController.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ZSSLargeViewController.h; path = ZSSRichTextEditor/Classes/ZSSLargeViewController.h; sourceTree = "<group>"; };
		28F526E648A3518F8638E381CD064A90 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Security.framework; sourceTree = DEVELOPER_DIR; };
		290B681D6D19B19A2AB302958798CEFE /* AWSS3Service.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSS3Service.h; path = AWSS3/AWSS3Service.h; sourceTree = "<group>"; };
		297C04CB8ED04EB643395E8C060F0666 /* AWSExecutor.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSExecutor.m; path = AWSCore/Bolts/AWSExecutor.m; sourceTree = "<group>"; };
		29CE6563281AD32FC9E4DCFAB9976742 /* IQUITextFieldView+Additions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "IQUITextFieldView+Additions.m"; path = "IQKeyboardManager/Categories/IQUITextFieldView+Additions.m"; sourceTree = "<group>"; };
		29E6ACF651319B9A26A854D6DA3CBB23 /* MASViewConstraint.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MASViewConstraint.h; path = Masonry/MASViewConstraint.h; sourceTree = "<group>"; };
		2B0A29AA8A0CC441B625B7CD0347D9D0 /* FigureFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FigureFormatter.swift; path = Aztec/Classes/Formatters/Implementations/FigureFormatter.swift; sourceTree = "<group>"; };
		2B4321EC5D63E49AC4FDC1BE57FF1D3F /* ColorFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ColorFormatter.swift; path = Aztec/Classes/Formatters/Implementations/ColorFormatter.swift; sourceTree = "<group>"; };
		2B661495505FAA6317D0B5324CD7AD7B /* ZSSfonts.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSfonts.png; path = ZSSRichTextEditor/Source/Images/ZSSfonts.png; sourceTree = "<group>"; };
		2C01276638E91D57CCD0895AC56A75C0 /* FormatBar.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FormatBar.swift; path = Aztec/Classes/GUI/FormatBar/FormatBar.swift; sourceTree = "<group>"; };
		2C827CBA650FA0C93DDF2ECD2E3CB064 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		2CE77014456596A4717032B476C7E555 /* CommentAttachmentRenderer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CommentAttachmentRenderer.swift; path = Aztec/Classes/Renderers/CommentAttachmentRenderer.swift; sourceTree = "<group>"; };
		2DA7C0F3D813B48B010E47DAB01C295D /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		2DDF2C4F33FD6482C1AC71B6D294822B /* AWSDDDispatchQueueLogFormatter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSDDDispatchQueueLogFormatter.m; path = AWSCore/Logging/Extensions/AWSDDDispatchQueueLogFormatter.m; sourceTree = "<group>"; };
		2EEDE148BA200B1BF0D05E7008289C8A /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		2F6A5EDE45053CCB5AB388186F1D7645 /* AWSTMCacheBackgroundTaskManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSTMCacheBackgroundTaskManager.h; path = AWSCore/TMCache/AWSTMCacheBackgroundTaskManager.h; sourceTree = "<group>"; };
		30B0C0D2B2CAD08CAE85660EA39786E8 /* FigcaptionFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FigcaptionFormatter.swift; path = Aztec/Classes/Formatters/Implementations/FigcaptionFormatter.swift; sourceTree = "<group>"; };
		313D9F017893EC5D1D39DC041EA291E1 /* AWSCancellationTokenSource.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSCancellationTokenSource.h; path = AWSCore/Bolts/AWSCancellationTokenSource.h; sourceTree = "<group>"; };
		32A480ECEAD05019B93201344BA19161 /* AWSDDFileLogger.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSDDFileLogger.h; path = AWSCore/Logging/AWSDDFileLogger.h; sourceTree = "<group>"; };
		32F27D73078787B366DFFB831C91CCE4 /* ImageElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ImageElementConverter.swift; path = Aztec/Classes/Converters/ElementsToAttributedString/Implementations/ImageElementConverter.swift; sourceTree = "<group>"; };
		337B4ACD9A49A40116934009DFFB7FFD /* WordPress-Aztec-iOS */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "WordPress-Aztec-iOS"; path = Aztec.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		349DEC503349A25288631757FD8339AD /* ZSSTextView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ZSSTextView.h; path = ZSSRichTextEditor/Source/ZSSTextView.h; sourceTree = "<group>"; };
		34CF0B5321B52651C350F3ABA3093B72 /* SubscriptStringAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SubscriptStringAttributeConverter.swift; path = Aztec/Classes/Converters/StringAttributesToAttributes/Implementations/SubscriptStringAttributeConverter.swift; sourceTree = "<group>"; };
		34ECFF26C3283CF927CEC068076A8230 /* HRColorPickerMacros.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = HRColorPickerMacros.h; path = "ZSSRichTextEditor/Source/Third Party/HRColorPickerMacros.h"; sourceTree = "<group>"; };
		356905AB70772357AA910F0C5CE07A98 /* PluginOutputCustomizer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PluginOutputCustomizer.swift; path = Aztec/Classes/Plugin/PluginOutputCustomizer.swift; sourceTree = "<group>"; };
		357EDED1CD149FA5D8556F07EA9F61CE /* HRBrightnessCursor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = HRBrightnessCursor.h; path = "ZSSRichTextEditor/Source/Third Party/HRBrightnessCursor.h"; sourceTree = "<group>"; };
		35B87544670F4E671FA17F93E1E53671 /* UnderlineCSSAttributeMatcher.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = UnderlineCSSAttributeMatcher.swift; path = Aztec/Classes/Libxml2/DOM/Logic/CSS/UnderlineCSSAttributeMatcher.swift; sourceTree = "<group>"; };
		363FEAFF429B76667F1B0502D1873343 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		365B2B752EA07985E0214D7560CD16BA /* AWSDDAbstractDatabaseLogger.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSDDAbstractDatabaseLogger.h; path = AWSCore/Logging/AWSDDAbstractDatabaseLogger.h; sourceTree = "<group>"; };
		3661F17A73E95C0DE5265638C5025A94 /* WordPressOutputCustomizer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = WordPressOutputCustomizer.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/WordPressOutputCustomizer.swift; sourceTree = "<group>"; };
		36DCD2F5E26EAE3DA08826FA45FE9ECF /* ForegroundColorCSSAttributeMatcher.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ForegroundColorCSSAttributeMatcher.swift; path = Aztec/Classes/Libxml2/DOM/Logic/CSS/ForegroundColorCSSAttributeMatcher.swift; sourceTree = "<group>"; };
		36E9F5AB776534B247915CE96EFD9212 /* AWSFMDB+AWSHelpers.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "AWSFMDB+AWSHelpers.h"; path = "AWSCore/FMDB/AWSFMDB+AWSHelpers.h"; sourceTree = "<group>"; };
		37081F17CBAE1F2535B46A6B59DB619B /* AWSCognitoIdentity+Fabric.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "AWSCognitoIdentity+Fabric.h"; path = "AWSCore/CognitoIdentity/AWSCognitoIdentity+Fabric.h"; sourceTree = "<group>"; };
		37EB391AF9C0460BD4DF10F4B6251888 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		3846ADF2FD96D5AB73C6E12A70C9C35A /* IQKeyboardManager-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardManager-prefix.pch"; sourceTree = "<group>"; };
		38E79041FE8351E3C2E42817B2AD2F19 /* HeaderFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HeaderFormatter.swift; path = Aztec/Classes/Formatters/Implementations/HeaderFormatter.swift; sourceTree = "<group>"; };
		38E87BDB93453DDF0BD1522C6E2842AB /* PluginInputCustomizer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PluginInputCustomizer.swift; path = Aztec/Classes/Plugin/PluginInputCustomizer.swift; sourceTree = "<group>"; };
		38F5F0F67785D719E9F71D6A7A00DB94 /* HRBrightnessCursor.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = HRBrightnessCursor.m; path = "ZSSRichTextEditor/Source/Third Party/HRBrightnessCursor.m"; sourceTree = "<group>"; };
		3926C62A6CA718012FE043321379A91A /* AWSUICKeyChainStore.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSUICKeyChainStore.h; path = AWSCore/UICKeyChainStore/AWSUICKeyChainStore.h; sourceTree = "<group>"; };
		393B080CD824A0BD742E121DC2D659EE /* NSAttributedString+Lists.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSAttributedString+Lists.swift"; path = "Aztec/Classes/Extensions/NSAttributedString+Lists.swift"; sourceTree = "<group>"; };
		39BB99D3270473231795762345668826 /* Array+Attribute.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Array+Attribute.swift"; path = "Aztec/Classes/Extensions/Array+Attribute.swift"; sourceTree = "<group>"; };
		3B402823BA5F31C7192627F11AB9939B /* ZSSh4.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSh4.png; path = ZSSRichTextEditor/Source/Images/ZSSh4.png; sourceTree = "<group>"; };
		3B63EC20DCE9FF02443F11E6996DB297 /* StrikethroughFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = StrikethroughFormatter.swift; path = Aztec/Classes/Formatters/Implementations/StrikethroughFormatter.swift; sourceTree = "<group>"; };
		******************************** /* ShortcodeAttributeSerializer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ShortcodeAttributeSerializer.swift; path = Aztec/Classes/Processor/ShortcodeAttributeSerializer.swift; sourceTree = "<group>"; };
		3C80B7E9AF74FA513E183FBE290E28F7 /* CustomIOSAlertView-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "CustomIOSAlertView-dummy.m"; sourceTree = "<group>"; };
		3D3B05789398B7B6198AD5365927E91D /* AWSIdentityProvider.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSIdentityProvider.m; path = AWSCore/Authentication/AWSIdentityProvider.m; sourceTree = "<group>"; };
		3DA3759212250A359947AC1E937476A7 /* AWSCore.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AWSCore.release.xcconfig; sourceTree = "<group>"; };
		3E244762E12B8DF439FC4306B59E9232 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		3E8BA41A73F8121A015507BD2EDC7846 /* AWSCredentialsProvider.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSCredentialsProvider.m; path = AWSCore/Authentication/AWSCredentialsProvider.m; sourceTree = "<group>"; };
		3E979FC80F584432AE1119072D70F554 /* ZSSorderedlist.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSorderedlist.png; path = ZSSRichTextEditor/Source/Images/ZSSorderedlist.png; sourceTree = "<group>"; };
		3EF8EAE788C771937E1415A893339F46 /* AWSServiceEnum.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSServiceEnum.h; path = AWSCore/Service/AWSServiceEnum.h; sourceTree = "<group>"; };
		3F617F83325E31A07FD8EB179E208CC8 /* ZSSoutdent.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSoutdent.png; path = ZSSRichTextEditor/Source/Images/ZSSoutdent.png; sourceTree = "<group>"; };
		3F68214465A80C238205DC7D43435219 /* TextStorage.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TextStorage.swift; path = Aztec/Classes/TextKit/TextStorage.swift; sourceTree = "<group>"; };
		3FF3E271007CF79F3F0557EFA40F26F0 /* CustomIOSAlertView-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "CustomIOSAlertView-prefix.pch"; sourceTree = "<group>"; };
		4017BF898984538205277618E23E83EB /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		40277157CEE8D04B755EA40AE3B5B5F2 /* AWSFMDatabaseAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSFMDatabaseAdditions.h; path = AWSCore/FMDB/AWSFMDatabaseAdditions.h; sourceTree = "<group>"; };
		405F1C37FB06DB35C92A64437575FF27 /* AWSFMDatabaseQueue.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSFMDatabaseQueue.m; path = AWSCore/FMDB/AWSFMDatabaseQueue.m; sourceTree = "<group>"; };
		40E30DA9C3BD6250D6D306AEC513E69F /* MASConstraintMaker.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MASConstraintMaker.m; path = Masonry/MASConstraintMaker.m; sourceTree = "<group>"; };
		40FE320447E269AEC1B033900A2F066D /* AWSSTSResources.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSSTSResources.h; path = AWSCore/STS/AWSSTSResources.h; sourceTree = "<group>"; };
		411D9C55F97896E1AFD3DCF13A8E0E53 /* AWSURLSessionManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSURLSessionManager.m; path = AWSCore/Networking/AWSURLSessionManager.m; sourceTree = "<group>"; };
		41333B3E10C40F0F6C451FFB07D501B9 /* HRColorPickerView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = HRColorPickerView.m; path = "ZSSRichTextEditor/Source/Third Party/HRColorPickerView.m"; sourceTree = "<group>"; };
		413BE7E313C4F81889A39015E63FEF62 /* AWSInfo.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSInfo.m; path = AWSCore/Service/AWSInfo.m; sourceTree = "<group>"; };
		423A40F8AF0C61C887E96EEE9820403F /* GallerySupportedAttribute.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GallerySupportedAttribute.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Calypso/GalleryShortcode/GallerySupportedAttribute.swift; sourceTree = "<group>"; };
		423D933DAFF2D5FEF249E738E07D5ADD /* IQKeyboardManager.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = IQKeyboardManager.modulemap; sourceTree = "<group>"; };
		4271FCF653EE523E6572B0CF14D0692B /* StandardAttributeFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = StandardAttributeFormatter.swift; path = Aztec/Classes/Formatters/Base/StandardAttributeFormatter.swift; sourceTree = "<group>"; };
		42BE4BB4FA24D3AB56CF7DFD6A2A1E50 /* GutenbergAttributeDecoder.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GutenbergAttributeDecoder.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Gutenberg/GutenbergAttributeDecoder.swift; sourceTree = "<group>"; };
		42C84C6628B8E4B28D6E3DFE65D31341 /* IQTitleBarButtonItem.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = IQTitleBarButtonItem.m; path = IQKeyboardManager/IQToolbar/IQTitleBarButtonItem.m; sourceTree = "<group>"; };
		42E7FFC595DDA2DA6367E0BD1322713B /* Media.xcassets */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = folder.assetcatalog; name = Media.xcassets; path = Aztec/Assets/Media.xcassets; sourceTree = "<group>"; };
		4382F7D17E5FE318299BDB4BD61E8AC7 /* Pods-SnapInspect3Tests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-SnapInspect3Tests-Info.plist"; sourceTree = "<group>"; };
		44779C7C477FE3FAFE32447C23B1FF27 /* AWSS3Serializer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSS3Serializer.m; path = AWSS3/AWSS3Serializer.m; sourceTree = "<group>"; };
		44CE3454C1307BA6FD69D3EF29B127D4 /* MASConstraintMaker.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MASConstraintMaker.h; path = Masonry/MASConstraintMaker.h; sourceTree = "<group>"; };
		44D25AFA0D910C603722D8A644685A3A /* String+HTML.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "String+HTML.swift"; path = "Aztec/Classes/Extensions/String+HTML.swift"; sourceTree = "<group>"; };
		45555B9376A64C0156962C778ED629DC /* AWSCognitoIdentityResources.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSCognitoIdentityResources.h; path = AWSCore/CognitoIdentity/AWSCognitoIdentityResources.h; sourceTree = "<group>"; };
		455ADBDA6E35123297CA2CA977F1AC7C /* RegexProcessor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RegexProcessor.swift; path = Aztec/Classes/Processor/RegexProcessor.swift; sourceTree = "<group>"; };
		457CCD794442C7C27C23936EA4CB0B17 /* IQUIScrollView+Additions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "IQUIScrollView+Additions.h"; path = "IQKeyboardManager/Categories/IQUIScrollView+Additions.h"; sourceTree = "<group>"; };
		46D31E2B8E7446A5E4DDB826A5D70EDF /* AWSCognitoIdentity.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSCognitoIdentity.h; path = AWSCore/CognitoIdentity/AWSCognitoIdentity.h; sourceTree = "<group>"; };
		47B0721500312E98CDDB9B79CCE98012 /* AWSEXTScope.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSEXTScope.h; path = AWSCore/Mantle/extobjc/AWSEXTScope.h; sourceTree = "<group>"; };
		489C7F9CA1EBAD893492F836A60B3984 /* CustomIOSAlertView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CustomIOSAlertView.h; path = CustomIOSAlertView/CustomIOSAlertView/View/CustomIOSAlertView.h; sourceTree = "<group>"; };
		4968981B497BB3A8BABDD479977EC3EA /* AWSClientContext.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSClientContext.m; path = AWSCore/Service/AWSClientContext.m; sourceTree = "<group>"; };
		49D71110221D757C0CB53D3F55D291F3 /* AWSURLRequestSerialization.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSURLRequestSerialization.h; path = AWSCore/Serialization/AWSURLRequestSerialization.h; sourceTree = "<group>"; };
		49EC90BD7B2EF167EEF9CAC88CDC6F67 /* IQNSArray+Sort.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "IQNSArray+Sort.m"; path = "IQKeyboardManager/Categories/IQNSArray+Sort.m"; sourceTree = "<group>"; };
		49EDD161022F1226ABBF8FA40729A345 /* ElementNode.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ElementNode.swift; path = Aztec/Classes/Libxml2/DOM/Data/ElementNode.swift; sourceTree = "<group>"; };
		4B0054E487BCEFC478BB837BB2D27E7A /* AWSSimpleDB-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AWSSimpleDB-umbrella.h"; sourceTree = "<group>"; };
		4B518F9BCE8A9F7586E3D8C692D21E3C /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		4B9B211F59242354487828265CB3D189 /* AWSSTSModel.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSSTSModel.h; path = AWSCore/STS/AWSSTSModel.h; sourceTree = "<group>"; };
		4BAB18119B32D5084251313A4A4A0E64 /* AWSS3TransferUtility.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSS3TransferUtility.m; path = AWSS3/AWSS3TransferUtility.m; sourceTree = "<group>"; };
		4C35D6B72AF803FF46236DEBD9538A40 /* IQUIView+IQKeyboardToolbar.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "IQUIView+IQKeyboardToolbar.m"; path = "IQKeyboardManager/IQToolbar/IQUIView+IQKeyboardToolbar.m"; sourceTree = "<group>"; };
		4D3E0AA31BFC20022F1FD3C1A73CD590 /* OptionsTablePresenter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = OptionsTablePresenter.swift; path = WordPressEditor/WordPressEditor/Classes/ViewControllers/OptionsTableViewController/OptionsTablePresenter.swift; sourceTree = "<group>"; };
		4D41FB80E50F514C1979F3200F232C77 /* WordPressInputCustomizer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = WordPressInputCustomizer.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/WordPressInputCustomizer.swift; sourceTree = "<group>"; };
		4EDC65361B2AB402DED6311C02F43B49 /* CYRTextStorage.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CYRTextStorage.m; path = "ZSSRichTextEditor/Source/Third Party/CYRTextStorage.m"; sourceTree = "<group>"; };
		4F05A80F68B8D260112B398C93C53D8B /* AWSDDMultiFormatter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSDDMultiFormatter.m; path = AWSCore/Logging/AWSDDMultiFormatter.m; sourceTree = "<group>"; };
		4F9194FC2F8BB5A93E35DA589D6DFA3A /* AWSMTLValueTransformer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSMTLValueTransformer.m; path = AWSCore/Mantle/AWSMTLValueTransformer.m; sourceTree = "<group>"; };
		4FF37E11D6B53A3D4838C1F163CF57BF /* Masonry-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Masonry-dummy.m"; sourceTree = "<group>"; };
		50584B4D5004395480A752508693C842 /* NSArray+MASShorthandAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSArray+MASShorthandAdditions.h"; path = "Masonry/NSArray+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		5059651588EA83D193D76041BA6F064A /* AWSURLSessionManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSURLSessionManager.h; path = AWSCore/Networking/AWSURLSessionManager.h; sourceTree = "<group>"; };
		50A25FDB1EF5F62CC4CFF4831358D64A /* AWSDDLogMacros.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSDDLogMacros.h; path = AWSCore/Logging/AWSDDLogMacros.h; sourceTree = "<group>"; };
		50E458AA0BD910EB50FB14EBBA97707F /* AWSSimpleDB-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "AWSSimpleDB-Info.plist"; sourceTree = "<group>"; };
		50EC52C80BB9351BD7FD3F77C1012B45 /* AWSDDLog.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSDDLog.m; path = AWSCore/Logging/AWSDDLog.m; sourceTree = "<group>"; };
		515991B90E307156A5BC561574050B72 /* AWSSimpleDBService.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSSimpleDBService.h; path = AWSSimpleDB/AWSSimpleDBService.h; sourceTree = "<group>"; };
		51C38653C21585E26F09E0FBFEB57DCB /* ZSSColorViewController.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ZSSColorViewController.m; path = ZSSRichTextEditor/Classes/ZSSColorViewController.m; sourceTree = "<group>"; };
		51CD230427EA642242F58513F7356C59 /* CiteElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CiteElementConverter.swift; path = Aztec/Classes/Converters/ElementsToAttributedString/Implementations/CiteElementConverter.swift; sourceTree = "<group>"; };
		52075A5E43EA9D657F0C3D72D9E3723C /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = IQKeyboardManager/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		521B7C312C31BA26032799373DFB8DB2 /* AWSSTSModel.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSSTSModel.m; path = AWSCore/STS/AWSSTSModel.m; sourceTree = "<group>"; };
		526CE59B9D59C6CD5AECCAF55C427E9C /* AWSSimpleDB-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AWSSimpleDB-prefix.pch"; sourceTree = "<group>"; };
		527DF69633C91FA79917BC7E6431D4AE /* AWSSimpleDBModel.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSSimpleDBModel.m; path = AWSSimpleDB/AWSSimpleDBModel.m; sourceTree = "<group>"; };
		5298F51E389DB6ECECC55D425173061F /* AWSS3Service.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSS3Service.m; path = AWSS3/AWSS3Service.m; sourceTree = "<group>"; };
		530A85936D44B96232C8AE5E4FA9A7CF /* AWSS3Resources.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSS3Resources.h; path = AWSS3/AWSS3Resources.h; sourceTree = "<group>"; };
		5398C69045FD4FE203EDAF9757769D0D /* AWSDDLegacyMacros.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSDDLegacyMacros.h; path = AWSCore/Logging/AWSDDLegacyMacros.h; sourceTree = "<group>"; };
		547EAB1BE3AA869CD15DBAB199FE5B54 /* NSError+AWSMTLModelException.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSError+AWSMTLModelException.h"; path = "AWSCore/Mantle/NSError+AWSMTLModelException.h"; sourceTree = "<group>"; };
		548E4579A2F41FC491D679739625C312 /* AWSDDTTYLogger.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSDDTTYLogger.m; path = AWSCore/Logging/AWSDDTTYLogger.m; sourceTree = "<group>"; };
		550E5C69144C26B309C8ED92E96C07B9 /* HRColorCursor.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = HRColorCursor.m; path = "ZSSRichTextEditor/Source/Third Party/HRColorCursor.m"; sourceTree = "<group>"; };
		5524DC621EC590F329D352C73F45E4CD /* NSAttributedStringKey+Conversion.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSAttributedStringKey+Conversion.swift"; path = "Aztec/Classes/Extensions/NSAttributedStringKey+Conversion.swift"; sourceTree = "<group>"; };
		555D9149D58E5FFD50F69F51EAB980C6 /* BRElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BRElementConverter.swift; path = Aztec/Classes/Converters/ElementsToAttributedString/Implementations/BRElementConverter.swift; sourceTree = "<group>"; };
		555E8F030F772F41331EF1EEB47ED4FD /* IQKeyboardManagerConstants.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = IQKeyboardManagerConstants.h; path = IQKeyboardManager/Constants/IQKeyboardManagerConstants.h; sourceTree = "<group>"; };
		55AE4E9798206559C3DCDCDBF725FECD /* TextView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TextView.swift; path = Aztec/Classes/TextKit/TextView.swift; sourceTree = "<group>"; };
		55F36AD1259D4BB93C53F2402D2EBD95 /* IQKeyboardManager-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "IQKeyboardManager-dummy.m"; sourceTree = "<group>"; };
		5651B66AE63DF3C9D28A23701727FBC4 /* AWSDDAssertMacros.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSDDAssertMacros.h; path = AWSCore/Logging/AWSDDAssertMacros.h; sourceTree = "<group>"; };
		5681CE57343F77FFD7C18484BE9D91F8 /* ParagraphPropertyConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParagraphPropertyConverter.swift; path = Aztec/Classes/NSAttributedString/Conversions/ParagraphPropertyConverters/Base/ParagraphPropertyConverter.swift; sourceTree = "<group>"; };
		56E0F20EEBDFA49113C6F76E28AF022E /* AWSMTLValueTransformer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSMTLValueTransformer.h; path = AWSCore/Mantle/AWSMTLValueTransformer.h; sourceTree = "<group>"; };
		5720D83C765870F489179B2733A34E8F /* DocumentReadingOptionKey+Swift4.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "DocumentReadingOptionKey+Swift4.swift"; path = "Aztec/Classes/Extensions/DocumentReadingOptionKey+Swift4.swift"; sourceTree = "<group>"; };
		5768CA66FC6D4F2A5A91F89AEC34999E /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		5775490B4D1B6EFF212E4736D032AA64 /* ZSSRichTextEditor.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ZSSRichTextEditor.m; path = ZSSRichTextEditor/Source/ZSSRichTextEditor.m; sourceTree = "<group>"; };
		577A82C3784C61980A348C3339C38EC3 /* HTMLConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLConverter.swift; path = Aztec/Classes/NSAttributedString/Conversions/HTMLConverter.swift; sourceTree = "<group>"; };
		58C45A4A7AAAC50FBEDA41BD860D23EC /* Header.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Header.swift; path = Aztec/Classes/TextKit/ParagraphProperty/Header.swift; sourceTree = "<group>"; };
		5962947B21FF09EF8535877A731122F6 /* WordPress-Aztec-iOS.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "WordPress-Aztec-iOS.debug.xcconfig"; sourceTree = "<group>"; };
		599E6006DA6A07FEB01679A64B6BA616 /* HRElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HRElementConverter.swift; path = Aztec/Classes/Converters/ElementsToAttributedString/Implementations/HRElementConverter.swift; sourceTree = "<group>"; };
		59B1D5E156E5017D65D84E40906457A1 /* HRCgUtil.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = HRCgUtil.m; path = "ZSSRichTextEditor/Source/Third Party/HRCgUtil.m"; sourceTree = "<group>"; };
		5A38E9FE9EB858D2A5F68995D1C7B8A8 /* ZSSBarButtonItem.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ZSSBarButtonItem.m; path = ZSSRichTextEditor/Source/ZSSBarButtonItem.m; sourceTree = "<group>"; };
		5A821347CD1D8A20D645FE673B89478C /* NSValueTransformer+AWSMTLInversionAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSValueTransformer+AWSMTLInversionAdditions.m"; path = "AWSCore/Mantle/NSValueTransformer+AWSMTLInversionAdditions.m"; sourceTree = "<group>"; };
		5B8CFB2B9F5E012D390A8ACDF2102A27 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		5BF5EECD1547D846386284170DC1D1E6 /* MASLayoutConstraint.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MASLayoutConstraint.h; path = Masonry/MASLayoutConstraint.h; sourceTree = "<group>"; };
		5CA57AAD93FB7DA49B17A49D9BA1F5FE /* WordPressPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = WordPressPlugin.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/WordPressPlugin.swift; sourceTree = "<group>"; };
		5CFD3A2AEAB2229824D502647EAB5E23 /* AWSDDASLLogger.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSDDASLLogger.m; path = AWSCore/Logging/AWSDDASLLogger.m; sourceTree = "<group>"; };
		5D8F3D63AAA227811B98BAAE24389EC1 /* IQUIView+Hierarchy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "IQUIView+Hierarchy.h"; path = "IQKeyboardManager/Categories/IQUIView+Hierarchy.h"; sourceTree = "<group>"; };
		5E0C197C502079E60EEE592311CFA30B /* Pods-SnapInspect3Tests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-SnapInspect3Tests.debug.xcconfig"; sourceTree = "<group>"; };
		5E1FC42EBC42141342AB83C301E52271 /* WordPress-Editor-iOS.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "WordPress-Editor-iOS.release.xcconfig"; sourceTree = "<group>"; };
		5E72AA636629923D4322311C823D9CD1 /* UIColor+Parsers.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIColor+Parsers.swift"; path = "Aztec/Classes/Extensions/UIColor+Parsers.swift"; sourceTree = "<group>"; };
		5E7C6C9BC56B01D4B66D50C4A4939C5E /* InAttributesConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = InAttributesConverter.swift; path = Aztec/Classes/Libxml2/Converters/In/InAttributesConverter.swift; sourceTree = "<group>"; };
		5ECC8B54AC81C10B88EB37406DEB8E31 /* ZSSCustomButtonsViewController.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ZSSCustomButtonsViewController.h; path = ZSSRichTextEditor/Classes/ZSSCustomButtonsViewController.h; sourceTree = "<group>"; };
		5EF7253309C5E66A79B9F7A0FC1DD411 /* FormattingIdentifier.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FormattingIdentifier.swift; path = Aztec/Classes/GUI/FormatBar/FormattingIdentifier.swift; sourceTree = "<group>"; };
		5F415AE0B84D32167555ABCC9005E6D5 /* ZSSPlaceholderViewController.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ZSSPlaceholderViewController.m; path = ZSSRichTextEditor/Classes/ZSSPlaceholderViewController.m; sourceTree = "<group>"; };
		5FE37BF832A23CF06031E09547E0B46B /* ZSSsubscript.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSsubscript.png; path = ZSSRichTextEditor/Source/Images/ZSSsubscript.png; sourceTree = "<group>"; };
		60594BCC19FB157FAE64A5765806EE3D /* HRColorPickerView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = HRColorPickerView.h; path = "ZSSRichTextEditor/Source/Third Party/HRColorPickerView.h"; sourceTree = "<group>"; };
		61050C66A590F543EEF7CE93E4D0C371 /* AWSS3TransferUtility.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSS3TransferUtility.h; path = AWSS3/AWSS3TransferUtility.h; sourceTree = "<group>"; };
		6142C216EF73A86D30B9F2ED8AAA7DF1 /* StringUTF16+RangeConversion.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "StringUTF16+RangeConversion.swift"; path = "Aztec/Classes/Extensions/StringUTF16+RangeConversion.swift"; sourceTree = "<group>"; };
		6166EFFEC336B3F993255148D94A61D5 /* AWSCore-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "AWSCore-dummy.m"; sourceTree = "<group>"; };
		617BEDD58C1F01349E22E8A7086FCB49 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		61B1580831F5EE4C99337A1E56114053 /* HTMLLi.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLLi.swift; path = Aztec/Classes/TextKit/ParagraphProperty/HTMLLi.swift; sourceTree = "<group>"; };
		623C2F459B9B84527E06BCD6444BD3F2 /* AWSSTS.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSSTS.h; path = AWSCore/STS/AWSSTS.h; sourceTree = "<group>"; };
		627559E1B38D7FA374DD781638070008 /* AWSGeneric.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSGeneric.h; path = AWSCore/Bolts/AWSGeneric.h; sourceTree = "<group>"; };
		62FF9C38311EA366D40408B6FDBEA95D /* AWSCognitoIdentity+Fabric.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "AWSCognitoIdentity+Fabric.m"; path = "AWSCore/CognitoIdentity/AWSCognitoIdentity+Fabric.m"; sourceTree = "<group>"; };
		63309F31146AB4C903CC7A3196078A5F /* AttributeFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AttributeFormatter.swift; path = Aztec/Classes/Formatters/Base/AttributeFormatter.swift; sourceTree = "<group>"; };
		638B59C57691081714DF94E1105414E8 /* AWSDDContextFilterLogFormatter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSDDContextFilterLogFormatter.m; path = AWSCore/Logging/Extensions/AWSDDContextFilterLogFormatter.m; sourceTree = "<group>"; };
		639A1130FC5C740DFC065F2FEAD27266 /* LineAttachment.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LineAttachment.swift; path = Aztec/Classes/TextKit/LineAttachment.swift; sourceTree = "<group>"; };
		641112455CC4BEB1F70702889FBDB738 /* AttributedStringParser.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AttributedStringParser.swift; path = Aztec/Classes/NSAttributedString/Conversions/AttributedStringParser.swift; sourceTree = "<group>"; };
		64208D4FE2A83D087C8B5A7F78FBF7CD /* FormatterElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FormatterElementConverter.swift; path = Aztec/Classes/Converters/ElementsToAttributedString/Base/FormatterElementConverter.swift; sourceTree = "<group>"; };
		64614B89BF46ED549F261F6168E38801 /* GenericElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GenericElementConverter.swift; path = Aztec/Classes/Converters/ElementsToAttributedString/Implementations/GenericElementConverter.swift; sourceTree = "<group>"; };
		65516CBFEC117BA6CB815E9F1AB8D668 /* Pods-SnapInspect3Tests */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-SnapInspect3Tests"; path = Pods_SnapInspect3Tests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		660C00ECF2F488914D5AA9E4701F664C /* HTMLParagraph.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLParagraph.swift; path = Aztec/Classes/TextKit/ParagraphProperty/HTMLParagraph.swift; sourceTree = "<group>"; };
		66A9CCEAF64BB0063D1A97C737417736 /* NSArray+MASAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSArray+MASAdditions.h"; path = "Masonry/NSArray+MASAdditions.h"; sourceTree = "<group>"; };
		671BBB6BC5C0AAE909871444BDC0AE52 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/SystemConfiguration.framework; sourceTree = DEVELOPER_DIR; };
		6728180B89FD8D473A33467EEF87CE5E /* AWSExecutor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSExecutor.h; path = AWSCore/Bolts/AWSExecutor.h; sourceTree = "<group>"; };
		681D312522EFBDBCC7C7C5D24188044D /* ZSSRichTextEditor-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "ZSSRichTextEditor-dummy.m"; sourceTree = "<group>"; };
		68DBF6A14B2F3292CB8AE931B00D4AA7 /* CaptionShortcodeOutputProcessor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionShortcodeOutputProcessor.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Calypso/CaptionShortcode/CaptionShortcodeOutputProcessor.swift; sourceTree = "<group>"; };
		698A8CF09E49986092016E5DE49A4E95 /* JSBeautifier.js */ = {isa = PBXFileReference; includeInIndex = 1; name = JSBeautifier.js; path = ZSSRichTextEditor/Source/JSBeautifier.js; sourceTree = "<group>"; };
		6A0DD016CF6AEC8D5E8AA37B55DEB66A /* AWSS3TransferUtilityDatabaseHelper.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSS3TransferUtilityDatabaseHelper.m; path = AWSS3/AWSS3TransferUtilityDatabaseHelper.m; sourceTree = "<group>"; };
		6ADF3CAA4370C2E24C0B1CD33BD8BCAE /* ZSSBarButtonItem.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ZSSBarButtonItem.h; path = ZSSRichTextEditor/Source/ZSSBarButtonItem.h; sourceTree = "<group>"; };
		6B0C62E41EEB1F8AF6B5E729C5A800DD /* AWSUICKeyChainStore.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSUICKeyChainStore.m; path = AWSCore/UICKeyChainStore/AWSUICKeyChainStore.m; sourceTree = "<group>"; };
		6BA5E53794CB1A7D5D1924A240439486 /* NSAttributedString+ReplaceOcurrences.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSAttributedString+ReplaceOcurrences.swift"; path = "Aztec/Classes/Extensions/NSAttributedString+ReplaceOcurrences.swift"; sourceTree = "<group>"; };
		6BBD635FA05A2D7526B765997F06D40E /* NSError+AWSMTLModelException.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSError+AWSMTLModelException.m"; path = "AWSCore/Mantle/NSError+AWSMTLModelException.m"; sourceTree = "<group>"; };
		6BE43D121C2414F242B3AC1077A2B67B /* ShortcodeProcessor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ShortcodeProcessor.swift; path = WordPressEditor/WordPressEditor/Classes/Processors/ShortcodeProcessor.swift; sourceTree = "<group>"; };
		6CCAA421ED6A2394D5CC7E8FCFD5727D /* AWSSignature.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSSignature.h; path = AWSCore/Authentication/AWSSignature.h; sourceTree = "<group>"; };
		6CF80707F361FB3A926D75902206F3A3 /* ZSSRichTextEditor-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "ZSSRichTextEditor-prefix.pch"; sourceTree = "<group>"; };
		6D510E1A2C5CF23529263847DFAB4BF2 /* AWSS3-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AWSS3-prefix.pch"; sourceTree = "<group>"; };
		6D59136F1388507489BB0A938C86C0CA /* AWSTaskCompletionSource.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSTaskCompletionSource.m; path = AWSCore/Bolts/AWSTaskCompletionSource.m; sourceTree = "<group>"; };
		6DBE0C1F3C2495032E38C84CD46909A7 /* ImageAttachment+WordPress.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "ImageAttachment+WordPress.swift"; path = "WordPressEditor/WordPressEditor/Classes/Extensions/ImageAttachment+WordPress.swift"; sourceTree = "<group>"; };
		6DEB0307CE0537D3DB877865D38ABD00 /* AWSTaskCompletionSource.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSTaskCompletionSource.h; path = AWSCore/Bolts/AWSTaskCompletionSource.h; sourceTree = "<group>"; };
		6E2903F26479939D102361A3562C2479 /* AWSKSReachability.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSKSReachability.h; path = AWSCore/KSReachability/AWSKSReachability.h; sourceTree = "<group>"; };
		6EDC72D8701ADAEE54C666BB207C79F1 /* AWSEXTScope.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSEXTScope.m; path = AWSCore/Mantle/extobjc/AWSEXTScope.m; sourceTree = "<group>"; };
		6F2A2CA14F27AD32A1CD80270410FAE8 /* UIImage+Resize.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIImage+Resize.swift"; path = "Aztec/Classes/Extensions/UIImage+Resize.swift"; sourceTree = "<group>"; };
		6F47FA784DD33522432B2CC185BC118F /* AWSDDOSLogger.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSDDOSLogger.m; path = AWSCore/Logging/AWSDDOSLogger.m; sourceTree = "<group>"; };
		6F95A12AE839B6405F69619B00BCC5BE /* VideoAttachment.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = VideoAttachment.swift; path = Aztec/Classes/TextKit/VideoAttachment.swift; sourceTree = "<group>"; };
		704368D961907C9D12638D4FA30555A9 /* IQKeyboardReturnKeyHandler.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = IQKeyboardReturnKeyHandler.h; path = IQKeyboardManager/IQKeyboardReturnKeyHandler.h; sourceTree = "<group>"; };
		70CF970A0876BD6863CCA882372A8855 /* TextList.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TextList.swift; path = Aztec/Classes/TextKit/ParagraphProperty/TextList.swift; sourceTree = "<group>"; };
		7222CE33BB26754480137A5A06A24C53 /* ResourceBundle-IQKeyboardManager-IQKeyboardManager-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-IQKeyboardManager-IQKeyboardManager-Info.plist"; sourceTree = "<group>"; };
		7222D67D962ED7B8792514D58F2B8FBF /* ParagraphStyle.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParagraphStyle.swift; path = Aztec/Classes/TextKit/ParagraphStyle.swift; sourceTree = "<group>"; };
		7225EB57D56745BF550C02D1730AD91C /* ImageAttachment.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ImageAttachment.swift; path = Aztec/Classes/TextKit/ImageAttachment.swift; sourceTree = "<group>"; };
		7240A1FFFCBE40594C39FA603F44590E /* GutenpackAttachmentRenderer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GutenpackAttachmentRenderer.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Gutenberg/GutenpackAttachmentRenderer.swift; sourceTree = "<group>"; };
		72499EE078AD8003B33D48EAADE71CF6 /* AWSNetworkingHelpers.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSNetworkingHelpers.h; path = AWSCore/Networking/AWSNetworkingHelpers.h; sourceTree = "<group>"; };
		728CE07C37573C09974CDEBE06F36D58 /* UITextView+Delegate.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UITextView+Delegate.swift"; path = "Aztec/Classes/Extensions/UITextView+Delegate.swift"; sourceTree = "<group>"; };
		730FBDA9EF3D89251EC5F6784A86D3D2 /* ZSSRichTextEditor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ZSSRichTextEditor.h; path = ZSSRichTextEditor/Source/ZSSRichTextEditor.h; sourceTree = "<group>"; };
		73304FC323A42EDC9BBB24BEF29EB36E /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		73573205F758A3C405D7A69AA503A9D0 /* AWSValidation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSValidation.h; path = AWSCore/Serialization/AWSValidation.h; sourceTree = "<group>"; };
		738FAD0985222D7E507BE7FEDAC9895E /* ImageAttachmentToElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ImageAttachmentToElementConverter.swift; path = Aztec/Classes/NSAttributedString/Conversions/AttachmentToElementConverter/ImageAttachmentToElementConverter.swift; sourceTree = "<group>"; };
		75332399D9905747E1517151D5D8869F /* ElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ElementConverter.swift; path = Aztec/Classes/Converters/ElementsToAttributedString/Base/ElementConverter.swift; sourceTree = "<group>"; };
		75398A29EDFDFA22F9212F8A8FD5923F /* IQUITextFieldView+Additions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "IQUITextFieldView+Additions.h"; path = "IQKeyboardManager/Categories/IQUITextFieldView+Additions.h"; sourceTree = "<group>"; };
		756F66AADA06C45EA43815C641BEB5C7 /* AWSS3.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AWSS3.debug.xcconfig; sourceTree = "<group>"; };
		758D76535C24029633B4CB5525FD6370 /* AWSLogging.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSLogging.m; path = AWSCore/Utility/AWSLogging.m; sourceTree = "<group>"; };
		75C78E06D081A1A6D228C76EDDD36AB5 /* ZSSRichTextEditor-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ZSSRichTextEditor-Info.plist"; sourceTree = "<group>"; };
		76BBF9674059B06F4ABF0CB90EDE2BAE /* AWSCategory.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSCategory.m; path = AWSCore/Utility/AWSCategory.m; sourceTree = "<group>"; };
		76CFE2037447BF1F045097EEBA4417EB /* AWSMTLModel.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSMTLModel.m; path = AWSCore/Mantle/AWSMTLModel.m; sourceTree = "<group>"; };
		76E4C6B4DCC6284EF00F55C768956C69 /* ZSSRichTextEditor */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = ZSSRichTextEditor; path = ZSSRichTextEditor.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		772044D1CBAEA66DF6DF7A7AD8B94FAD /* AWSXMLWriter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSXMLWriter.m; path = AWSCore/XMLWriter/AWSXMLWriter.m; sourceTree = "<group>"; };
		77BB5EE6ED2EDECBD411F1CD24F81615 /* ZSSleftjustify.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSleftjustify.png; path = ZSSRichTextEditor/Source/Images/ZSSleftjustify.png; sourceTree = "<group>"; };
		78B19AEF248AFAF06A15F0F6DA9141EF /* ZSSimageDevice.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSimageDevice.png; path = ZSSRichTextEditor/Source/Images/ZSSimageDevice.png; sourceTree = "<group>"; };
		78D5531F362D6B1C64A1CAD7A6BBBCA7 /* AWSSimpleDB.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AWSSimpleDB.debug.xcconfig; sourceTree = "<group>"; };
		796C1D4D0E02C7E49C871D77A15B055D /* AWSValidation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSValidation.m; path = AWSCore/Serialization/AWSValidation.m; sourceTree = "<group>"; };
		797C45CC75CDE85071DA2BAA7E18EFA1 /* MediaAttachment.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MediaAttachment.swift; path = Aztec/Classes/TextKit/MediaAttachment.swift; sourceTree = "<group>"; };
		7A16A5E3CF3693344B4A482033D39E53 /* UITextView+Placeholder.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "UITextView+Placeholder.modulemap"; sourceTree = "<group>"; };
		7B6CB535B781BF4B6B396F79FB55DF41 /* NSMutableAttributedString+ReplaceAttributes.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSMutableAttributedString+ReplaceAttributes.swift"; path = "Aztec/Classes/Extensions/NSMutableAttributedString+ReplaceAttributes.swift"; sourceTree = "<group>"; };
		7BAE45001CE72B3C620CCAC0A8BE196A /* IQBarButtonItem.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = IQBarButtonItem.h; path = IQKeyboardManager/IQToolbar/IQBarButtonItem.h; sourceTree = "<group>"; };
		7C49B484F3F261F33738AA0DD9AA4BB2 /* NSDictionary+AWSMTLManipulationAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSDictionary+AWSMTLManipulationAdditions.h"; path = "AWSCore/Mantle/NSDictionary+AWSMTLManipulationAdditions.h"; sourceTree = "<group>"; };
		7C65FC3A7A72534371AC6444DF4914AE /* ZSSclearstyle.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSclearstyle.png; path = ZSSRichTextEditor/Source/Images/ZSSclearstyle.png; sourceTree = "<group>"; };
		7CD09A026EBA331CC6FFF4A7A0350C52 /* WordPress-Editor-iOS.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "WordPress-Editor-iOS.modulemap"; sourceTree = "<group>"; };
		7DD71838E5EDBD9BBE5B20E7963C4B22 /* AWSCognitoIdentityModel.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSCognitoIdentityModel.h; path = AWSCore/CognitoIdentity/AWSCognitoIdentityModel.h; sourceTree = "<group>"; };
		7DDBECEED799A7A43F80F855AA433018 /* UITextView+Placeholder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UITextView+Placeholder.h"; path = "Sources/UITextView+Placeholder.h"; sourceTree = "<group>"; };
		7DE33AC5AF743E2B5FA4B20250E3063E /* ZSScenterjustify.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSScenterjustify.png; path = ZSSRichTextEditor/Source/Images/ZSScenterjustify.png; sourceTree = "<group>"; };
		7E20CDF0898283444674C3CA432257AD /* ItalicElementAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ItalicElementAttributeConverter.swift; path = Aztec/Classes/Converters/AttributesToStringAttributes/Implementations/ItalicElementAttributeConverter.swift; sourceTree = "<group>"; };
		7E2EECDA23F704DD9AE43511878D3CA4 /* Figcaption.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Figcaption.swift; path = Aztec/Classes/TextKit/ParagraphProperty/Figcaption.swift; sourceTree = "<group>"; };
		7E984FB8DCF638BB61DCE418C8F67B05 /* WordPress-Editor-iOS-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "WordPress-Editor-iOS-dummy.m"; sourceTree = "<group>"; };
		7EDECE18E660C72B4D9FA7CB4582081F /* AWSSynchronizedMutableDictionary.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSSynchronizedMutableDictionary.m; path = AWSCore/Utility/AWSSynchronizedMutableDictionary.m; sourceTree = "<group>"; };
		7F038CD9E930B4AD0D7E067D11EDBF54 /* Figure.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Figure.swift; path = Aztec/Classes/TextKit/ParagraphProperty/Figure.swift; sourceTree = "<group>"; };
		7FB463E08CB491D770D1C136C8C1263E /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		7FD88E10375476175A0144CCC33D86B5 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		8019BE9C525449AC6777B20200813346 /* MainAttributesConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MainAttributesConverter.swift; path = Aztec/Classes/Converters/AttributesToStringAttributes/Base/MainAttributesConverter.swift; sourceTree = "<group>"; };
		8034E723B38DF6B087138D044FD8D296 /* GalleryShortcodeInputProcessor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GalleryShortcodeInputProcessor.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Calypso/GalleryShortcode/GalleryShortcodeInputProcessor.swift; sourceTree = "<group>"; };
		80E9B08FED6EB8F3A7CD09807B717871 /* ShortcodeAttribute.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ShortcodeAttribute.swift; path = Aztec/Classes/Processor/ShortcodeAttribute.swift; sourceTree = "<group>"; };
		8111EBB5048CB52A29C0402C6C90E7A4 /* AWSTMMemoryCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSTMMemoryCache.m; path = AWSCore/TMCache/AWSTMMemoryCache.m; sourceTree = "<group>"; };
		812B1F03A46C5A35C0D0AA5980C4B8A4 /* Pods-SnapInspect3Tests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-SnapInspect3Tests-acknowledgements.markdown"; sourceTree = "<group>"; };
		818F5E6B4DBD91C42E8C9380DE4D5018 /* HTMLAttachmentRenderer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLAttachmentRenderer.swift; path = Aztec/Classes/Renderers/HTMLAttachmentRenderer.swift; sourceTree = "<group>"; };
		8216011AB3DBE55EAC3EC1E695892B37 /* AWSCancellationTokenRegistration.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSCancellationTokenRegistration.h; path = AWSCore/Bolts/AWSCancellationTokenRegistration.h; sourceTree = "<group>"; };
		826E8F3C56C8D0005C3D245652B575C1 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		829A43B8A8EEFE789AE6D7115BD89ACB /* FormatBarDelegate.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FormatBarDelegate.swift; path = Aztec/Classes/GUI/FormatBar/FormatBarDelegate.swift; sourceTree = "<group>"; };
		834D5D87CAFE0E36DDBBB77A63249B4A /* AWSS3RequestRetryHandler.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSS3RequestRetryHandler.h; path = AWSS3/AWSS3RequestRetryHandler.h; sourceTree = "<group>"; };
		83FA287D7A85D8517864E61A69BB2EC4 /* AWSDDMultiFormatter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSDDMultiFormatter.h; path = AWSCore/Logging/Extensions/AWSDDMultiFormatter.h; sourceTree = "<group>"; };
		8459F05ECCAAD575CF7CB6C028E43445 /* AWSURLRequestRetryHandler.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSURLRequestRetryHandler.m; path = AWSCore/Serialization/AWSURLRequestRetryHandler.m; sourceTree = "<group>"; };
		845B34CC7B2DAD2ED03AEBCF5DB4601C /* AWSCancellationTokenRegistration.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSCancellationTokenRegistration.m; path = AWSCore/Bolts/AWSCancellationTokenRegistration.m; sourceTree = "<group>"; };
		847EF218D80B5A990D6646B162B88F86 /* AWSInfo.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSInfo.h; path = AWSCore/Service/AWSInfo.h; sourceTree = "<group>"; };
		84F85EEF0E7442B2B1EE9396B766DCA2 /* FigureElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FigureElementConverter.swift; path = Aztec/Classes/Converters/ElementsToAttributedString/Implementations/FigureElementConverter.swift; sourceTree = "<group>"; };
		850083AE3F6ED8106C86F0A76D3331BA /* PluginManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PluginManager.swift; path = Aztec/Classes/Plugin/PluginManager.swift; sourceTree = "<group>"; };
		8545602ABE422AEBDB226FBF2D2399BB /* Pods-SnapInspect3Tests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-SnapInspect3Tests-dummy.m"; sourceTree = "<group>"; };
		85506C33D6ACBC73C840990C39D8C24B /* AWSService.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSService.m; path = AWSCore/Service/AWSService.m; sourceTree = "<group>"; };
		8565124D9E31139351BC2015D6343353 /* NSMutableAttributedString+ParagraphProperty.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSMutableAttributedString+ParagraphProperty.swift"; path = "Aztec/Classes/Extensions/NSMutableAttributedString+ParagraphProperty.swift"; sourceTree = "<group>"; };
		86082A689D2C66A48D5AAF2AEA2E0094 /* ZSSquicklink.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSquicklink.png; path = ZSSRichTextEditor/Source/Images/ZSSquicklink.png; sourceTree = "<group>"; };
		8686E3BD6A3C230B9041630D692A0943 /* WordPress-Aztec-iOS-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "WordPress-Aztec-iOS-dummy.m"; sourceTree = "<group>"; };
		876ABF628D9DE72E7646E9ED6545047C /* Converter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Converter.swift; path = Aztec/Classes/Converters/HTMLToElements/Converter.swift; sourceTree = "<group>"; };
		877AF6C35718C06CF18D7634D6655A04 /* AWSS3TransferUtilityDatabaseHelper.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSS3TransferUtilityDatabaseHelper.h; path = AWSS3/AWSS3TransferUtilityDatabaseHelper.h; sourceTree = "<group>"; };
		87E07E9CCE04E995EE98E4A3E63B4B9A /* Masonry.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = Masonry.modulemap; sourceTree = "<group>"; };
		89FE963973B0B1859068323549AD1F27 /* CYRLayoutManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CYRLayoutManager.h; path = "ZSSRichTextEditor/Source/Third Party/CYRLayoutManager.h"; sourceTree = "<group>"; };
		89FFE461E69C6CE0E84EA6BFC9E5F32B /* ZSSforcejustify.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSforcejustify.png; path = ZSSRichTextEditor/Source/Images/ZSSforcejustify.png; sourceTree = "<group>"; };
		8A0A756442DFB9E20738362928F051C6 /* UnderlineFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = UnderlineFormatter.swift; path = Aztec/Classes/Formatters/Implementations/UnderlineFormatter.swift; sourceTree = "<group>"; };
		8A9C455940ED58B87DB93AE3AF77AB40 /* UITextView+Placeholder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UITextView+Placeholder.m"; path = "Sources/UITextView+Placeholder.m"; sourceTree = "<group>"; };
		8B8CCEA8562FB0CAA855E9892319AE81 /* NSValueTransformer+AWSMTLInversionAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSValueTransformer+AWSMTLInversionAdditions.h"; path = "AWSCore/Mantle/NSValueTransformer+AWSMTLInversionAdditions.h"; sourceTree = "<group>"; };
		8C0DC31065D92ABF0464048E46D47057 /* FontProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FontProvider.swift; path = Aztec/Classes/TextKit/FontProvider.swift; sourceTree = "<group>"; };
		8C1477268A32B3F2E038654C21706386 /* Pods-SnapInspect3.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-SnapInspect3.modulemap"; sourceTree = "<group>"; };
		8C5B1742EBD4FABEA46AA50116D42214 /* HTMLRepresentation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLRepresentation.swift; path = Aztec/Classes/NSAttributedString/Attributes/HTMLRepresentation.swift; sourceTree = "<group>"; };
		8C6CEAD6FF46BCFFC517DBF6D3AB88C5 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		8C6D278652EA585D68FB13E2A840CCE0 /* BoldStringAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BoldStringAttributeConverter.swift; path = Aztec/Classes/Converters/StringAttributesToAttributes/Implementations/BoldStringAttributeConverter.swift; sourceTree = "<group>"; };
		8C83C48284F8921E0632E8AA48CA43BB /* AWSCredentialsProvider.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSCredentialsProvider.h; path = AWSCore/Authentication/AWSCredentialsProvider.h; sourceTree = "<group>"; };
		8C9BCEA2B3640D180AFF3625E28AE130 /* DocumentType+Swift4.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "DocumentType+Swift4.swift"; path = "Aztec/Classes/Extensions/DocumentType+Swift4.swift"; sourceTree = "<group>"; };
		8CBE5939B36C709164984BFB030ABEB7 /* AWSURLRequestSerialization.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSURLRequestSerialization.m; path = AWSCore/Serialization/AWSURLRequestSerialization.m; sourceTree = "<group>"; };
		8DCAD714A8E80AAAC7528B3FED4DA767 /* ForegroundColorElementAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ForegroundColorElementAttributeConverter.swift; path = Aztec/Classes/Converters/AttributesToStringAttributes/Implementations/ForegroundColorElementAttributeConverter.swift; sourceTree = "<group>"; };
		8DD3DE6D2733041CEF845E8314E85E29 /* ZSSSelectiveViewController.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ZSSSelectiveViewController.m; path = ZSSRichTextEditor/Classes/ZSSSelectiveViewController.m; sourceTree = "<group>"; };
		8DF46159153BEF3ECE1FD6F070554176 /* ZSStable.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSStable.png; path = ZSSRichTextEditor/Source/Images/ZSStable.png; sourceTree = "<group>"; };
		8E85B120A0979EE2ED5F1E5784756B04 /* IQBarButtonItem.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = IQBarButtonItem.m; path = IQKeyboardManager/IQToolbar/IQBarButtonItem.m; sourceTree = "<group>"; };
		8E95744135DE0D8AA27F6AE43903438E /* ZSSRichTextEditor.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = ZSSRichTextEditor.release.xcconfig; sourceTree = "<group>"; };
		8F5DE4B797E346FDB6C6F6704B8AD042 /* CSSAttribute.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CSSAttribute.swift; path = Aztec/Classes/Libxml2/DOM/Data/CSSAttribute.swift; sourceTree = "<group>"; };
		8FAB08C499E17F0B00712D1A24584AD9 /* UITextView+Placeholder-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "UITextView+Placeholder-dummy.m"; sourceTree = "<group>"; };
		8FBCF675096DCEFB635C9BFCBFF69500 /* Array+Helpers.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Array+Helpers.swift"; path = "Aztec/Classes/Extensions/Array+Helpers.swift"; sourceTree = "<group>"; };
		903C5C496D228289A15316CF176BA1C7 /* CYRTextStorage.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CYRTextStorage.h; path = "ZSSRichTextEditor/Source/Third Party/CYRTextStorage.h"; sourceTree = "<group>"; };
		9053F06430FDBF219FFE8F6EA3348170 /* ParagraphAttributeFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParagraphAttributeFormatter.swift; path = Aztec/Classes/Formatters/Base/ParagraphAttributeFormatter.swift; sourceTree = "<group>"; };
		9082B30DFDF81429C19E87E82407B053 /* ZSSColorViewController.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ZSSColorViewController.h; path = ZSSRichTextEditor/Classes/ZSSColorViewController.h; sourceTree = "<group>"; };
		911FFA8275F5C2BCA0C06B955E7C61B7 /* AWSSerialization.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSSerialization.h; path = AWSCore/Serialization/AWSSerialization.h; sourceTree = "<group>"; };
		917E30E7AF36AE4FC09DB3946E54C9BC /* HTMLDiv.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLDiv.swift; path = Aztec/Classes/TextKit/ParagraphProperty/HTMLDiv.swift; sourceTree = "<group>"; };
		9191DC1E4A6D751C6E695A38D86D218B /* AWSCore-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "AWSCore-Info.plist"; sourceTree = "<group>"; };
		91CB469A5BDECC08AA507FED5FBF05E5 /* ZSSh1.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSh1.png; path = ZSSRichTextEditor/Source/Images/ZSSh1.png; sourceTree = "<group>"; };
		9247040712D2E676ABE303146C08E865 /* CiteStringAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CiteStringAttributeConverter.swift; path = Aztec/Classes/Converters/StringAttributesToAttributes/Implementations/CiteStringAttributeConverter.swift; sourceTree = "<group>"; };
		930FF5049DD63C81F91352537AD13FC5 /* ZSSkeyboard.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSkeyboard.png; path = ZSSRichTextEditor/Source/Images/ZSSkeyboard.png; sourceTree = "<group>"; };
		93A3ECEAA852629251E567DD02905F1E /* SpecialTagAttachmentRenderer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SpecialTagAttachmentRenderer.swift; path = WordPressEditor/WordPressEditor/Classes/Renderers/SpecialTagAttachmentRenderer.swift; sourceTree = "<group>"; };
		93FB0C3238BA760211FE768FFF134809 /* AWSDDAbstractDatabaseLogger.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSDDAbstractDatabaseLogger.m; path = AWSCore/Logging/AWSDDAbstractDatabaseLogger.m; sourceTree = "<group>"; };
		940BEA802B685570975157FD009EE6ED /* UIFont+Emoji.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIFont+Emoji.swift"; path = "Aztec/Classes/Extensions/UIFont+Emoji.swift"; sourceTree = "<group>"; };
		9428216B36ED69D2AAEAB5BF7B64E3C9 /* NSAttributedString+ParagraphRange.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSAttributedString+ParagraphRange.swift"; path = "Aztec/Classes/Extensions/NSAttributedString+ParagraphRange.swift"; sourceTree = "<group>"; };
		9447A291573AA1FBEA2C01F4787364C2 /* CustomIOSAlertView.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = CustomIOSAlertView.debug.xcconfig; sourceTree = "<group>"; };
		958E826129D5AA9DEC5074C9F89BC3AC /* ZSSPlaceholderViewController.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ZSSPlaceholderViewController.h; path = ZSSRichTextEditor/Classes/ZSSPlaceholderViewController.h; sourceTree = "<group>"; };
		95AAA80023A09D43DF2B01519622C519 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		95E34300DCEEA53679C0878FF8D099A9 /* HTMLParagraphFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLParagraphFormatter.swift; path = Aztec/Classes/Formatters/Implementations/HTMLParagraphFormatter.swift; sourceTree = "<group>"; };
		96165BF12398A6C2770E17940E8CC6AD /* AWSCognitoIdentityResources.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSCognitoIdentityResources.m; path = AWSCore/CognitoIdentity/AWSCognitoIdentityResources.m; sourceTree = "<group>"; };
		96CD570ACBFACC29A9BE9C8DDE8A7F92 /* HRColorCursor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = HRColorCursor.h; path = "ZSSRichTextEditor/Source/Third Party/HRColorCursor.h"; sourceTree = "<group>"; };
		97178E4CD9890DD8945143B4F35552F6 /* AWSS3TransferManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSS3TransferManager.m; path = AWSS3/AWSS3TransferManager.m; sourceTree = "<group>"; };
		97A83F9E5D08C4AFF2DB0AE534D85A9C /* AWSCancellationToken.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSCancellationToken.m; path = AWSCore/Bolts/AWSCancellationToken.m; sourceTree = "<group>"; };
		97ADBE32A4F1B909C5E451F8D7903634 /* AWSNetworking.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSNetworking.m; path = AWSCore/Networking/AWSNetworking.m; sourceTree = "<group>"; };
		97AF1FAE6D3DA554621D5D56F7EBEDDF /* NSAttributedString+Analyzers.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSAttributedString+Analyzers.swift"; path = "Aztec/Classes/Extensions/NSAttributedString+Analyzers.swift"; sourceTree = "<group>"; };
		98151BE1EB7039A64A7B4C3B74E7F59D /* Element.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Element.swift; path = Aztec/Classes/Libxml2/DOM/Data/Element.swift; sourceTree = "<group>"; };
		981C790AD43EC86299CCCA039FF11C34 /* AWSTMMemoryCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSTMMemoryCache.h; path = AWSCore/TMCache/AWSTMMemoryCache.h; sourceTree = "<group>"; };
		98527D7196957AAB07B79E2E2AFDE23E /* IQKeyboardManager */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = IQKeyboardManager; path = IQKeyboardManager.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		995866F4E62AFEFB7B0B8D4CD643057A /* Pods-SnapInspect3-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-SnapInspect3-acknowledgements.markdown"; sourceTree = "<group>"; };
		9968ABA13A812750759CBD89C6F4A0A4 /* AttachmentToElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AttachmentToElementConverter.swift; path = Aztec/Classes/NSAttributedString/Conversions/AttachmentToElementConverter/Base/AttachmentToElementConverter.swift; sourceTree = "<group>"; };
		99C4601DBF9EA72727271589E9B79F3A /* AWSTMDiskCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSTMDiskCache.h; path = AWSCore/TMCache/AWSTMDiskCache.h; sourceTree = "<group>"; };
		99F56AF89A82A83979896B07F61C4F55 /* AWSDDASLLogCapture.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSDDASLLogCapture.h; path = AWSCore/Logging/AWSDDASLLogCapture.h; sourceTree = "<group>"; };
		9AD41CE3DD9D13779F236141AB923239 /* MASConstraint.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MASConstraint.h; path = Masonry/MASConstraint.h; sourceTree = "<group>"; };
		9AFCE0ECCC017ECBA8B0157C0A9F326F /* InAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = InAttributeConverter.swift; path = Aztec/Classes/Libxml2/Converters/In/InAttributeConverter.swift; sourceTree = "<group>"; };
		9B558202ADA4407D1A64B290FF504C72 /* CYRLayoutManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CYRLayoutManager.m; path = "ZSSRichTextEditor/Source/Third Party/CYRLayoutManager.m"; sourceTree = "<group>"; };
		9B776A3E797E4B8EEDA61EF1F11B6FBE /* AWSEXTRuntimeExtensions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSEXTRuntimeExtensions.m; path = AWSCore/Mantle/extobjc/AWSEXTRuntimeExtensions.m; sourceTree = "<group>"; };
		9CEC9BED47F9A9090F913FA2EE294605 /* AWSFMDatabase.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSFMDatabase.m; path = AWSCore/FMDB/AWSFMDatabase.m; sourceTree = "<group>"; };
		9D15296881BFAB381C59EFC4966C6D01 /* CoreText.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreText.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreText.framework; sourceTree = DEVELOPER_DIR; };
		9D898348183AE73ADEE5C5707B0827E2 /* ZSSrightjustify.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSrightjustify.png; path = ZSSRichTextEditor/Source/Images/ZSSrightjustify.png; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9D99A603CF49DDE72E4979503156B621 /* AWSSimpleDB.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AWSSimpleDB.release.xcconfig; sourceTree = "<group>"; };
		9DA3DE632D60541EB7D834021067DA80 /* MASViewAttribute.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MASViewAttribute.m; path = Masonry/MASViewAttribute.m; sourceTree = "<group>"; };
		9E02BD2CFC8A70D922DDE96CA66A46A1 /* ConditionalStringAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConditionalStringAttributeConverter.swift; path = Aztec/Classes/Converters/StringAttributesToAttributes/Base/ConditionalStringAttributeConverter.swift; sourceTree = "<group>"; };
		9E8368C3580161A5C1D43904197F5581 /* BoldElementAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BoldElementAttributeConverter.swift; path = Aztec/Classes/Converters/AttributesToStringAttributes/Implementations/BoldElementAttributeConverter.swift; sourceTree = "<group>"; };
		9FE94E5220B1B4EDEB0DDF7CC3954033 /* HRColorPickerViewController.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = HRColorPickerViewController.m; path = "ZSSRichTextEditor/Source/Third Party/HRColorPickerViewController.m"; sourceTree = "<group>"; };
		A0903D0074BBBD56225535B98AFE5B90 /* AWSMantle.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSMantle.h; path = AWSCore/Mantle/AWSMantle.h; sourceTree = "<group>"; };
		A105034077CFBB0E2A112BF033BAD528 /* WordPress-Editor-iOS-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "WordPress-Editor-iOS-umbrella.h"; sourceTree = "<group>"; };
		A116A72EC0E1FAE72337F8394B1C38E2 /* AWSSimpleDBResources.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSSimpleDBResources.h; path = AWSSimpleDB/AWSSimpleDBResources.h; sourceTree = "<group>"; };
		A13EF83225F8280989C1B4644F4C0201 /* HTMLTreeProcessor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLTreeProcessor.swift; path = Aztec/Classes/Processor/HTMLTreeProcessor.swift; sourceTree = "<group>"; };
		A1C522ACEAE3E35517A873AB77C7B09D /* AWSFMDB+AWSHelpers.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "AWSFMDB+AWSHelpers.m"; path = "AWSCore/FMDB/AWSFMDB+AWSHelpers.m"; sourceTree = "<group>"; };
		A20AE295FDEB3E4FB7D13B49A9E8F530 /* NSValueTransformer+AWSMTLPredefinedTransformerAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSValueTransformer+AWSMTLPredefinedTransformerAdditions.h"; path = "AWSCore/Mantle/NSValueTransformer+AWSMTLPredefinedTransformerAdditions.h"; sourceTree = "<group>"; };
		A2371E8F49DCD048BF18D94BFD29DC4C /* AWSDDLog+LOGV.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "AWSDDLog+LOGV.h"; path = "AWSCore/Logging/AWSDDLog+LOGV.h"; sourceTree = "<group>"; };
		A4D56EE434F98C7FC54FB7DAC79CF1CC /* InNodeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = InNodeConverter.swift; path = Aztec/Classes/Libxml2/Converters/In/InNodeConverter.swift; sourceTree = "<group>"; };
		A4E718B7D0E0B58A336D2D0FDE7C9FDA /* CommentAttachmentToElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CommentAttachmentToElementConverter.swift; path = Aztec/Classes/NSAttributedString/Conversions/AttachmentToElementConverter/CommentAttachmentToElementConverter.swift; sourceTree = "<group>"; };
		A59475F444CA3AC4FDC597B6C0C671CC /* AWSS3.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSS3.h; path = AWSS3/AWSS3.h; sourceTree = "<group>"; };
		A5B2838E4866923F3DA717B058EBFF5D /* IQKeyboardManager-IQKeyboardManager */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "IQKeyboardManager-IQKeyboardManager"; path = IQKeyboardManager.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		A630A05CA0CA688C3B11F4807D366C77 /* WordPress-Editor-iOS */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "WordPress-Editor-iOS"; path = WordPressEditor.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A64EDFF3CE34949D3A965A50D0060C8A /* AWSFMDatabaseAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSFMDatabaseAdditions.m; path = AWSCore/FMDB/AWSFMDatabaseAdditions.m; sourceTree = "<group>"; };
		A7B73D648F0D281C558AF0E3CA4195E2 /* Masonry.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = Masonry.h; path = Masonry/Masonry.h; sourceTree = "<group>"; };
		A7C002473DB627C553D0EE1423982BA7 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		A824636F156CD9FB741A732835E18402 /* AWSXMLWriter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSXMLWriter.h; path = AWSCore/XMLWriter/AWSXMLWriter.h; sourceTree = "<group>"; };
		A8DD60E2084C5DE8ACF1B2C6583E1EF2 /* AWSCognitoIdentityService.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSCognitoIdentityService.m; path = AWSCore/CognitoIdentity/AWSCognitoIdentityService.m; sourceTree = "<group>"; };
		A8E5A062BC2C12D27FDAB47AB0DCD1EC /* editor.html */ = {isa = PBXFileReference; includeInIndex = 1; name = editor.html; path = ZSSRichTextEditor/Source/editor.html; sourceTree = "<group>"; };
		A908D8227F1E42268F80EA280762725A /* Pods-SnapInspect3-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-SnapInspect3-dummy.m"; sourceTree = "<group>"; };
		A918900FAA168905AA5694B24E77B979 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		A9420D27C675E14B58E526325816B5F3 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		AA2704F94C3FCAC9249EDFDC5E3FA21F /* UnderlineElementAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = UnderlineElementAttributeConverter.swift; path = Aztec/Classes/Converters/AttributesToStringAttributes/Implementations/UnderlineElementAttributeConverter.swift; sourceTree = "<group>"; };
		AA9F7CAF04778BAF43B8AEAB75680FA0 /* NSRange+Helpers.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSRange+Helpers.swift"; path = "Aztec/Classes/Extensions/NSRange+Helpers.swift"; sourceTree = "<group>"; };
		AAAABF29488FA39F76C549E9F8D0CDE6 /* AWSDDContextFilterLogFormatter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSDDContextFilterLogFormatter.h; path = AWSCore/Logging/Extensions/AWSDDContextFilterLogFormatter.h; sourceTree = "<group>"; };
		AAD320CE6517A51936DFD2C6A8017043 /* HRColorUtil.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = HRColorUtil.h; path = "ZSSRichTextEditor/Source/Third Party/HRColorUtil.h"; sourceTree = "<group>"; };
		AADF95D6950248D1E241198FCF2ABE15 /* Metrics.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Metrics.swift; path = Aztec/Classes/Constants/Metrics.swift; sourceTree = "<group>"; };
		AB3B7A57EAF5A49169480418B2C4B56F /* NSAttributedString+Archive.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSAttributedString+Archive.swift"; path = "Aztec/Classes/Extensions/NSAttributedString+Archive.swift"; sourceTree = "<group>"; };
		AB5FD88DF442EF74E7ADC537ED171E54 /* VideoShortcodeProcessor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = VideoShortcodeProcessor.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Calypso/VideoShortcode/VideoShortcodeProcessor.swift; sourceTree = "<group>"; };
		ABC4C62B6B80178C30FB33C7D08D1839 /* CommentAttachment.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CommentAttachment.swift; path = Aztec/Classes/TextKit/CommentAttachment.swift; sourceTree = "<group>"; };
		ABFB8288FEC824AEC757558B8201837D /* AWSTMCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSTMCache.m; path = AWSCore/TMCache/AWSTMCache.m; sourceTree = "<group>"; };
		AC1EC3852FAA899F11800775A2A52D3C /* UITextView+Undoable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UITextView+Undoable.swift"; path = "Aztec/Classes/Extensions/UITextView+Undoable.swift"; sourceTree = "<group>"; };
		AC84E94C514DA4AF02D2DF649F69B650 /* AWSModel.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSModel.h; path = AWSCore/Utility/AWSModel.h; sourceTree = "<group>"; };
		AD2B778079026D96FD5732ED78ACFFDF /* AWSEXTKeyPathCoding.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSEXTKeyPathCoding.h; path = AWSCore/Mantle/extobjc/AWSEXTKeyPathCoding.h; sourceTree = "<group>"; };
		AD3B9EC43358F4BF419BB354BD9D0534 /* View+MASAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "View+MASAdditions.m"; path = "Masonry/View+MASAdditions.m"; sourceTree = "<group>"; };
		AD7FFD189A8598CD3AAF4E564F7ACBED /* AWSS3TransferManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSS3TransferManager.h; path = AWSS3/AWSS3TransferManager.h; sourceTree = "<group>"; };
		ADA5741F6BCC100592751FA0D4AA8C7C /* AWSSimpleDBResources.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSSimpleDBResources.m; path = AWSSimpleDB/AWSSimpleDBResources.m; sourceTree = "<group>"; };
		AF4069A14D9C01FB0A3E774A00E6D15C /* aztec.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = aztec.png; path = WordPressEditor/WordPressEditor/Assets/aztec.png; sourceTree = "<group>"; };
		B02164EA8470858C577C423F5D967E89 /* AWSS3-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AWSS3-umbrella.h"; sourceTree = "<group>"; };
		B08508F0601023DE71C82068EDE7DF1B /* AWSMTLManagedObjectAdapter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSMTLManagedObjectAdapter.h; path = AWSCore/Mantle/AWSMTLManagedObjectAdapter.h; sourceTree = "<group>"; };
		B0E0975267A737851C218C21DC21F058 /* AWSEXTRuntimeExtensions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSEXTRuntimeExtensions.h; path = AWSCore/Mantle/extobjc/AWSEXTRuntimeExtensions.h; sourceTree = "<group>"; };
		B11BE74E22A12A4098F043BF3B407E34 /* AWSS3Serializer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSS3Serializer.h; path = AWSS3/AWSS3Serializer.h; sourceTree = "<group>"; };
		B15DCBE8E89EA8C8E0E845B756A805F5 /* NSDictionary+AWSMTLManipulationAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSDictionary+AWSMTLManipulationAdditions.m"; path = "AWSCore/Mantle/NSDictionary+AWSMTLManipulationAdditions.m"; sourceTree = "<group>"; };
		B15FE37152D666D5C1D33395B4B78CF8 /* IQKeyboardReturnKeyHandler.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = IQKeyboardReturnKeyHandler.m; path = IQKeyboardManager/IQKeyboardReturnKeyHandler.m; sourceTree = "<group>"; };
		B1679FC776EDCED632CADABF467AA512 /* ZSSh5.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSh5.png; path = ZSSRichTextEditor/Source/Images/ZSSh5.png; sourceTree = "<group>"; };
		B16CD00475BF1284F5E2CF445243F49E /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		B17D712E52732810E6EBB6A84BDEC604 /* CustomIOSAlertView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CustomIOSAlertView.m; path = CustomIOSAlertView/CustomIOSAlertView/View/CustomIOSAlertView.m; sourceTree = "<group>"; };
		B32AEC38238F7C01AE5609556EA5957F /* LIElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LIElementConverter.swift; path = Aztec/Classes/Converters/ElementsToAttributedString/Implementations/LIElementConverter.swift; sourceTree = "<group>"; };
		B34595B0BEDC3B2D63D1FC080E1668E3 /* AWSFMDatabasePool.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSFMDatabasePool.h; path = AWSCore/FMDB/AWSFMDatabasePool.h; sourceTree = "<group>"; };
		B34B5E2F0FAD624DFA7AEDE2C00F9750 /* MASConstraint.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MASConstraint.m; path = Masonry/MASConstraint.m; sourceTree = "<group>"; };
		B391CB9E214DA15C37037A217641F72F /* AWSMTLModel.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSMTLModel.h; path = AWSCore/Mantle/AWSMTLModel.h; sourceTree = "<group>"; };
		B3F38191D31D14F81C9D1036C16F553E /* Pods-SnapInspect3Tests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-SnapInspect3Tests.modulemap"; sourceTree = "<group>"; };
		B42EE2D57EB703D1BF8AB71C3B9FDB31 /* Pods-SnapInspect3.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-SnapInspect3.release.xcconfig"; sourceTree = "<group>"; };
		B489F756CBB488250AC94B05B84D66C6 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		B575BD8E16AAAB8FE3CE326F713BAD0B /* ZSSunlink.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSunlink.png; path = ZSSRichTextEditor/Source/Images/ZSSunlink.png; sourceTree = "<group>"; };
		B5C3AAD1FF47242B09A33D914CE9A172 /* AWSDDOSLogger.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSDDOSLogger.h; path = AWSCore/Logging/AWSDDOSLogger.h; sourceTree = "<group>"; };
		B7B72B9BFA15BA7916D51B6BAF4931ED /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		B7E7238C0AEE25E2932705C5DB26BD5C /* AWSModel.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSModel.m; path = AWSCore/Utility/AWSModel.m; sourceTree = "<group>"; };
		B7F581F8772EBEEC50F4C0F8F3D1DBF9 /* String+RegEx.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "String+RegEx.swift"; path = "WordPressEditor/WordPressEditor/Classes/Extensions/String+RegEx.swift"; sourceTree = "<group>"; };
		B85C045D7629FC0E880202F650B37877 /* GalleryElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GalleryElementConverter.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Calypso/GalleryShortcode/GalleryElementConverter.swift; sourceTree = "<group>"; };
		B86D8491319968018CDF9D79B418C700 /* html_colors.json */ = {isa = PBXFileReference; includeInIndex = 1; name = html_colors.json; path = Aztec/Assets/html_colors.json; sourceTree = "<group>"; };
		B87AFFFA09ACC44F701E260423559E51 /* WordPress-Aztec-iOS.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "WordPress-Aztec-iOS.modulemap"; sourceTree = "<group>"; };
		B897E25B523117B1B4F2B60DF273045F /* AWSDDTTYLogger.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSDDTTYLogger.h; path = AWSCore/Logging/AWSDDTTYLogger.h; sourceTree = "<group>"; };
		B89995A7AC4D608EA58571EA22CCBEAE /* CLinkedListToArrayConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CLinkedListToArrayConverter.swift; path = Aztec/Classes/Converters/HTMLToElements/CLinkedListToArrayConverter.swift; sourceTree = "<group>"; };
		B93713E9E814D5C3D4FC2578B1E8D053 /* Pods-SnapInspect3-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-SnapInspect3-frameworks.sh"; sourceTree = "<group>"; };
		B9A442ED36519EB4C4BAF837A19B5A49 /* CaptionShortcodeInputProcessor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionShortcodeInputProcessor.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Calypso/CaptionShortcode/CaptionShortcodeInputProcessor.swift; sourceTree = "<group>"; };
		B9DEA711B1F797284E5A9708F277020F /* CYRToken.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CYRToken.m; path = "ZSSRichTextEditor/Source/Third Party/CYRToken.m"; sourceTree = "<group>"; };
		B9ED883D190A2A182BAEEBF81746DF9D /* AWSSimpleDBModel.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSSimpleDBModel.h; path = AWSSimpleDB/AWSSimpleDBModel.h; sourceTree = "<group>"; };
		BA2197EC871A6323D44BB9675BFF43ED /* IQNSArray+Sort.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "IQNSArray+Sort.h"; path = "IQKeyboardManager/Categories/IQNSArray+Sort.h"; sourceTree = "<group>"; };
		BAA9AAE0FE730578FA248BAC637AC620 /* MASViewConstraint.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MASViewConstraint.m; path = Masonry/MASViewConstraint.m; sourceTree = "<group>"; };
		BB452189A6A2527BA2572E1FED9FD05F /* NSMutableAttributedString+ReplaceOcurrences.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSMutableAttributedString+ReplaceOcurrences.swift"; path = "Aztec/Classes/Extensions/NSMutableAttributedString+ReplaceOcurrences.swift"; sourceTree = "<group>"; };
		BBBB244FFE50AAE6DB1130886DF23032 /* CustomIOSAlertView.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = CustomIOSAlertView.release.xcconfig; sourceTree = "<group>"; };
		BBF0B4C8483D374CF913D7A32C3B4F38 /* IQTextView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = IQTextView.h; path = IQKeyboardManager/IQTextView/IQTextView.h; sourceTree = "<group>"; };
		BCD43C2F7D1E824EADF092422238B70C /* LineAttachmentToElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LineAttachmentToElementConverter.swift; path = Aztec/Classes/NSAttributedString/Conversions/AttachmentToElementConverter/LineAttachmentToElementConverter.swift; sourceTree = "<group>"; };
		BCF06F1A4556E293158A2D3427D56AAB /* NSLayoutManager+Attachments.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSLayoutManager+Attachments.swift"; path = "Aztec/Classes/Extensions/NSLayoutManager+Attachments.swift"; sourceTree = "<group>"; };
		BD036081E355A0A78E5C83461E07BACA /* View+MASAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "View+MASAdditions.h"; path = "Masonry/View+MASAdditions.h"; sourceTree = "<group>"; };
		BD7C9D5AB40262B95BB3FB53FAC06428 /* CSSAttributeType.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CSSAttributeType.swift; path = Aztec/Classes/Libxml2/DOM/Data/CSSAttributeType.swift; sourceTree = "<group>"; };
		BD82597FFB703C1EBA39C5D90AA148D9 /* ZSSSelectiveViewController.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ZSSSelectiveViewController.h; path = ZSSRichTextEditor/Classes/ZSSSelectiveViewController.h; sourceTree = "<group>"; };
		BDF2A6850655F9CC6A63DB78241F3978 /* ZSSh3.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSh3.png; path = ZSSRichTextEditor/Source/Images/ZSSh3.png; sourceTree = "<group>"; };
		BE3149C5151BA104A29FEE081968A56B /* CiteFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CiteFormatter.swift; path = Aztec/Classes/Formatters/Implementations/CiteFormatter.swift; sourceTree = "<group>"; };
		BE5BBF965B3752D8FF960CACBBF77D02 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework; sourceTree = DEVELOPER_DIR; };
		BEC2A893C33575FC31B14A52454E06E7 /* ZSSCustomButtonsViewController.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ZSSCustomButtonsViewController.m; path = ZSSRichTextEditor/Classes/ZSSCustomButtonsViewController.m; sourceTree = "<group>"; };
		BED37574F7251385DF55FAB4D8BA7D1C /* AWSS3Resources.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSS3Resources.m; path = AWSS3/AWSS3Resources.m; sourceTree = "<group>"; };
		BF341CD0B675238BFEC0443733F1BBD9 /* CommentNode+Gutenberg.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "CommentNode+Gutenberg.swift"; path = "WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Gutenberg/CommentNode+Gutenberg.swift"; sourceTree = "<group>"; };
		BF42115721F9D66BBA24B92B2CC207E8 /* ItalicFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ItalicFormatter.swift; path = Aztec/Classes/Formatters/Implementations/ItalicFormatter.swift; sourceTree = "<group>"; };
		BF848E21E7446262B39EC796C68C40E6 /* HTMLStyleToggler.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLStyleToggler.swift; path = Aztec/Classes/Converters/StringAttributesToAttributes/Utility/HTMLStyleToggler.swift; sourceTree = "<group>"; };
		BF9ADCE97343827849DC20CFDCA4F04D /* AttachmentElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AttachmentElementConverter.swift; path = Aztec/Classes/Converters/ElementsToAttributedString/Base/AttachmentElementConverter.swift; sourceTree = "<group>"; };
		BFA5D86CE146F831C977D29ADDAF27D9 /* ZSSFontsViewController.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ZSSFontsViewController.h; path = ZSSRichTextEditor/Source/ZSSFontsViewController.h; sourceTree = "<group>"; };
		BFFFC89A31DE183078A5145DB51BFE05 /* ItalicCSSAttributeMatcher.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ItalicCSSAttributeMatcher.swift; path = Aztec/Classes/Libxml2/DOM/Logic/CSS/ItalicCSSAttributeMatcher.swift; sourceTree = "<group>"; };
		C18B489BE2B5ADD61B238F6DB866EA23 /* AWSTMDiskCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSTMDiskCache.m; path = AWSCore/TMCache/AWSTMDiskCache.m; sourceTree = "<group>"; };
		C1B424D9A4ED7DC7AF7B7C7B262C16D0 /* IQKeyboardManager.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardManager.release.xcconfig; sourceTree = "<group>"; };
		C209BE1B2AC0B2E433FBC807B789B8D7 /* HTMLPre.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLPre.swift; path = Aztec/Classes/TextKit/ParagraphProperty/HTMLPre.swift; sourceTree = "<group>"; };
		C23576EE4D80E4F01F94FF878DBA4BF2 /* AWSGZIP.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSGZIP.h; path = AWSCore/GZIP/AWSGZIP.h; sourceTree = "<group>"; };
		C33AD5851F6175CE30C9A55A7BEA503D /* OptionsTableViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = OptionsTableViewController.swift; path = WordPressEditor/WordPressEditor/Classes/ViewControllers/OptionsTableViewController/OptionsTableViewController.swift; sourceTree = "<group>"; };
		C375E2FE5A4C3C599F3B0482E623243B /* FontFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FontFormatter.swift; path = Aztec/Classes/Formatters/Base/FontFormatter.swift; sourceTree = "<group>"; };
		C417EB24EA443F14B2EF5F35E63FA887 /* WordPress-Aztec-iOS-WordPress-Aztec-iOS */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "WordPress-Aztec-iOS-WordPress-Aztec-iOS"; path = "WordPress-Aztec-iOS.bundle"; sourceTree = BUILT_PRODUCTS_DIR; };
		C4C9485AEB65EFA224A8882C58E5D0F2 /* AWSSTSService.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSSTSService.h; path = AWSCore/STS/AWSSTSService.h; sourceTree = "<group>"; };
		C4EAAC03AA682321088C5C7E119902F4 /* UITextView+Placeholder.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "UITextView+Placeholder.debug.xcconfig"; sourceTree = "<group>"; };
		C4EBDCB812DE082CC1D8A443279C1006 /* AWSCore-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AWSCore-umbrella.h"; sourceTree = "<group>"; };
		C5198901E04E501A833F0FF49F1EAFAE /* GutenpackConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GutenpackConverter.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Gutenberg/GutenpackConverter.swift; sourceTree = "<group>"; };
		C55E09C0A9B0FD2471683DEAF395BBBB /* NSObject+AWSMTLComparisonAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSObject+AWSMTLComparisonAdditions.m"; path = "AWSCore/Mantle/NSObject+AWSMTLComparisonAdditions.m"; sourceTree = "<group>"; };
		C5C7E5DECF3D9295CDBE42349FB9366E /* AWSClientContext.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSClientContext.h; path = AWSCore/Service/AWSClientContext.h; sourceTree = "<group>"; };
		C5D43DBF89C84CEF2712E850CD1FA4DC /* FormatBarItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FormatBarItem.swift; path = Aztec/Classes/GUI/FormatBar/FormatBarItem.swift; sourceTree = "<group>"; };
		C704F55FD3FC1E0B66E7F3AAD1B60B07 /* WordPress-Aztec-iOS.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "WordPress-Aztec-iOS.release.xcconfig"; sourceTree = "<group>"; };
		C76A3AEEC5AE69CF8633AE1E1ADFCB68 /* ZSSbgcolor.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSbgcolor.png; path = ZSSRichTextEditor/Source/Images/ZSSbgcolor.png; sourceTree = "<group>"; };
		C77AD6683490ADE50CAFE73BC1BB0995 /* AWSNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSNetworking.h; path = AWSCore/Networking/AWSNetworking.h; sourceTree = "<group>"; };
		C7A329294BBA948213A73A6CDFB4EC53 /* NSAttributedString+CharacterName.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSAttributedString+CharacterName.swift"; path = "Aztec/Classes/Extensions/NSAttributedString+CharacterName.swift"; sourceTree = "<group>"; };
		C7B6DB1429448F12BC797DA3929048F1 /* Masonry.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Masonry.debug.xcconfig; sourceTree = "<group>"; };
		C8F6CBB1764E09A11791565EA0D7FC7A /* GutenpackAttachment.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GutenpackAttachment.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Gutenberg/GutenpackAttachment.swift; sourceTree = "<group>"; };
		C9A8EB2E724C516C03CF1440E39D450D /* FABKitProtocol.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FABKitProtocol.h; path = AWSCore/Fabric/FABKitProtocol.h; sourceTree = "<group>"; };
		C9AFB7272C30CA1C4B4A415161C2D87E /* ZSSredo.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSredo.png; path = ZSSRichTextEditor/Source/Images/ZSSredo.png; sourceTree = "<group>"; };
		C9F5126C0EC1ED09C4BB52767F9799FF /* VideoElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = VideoElementConverter.swift; path = Aztec/Classes/Converters/ElementsToAttributedString/Implementations/VideoElementConverter.swift; sourceTree = "<group>"; };
		CA4B55324018AE6F3874D130E483F4BB /* HTMLParser.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLParser.swift; path = Aztec/Classes/Libxml2/Converters/In/HTMLParser.swift; sourceTree = "<group>"; };
		CBE29738EE0127A981F04E41AAF8DAC9 /* IQToolbar.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = IQToolbar.h; path = IQKeyboardManager/IQToolbar/IQToolbar.h; sourceTree = "<group>"; };
		CC14EA37003C9F9EDDFAB2B4D8522780 /* AWSDDASLLogger.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSDDASLLogger.h; path = AWSCore/Logging/AWSDDASLLogger.h; sourceTree = "<group>"; };
		CC16870CD931C70EED97EFDEB5C7135F /* NSTextingResult+Helpers.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSTextingResult+Helpers.swift"; path = "Aztec/Classes/Extensions/NSTextingResult+Helpers.swift"; sourceTree = "<group>"; };
		CC301C3181DDEFDAA75801B80996AB23 /* GutenbergAttributeEncoder.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GutenbergAttributeEncoder.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Gutenberg/GutenbergAttributeEncoder.swift; sourceTree = "<group>"; };
		CCFB8F03867DB892EFC47B53186590C4 /* AWSCore */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = AWSCore; path = AWSCore.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		CDD7A04EDE672FB8DF3E2F05A8272A51 /* AWSS3PreSignedURL.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSS3PreSignedURL.m; path = AWSS3/AWSS3PreSignedURL.m; sourceTree = "<group>"; };
		CDFB470C5D11C6BC6069B4E0C3C07CF3 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		CDFF82EA0C1F246765CD4A311891D7C3 /* Pods-SnapInspect3Tests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-SnapInspect3Tests.release.xcconfig"; sourceTree = "<group>"; };
		CF23F6F37818B43A7807B1850D91DB37 /* String+RangeConversion.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "String+RangeConversion.swift"; path = "Aztec/Classes/Extensions/String+RangeConversion.swift"; sourceTree = "<group>"; };
		CF292F57BA682294ED768150923FB82F /* CodeFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CodeFormatter.swift; path = Aztec/Classes/Formatters/Implementations/CodeFormatter.swift; sourceTree = "<group>"; };
		CF4ECA173328DA930216D26C29718794 /* GalleryAttachmentToElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GalleryAttachmentToElementConverter.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Calypso/GalleryShortcode/GalleryAttachmentToElementConverter.swift; sourceTree = "<group>"; };
		CF54D9BACDC044506D6F68DBBC66BA63 /* ZSStextcolor.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSStextcolor.png; path = ZSSRichTextEditor/Source/Images/ZSStextcolor.png; sourceTree = "<group>"; };
		CF5D36114A8AB10D32B0D58F5713DEBA /* Node.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Node.swift; path = Aztec/Classes/Libxml2/DOM/Data/Node.swift; sourceTree = "<group>"; };
		CF70592D5CE0FE6B21AF87DDB648F76E /* Plugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Plugin.swift; path = Aztec/Classes/Plugin/Plugin.swift; sourceTree = "<group>"; };
		CFE6C1F0ACD53B397DC9998112E1C7EC /* AWSSTSResources.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSSTSResources.m; path = AWSCore/STS/AWSSTSResources.m; sourceTree = "<group>"; };
		D04C1B1DB16ACC78AC9EE2605D85D852 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		D0C08AE8BA5A4F1D594434167187487E /* UIPasteboard+Helpers.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIPasteboard+Helpers.swift"; path = "Aztec/Classes/Extensions/UIPasteboard+Helpers.swift"; sourceTree = "<group>"; };
		D1450B441CC78E73C0A765A7E605DD4C /* ZSSRichTextEditor-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "ZSSRichTextEditor-umbrella.h"; sourceTree = "<group>"; };
		D1732181FE21DAEBF85DD3ADB8B88188 /* AWSDDFileLogger.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSDDFileLogger.m; path = AWSCore/Logging/AWSDDFileLogger.m; sourceTree = "<group>"; };
		D219E668629EAE6376E222689F80645D /* AWSS3.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = AWSS3.modulemap; sourceTree = "<group>"; };
		D221ABA24362788B04C38371DC7CBC09 /* AWSIdentityProvider.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSIdentityProvider.h; path = AWSCore/Authentication/AWSIdentityProvider.h; sourceTree = "<group>"; };
		D26F2354B5672DDDB6EFE846E71C36EC /* AWSCore.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = AWSCore.modulemap; sourceTree = "<group>"; };
		D36AE816EC31BEAC9AB9DB9EA2B49AFE /* AWSSynchronizedMutableDictionary.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSSynchronizedMutableDictionary.h; path = AWSCore/Utility/AWSSynchronizedMutableDictionary.h; sourceTree = "<group>"; };
		D378FA8A15146EB2BD288F751FF479F0 /* SuperscriptStringAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SuperscriptStringAttributeConverter.swift; path = Aztec/Classes/Converters/StringAttributesToAttributes/Implementations/SuperscriptStringAttributeConverter.swift; sourceTree = "<group>"; };
		D38D8B63299065FBB13B66182BCB12B2 /* ZSSundo.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSundo.png; path = ZSSRichTextEditor/Source/Images/ZSSundo.png; sourceTree = "<group>"; };
		D3B39C2207688A9CED3631CC39C87B3E /* Pods-SnapInspect3-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-SnapInspect3-Info.plist"; sourceTree = "<group>"; };
		D3B7410F56195D34BA6FA4341CD76FEA /* AWSDDDispatchQueueLogFormatter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSDDDispatchQueueLogFormatter.h; path = AWSCore/Logging/Extensions/AWSDDDispatchQueueLogFormatter.h; sourceTree = "<group>"; };
		D47AE08537AEA4D6E9057597D2BB8FC7 /* HTMLDivFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLDivFormatter.swift; path = Aztec/Classes/Formatters/Implementations/HTMLDivFormatter.swift; sourceTree = "<group>"; };
		D4ECEE9567271A5B94C7B83FE70ABF5F /* NSLayoutConstraint+MASDebugAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSLayoutConstraint+MASDebugAdditions.m"; path = "Masonry/NSLayoutConstraint+MASDebugAdditions.m"; sourceTree = "<group>"; };
		D51983E998BEB28DC943C1579C4948C9 /* NSAttributedString+Attachments.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSAttributedString+Attachments.swift"; path = "Aztec/Classes/Extensions/NSAttributedString+Attachments.swift"; sourceTree = "<group>"; };
		D6081B9B734C533F2D1A836FEE841432 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		D636AA42F89F893F58FDECE85FC1FBFF /* String+Paragraph.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "String+Paragraph.swift"; path = "Aztec/Classes/Extensions/String+Paragraph.swift"; sourceTree = "<group>"; };
		D73296DC00FB984B0D4CCBA98FEEB245 /* GenericElementToTagConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GenericElementToTagConverter.swift; path = Aztec/Classes/Converters/ElementsToHTML/Implementations/GenericElementToTagConverter.swift; sourceTree = "<group>"; };
		D7556B2EB464D0DE8451B690BBFB5BB3 /* CustomIOSAlertView */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = CustomIOSAlertView; path = CustomIOSAlertView.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		D7748D5F8C9CABE568A6A1AAB5F7FF5E /* GutenbergOutputHTMLTreeProcessor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GutenbergOutputHTMLTreeProcessor.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Gutenberg/GutenbergOutputHTMLTreeProcessor.swift; sourceTree = "<group>"; };
		D77752F458417B3BEDD1EF059DEB14E1 /* IQUIView+Hierarchy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "IQUIView+Hierarchy.m"; path = "IQKeyboardManager/Categories/IQUIView+Hierarchy.m"; sourceTree = "<group>"; };
		D78E6A5586279CEE4056F8FEA4754460 /* IQUIViewController+Additions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "IQUIViewController+Additions.m"; path = "IQKeyboardManager/Categories/IQUIViewController+Additions.m"; sourceTree = "<group>"; };
		D78EA46DC425CA54AC914AA35DB7AC9E /* AWSSimpleDB */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = AWSSimpleDB; path = AWSSimpleDB.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		D7B8CB609AB33A42406F276FE6C1D1A9 /* GutenblockConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GutenblockConverter.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Gutenberg/GutenblockConverter.swift; sourceTree = "<group>"; };
		D7E3B4F930F504C5E1706A478ED2942C /* AWSFMDatabase.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSFMDatabase.h; path = AWSCore/FMDB/AWSFMDatabase.h; sourceTree = "<group>"; };
		D80FD5776C34FB276B14EA221751D4D9 /* WordPress-Editor-iOS.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "WordPress-Editor-iOS.debug.xcconfig"; sourceTree = "<group>"; };
		D819C408A8ADCF07B67904A8D572502E /* AWSS3TransferUtility+Validation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "AWSS3TransferUtility+Validation.m"; path = "AWSS3/AWSS3TransferUtility+Validation.m"; sourceTree = "<group>"; };
		D8685D6C7C6BCE54476678671D6808F8 /* CommentNode.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CommentNode.swift; path = Aztec/Classes/Libxml2/DOM/Data/CommentNode.swift; sourceTree = "<group>"; };
		D93F207F5E4A55F70E904D3350E47A2C /* UnderlineStringAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = UnderlineStringAttributeConverter.swift; path = Aztec/Classes/Converters/StringAttributesToAttributes/Implementations/UnderlineStringAttributeConverter.swift; sourceTree = "<group>"; };
		D99737B2BC8F7D0892BF7F1DA1F200B7 /* AWSCancellationToken.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSCancellationToken.h; path = AWSCore/Bolts/AWSCancellationToken.h; sourceTree = "<group>"; };
		D9A2F0252AC9448237DEEFA677853A89 /* GutenbergInputHTMLTreeProcessor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GutenbergInputHTMLTreeProcessor.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Gutenberg/GutenbergInputHTMLTreeProcessor.swift; sourceTree = "<group>"; };
		D9B1729EEBD0ADC4FF2C2C1769353F34 /* NSObject+AWSMTLComparisonAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSObject+AWSMTLComparisonAdditions.h"; path = "AWSCore/Mantle/NSObject+AWSMTLComparisonAdditions.h"; sourceTree = "<group>"; };
		DA37F4A41BB30CADBA20FCB9F226E9EE /* AWSXMLDictionary.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSXMLDictionary.h; path = AWSCore/XMLDictionary/AWSXMLDictionary.h; sourceTree = "<group>"; };
		DA54B32D41AF298D6558E6BDAB0C33D0 /* ZSSbold.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSbold.png; path = ZSSRichTextEditor/Source/Images/ZSSbold.png; sourceTree = "<group>"; };
		DAC39BB747BE315892883EC173D22E09 /* NSAttributedStringKey+Aztec.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSAttributedStringKey+Aztec.swift"; path = "Aztec/Classes/Extensions/NSAttributedStringKey+Aztec.swift"; sourceTree = "<group>"; };
		DAD1927B144306FD16FE78AB6F83CFA9 /* AWSFMDatabasePool.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSFMDatabasePool.m; path = AWSCore/FMDB/AWSFMDatabasePool.m; sourceTree = "<group>"; };
		DB11D3FD6BC33D77BB6D82183398392E /* Pods-SnapInspect3-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-SnapInspect3-umbrella.h"; sourceTree = "<group>"; };
		DB1DD27CB50948A64DB1B8372CC800B5 /* AWSFMResultSet.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSFMResultSet.h; path = AWSCore/FMDB/AWSFMResultSet.h; sourceTree = "<group>"; };
		DB2A8C3E7BD092724C9D127FB2EC2647 /* MASViewAttribute.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MASViewAttribute.h; path = Masonry/MASViewAttribute.h; sourceTree = "<group>"; };
		DBAB142ECACF1C5340488046EBD3E9E6 /* ZSSindent.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSindent.png; path = ZSSRichTextEditor/Source/Images/ZSSindent.png; sourceTree = "<group>"; };
		DC95FC2B3239D664802460A2BAD3E045 /* WordPressPasteboardDelegate.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = WordPressPasteboardDelegate.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Calypso/Embeds/WordPressPasteboardDelegate.swift; sourceTree = "<group>"; };
		DCC41F38A7F148F05A397CA73A7D9390 /* IQUIScrollView+Additions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "IQUIScrollView+Additions.m"; path = "IQKeyboardManager/Categories/IQUIScrollView+Additions.m"; sourceTree = "<group>"; };
		DCC664C99DDBB480FFEC29AB06B29BA8 /* NSLayoutConstraint+MASDebugAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSLayoutConstraint+MASDebugAdditions.h"; path = "Masonry/NSLayoutConstraint+MASDebugAdditions.h"; sourceTree = "<group>"; };
		DCCC8562DCFD1E3FCC9A183247D40ED8 /* AWSS3PreSignedURL.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSS3PreSignedURL.h; path = AWSS3/AWSS3PreSignedURL.h; sourceTree = "<group>"; };
		DD9025ADA9F348524356867F4D7C2AF2 /* GalleryAttachment.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GalleryAttachment.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Calypso/GalleryShortcode/GalleryAttachment.swift; sourceTree = "<group>"; };
		DE1E0AD4D0ECB835EC6ACBC70FEE7A14 /* ElementToStringConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ElementToStringConverter.swift; path = Aztec/Classes/Converters/ElementsToHTML/Base/ElementToStringConverter.swift; sourceTree = "<group>"; };
		DE346AB1823BC03915BA30442AAF2579 /* CustomIOSAlertView.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = CustomIOSAlertView.modulemap; sourceTree = "<group>"; };
		DE3C256959ECE4A37854A454867350F5 /* AWSFMResultSet.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSFMResultSet.m; path = AWSCore/FMDB/AWSFMResultSet.m; sourceTree = "<group>"; };
		DED96BA25C711FD0BF237D2D210D7FBE /* FigcaptionElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FigcaptionElementConverter.swift; path = Aztec/Classes/Converters/ElementsToAttributedString/Implementations/FigcaptionElementConverter.swift; sourceTree = "<group>"; };
		DF5C99563404E05B7CED21B339F1E03F /* AWSS3RequestRetryHandler.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSS3RequestRetryHandler.m; path = AWSS3/AWSS3RequestRetryHandler.m; sourceTree = "<group>"; };
		DF7B55685454B1762A22CEC8D0310AE9 /* MASConstraint+Private.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "MASConstraint+Private.h"; path = "Masonry/MASConstraint+Private.h"; sourceTree = "<group>"; };
		DFE9FB38AFE4807ADF098632397DFE68 /* AWSURLResponseSerialization.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSURLResponseSerialization.h; path = AWSCore/Serialization/AWSURLResponseSerialization.h; sourceTree = "<group>"; };
		E00A223B033023252D3906B75835325B /* NSArray+AWSMTLManipulationAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSArray+AWSMTLManipulationAdditions.h"; path = "AWSCore/Mantle/NSArray+AWSMTLManipulationAdditions.h"; sourceTree = "<group>"; };
		E12AF7C977258FF6635EF3A52CD63EE3 /* BoldFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BoldFormatter.swift; path = Aztec/Classes/Formatters/Implementations/BoldFormatter.swift; sourceTree = "<group>"; };
		E1318762F8470F95594D57AD21E65A92 /* AWSS3Model.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSS3Model.m; path = AWSS3/AWSS3Model.m; sourceTree = "<group>"; };
		E1A9BAB022FAC5F5CFF7C98848DABF20 /* Configuration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Configuration.swift; path = Aztec/Classes/TextKit/Configuration.swift; sourceTree = "<group>"; };
		E230B3086A0FF4B1388CDFEFEDE3532B /* MarkStringAttributeConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MarkStringAttributeConverter.swift; path = Aztec/Classes/Converters/StringAttributesToAttributes/Implementations/MarkStringAttributeConverter.swift; sourceTree = "<group>"; };
		E24AE983ACB78BFC1BD2C78449EC4489 /* AWSSimpleDB-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "AWSSimpleDB-dummy.m"; sourceTree = "<group>"; };
		E26EACC90EF23460C230D65A5D55CE9E /* MASUtilities.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MASUtilities.h; path = Masonry/MASUtilities.h; sourceTree = "<group>"; };
		E2982D83493A9251C7C6BE0F4CACEBE6 /* NSValueTransformer+AWSMTLPredefinedTransformerAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSValueTransformer+AWSMTLPredefinedTransformerAdditions.m"; path = "AWSCore/Mantle/NSValueTransformer+AWSMTLPredefinedTransformerAdditions.m"; sourceTree = "<group>"; };
		E327696F96EDCFE45B8D2E2A5A106720 /* AWSCore-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AWSCore-prefix.pch"; sourceTree = "<group>"; };
		E350DD445214158E2DC092E17E7ECD29 /* ResourceBundle-WordPress-Aztec-iOS-WordPress-Aztec-iOS-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-WordPress-Aztec-iOS-WordPress-Aztec-iOS-Info.plist"; sourceTree = "<group>"; };
		E37011BCFBBA8A6BD4F0338BDB01D86C /* UITextView+Placeholder-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "UITextView+Placeholder-prefix.pch"; sourceTree = "<group>"; };
		E41F24F034D5ECC74CC512127BB37461 /* ZSShorizontalrule.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSShorizontalrule.png; path = ZSSRichTextEditor/Source/Images/ZSShorizontalrule.png; sourceTree = "<group>"; };
		E432BD589A2FCE4F28C251DD1FD55074 /* AWSCocoaLumberjack.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSCocoaLumberjack.h; path = AWSCore/Logging/AWSCocoaLumberjack.h; sourceTree = "<group>"; };
		E465246DA8193C8320E29AA4CDB2BC23 /* AWSCancellationTokenSource.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSCancellationTokenSource.m; path = AWSCore/Bolts/AWSCancellationTokenSource.m; sourceTree = "<group>"; };
		E4EF326ED9DB7B756E1EBC7A70FE23E1 /* Pods-SnapInspect3Tests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-SnapInspect3Tests-umbrella.h"; sourceTree = "<group>"; };
		E526BBF21BFA988799270209C8BA8A14 /* AWSS3-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "AWSS3-Info.plist"; sourceTree = "<group>"; };
		E5285A404596A0935B0BA6597DB3A47D /* IQTextView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = IQTextView.m; path = IQKeyboardManager/IQTextView/IQTextView.m; sourceTree = "<group>"; };
		E5A10819C69BFCFC90328EE0CA5F6342 /* Fabric.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = Fabric.h; path = AWSCore/Fabric/Fabric.h; sourceTree = "<group>"; };
		E5B3325338CB083CF942BBE997C46E02 /* AWSS3TransferUtility+HeaderHelper.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "AWSS3TransferUtility+HeaderHelper.h"; path = "AWSS3/AWSS3TransferUtility+HeaderHelper.h"; sourceTree = "<group>"; };
		E65FB0FDBC463303A34DD96661351FA1 /* BoldWithShadowForHeadingFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BoldWithShadowForHeadingFormatter.swift; path = Aztec/Classes/Formatters/Implementations/BoldWithShadowForHeadingFormatter.swift; sourceTree = "<group>"; };
		E66718A179DA93FA5F965387502D0DCD /* LinkFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LinkFormatter.swift; path = Aztec/Classes/Formatters/Implementations/LinkFormatter.swift; sourceTree = "<group>"; };
		E6D6733F786B5D3F02F4DFCBF4E02370 /* NSArray+AWSMTLManipulationAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSArray+AWSMTLManipulationAdditions.m"; path = "AWSCore/Mantle/NSArray+AWSMTLManipulationAdditions.m"; sourceTree = "<group>"; };
		E70598D921E3E2ECC5DAC54D91C51FB3 /* ViewController+MASAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "ViewController+MASAdditions.m"; path = "Masonry/ViewController+MASAdditions.m"; sourceTree = "<group>"; };
		E72D7893E3009E211340A2B9B5A03ADD /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		E75FEFF63044BB7DA3F65090089C66AA /* Masonry-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Masonry-umbrella.h"; sourceTree = "<group>"; };
		E77ABE55BFAE11F4FC7355018A66637A /* ZSSunorderedlist.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSunorderedlist.png; path = ZSSRichTextEditor/Source/Images/ZSSunorderedlist.png; sourceTree = "<group>"; };
		E7F57C8FBBEA23A5D7F81300162CCB70 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		E810F8340D1D7BE3CB183C7BD2288F34 /* AWSCore.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AWSCore.debug.xcconfig; sourceTree = "<group>"; };
		E97BCF1D169A5A1D63095CD780C88282 /* AWSFMDB.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSFMDB.h; path = AWSCore/FMDB/AWSFMDB.h; sourceTree = "<group>"; };
		EABD0A4FAE87DDEB84E8CE005A706B75 /* UITextView+Placeholder-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "UITextView+Placeholder-Info.plist"; sourceTree = "<group>"; };
		EAE9C430705DCB6836D4E92429017830 /* HTMLAttachmentToElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLAttachmentToElementConverter.swift; path = Aztec/Classes/NSAttributedString/Conversions/AttachmentToElementConverter/HTMLAttachmentToElementConverter.swift; sourceTree = "<group>"; };
		EB057E231D1002C7FB222DF42A01C3DC /* AttributedStringSerializer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AttributedStringSerializer.swift; path = Aztec/Classes/NSAttributedString/Conversions/AttributedStringSerializer.swift; sourceTree = "<group>"; };
		EB490F0B68D427E72F1F760FEA34DFCA /* AttributeType.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AttributeType.swift; path = Aztec/Classes/Libxml2/DOM/Data/AttributeType.swift; sourceTree = "<group>"; };
		EBCDCAE8871EBCE71CEA84C7A443F151 /* TextViewPasteboardDelegate.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TextViewPasteboardDelegate.swift; path = Aztec/Classes/TextKit/TextViewPasteboardDelegate.swift; sourceTree = "<group>"; };
		EBD4716179247B892B0408640A886585 /* Assets.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Assets.swift; path = Aztec/Classes/GUI/Assets.swift; sourceTree = "<group>"; };
		EC410C1A7CBE4A0BAF57504964554143 /* CYRTextView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CYRTextView.m; path = "ZSSRichTextEditor/Source/Third Party/CYRTextView.m"; sourceTree = "<group>"; };
		EC8E359082632DD368135B02AAF7E8DA /* AWSMTLJSONAdapter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSMTLJSONAdapter.m; path = AWSCore/Mantle/AWSMTLJSONAdapter.m; sourceTree = "<group>"; };
		ED742648F42A0FF1C7C3BFD356543434 /* UIFont+Traits.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIFont+Traits.swift"; path = "Aztec/Classes/Extensions/UIFont+Traits.swift"; sourceTree = "<group>"; };
		EEF7C1919374A21C682A1FBC0CA362EA /* AWSLogging.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSLogging.h; path = AWSCore/Utility/AWSLogging.h; sourceTree = "<group>"; };
		EF9A7B350CD6915D385FC0B851BEB0F8 /* EditorView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorView.swift; path = Aztec/Classes/EditorView/EditorView.swift; sourceTree = "<group>"; };
		EFA116471150875E379A82E2A8C98597 /* AWSMTLJSONAdapter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSMTLJSONAdapter.h; path = AWSCore/Mantle/AWSMTLJSONAdapter.h; sourceTree = "<group>"; };
		F05BAE02252D74D7B681179D52313356 /* ZSSRichTextEditor.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = ZSSRichTextEditor.debug.xcconfig; sourceTree = "<group>"; };
		F0C7FE88342E3772A8A133204A1E1910 /* UITextView+Placeholder */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "UITextView+Placeholder"; path = UITextView_Placeholder.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		F10A4882050CF12A800FCBE09E4A11FD /* String+EndOfLine.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "String+EndOfLine.swift"; path = "Aztec/Classes/Extensions/String+EndOfLine.swift"; sourceTree = "<group>"; };
		F18CFB4577F1D7DFE58D9743FE984C62 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		F1E1C31E6F9833AF2280F8283372F395 /* Pods-SnapInspect3.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-SnapInspect3.debug.xcconfig"; sourceTree = "<group>"; };
		F2081AE1A1284F945259F1A770E34889 /* CSSParser.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CSSParser.swift; path = Aztec/Classes/Libxml2/Converters/In/CSSParser.swift; sourceTree = "<group>"; };
		F216A56A86E2A5A208E098F355013AD0 /* Masonry.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Masonry.release.xcconfig; sourceTree = "<group>"; };
		F2B1DE607CE9A45D4DBF9A57065CF354 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		F43D81C739F6CFA1BC5B2E3863D24BFB /* AWSMTLManagedObjectAdapter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSMTLManagedObjectAdapter.m; path = AWSCore/Mantle/AWSMTLManagedObjectAdapter.m; sourceTree = "<group>"; };
		F459000FF37B6A661E5CCB88B45753C0 /* AWSTask.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSTask.h; path = AWSCore/Bolts/AWSTask.h; sourceTree = "<group>"; };
		F4A5BFA29695EAA8C8F0BB2BCC5A29E9 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		F564312154567EEF4E7EC72E5C9F4573 /* ViewController+MASAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "ViewController+MASAdditions.h"; path = "Masonry/ViewController+MASAdditions.h"; sourceTree = "<group>"; };
		F5772291CE124DEDB5DDEE211525061C /* AWSDDASLLogCapture.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSDDASLLogCapture.m; path = AWSCore/Logging/AWSDDASLLogCapture.m; sourceTree = "<group>"; };
		F59A5756411A47B93814FAFDDA43BE59 /* HTMLAttachment.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLAttachment.swift; path = Aztec/Classes/TextKit/HTMLAttachment.swift; sourceTree = "<group>"; };
		F5DC4F3600A2A49E4A906226A7A5E292 /* IQUIViewController+Additions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "IQUIViewController+Additions.h"; path = "IQKeyboardManager/Categories/IQUIViewController+Additions.h"; sourceTree = "<group>"; };
		F6156E84C45AB51D43FAC904D18A8A03 /* ZSSparagraph.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSparagraph.png; path = ZSSRichTextEditor/Source/Images/ZSSparagraph.png; sourceTree = "<group>"; };
		F799409FBBBFFF3DBB27A1413D712D74 /* Pods-SnapInspect3-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-SnapInspect3-acknowledgements.plist"; sourceTree = "<group>"; };
		F81DA223794E75F47D653205636DE507 /* AWSBolts.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSBolts.h; path = AWSCore/Bolts/AWSBolts.h; sourceTree = "<group>"; };
		F8341D1E0EBD0BCE67F8476689B03490 /* AWSGZIP.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSGZIP.m; path = AWSCore/GZIP/AWSGZIP.m; sourceTree = "<group>"; };
		F847C9C848B73C5FF8634045B6DB557B /* HRColorPickerViewController.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = HRColorPickerViewController.h; path = "ZSSRichTextEditor/Source/Third Party/HRColorPickerViewController.h"; sourceTree = "<group>"; };
		F888173F09CEEAB4493DD473DB3B7465 /* AWSSimpleDB.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSSimpleDB.h; path = AWSSimpleDB/AWSSimpleDB.h; sourceTree = "<group>"; };
		F8DB57B22242369D57372EBC7E718895 /* InNodesConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = InNodesConverter.swift; path = Aztec/Classes/Libxml2/Converters/In/InNodesConverter.swift; sourceTree = "<group>"; };
		F8F5E8BED95199D4F7A100BE19F99C1D /* LayoutManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LayoutManager.swift; path = Aztec/Classes/TextKit/LayoutManager.swift; sourceTree = "<group>"; };
		F92F459EC99C7CC257CEF2291D790715 /* IQKeyboardManagerConstantsInternal.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = IQKeyboardManagerConstantsInternal.h; path = IQKeyboardManager/Constants/IQKeyboardManagerConstantsInternal.h; sourceTree = "<group>"; };
		F96508A77801158BFFDF20B1046D8011 /* GutenbergAttributeNames.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GutenbergAttributeNames.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Gutenberg/GutenbergAttributeNames.swift; sourceTree = "<group>"; };
		F969559232E7B06DB96ECC88B5291E47 /* AWSS3-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "AWSS3-dummy.m"; sourceTree = "<group>"; };
		F975BDE7BFE498FFEAD0E12DEC01670B /* EmbedURLProcessor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EmbedURLProcessor.swift; path = WordPressEditor/WordPressEditor/Classes/Processors/EmbedURLProcessor.swift; sourceTree = "<group>"; };
		F97BCD847DC357F3DCC362F08C9F3B97 /* AWSSTSService.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSSTSService.m; path = AWSCore/STS/AWSSTSService.m; sourceTree = "<group>"; };
		F9C7BD95E4E51F2DD088F0C2452EFFF5 /* LiFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LiFormatter.swift; path = Aztec/Classes/Formatters/Implementations/LiFormatter.swift; sourceTree = "<group>"; };
		FA7B0C8D1975D6F2E9C29EBBA0284F1E /* AWSSignature.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSSignature.m; path = AWSCore/Authentication/AWSSignature.m; sourceTree = "<group>"; };
		FA93A6B40EC8EE06F9D01F4581D01807 /* ZSSpicker.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSpicker.png; path = ZSSRichTextEditor/Source/Images/ZSSpicker.png; sourceTree = "<group>"; };
		FAC0EB99E32B88789CC8AAF1824B23E8 /* ZSSviewsource.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSviewsource.png; path = ZSSRichTextEditor/Source/Images/ZSSviewsource.png; sourceTree = "<group>"; };
		FAE342899099CA96E4C99DF48AD45A7F /* AWSS3.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AWSS3.release.xcconfig; sourceTree = "<group>"; };
		FB0A191EB4E8D3F2D8E3BEECDBB30F34 /* CSSAttributeMatcher.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CSSAttributeMatcher.swift; path = Aztec/Classes/Libxml2/DOM/Logic/CSS/CSSAttributeMatcher.swift; sourceTree = "<group>"; };
		FB65446C53D6966FB1D3B7C22CEDEFBC /* ZSSRichTextEditor.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = ZSSRichTextEditor.modulemap; sourceTree = "<group>"; };
		FB8D412F745D7E32C26BB00D411F1CFB /* CYRToken.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CYRToken.h; path = "ZSSRichTextEditor/Source/Third Party/CYRToken.h"; sourceTree = "<group>"; };
		FB93B472172D5A1221F4A68802398F34 /* HTMLProcessor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTMLProcessor.swift; path = Aztec/Classes/Processor/HTMLProcessor.swift; sourceTree = "<group>"; };
		FBF10A99698398BB3B52F654632C0D19 /* AWSXMLDictionary.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AWSXMLDictionary.m; path = AWSCore/XMLDictionary/AWSXMLDictionary.m; sourceTree = "<group>"; };
		FBF57D77F6B3B7389719A0285F52CD9D /* BlockquoteFormatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BlockquoteFormatter.swift; path = Aztec/Classes/Formatters/Implementations/BlockquoteFormatter.swift; sourceTree = "<group>"; };
		FC605FFBB3DE7BA7F708C726F71575B8 /* AWSCognitoIdentityService.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSCognitoIdentityService.h; path = AWSCore/CognitoIdentity/AWSCognitoIdentityService.h; sourceTree = "<group>"; };
		FC785106A32817B914FFF26BDEF1DB3F /* AWSmetamacros.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AWSmetamacros.h; path = AWSCore/Mantle/extobjc/AWSmetamacros.h; sourceTree = "<group>"; };
		FCAE400E9F687984A707BDDCCC6578F2 /* IQKeyboardManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = IQKeyboardManager.m; path = IQKeyboardManager/IQKeyboardManager.m; sourceTree = "<group>"; };
		FCEB3C046E5E247626A1DA623B152DF0 /* <EMAIL> */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = "<EMAIL>"; path = "ZSSRichTextEditor/Source/Images/<EMAIL>"; sourceTree = "<group>"; };
		FD2E72D0432611CE3DBB82A7118D6C2B /* ZSSitalic.png */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = image.png; name = ZSSitalic.png; path = ZSSRichTextEditor/Source/Images/ZSSitalic.png; sourceTree = "<group>"; };
		FD649B02DEFCC16A5EBA3842C6C27CA0 /* AWSFMDatabase+Private.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "AWSFMDatabase+Private.h"; path = "AWSCore/FMDB/AWSFMDatabase+Private.h"; sourceTree = "<group>"; };
		FE9C3C3DCCAF9CE6C6A54DA80A656949 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/QuartzCore.framework; sourceTree = DEVELOPER_DIR; };
		FEC8C967B96E7DF9B00007197E3426B9 /* VideoAttachmentToElementConverter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = VideoAttachmentToElementConverter.swift; path = Aztec/Classes/NSAttributedString/Conversions/AttachmentToElementConverter/VideoAttachmentToElementConverter.swift; sourceTree = "<group>"; };
		FF81762FE99A9978A8B94631E05653D7 /* AutoPProcessor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AutoPProcessor.swift; path = WordPressEditor/WordPressEditor/Classes/Plugins/WordPressPlugin/Calypso/AutopRemovep/AutoPProcessor.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		12A799DC8ABB2C283ADDDED4421A5EAB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D663837F4347AF58660EE6F7FD426ECE /* Foundation.framework in Frameworks */,
				4571A0EA37DC84F39E3830D38A1531AB /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13AFF7075F479858CD5252F8DEA9B974 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CD11E21BE5B056E5E8FA14A13A6D783A /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2B1F764647943B12FE0420886AD8C0EE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		351485811EA2D91881CB81DD3ED1CB04 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				29C1C361BEA65B19EF2508CCBD0D0E05 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		377B59795CF36DFDA02F63A09E32D27A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9C4E9B21BB983ABE8D5D0EA33A25F2E3 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3D16B15FA43160E3AAFCEBED7B52C7FE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				62E7550568A5A4FDF0CB9E5387909E75 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4359F1CE9D099ABD7E09B5506FDE3086 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B9200DA853AF98730F23F73058CD490F /* CoreGraphics.framework in Frameworks */,
				B5C718CE631C4DA647770FD7EDF0752E /* Foundation.framework in Frameworks */,
				7342C00FB2A7218BD66EE50A5BC803FB /* Security.framework in Frameworks */,
				E1619B51610316C723561ECE1AE8EF02 /* SystemConfiguration.framework in Frameworks */,
				686D0A4F22E81F1DCC0479F291DF0BDC /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4532A8BBDD5896C0299C7C1BDC26306F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D81CC2C58DA7A0F9364DA9A5F3F883DC /* CoreGraphics.framework in Frameworks */,
				E3DC44EF91CB364EFFBD74B9F7A2EF41 /* CoreText.framework in Frameworks */,
				04EFFC931E41041F65D51D7DFD2FDBB9 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7F025C68D7AB2CBEAA20335DC4A82BD9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BF61A9638C4046244F7504489D6627BB /* CoreGraphics.framework in Frameworks */,
				BE8DE890E953564D5486DD6CBED46FA8 /* Foundation.framework in Frameworks */,
				46BB28F62F6521E1CA1BB3A729629041 /* QuartzCore.framework in Frameworks */,
				607B8A893A5D58B9CE8D3E9B0679742F /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		90462950923247E9523FDB0E6B48F598 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A789484945AC0B27E45F315880D28FB3 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A56843A19DB4E4EDBB19F2C2DF79CA3B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				014A3D103E37C91B0CF8E2D25F6EF5DE /* Foundation.framework in Frameworks */,
				7DC189DB314BF660CB5DB4140CCD7B07 /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D1D37E786C94FD0CCF4E902C379B6A6D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DAF44B18145F9D761F81C0BBD28FCFAA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4A997E78B54E039EF0FF48C2DEE3BDEC /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDF19883169DA0ADE6042BDD6F71170E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EDAD4950F0CAEFE4E5B225A25DB7E1D2 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		05A48C37CADBF2B91CD0EEEC18644875 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				7CD09A026EBA331CC6FFF4A7A0350C52 /* WordPress-Editor-iOS.modulemap */,
				7E984FB8DCF638BB61DCE418C8F67B05 /* WordPress-Editor-iOS-dummy.m */,
				004E2DA89A9EDD6F56D27B9A8FE55A9A /* WordPress-Editor-iOS-Info.plist */,
				1AE09A7EF3D9A2E1A93E931D04D34824 /* WordPress-Editor-iOS-prefix.pch */,
				A105034077CFBB0E2A112BF033BAD528 /* WordPress-Editor-iOS-umbrella.h */,
				D80FD5776C34FB276B14EA221751D4D9 /* WordPress-Editor-iOS.debug.xcconfig */,
				5E1FC42EBC42141342AB83C301E52271 /* WordPress-Editor-iOS.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/WordPress-Editor-iOS";
			sourceTree = "<group>";
		};
		08E2414D15BDA950903EA2096AB62AD0 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				FB65446C53D6966FB1D3B7C22CEDEFBC /* ZSSRichTextEditor.modulemap */,
				681D312522EFBDBCC7C7C5D24188044D /* ZSSRichTextEditor-dummy.m */,
				75C78E06D081A1A6D228C76EDDD36AB5 /* ZSSRichTextEditor-Info.plist */,
				6CF80707F361FB3A926D75902206F3A3 /* ZSSRichTextEditor-prefix.pch */,
				D1450B441CC78E73C0A765A7E605DD4C /* ZSSRichTextEditor-umbrella.h */,
				F05BAE02252D74D7B681179D52313356 /* ZSSRichTextEditor.debug.xcconfig */,
				8E95744135DE0D8AA27F6AE43903438E /* ZSSRichTextEditor.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/ZSSRichTextEditor";
			sourceTree = "<group>";
		};
		0D2984BBA92DCB344E9ADB4FAEC931B3 /* Pods */ = {
			isa = PBXGroup;
			children = (
				530681A6968A898F00592E607050ED30 /* AWSCore */,
				FC927DCC664897A522D3B7FEB5829BCD /* AWSS3 */,
				8FE2C0B353E80E7D53023F232D779C90 /* AWSSimpleDB */,
				F78508D91C936154974A2598BFF8A09D /* CustomIOSAlertView */,
				2A0B17EDB02808983D8E0D7EF3FE98CF /* IQKeyboardManager */,
				59938EBA1D39EB5E693BD59580F17E63 /* Masonry */,
				E06F90E2F06C24D57D10C92303EA1079 /* UITextView+Placeholder */,
				6D136611B4E8FD28F841FD11920B8110 /* WordPress-Aztec-iOS */,
				C56DB110F133090AE6D5115ED22B6229 /* WordPress-Editor-iOS */,
				8D21384C589475617632DAD18A05DF46 /* ZSSRichTextEditor */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		2773E8D5EA328D19E4B1267AD4602EC3 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				13966B8E431CFA857A9DCDB2ACBCC530 /* AWSSimpleDB.modulemap */,
				E24AE983ACB78BFC1BD2C78449EC4489 /* AWSSimpleDB-dummy.m */,
				50E458AA0BD910EB50FB14EBBA97707F /* AWSSimpleDB-Info.plist */,
				526CE59B9D59C6CD5AECCAF55C427E9C /* AWSSimpleDB-prefix.pch */,
				4B0054E487BCEFC478BB837BB2D27E7A /* AWSSimpleDB-umbrella.h */,
				78D5531F362D6B1C64A1CAD7A6BBBCA7 /* AWSSimpleDB.debug.xcconfig */,
				9D99A603CF49DDE72E4979503156B621 /* AWSSimpleDB.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/AWSSimpleDB";
			sourceTree = "<group>";
		};
		2918CAC754588F665139C982363FE39D /* iOS */ = {
			isa = PBXGroup;
			children = (
				07431828B67BD39967B508F00CE4DBAF /* CoreGraphics.framework */,
				9D15296881BFAB381C59EFC4966C6D01 /* CoreText.framework */,
				7FB463E08CB491D770D1C136C8C1263E /* Foundation.framework */,
				FE9C3C3DCCAF9CE6C6A54DA80A656949 /* QuartzCore.framework */,
				28F526E648A3518F8638E381CD064A90 /* Security.framework */,
				671BBB6BC5C0AAE909871444BDC0AE52 /* SystemConfiguration.framework */,
				BE5BBF965B3752D8FF960CACBBF77D02 /* UIKit.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		2A0B17EDB02808983D8E0D7EF3FE98CF /* IQKeyboardManager */ = {
			isa = PBXGroup;
			children = (
				7BAE45001CE72B3C620CCAC0A8BE196A /* IQBarButtonItem.h */,
				8E85B120A0979EE2ED5F1E5784756B04 /* IQBarButtonItem.m */,
				24CD46B7BC4C45C484CC6D328B4A9063 /* IQKeyboardManager.h */,
				FCAE400E9F687984A707BDDCCC6578F2 /* IQKeyboardManager.m */,
				555E8F030F772F41331EF1EEB47ED4FD /* IQKeyboardManagerConstants.h */,
				F92F459EC99C7CC257CEF2291D790715 /* IQKeyboardManagerConstantsInternal.h */,
				704368D961907C9D12638D4FA30555A9 /* IQKeyboardReturnKeyHandler.h */,
				B15FE37152D666D5C1D33395B4B78CF8 /* IQKeyboardReturnKeyHandler.m */,
				BA2197EC871A6323D44BB9675BFF43ED /* IQNSArray+Sort.h */,
				49EC90BD7B2EF167EEF9CAC88CDC6F67 /* IQNSArray+Sort.m */,
				1FD9C5127E16DDB5B0698CC793BF066B /* IQPreviousNextView.h */,
				0F2BD190F1AF129F491C83B157B82BF6 /* IQPreviousNextView.m */,
				BBF0B4C8483D374CF913D7A32C3B4F38 /* IQTextView.h */,
				E5285A404596A0935B0BA6597DB3A47D /* IQTextView.m */,
				144D7D8856C187E674643B1968669749 /* IQTitleBarButtonItem.h */,
				42C84C6628B8E4B28D6E3DFE65D31341 /* IQTitleBarButtonItem.m */,
				CBE29738EE0127A981F04E41AAF8DAC9 /* IQToolbar.h */,
				01B2F9115C1925D20200A33F7A44087B /* IQToolbar.m */,
				457CCD794442C7C27C23936EA4CB0B17 /* IQUIScrollView+Additions.h */,
				DCC41F38A7F148F05A397CA73A7D9390 /* IQUIScrollView+Additions.m */,
				75398A29EDFDFA22F9212F8A8FD5923F /* IQUITextFieldView+Additions.h */,
				29CE6563281AD32FC9E4DCFAB9976742 /* IQUITextFieldView+Additions.m */,
				5D8F3D63AAA227811B98BAAE24389EC1 /* IQUIView+Hierarchy.h */,
				D77752F458417B3BEDD1EF059DEB14E1 /* IQUIView+Hierarchy.m */,
				1BA9A17680202558687A37AA159DCB2D /* IQUIView+IQKeyboardToolbar.h */,
				4C35D6B72AF803FF46236DEBD9538A40 /* IQUIView+IQKeyboardToolbar.m */,
				F5DC4F3600A2A49E4A906226A7A5E292 /* IQUIViewController+Additions.h */,
				D78E6A5586279CEE4056F8FEA4754460 /* IQUIViewController+Additions.m */,
				E535AD8F04C5E4441DFA24A91712B7BB /* Resources */,
				C8B0AA9F2B5F46212DEE592DB5C87F96 /* Support Files */,
			);
			name = IQKeyboardManager;
			path = IQKeyboardManager;
			sourceTree = "<group>";
		};
		4E170486B02F2237ECF75D121C412DAD /* Resources */ = {
			isa = PBXGroup;
			children = (
				AF4069A14D9C01FB0A3E774A00E6D15C /* aztec.png */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		530681A6968A898F00592E607050ED30 /* AWSCore */ = {
			isa = PBXGroup;
			children = (
				F81DA223794E75F47D653205636DE507 /* AWSBolts.h */,
				211A0E041B95372827B5C3CD90B67C98 /* AWSBolts.m */,
				D99737B2BC8F7D0892BF7F1DA1F200B7 /* AWSCancellationToken.h */,
				97A83F9E5D08C4AFF2DB0AE534D85A9C /* AWSCancellationToken.m */,
				8216011AB3DBE55EAC3EC1E695892B37 /* AWSCancellationTokenRegistration.h */,
				845B34CC7B2DAD2ED03AEBCF5DB4601C /* AWSCancellationTokenRegistration.m */,
				313D9F017893EC5D1D39DC041EA291E1 /* AWSCancellationTokenSource.h */,
				E465246DA8193C8320E29AA4CDB2BC23 /* AWSCancellationTokenSource.m */,
				146F47E00215443494348808DD1AC419 /* AWSCategory.h */,
				76BBF9674059B06F4ABF0CB90EDE2BAE /* AWSCategory.m */,
				C5C7E5DECF3D9295CDBE42349FB9366E /* AWSClientContext.h */,
				4968981B497BB3A8BABDD479977EC3EA /* AWSClientContext.m */,
				E432BD589A2FCE4F28C251DD1FD55074 /* AWSCocoaLumberjack.h */,
				46D31E2B8E7446A5E4DDB826A5D70EDF /* AWSCognitoIdentity.h */,
				37081F17CBAE1F2535B46A6B59DB619B /* AWSCognitoIdentity+Fabric.h */,
				62FF9C38311EA366D40408B6FDBEA95D /* AWSCognitoIdentity+Fabric.m */,
				7DD71838E5EDBD9BBE5B20E7963C4B22 /* AWSCognitoIdentityModel.h */,
				1A8D3BDE8DB11B9DE4D293810156F319 /* AWSCognitoIdentityModel.m */,
				45555B9376A64C0156962C778ED629DC /* AWSCognitoIdentityResources.h */,
				96165BF12398A6C2770E17940E8CC6AD /* AWSCognitoIdentityResources.m */,
				FC605FFBB3DE7BA7F708C726F71575B8 /* AWSCognitoIdentityService.h */,
				A8DD60E2084C5DE8ACF1B2C6583E1EF2 /* AWSCognitoIdentityService.m */,
				1BA06FC2FBB82A646E0B8E92DBC41C2F /* AWSCore.h */,
				8C83C48284F8921E0632E8AA48CA43BB /* AWSCredentialsProvider.h */,
				3E8BA41A73F8121A015507BD2EDC7846 /* AWSCredentialsProvider.m */,
				365B2B752EA07985E0214D7560CD16BA /* AWSDDAbstractDatabaseLogger.h */,
				93FB0C3238BA760211FE768FFF134809 /* AWSDDAbstractDatabaseLogger.m */,
				99F56AF89A82A83979896B07F61C4F55 /* AWSDDASLLogCapture.h */,
				F5772291CE124DEDB5DDEE211525061C /* AWSDDASLLogCapture.m */,
				CC14EA37003C9F9EDDFAB2B4D8522780 /* AWSDDASLLogger.h */,
				5CFD3A2AEAB2229824D502647EAB5E23 /* AWSDDASLLogger.m */,
				5651B66AE63DF3C9D28A23701727FBC4 /* AWSDDAssertMacros.h */,
				AAAABF29488FA39F76C549E9F8D0CDE6 /* AWSDDContextFilterLogFormatter.h */,
				638B59C57691081714DF94E1105414E8 /* AWSDDContextFilterLogFormatter.m */,
				D3B7410F56195D34BA6FA4341CD76FEA /* AWSDDDispatchQueueLogFormatter.h */,
				2DDF2C4F33FD6482C1AC71B6D294822B /* AWSDDDispatchQueueLogFormatter.m */,
				32A480ECEAD05019B93201344BA19161 /* AWSDDFileLogger.h */,
				D1732181FE21DAEBF85DD3ADB8B88188 /* AWSDDFileLogger.m */,
				5398C69045FD4FE203EDAF9757769D0D /* AWSDDLegacyMacros.h */,
				1EAB1389E7B7667B9767F2E6F81B20E6 /* AWSDDLog.h */,
				50EC52C80BB9351BD7FD3F77C1012B45 /* AWSDDLog.m */,
				A2371E8F49DCD048BF18D94BFD29DC4C /* AWSDDLog+LOGV.h */,
				50A25FDB1EF5F62CC4CFF4831358D64A /* AWSDDLogMacros.h */,
				83FA287D7A85D8517864E61A69BB2EC4 /* AWSDDMultiFormatter.h */,
				4F05A80F68B8D260112B398C93C53D8B /* AWSDDMultiFormatter.m */,
				B5C3AAD1FF47242B09A33D914CE9A172 /* AWSDDOSLogger.h */,
				6F47FA784DD33522432B2CC185BC118F /* AWSDDOSLogger.m */,
				B897E25B523117B1B4F2B60DF273045F /* AWSDDTTYLogger.h */,
				548E4579A2F41FC491D679739625C312 /* AWSDDTTYLogger.m */,
				6728180B89FD8D473A33467EEF87CE5E /* AWSExecutor.h */,
				297C04CB8ED04EB643395E8C060F0666 /* AWSExecutor.m */,
				AD2B778079026D96FD5732ED78ACFFDF /* AWSEXTKeyPathCoding.h */,
				B0E0975267A737851C218C21DC21F058 /* AWSEXTRuntimeExtensions.h */,
				9B776A3E797E4B8EEDA61EF1F11B6FBE /* AWSEXTRuntimeExtensions.m */,
				47B0721500312E98CDDB9B79CCE98012 /* AWSEXTScope.h */,
				6EDC72D8701ADAEE54C666BB207C79F1 /* AWSEXTScope.m */,
				D7E3B4F930F504C5E1706A478ED2942C /* AWSFMDatabase.h */,
				9CEC9BED47F9A9090F913FA2EE294605 /* AWSFMDatabase.m */,
				FD649B02DEFCC16A5EBA3842C6C27CA0 /* AWSFMDatabase+Private.h */,
				40277157CEE8D04B755EA40AE3B5B5F2 /* AWSFMDatabaseAdditions.h */,
				A64EDFF3CE34949D3A965A50D0060C8A /* AWSFMDatabaseAdditions.m */,
				B34595B0BEDC3B2D63D1FC080E1668E3 /* AWSFMDatabasePool.h */,
				DAD1927B144306FD16FE78AB6F83CFA9 /* AWSFMDatabasePool.m */,
				192CB99A1A3CABA30AA090348A75CC24 /* AWSFMDatabaseQueue.h */,
				405F1C37FB06DB35C92A64437575FF27 /* AWSFMDatabaseQueue.m */,
				E97BCF1D169A5A1D63095CD780C88282 /* AWSFMDB.h */,
				36E9F5AB776534B247915CE96EFD9212 /* AWSFMDB+AWSHelpers.h */,
				A1C522ACEAE3E35517A873AB77C7B09D /* AWSFMDB+AWSHelpers.m */,
				DB1DD27CB50948A64DB1B8372CC800B5 /* AWSFMResultSet.h */,
				DE3C256959ECE4A37854A454867350F5 /* AWSFMResultSet.m */,
				627559E1B38D7FA374DD781638070008 /* AWSGeneric.h */,
				C23576EE4D80E4F01F94FF878DBA4BF2 /* AWSGZIP.h */,
				F8341D1E0EBD0BCE67F8476689B03490 /* AWSGZIP.m */,
				D221ABA24362788B04C38371DC7CBC09 /* AWSIdentityProvider.h */,
				3D3B05789398B7B6198AD5365927E91D /* AWSIdentityProvider.m */,
				847EF218D80B5A990D6646B162B88F86 /* AWSInfo.h */,
				413BE7E313C4F81889A39015E63FEF62 /* AWSInfo.m */,
				6E2903F26479939D102361A3562C2479 /* AWSKSReachability.h */,
				18C050F7D65590348644F2EFCF1BBE20 /* AWSKSReachability.m */,
				EEF7C1919374A21C682A1FBC0CA362EA /* AWSLogging.h */,
				758D76535C24029633B4CB5525FD6370 /* AWSLogging.m */,
				A0903D0074BBBD56225535B98AFE5B90 /* AWSMantle.h */,
				FC785106A32817B914FFF26BDEF1DB3F /* AWSmetamacros.h */,
				AC84E94C514DA4AF02D2DF649F69B650 /* AWSModel.h */,
				B7E7238C0AEE25E2932705C5DB26BD5C /* AWSModel.m */,
				EFA116471150875E379A82E2A8C98597 /* AWSMTLJSONAdapter.h */,
				EC8E359082632DD368135B02AAF7E8DA /* AWSMTLJSONAdapter.m */,
				B08508F0601023DE71C82068EDE7DF1B /* AWSMTLManagedObjectAdapter.h */,
				F43D81C739F6CFA1BC5B2E3863D24BFB /* AWSMTLManagedObjectAdapter.m */,
				B391CB9E214DA15C37037A217641F72F /* AWSMTLModel.h */,
				76CFE2037447BF1F045097EEBA4417EB /* AWSMTLModel.m */,
				22887F82031644416E84A7204E5F160F /* AWSMTLModel+NSCoding.h */,
				1281A45D13B60DFC43269917F71F7BF5 /* AWSMTLModel+NSCoding.m */,
				1EF07C441AD75BE9D582C89492B7F35E /* AWSMTLReflection.h */,
				1E704FCBEDE3AFDF36B4835141628046 /* AWSMTLReflection.m */,
				56E0F20EEBDFA49113C6F76E28AF022E /* AWSMTLValueTransformer.h */,
				4F9194FC2F8BB5A93E35DA589D6DFA3A /* AWSMTLValueTransformer.m */,
				C77AD6683490ADE50CAFE73BC1BB0995 /* AWSNetworking.h */,
				97ADBE32A4F1B909C5E451F8D7903634 /* AWSNetworking.m */,
				72499EE078AD8003B33D48EAADE71CF6 /* AWSNetworkingHelpers.h */,
				00E06E4832B4FB41B2D9633BB589906A /* AWSNetworkingHelpers.m */,
				911FFA8275F5C2BCA0C06B955E7C61B7 /* AWSSerialization.h */,
				195D12B4CE31F09A47E018BD02E15C5F /* AWSSerialization.m */,
				0E1141B7368A68052DC4226DCD2F35A6 /* AWSService.h */,
				85506C33D6ACBC73C840990C39D8C24B /* AWSService.m */,
				3EF8EAE788C771937E1415A893339F46 /* AWSServiceEnum.h */,
				6CCAA421ED6A2394D5CC7E8FCFD5727D /* AWSSignature.h */,
				FA7B0C8D1975D6F2E9C29EBBA0284F1E /* AWSSignature.m */,
				623C2F459B9B84527E06BCD6444BD3F2 /* AWSSTS.h */,
				4B9B211F59242354487828265CB3D189 /* AWSSTSModel.h */,
				521B7C312C31BA26032799373DFB8DB2 /* AWSSTSModel.m */,
				40FE320447E269AEC1B033900A2F066D /* AWSSTSResources.h */,
				CFE6C1F0ACD53B397DC9998112E1C7EC /* AWSSTSResources.m */,
				C4C9485AEB65EFA224A8882C58E5D0F2 /* AWSSTSService.h */,
				F97BCD847DC357F3DCC362F08C9F3B97 /* AWSSTSService.m */,
				D36AE816EC31BEAC9AB9DB9EA2B49AFE /* AWSSynchronizedMutableDictionary.h */,
				7EDECE18E660C72B4D9FA7CB4582081F /* AWSSynchronizedMutableDictionary.m */,
				F459000FF37B6A661E5CCB88B45753C0 /* AWSTask.h */,
				08FAAE69093894648B755A8AB25E808E /* AWSTask.m */,
				6DEB0307CE0537D3DB877865D38ABD00 /* AWSTaskCompletionSource.h */,
				6D59136F1388507489BB0A938C86C0CA /* AWSTaskCompletionSource.m */,
				17E1A3BC2775421D2C200F672365778D /* AWSTMCache.h */,
				ABFB8288FEC824AEC757558B8201837D /* AWSTMCache.m */,
				2F6A5EDE45053CCB5AB388186F1D7645 /* AWSTMCacheBackgroundTaskManager.h */,
				99C4601DBF9EA72727271589E9B79F3A /* AWSTMDiskCache.h */,
				C18B489BE2B5ADD61B238F6DB866EA23 /* AWSTMDiskCache.m */,
				981C790AD43EC86299CCCA039FF11C34 /* AWSTMMemoryCache.h */,
				8111EBB5048CB52A29C0402C6C90E7A4 /* AWSTMMemoryCache.m */,
				3926C62A6CA718012FE043321379A91A /* AWSUICKeyChainStore.h */,
				6B0C62E41EEB1F8AF6B5E729C5A800DD /* AWSUICKeyChainStore.m */,
				1201C4BCEBE15F5A9A9A6B9C31AC2F53 /* AWSURLRequestRetryHandler.h */,
				8459F05ECCAAD575CF7CB6C028E43445 /* AWSURLRequestRetryHandler.m */,
				49D71110221D757C0CB53D3F55D291F3 /* AWSURLRequestSerialization.h */,
				8CBE5939B36C709164984BFB030ABEB7 /* AWSURLRequestSerialization.m */,
				DFE9FB38AFE4807ADF098632397DFE68 /* AWSURLResponseSerialization.h */,
				20AF0B625EEB1A41F012EAAB78307DB2 /* AWSURLResponseSerialization.m */,
				5059651588EA83D193D76041BA6F064A /* AWSURLSessionManager.h */,
				411D9C55F97896E1AFD3DCF13A8E0E53 /* AWSURLSessionManager.m */,
				73573205F758A3C405D7A69AA503A9D0 /* AWSValidation.h */,
				796C1D4D0E02C7E49C871D77A15B055D /* AWSValidation.m */,
				DA37F4A41BB30CADBA20FCB9F226E9EE /* AWSXMLDictionary.h */,
				FBF10A99698398BB3B52F654632C0D19 /* AWSXMLDictionary.m */,
				A824636F156CD9FB741A732835E18402 /* AWSXMLWriter.h */,
				772044D1CBAEA66DF6DF7A7AD8B94FAD /* AWSXMLWriter.m */,
				064EDF41254270D4B8DF6B0160E321A3 /* FABAttributes.h */,
				C9A8EB2E724C516C03CF1440E39D450D /* FABKitProtocol.h */,
				E5A10819C69BFCFC90328EE0CA5F6342 /* Fabric.h */,
				1910B7E730B49715E0CC9D517563030F /* Fabric+FABKits.h */,
				E00A223B033023252D3906B75835325B /* NSArray+AWSMTLManipulationAdditions.h */,
				E6D6733F786B5D3F02F4DFCBF4E02370 /* NSArray+AWSMTLManipulationAdditions.m */,
				7C49B484F3F261F33738AA0DD9AA4BB2 /* NSDictionary+AWSMTLManipulationAdditions.h */,
				B15DCBE8E89EA8C8E0E845B756A805F5 /* NSDictionary+AWSMTLManipulationAdditions.m */,
				547EAB1BE3AA869CD15DBAB199FE5B54 /* NSError+AWSMTLModelException.h */,
				6BBD635FA05A2D7526B765997F06D40E /* NSError+AWSMTLModelException.m */,
				D9B1729EEBD0ADC4FF2C2C1769353F34 /* NSObject+AWSMTLComparisonAdditions.h */,
				C55E09C0A9B0FD2471683DEAF395BBBB /* NSObject+AWSMTLComparisonAdditions.m */,
				8B8CCEA8562FB0CAA855E9892319AE81 /* NSValueTransformer+AWSMTLInversionAdditions.h */,
				5A821347CD1D8A20D645FE673B89478C /* NSValueTransformer+AWSMTLInversionAdditions.m */,
				A20AE295FDEB3E4FB7D13B49A9E8F530 /* NSValueTransformer+AWSMTLPredefinedTransformerAdditions.h */,
				E2982D83493A9251C7C6BE0F4CACEBE6 /* NSValueTransformer+AWSMTLPredefinedTransformerAdditions.m */,
				5F1AD7AAB70BFC8E18BA36D1560D0598 /* Support Files */,
			);
			name = AWSCore;
			path = AWSCore;
			sourceTree = "<group>";
		};
		59938EBA1D39EB5E693BD59580F17E63 /* Masonry */ = {
			isa = PBXGroup;
			children = (
				038253724BF4626A000CFC2A50C6DE08 /* MASCompositeConstraint.h */,
				09D07A5FD2DF28FD7A522BD3C53F2ECB /* MASCompositeConstraint.m */,
				9AD41CE3DD9D13779F236141AB923239 /* MASConstraint.h */,
				B34B5E2F0FAD624DFA7AEDE2C00F9750 /* MASConstraint.m */,
				DF7B55685454B1762A22CEC8D0310AE9 /* MASConstraint+Private.h */,
				44CE3454C1307BA6FD69D3EF29B127D4 /* MASConstraintMaker.h */,
				40E30DA9C3BD6250D6D306AEC513E69F /* MASConstraintMaker.m */,
				5BF5EECD1547D846386284170DC1D1E6 /* MASLayoutConstraint.h */,
				27BC91EDEA8EF9984D9F9A1E5B2FF42C /* MASLayoutConstraint.m */,
				A7B73D648F0D281C558AF0E3CA4195E2 /* Masonry.h */,
				E26EACC90EF23460C230D65A5D55CE9E /* MASUtilities.h */,
				DB2A8C3E7BD092724C9D127FB2EC2647 /* MASViewAttribute.h */,
				9DA3DE632D60541EB7D834021067DA80 /* MASViewAttribute.m */,
				29E6ACF651319B9A26A854D6DA3CBB23 /* MASViewConstraint.h */,
				BAA9AAE0FE730578FA248BAC637AC620 /* MASViewConstraint.m */,
				66A9CCEAF64BB0063D1A97C737417736 /* NSArray+MASAdditions.h */,
				1CA742AAA15F496FEC813F2CC9407159 /* NSArray+MASAdditions.m */,
				50584B4D5004395480A752508693C842 /* NSArray+MASShorthandAdditions.h */,
				DCC664C99DDBB480FFEC29AB06B29BA8 /* NSLayoutConstraint+MASDebugAdditions.h */,
				D4ECEE9567271A5B94C7B83FE70ABF5F /* NSLayoutConstraint+MASDebugAdditions.m */,
				BD036081E355A0A78E5C83461E07BACA /* View+MASAdditions.h */,
				AD3B9EC43358F4BF419BB354BD9D0534 /* View+MASAdditions.m */,
				20DD5DD231841EB0CD054E9FD7A7BE96 /* View+MASShorthandAdditions.h */,
				F564312154567EEF4E7EC72E5C9F4573 /* ViewController+MASAdditions.h */,
				E70598D921E3E2ECC5DAC54D91C51FB3 /* ViewController+MASAdditions.m */,
				87A8A8A1B34A3885B2E477E32A0BA57B /* Support Files */,
			);
			name = Masonry;
			path = Masonry;
			sourceTree = "<group>";
		};
		5F1AD7AAB70BFC8E18BA36D1560D0598 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				D26F2354B5672DDDB6EFE846E71C36EC /* AWSCore.modulemap */,
				6166EFFEC336B3F993255148D94A61D5 /* AWSCore-dummy.m */,
				9191DC1E4A6D751C6E695A38D86D218B /* AWSCore-Info.plist */,
				E327696F96EDCFE45B8D2E2A5A106720 /* AWSCore-prefix.pch */,
				C4EBDCB812DE082CC1D8A443279C1006 /* AWSCore-umbrella.h */,
				E810F8340D1D7BE3CB183C7BD2288F34 /* AWSCore.debug.xcconfig */,
				3DA3759212250A359947AC1E937476A7 /* AWSCore.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/AWSCore";
			sourceTree = "<group>";
		};
		68FAB5CD3515A90A29C7DD455FB8DFCB /* Pods-SnapInspect3Tests */ = {
			isa = PBXGroup;
			children = (
				B3F38191D31D14F81C9D1036C16F553E /* Pods-SnapInspect3Tests.modulemap */,
				812B1F03A46C5A35C0D0AA5980C4B8A4 /* Pods-SnapInspect3Tests-acknowledgements.markdown */,
				0F30CB740A9D0D16D82A2970D27FD906 /* Pods-SnapInspect3Tests-acknowledgements.plist */,
				8545602ABE422AEBDB226FBF2D2399BB /* Pods-SnapInspect3Tests-dummy.m */,
				4382F7D17E5FE318299BDB4BD61E8AC7 /* Pods-SnapInspect3Tests-Info.plist */,
				E4EF326ED9DB7B756E1EBC7A70FE23E1 /* Pods-SnapInspect3Tests-umbrella.h */,
				5E0C197C502079E60EEE592311CFA30B /* Pods-SnapInspect3Tests.debug.xcconfig */,
				CDFF82EA0C1F246765CD4A311891D7C3 /* Pods-SnapInspect3Tests.release.xcconfig */,
			);
			name = "Pods-SnapInspect3Tests";
			path = "Target Support Files/Pods-SnapInspect3Tests";
			sourceTree = "<group>";
		};
		6D136611B4E8FD28F841FD11920B8110 /* WordPress-Aztec-iOS */ = {
			isa = PBXGroup;
			children = (
				39BB99D3270473231795762345668826 /* Array+Attribute.swift */,
				8FBCF675096DCEFB635C9BFCBFF69500 /* Array+Helpers.swift */,
				150FCA1A936738D5AFEC87092DE8AFF4 /* Array+ShortcodeAttribute.swift */,
				EBD4716179247B892B0408640A886585 /* Assets.swift */,
				BF9ADCE97343827849DC20CFDCA4F04D /* AttachmentElementConverter.swift */,
				9968ABA13A812750759CBD89C6F4A0A4 /* AttachmentToElementConverter.swift */,
				04D414B15ACD38A9FD5A3FD390B38AD8 /* Attribute.swift */,
				641112455CC4BEB1F70702889FBDB738 /* AttributedStringParser.swift */,
				EB057E231D1002C7FB222DF42A01C3DC /* AttributedStringSerializer.swift */,
				63309F31146AB4C903CC7A3196078A5F /* AttributeFormatter.swift */,
				EB490F0B68D427E72F1F760FEA34DFCA /* AttributeType.swift */,
				274699B1D2C22F0EB55A6F7BD9C6FA21 /* Blockquote.swift */,
				FBF57D77F6B3B7389719A0285F52CD9D /* BlockquoteFormatter.swift */,
				12BC206C8845BC6D96492ABA143BF767 /* BoldCSSAttributeMatcher.swift */,
				9E8368C3580161A5C1D43904197F5581 /* BoldElementAttributeConverter.swift */,
				E12AF7C977258FF6635EF3A52CD63EE3 /* BoldFormatter.swift */,
				8C6D278652EA585D68FB13E2A840CCE0 /* BoldStringAttributeConverter.swift */,
				E65FB0FDBC463303A34DD96661351FA1 /* BoldWithShadowForHeadingFormatter.swift */,
				555D9149D58E5FFD50F69F51EAB980C6 /* BRElementConverter.swift */,
				1EA243511A78898E9242B57B682A48AC /* Character+Name.swift */,
				51CD230427EA642242F58513F7356C59 /* CiteElementConverter.swift */,
				BE3149C5151BA104A29FEE081968A56B /* CiteFormatter.swift */,
				9247040712D2E676ABE303146C08E865 /* CiteStringAttributeConverter.swift */,
				B89995A7AC4D608EA58571EA22CCBEAE /* CLinkedListToArrayConverter.swift */,
				CF292F57BA682294ED768150923FB82F /* CodeFormatter.swift */,
				2B4321EC5D63E49AC4FDC1BE57FF1D3F /* ColorFormatter.swift */,
				1B397F1E81751913B882870C417D2559 /* ColorProvider.swift */,
				ABC4C62B6B80178C30FB33C7D08D1839 /* CommentAttachment.swift */,
				2CE77014456596A4717032B476C7E555 /* CommentAttachmentRenderer.swift */,
				A4E718B7D0E0B58A336D2D0FDE7C9FDA /* CommentAttachmentToElementConverter.swift */,
				D8685D6C7C6BCE54476678671D6808F8 /* CommentNode.swift */,
				03B6F41E23C27BC816E8FE9F8D0269DF /* ConditionalItalicStringAttributeConverter.swift */,
				9E02BD2CFC8A70D922DDE96CA66A46A1 /* ConditionalStringAttributeConverter.swift */,
				E1A9BAB022FAC5F5CFF7C98848DABF20 /* Configuration.swift */,
				876ABF628D9DE72E7646E9ED6545047C /* Converter.swift */,
				8F5DE4B797E346FDB6C6F6704B8AD042 /* CSSAttribute.swift */,
				FB0A191EB4E8D3F2D8E3BEECDBB30F34 /* CSSAttributeMatcher.swift */,
				BD7C9D5AB40262B95BB3FB53FAC06428 /* CSSAttributeType.swift */,
				F2081AE1A1284F945259F1A770E34889 /* CSSParser.swift */,
				02DC8FB325DEDC74EA982677F1587231 /* Dictionary+AttributedStringKey.swift */,
				5720D83C765870F489179B2733A34E8F /* DocumentReadingOptionKey+Swift4.swift */,
				8C9BCEA2B3640D180AFF3625E28AE130 /* DocumentType+Swift4.swift */,
				EF9A7B350CD6915D385FC0B851BEB0F8 /* EditorView.swift */,
				98151BE1EB7039A64A7B4C3B74E7F59D /* Element.swift */,
				1275F9CFE4974B39A872C32F8D8BDD8C /* ElementAttributeConverter.swift */,
				75332399D9905747E1517151D5D8869F /* ElementConverter.swift */,
				49EDD161022F1226ABBF8FA40729A345 /* ElementNode.swift */,
				DE1E0AD4D0ECB835EC6ACBC70FEE7A14 /* ElementToStringConverter.swift */,
				7E2EECDA23F704DD9AE43511878D3CA4 /* Figcaption.swift */,
				DED96BA25C711FD0BF237D2D210D7FBE /* FigcaptionElementConverter.swift */,
				30B0C0D2B2CAD08CAE85660EA39786E8 /* FigcaptionFormatter.swift */,
				7F038CD9E930B4AD0D7E067D11EDBF54 /* Figure.swift */,
				84F85EEF0E7442B2B1EE9396B766DCA2 /* FigureElementConverter.swift */,
				2B0A29AA8A0CC441B625B7CD0347D9D0 /* FigureFormatter.swift */,
				C375E2FE5A4C3C599F3B0482E623243B /* FontFormatter.swift */,
				8C0DC31065D92ABF0464048E46D47057 /* FontProvider.swift */,
				36DCD2F5E26EAE3DA08826FA45FE9ECF /* ForegroundColorCSSAttributeMatcher.swift */,
				8DCAD714A8E80AAAC7528B3FED4DA767 /* ForegroundColorElementAttributeConverter.swift */,
				2C01276638E91D57CCD0895AC56A75C0 /* FormatBar.swift */,
				829A43B8A8EEFE789AE6D7115BD89ACB /* FormatBarDelegate.swift */,
				C5D43DBF89C84CEF2712E850CD1FA4DC /* FormatBarItem.swift */,
				64208D4FE2A83D087C8B5A7F78FBF7CD /* FormatterElementConverter.swift */,
				5EF7253309C5E66A79B9F7A0FC1DD411 /* FormattingIdentifier.swift */,
				64614B89BF46ED549F261F6168E38801 /* GenericElementConverter.swift */,
				D73296DC00FB984B0D4CCBA98FEEB245 /* GenericElementToTagConverter.swift */,
				58C45A4A7AAAC50FBEDA41BD860D23EC /* Header.swift */,
				38E79041FE8351E3C2E42817B2AD2F19 /* HeaderFormatter.swift */,
				599E6006DA6A07FEB01679A64B6BA616 /* HRElementConverter.swift */,
				F59A5756411A47B93814FAFDDA43BE59 /* HTMLAttachment.swift */,
				818F5E6B4DBD91C42E8C9380DE4D5018 /* HTMLAttachmentRenderer.swift */,
				EAE9C430705DCB6836D4E92429017830 /* HTMLAttachmentToElementConverter.swift */,
				577A82C3784C61980A348C3339C38EC3 /* HTMLConverter.swift */,
				917E30E7AF36AE4FC09DB3946E54C9BC /* HTMLDiv.swift */,
				D47AE08537AEA4D6E9057597D2BB8FC7 /* HTMLDivFormatter.swift */,
				61B1580831F5EE4C99337A1E56114053 /* HTMLLi.swift */,
				660C00ECF2F488914D5AA9E4701F664C /* HTMLParagraph.swift */,
				95E34300DCEEA53679C0878FF8D099A9 /* HTMLParagraphFormatter.swift */,
				CA4B55324018AE6F3874D130E483F4BB /* HTMLParser.swift */,
				C209BE1B2AC0B2E433FBC807B789B8D7 /* HTMLPre.swift */,
				FB93B472172D5A1221F4A68802398F34 /* HTMLProcessor.swift */,
				8C5B1742EBD4FABEA46AA50116D42214 /* HTMLRepresentation.swift */,
				0D377072F910FDB79D7AE76CE72E0DEE /* HTMLSerializer.swift */,
				120A7EB69C81F1A7EF1F516A8EDBD0EB /* HTMLStorage.swift */,
				BF848E21E7446262B39EC796C68C40E6 /* HTMLStyleToggler.swift */,
				A13EF83225F8280989C1B4644F4C0201 /* HTMLTreeProcessor.swift */,
				7225EB57D56745BF550C02D1730AD91C /* ImageAttachment.swift */,
				738FAD0985222D7E507BE7FEDAC9895E /* ImageAttachmentToElementConverter.swift */,
				32F27D73078787B366DFFB831C91CCE4 /* ImageElementConverter.swift */,
				9AFCE0ECCC017ECBA8B0157C0A9F326F /* InAttributeConverter.swift */,
				5E7C6C9BC56B01D4B66D50C4A4939C5E /* InAttributesConverter.swift */,
				A4D56EE434F98C7FC54FB7DAC79CF1CC /* InNodeConverter.swift */,
				F8DB57B22242369D57372EBC7E718895 /* InNodesConverter.swift */,
				BFFFC89A31DE183078A5145DB51BFE05 /* ItalicCSSAttributeMatcher.swift */,
				7E20CDF0898283444674C3CA432257AD /* ItalicElementAttributeConverter.swift */,
				BF42115721F9D66BBA24B92B2CC207E8 /* ItalicFormatter.swift */,
				125EA11C82FA2EC02252CFE3A1472000 /* ItalicStringAttributeConverter.swift */,
				F8F5E8BED95199D4F7A100BE19F99C1D /* LayoutManager.swift */,
				B32AEC38238F7C01AE5609556EA5957F /* LIElementConverter.swift */,
				F9C7BD95E4E51F2DD088F0C2452EFFF5 /* LiFormatter.swift */,
				639A1130FC5C740DFC065F2FEAD27266 /* LineAttachment.swift */,
				BCD43C2F7D1E824EADF092422238B70C /* LineAttachmentToElementConverter.swift */,
				E66718A179DA93FA5F965387502D0DCD /* LinkFormatter.swift */,
				8019BE9C525449AC6777B20200813346 /* MainAttributesConverter.swift */,
				06851E661D2C17A43BC27D5A4D344DB3 /* MarkFormatter.swift */,
				E230B3086A0FF4B1388CDFEFEDE3532B /* MarkStringAttributeConverter.swift */,
				797C45CC75CDE85071DA2BAA7E18EFA1 /* MediaAttachment.swift */,
				AADF95D6950248D1E241198FCF2ABE15 /* Metrics.swift */,
				CF5D36114A8AB10D32B0D58F5713DEBA /* Node.swift */,
				97AF1FAE6D3DA554621D5D56F7EBEDDF /* NSAttributedString+Analyzers.swift */,
				AB3B7A57EAF5A49169480418B2C4B56F /* NSAttributedString+Archive.swift */,
				D51983E998BEB28DC943C1579C4948C9 /* NSAttributedString+Attachments.swift */,
				C7A329294BBA948213A73A6CDFB4EC53 /* NSAttributedString+CharacterName.swift */,
				04712930695581C893D0FA730F42AF22 /* NSAttributedString+FontTraits.swift */,
				393B080CD824A0BD742E121DC2D659EE /* NSAttributedString+Lists.swift */,
				9428216B36ED69D2AAEAB5BF7B64E3C9 /* NSAttributedString+ParagraphRange.swift */,
				6BA5E53794CB1A7D5D1924A240439486 /* NSAttributedString+ReplaceOcurrences.swift */,
				DAC39BB747BE315892883EC173D22E09 /* NSAttributedStringKey+Aztec.swift */,
				5524DC621EC590F329D352C73F45E4CD /* NSAttributedStringKey+Conversion.swift */,
				039BFD41CC88B7E9A28C9AC2A4E3A37A /* NSBundle+AztecBundle.swift */,
				BCF06F1A4556E293158A2D3427D56AAB /* NSLayoutManager+Attachments.swift */,
				8565124D9E31139351BC2015D6343353 /* NSMutableAttributedString+ParagraphProperty.swift */,
				7B6CB535B781BF4B6B396F79FB55DF41 /* NSMutableAttributedString+ReplaceAttributes.swift */,
				BB452189A6A2527BA2572E1FED9FD05F /* NSMutableAttributedString+ReplaceOcurrences.swift */,
				AA9F7CAF04778BAF43B8AEAB75680FA0 /* NSRange+Helpers.swift */,
				CC16870CD931C70EED97EFDEB5C7135F /* NSTextingResult+Helpers.swift */,
				9053F06430FDBF219FFE8F6EA3348170 /* ParagraphAttributeFormatter.swift */,
				073011D63121F15BAB242E48C9DD55F4 /* ParagraphProperty.swift */,
				5681CE57343F77FFD7C18484BE9D91F8 /* ParagraphPropertyConverter.swift */,
				7222D67D962ED7B8792514D58F2B8FBF /* ParagraphStyle.swift */,
				1AD819682E18D1A33EC662C4228B03A8 /* PipelineProcessor.swift */,
				CF70592D5CE0FE6B21AF87DDB648F76E /* Plugin.swift */,
				38E87BDB93453DDF0BD1522C6E2842AB /* PluginInputCustomizer.swift */,
				850083AE3F6ED8106C86F0A76D3331BA /* PluginManager.swift */,
				356905AB70772357AA910F0C5CE07A98 /* PluginOutputCustomizer.swift */,
				1298B59F12519D51BC957A3A0386CC99 /* PreFormatter.swift */,
				251BF72F713401D3B45B731343FF621F /* Processor.swift */,
				455ADBDA6E35123297CA2CA977F1AC7C /* RegexProcessor.swift */,
				06326BD3E7AAF964B86655D35A00CAAF /* RenderableAttachment.swift */,
				80E9B08FED6EB8F3A7CD09807B717871 /* ShortcodeAttribute.swift */,
				13B1EDA7EC9AE069B4BB6D3A22118DCC /* ShortcodeAttributeParser.swift */,
				******************************** /* ShortcodeAttributeSerializer.swift */,
				4271FCF653EE523E6572B0CF14D0692B /* StandardAttributeFormatter.swift */,
				3B63EC20DCE9FF02443F11E6996DB297 /* StrikethroughFormatter.swift */,
				215CF00CC0D2E2F90D8FC85852A8CEF1 /* String+CharacterName.swift */,
				F10A4882050CF12A800FCBE09E4A11FD /* String+EndOfLine.swift */,
				44D25AFA0D910C603722D8A644685A3A /* String+HTML.swift */,
				D636AA42F89F893F58FDECE85FC1FBFF /* String+Paragraph.swift */,
				CF23F6F37818B43A7807B1850D91DB37 /* String+RangeConversion.swift */,
				122A1FDB2A5EA16088E24D982A4137D7 /* StringAttributeConverter.swift */,
				6142C216EF73A86D30B9F2ED8AAA7DF1 /* StringUTF16+RangeConversion.swift */,
				12769C1EC854AE0D071FBA8D0F088AC5 /* SubscriptFormatter.swift */,
				34CF0B5321B52651C350F3ABA3093B72 /* SubscriptStringAttributeConverter.swift */,
				151F4074C4E3C4C4B13BD19991E61B04 /* SuperscriptFormatter.swift */,
				D378FA8A15146EB2BD288F751FF479F0 /* SuperscriptStringAttributeConverter.swift */,
				70CF970A0876BD6863CCA882372A8855 /* TextList.swift */,
				03B4854CBE49FA43EED2306959C2C939 /* TextListFormatter.swift */,
				225A16318E4C53492B121D4E9E32EC64 /* TextNode.swift */,
				3F68214465A80C238205DC7D43435219 /* TextStorage.swift */,
				55AE4E9798206559C3DCDCDBF725FECD /* TextView.swift */,
				EBCDCAE8871EBCE71CEA84C7A443F151 /* TextViewPasteboardDelegate.swift */,
				5E72AA636629923D4322311C823D9CD1 /* UIColor+Parsers.swift */,
				940BEA802B685570975157FD009EE6ED /* UIFont+Emoji.swift */,
				ED742648F42A0FF1C7C3BFD356543434 /* UIFont+Traits.swift */,
				6F2A2CA14F27AD32A1CD80270410FAE8 /* UIImage+Resize.swift */,
				28C9E10CDA6BF017DD893E3A2B5DCF66 /* UILayoutPriority+Swift4.swift */,
				D0C08AE8BA5A4F1D594434167187487E /* UIPasteboard+Helpers.swift */,
				0937A48B91842E46510D876C7A64338E /* UIStackView+Helpers.swift */,
				728CE07C37573C09974CDEBE06F36D58 /* UITextView+Delegate.swift */,
				AC1EC3852FAA899F11800775A2A52D3C /* UITextView+Undoable.swift */,
				35B87544670F4E671FA17F93E1E53671 /* UnderlineCSSAttributeMatcher.swift */,
				AA2704F94C3FCAC9249EDFDC5E3FA21F /* UnderlineElementAttributeConverter.swift */,
				8A0A756442DFB9E20738362928F051C6 /* UnderlineFormatter.swift */,
				D93F207F5E4A55F70E904D3350E47A2C /* UnderlineStringAttributeConverter.swift */,
				0F7010BE81BBA3DFE1A7DD01500D17D7 /* UnsupportedHTML.swift */,
				6F95A12AE839B6405F69619B00BCC5BE /* VideoAttachment.swift */,
				FEC8C967B96E7DF9B00007197E3426B9 /* VideoAttachmentToElementConverter.swift */,
				C9F5126C0EC1ED09C4BB52767F9799FF /* VideoElementConverter.swift */,
				B89F57EDCAB14D61E131E8E71D6B0A84 /* Resources */,
				8862CE94BB1443A46E431D5933168813 /* Support Files */,
			);
			name = "WordPress-Aztec-iOS";
			path = "WordPress-Aztec-iOS";
			sourceTree = "<group>";
		};
		783EDFEB62601D65F946D3C62CE8CE3A /* Resources */ = {
			isa = PBXGroup;
			children = (
				A8E5A062BC2C12D27FDAB47AB0DCD1EC /* editor.html */,
				0CA0E7960986B255EB42D0BD04524C2C /* jQuery.js */,
				698A8CF09E49986092016E5DE49A4E95 /* JSBeautifier.js */,
				C76A3AEEC5AE69CF8633AE1E1ADFCB68 /* ZSSbgcolor.png */,
				D6081B9B734C533F2D1A836FEE841432 /* <EMAIL> */,
				DA54B32D41AF298D6558E6BDAB0C33D0 /* ZSSbold.png */,
				0A5FE2974BDB1311E15E603A6754B2CD /* <EMAIL> */,
				7DE33AC5AF743E2B5FA4B20250E3063E /* ZSScenterjustify.png */,
				E72D7893E3009E211340A2B9B5A03ADD /* <EMAIL> */,
				7C65FC3A7A72534371AC6444DF4914AE /* ZSSclearstyle.png */,
				826E8F3C56C8D0005C3D245652B575C1 /* <EMAIL> */,
				2B661495505FAA6317D0B5324CD7AD7B /* ZSSfonts.png */,
				0AA8C6968875E0578A0D46F4B1FC0A98 /* <EMAIL> */,
				89FFE461E69C6CE0E84EA6BFC9E5F32B /* ZSSforcejustify.png */,
				CDFB470C5D11C6BC6069B4E0C3C07CF3 /* <EMAIL> */,
				91CB469A5BDECC08AA507FED5FBF05E5 /* ZSSh1.png */,
				0C8D2038A9D99F02D343E6D2F7779FA5 /* <EMAIL> */,
				181214419C4F510ED3AE893D1A4145A1 /* ZSSh2.png */,
				5B8CFB2B9F5E012D390A8ACDF2102A27 /* <EMAIL> */,
				BDF2A6850655F9CC6A63DB78241F3978 /* ZSSh3.png */,
				2C827CBA650FA0C93DDF2ECD2E3CB064 /* <EMAIL> */,
				3B402823BA5F31C7192627F11AB9939B /* ZSSh4.png */,
				B16CD00475BF1284F5E2CF445243F49E /* <EMAIL> */,
				B1679FC776EDCED632CADABF467AA512 /* ZSSh5.png */,
				F4A5BFA29695EAA8C8F0BB2BCC5A29E9 /* <EMAIL> */,
				2484BDB79E84DB4E30C811523EA5AC75 /* ZSSh6.png */,
				8C6CEAD6FF46BCFFC517DBF6D3AB88C5 /* <EMAIL> */,
				E41F24F034D5ECC74CC512127BB37461 /* ZSShorizontalrule.png */,
				A7C002473DB627C553D0EE1423982BA7 /* <EMAIL> */,
				0A242436B71B27FC1E4A5244459D73F5 /* ZSSimage.png */,
				F2B1DE607CE9A45D4DBF9A57065CF354 /* <EMAIL> */,
				78B19AEF248AFAF06A15F0F6DA9141EF /* ZSSimageDevice.png */,
				E7F57C8FBBEA23A5D7F81300162CCB70 /* <EMAIL> */,
				DBAB142ECACF1C5340488046EBD3E9E6 /* ZSSindent.png */,
				2DA7C0F3D813B48B010E47DAB01C295D /* <EMAIL> */,
				1F273EC6E6AC1D4C91422AE42A0A40D2 /* ZSSinsertkeyword.png */,
				0EFC9290CDB3BCDABF661E53BCC38C49 /* <EMAIL> */,
				FD2E72D0432611CE3DBB82A7118D6C2B /* ZSSitalic.png */,
				D04C1B1DB16ACC78AC9EE2605D85D852 /* <EMAIL> */,
				930FF5049DD63C81F91352537AD13FC5 /* ZSSkeyboard.png */,
				4017BF898984538205277618E23E83EB /* <EMAIL> */,
				77BB5EE6ED2EDECBD411F1CD24F81615 /* ZSSleftjustify.png */,
				73304FC323A42EDC9BBB24BEF29EB36E /* <EMAIL> */,
				1E73B3D82EF8948B09B08E7989CCA8E6 /* ZSSlink.png */,
				FCEB3C046E5E247626A1DA623B152DF0 /* <EMAIL> */,
				3E979FC80F584432AE1119072D70F554 /* ZSSorderedlist.png */,
				16E9830A962849892F6E7F367F0F579D /* <EMAIL> */,
				3F617F83325E31A07FD8EB179E208CC8 /* ZSSoutdent.png */,
				F18CFB4577F1D7DFE58D9743FE984C62 /* <EMAIL> */,
				F6156E84C45AB51D43FAC904D18A8A03 /* ZSSparagraph.png */,
				B489F756CBB488250AC94B05B84D66C6 /* <EMAIL> */,
				FA93A6B40EC8EE06F9D01F4581D01807 /* ZSSpicker.png */,
				7FD88E10375476175A0144CCC33D86B5 /* <EMAIL> */,
				86082A689D2C66A48D5AAF2AEA2E0094 /* ZSSquicklink.png */,
				617BEDD58C1F01349E22E8A7086FCB49 /* <EMAIL> */,
				C9AFB7272C30CA1C4B4A415161C2D87E /* ZSSredo.png */,
				2EEDE148BA200B1BF0D05E7008289C8A /* <EMAIL> */,
				******************************** /* ZSSRichTextEditor.js */,
				9D898348183AE73ADEE5C5707B0827E2 /* ZSSrightjustify.png */,
				37EB391AF9C0460BD4DF10F4B6251888 /* <EMAIL> */,
				15C8C9E0261563ABE5CF764B84881735 /* ZSSstrikethrough.png */,
				3E244762E12B8DF439FC4306B59E9232 /* <EMAIL> */,
				5FE37BF832A23CF06031E09547E0B46B /* ZSSsubscript.png */,
				95AAA80023A09D43DF2B01519622C519 /* <EMAIL> */,
				105D8A2CE972CC478BA50EBE9A4D6999 /* ZSSsuperscript.png */,
				5768CA66FC6D4F2A5A91F89AEC34999E /* <EMAIL> */,
				8DF46159153BEF3ECE1FD6F070554176 /* ZSStable.png */,
				A9420D27C675E14B58E526325816B5F3 /* <EMAIL> */,
				CF54D9BACDC044506D6F68DBBC66BA63 /* ZSStextcolor.png */,
				097ABECCD50A7F3C7D61D73B87E6226F /* <EMAIL> */,
				21C04AD0E8C825BF988BCACBEE9C1B22 /* ZSSunderline.png */,
				363FEAFF429B76667F1B0502D1873343 /* <EMAIL> */,
				D38D8B63299065FBB13B66182BCB12B2 /* ZSSundo.png */,
				4B518F9BCE8A9F7586E3D8C692D21E3C /* <EMAIL> */,
				B575BD8E16AAAB8FE3CE326F713BAD0B /* ZSSunlink.png */,
				B7B72B9BFA15BA7916D51B6BAF4931ED /* <EMAIL> */,
				E77ABE55BFAE11F4FC7355018A66637A /* ZSSunorderedlist.png */,
				0B5610F6E07B1A7AEA1AE3A2F7969DDC /* <EMAIL> */,
				FAC0EB99E32B88789CC8AAF1824B23E8 /* ZSSviewsource.png */,
				A918900FAA168905AA5694B24E77B979 /* <EMAIL> */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		79F18515CD6DD5DCF742A8C230CD84FD /* Support Files */ = {
			isa = PBXGroup;
			children = (
				D219E668629EAE6376E222689F80645D /* AWSS3.modulemap */,
				F969559232E7B06DB96ECC88B5291E47 /* AWSS3-dummy.m */,
				E526BBF21BFA988799270209C8BA8A14 /* AWSS3-Info.plist */,
				6D510E1A2C5CF23529263847DFAB4BF2 /* AWSS3-prefix.pch */,
				B02164EA8470858C577C423F5D967E89 /* AWSS3-umbrella.h */,
				756F66AADA06C45EA43815C641BEB5C7 /* AWSS3.debug.xcconfig */,
				FAE342899099CA96E4C99DF48AD45A7F /* AWSS3.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/AWSS3";
			sourceTree = "<group>";
		};
		8275CCB4E8732497B1021FCBC240F3C2 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				2918CAC754588F665139C982363FE39D /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		85CE9BDE28E6FCB201352A1084A2537F /* Products */ = {
			isa = PBXGroup;
			children = (
				CCFB8F03867DB892EFC47B53186590C4 /* AWSCore */,
				1D216CE4A8B6F50D603960D17EE072C6 /* AWSS3 */,
				D78EA46DC425CA54AC914AA35DB7AC9E /* AWSSimpleDB */,
				D7556B2EB464D0DE8451B690BBFB5BB3 /* CustomIOSAlertView */,
				98527D7196957AAB07B79E2E2AFDE23E /* IQKeyboardManager */,
				A5B2838E4866923F3DA717B058EBFF5D /* IQKeyboardManager-IQKeyboardManager */,
				1FFED36A657123030ABB700256D73F15 /* Masonry */,
				063F586E7A8CB9B81ACDB8D916030C1B /* Pods-SnapInspect3 */,
				65516CBFEC117BA6CB815E9F1AB8D668 /* Pods-SnapInspect3Tests */,
				F0C7FE88342E3772A8A133204A1E1910 /* UITextView+Placeholder */,
				337B4ACD9A49A40116934009DFFB7FFD /* WordPress-Aztec-iOS */,
				C417EB24EA443F14B2EF5F35E63FA887 /* WordPress-Aztec-iOS-WordPress-Aztec-iOS */,
				A630A05CA0CA688C3B11F4807D366C77 /* WordPress-Editor-iOS */,
				76E4C6B4DCC6284EF00F55C768956C69 /* ZSSRichTextEditor */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		87A8A8A1B34A3885B2E477E32A0BA57B /* Support Files */ = {
			isa = PBXGroup;
			children = (
				87E07E9CCE04E995EE98E4A3E63B4B9A /* Masonry.modulemap */,
				4FF37E11D6B53A3D4838C1F163CF57BF /* Masonry-dummy.m */,
				21210BFF7AB19CAD62B5789683A3EF46 /* Masonry-Info.plist */,
				0090612116EAD6CFDF6527ED6C0A7615 /* Masonry-prefix.pch */,
				E75FEFF63044BB7DA3F65090089C66AA /* Masonry-umbrella.h */,
				C7B6DB1429448F12BC797DA3929048F1 /* Masonry.debug.xcconfig */,
				F216A56A86E2A5A208E098F355013AD0 /* Masonry.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/Masonry";
			sourceTree = "<group>";
		};
		8862CE94BB1443A46E431D5933168813 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				E350DD445214158E2DC092E17E7ECD29 /* ResourceBundle-WordPress-Aztec-iOS-WordPress-Aztec-iOS-Info.plist */,
				B87AFFFA09ACC44F701E260423559E51 /* WordPress-Aztec-iOS.modulemap */,
				8686E3BD6A3C230B9041630D692A0943 /* WordPress-Aztec-iOS-dummy.m */,
				0FB4F3CB2AFC04DB3FD5AEC3485CC221 /* WordPress-Aztec-iOS-Info.plist */,
				1205509CA36681694DC1CE169F5A56E8 /* WordPress-Aztec-iOS-prefix.pch */,
				1C10EA016C6C7A70C2A4D952793CEABB /* WordPress-Aztec-iOS-umbrella.h */,
				5962947B21FF09EF8535877A731122F6 /* WordPress-Aztec-iOS.debug.xcconfig */,
				C704F55FD3FC1E0B66E7F3AAD1B60B07 /* WordPress-Aztec-iOS.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/WordPress-Aztec-iOS";
			sourceTree = "<group>";
		};
		8D21384C589475617632DAD18A05DF46 /* ZSSRichTextEditor */ = {
			isa = PBXGroup;
			children = (
				89FE963973B0B1859068323549AD1F27 /* CYRLayoutManager.h */,
				9B558202ADA4407D1A64B290FF504C72 /* CYRLayoutManager.m */,
				903C5C496D228289A15316CF176BA1C7 /* CYRTextStorage.h */,
				4EDC65361B2AB402DED6311C02F43B49 /* CYRTextStorage.m */,
				1DD9D813F4256C2D550E728DF0B29480 /* CYRTextView.h */,
				EC410C1A7CBE4A0BAF57504964554143 /* CYRTextView.m */,
				FB8D412F745D7E32C26BB00D411F1CFB /* CYRToken.h */,
				B9DEA711B1F797284E5A9708F277020F /* CYRToken.m */,
				0711EA3F47547EAEFCC09F6FDB0EA1CB /* DemoModalViewController.h */,
				0D83C5CC3DA7D07A83C6E0DEFF2103F3 /* DemoModalViewController.m */,
				357EDED1CD149FA5D8556F07EA9F61CE /* HRBrightnessCursor.h */,
				38F5F0F67785D719E9F71D6A7A00DB94 /* HRBrightnessCursor.m */,
				******************************** /* HRCgUtil.h */,
				59B1D5E156E5017D65D84E40906457A1 /* HRCgUtil.m */,
				96CD570ACBFACC29A9BE9C8DDE8A7F92 /* HRColorCursor.h */,
				550E5C69144C26B309C8ED92E96C07B9 /* HRColorCursor.m */,
				34ECFF26C3283CF927CEC068076A8230 /* HRColorPickerMacros.h */,
				60594BCC19FB157FAE64A5765806EE3D /* HRColorPickerView.h */,
				41333B3E10C40F0F6C451FFB07D501B9 /* HRColorPickerView.m */,
				F847C9C848B73C5FF8634045B6DB557B /* HRColorPickerViewController.h */,
				9FE94E5220B1B4EDEB0DDF7CC3954033 /* HRColorPickerViewController.m */,
				AAD320CE6517A51936DFD2C6A8017043 /* HRColorUtil.h */,
				03D44C2546E374AFC6D8F598130AC58D /* HRColorUtil.m */,
				6ADF3CAA4370C2E24C0B1CD33BD8BCAE /* ZSSBarButtonItem.h */,
				5A38E9FE9EB858D2A5F68995D1C7B8A8 /* ZSSBarButtonItem.m */,
				9082B30DFDF81429C19E87E82407B053 /* ZSSColorViewController.h */,
				51C38653C21585E26F09E0FBFEB57DCB /* ZSSColorViewController.m */,
				5ECC8B54AC81C10B88EB37406DEB8E31 /* ZSSCustomButtonsViewController.h */,
				BEC2A893C33575FC31B14A52454E06E7 /* ZSSCustomButtonsViewController.m */,
				BFA5D86CE146F831C977D29ADDAF27D9 /* ZSSFontsViewController.h */,
				22BCE032BBD33162D3E9275C9C97F41D /* ZSSFontsViewController.m */,
				28E59BCC5C47ACAA00937C7DCB09380E /* ZSSLargeViewController.h */,
				1E448A42284A180CEFC211B3EDCC37A1 /* ZSSLargeViewController.m */,
				958E826129D5AA9DEC5074C9F89BC3AC /* ZSSPlaceholderViewController.h */,
				5F415AE0B84D32167555ABCC9005E6D5 /* ZSSPlaceholderViewController.m */,
				730FBDA9EF3D89251EC5F6784A86D3D2 /* ZSSRichTextEditor.h */,
				5775490B4D1B6EFF212E4736D032AA64 /* ZSSRichTextEditor.m */,
				BD82597FFB703C1EBA39C5D90AA148D9 /* ZSSSelectiveViewController.h */,
				8DD3DE6D2733041CEF845E8314E85E29 /* ZSSSelectiveViewController.m */,
				349DEC503349A25288631757FD8339AD /* ZSSTextView.h */,
				23CED6C054FF7BDBF12E90D288D9855A /* ZSSTextView.m */,
				783EDFEB62601D65F946D3C62CE8CE3A /* Resources */,
				08E2414D15BDA950903EA2096AB62AD0 /* Support Files */,
			);
			name = ZSSRichTextEditor;
			path = ZSSRichTextEditor;
			sourceTree = "<group>";
		};
		8F4A262D08DFBD4E49C3D5650041AC95 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				DE346AB1823BC03915BA30442AAF2579 /* CustomIOSAlertView.modulemap */,
				3C80B7E9AF74FA513E183FBE290E28F7 /* CustomIOSAlertView-dummy.m */,
				02186B0D32F4092812EC9CECB2859997 /* CustomIOSAlertView-Info.plist */,
				3FF3E271007CF79F3F0557EFA40F26F0 /* CustomIOSAlertView-prefix.pch */,
				******************************** /* CustomIOSAlertView-umbrella.h */,
				9447A291573AA1FBEA2C01F4787364C2 /* CustomIOSAlertView.debug.xcconfig */,
				BBBB244FFE50AAE6DB1130886DF23032 /* CustomIOSAlertView.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/CustomIOSAlertView";
			sourceTree = "<group>";
		};
		8FE2C0B353E80E7D53023F232D779C90 /* AWSSimpleDB */ = {
			isa = PBXGroup;
			children = (
				F888173F09CEEAB4493DD473DB3B7465 /* AWSSimpleDB.h */,
				B9ED883D190A2A182BAEEBF81746DF9D /* AWSSimpleDBModel.h */,
				527DF69633C91FA79917BC7E6431D4AE /* AWSSimpleDBModel.m */,
				A116A72EC0E1FAE72337F8394B1C38E2 /* AWSSimpleDBResources.h */,
				ADA5741F6BCC100592751FA0D4AA8C7C /* AWSSimpleDBResources.m */,
				515991B90E307156A5BC561574050B72 /* AWSSimpleDBService.h */,
				0E4DF85BAF486D03F07290BA1EAAE4F3 /* AWSSimpleDBService.m */,
				2773E8D5EA328D19E4B1267AD4602EC3 /* Support Files */,
			);
			name = AWSSimpleDB;
			path = AWSSimpleDB;
			sourceTree = "<group>";
		};
		931E52EA987558A8602B91E699CBDFED /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				BC7CB058A3957AC4D9C3CB7D47273206 /* Pods-SnapInspect3 */,
				68FAB5CD3515A90A29C7DD455FB8DFCB /* Pods-SnapInspect3Tests */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		B89F57EDCAB14D61E131E8E71D6B0A84 /* Resources */ = {
			isa = PBXGroup;
			children = (
				B86D8491319968018CDF9D79B418C700 /* html_colors.json */,
				42E7FFC595DDA2DA6367E0BD1322713B /* Media.xcassets */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		BC7CB058A3957AC4D9C3CB7D47273206 /* Pods-SnapInspect3 */ = {
			isa = PBXGroup;
			children = (
				8C1477268A32B3F2E038654C21706386 /* Pods-SnapInspect3.modulemap */,
				995866F4E62AFEFB7B0B8D4CD643057A /* Pods-SnapInspect3-acknowledgements.markdown */,
				F799409FBBBFFF3DBB27A1413D712D74 /* Pods-SnapInspect3-acknowledgements.plist */,
				A908D8227F1E42268F80EA280762725A /* Pods-SnapInspect3-dummy.m */,
				B93713E9E814D5C3D4FC2578B1E8D053 /* Pods-SnapInspect3-frameworks.sh */,
				D3B39C2207688A9CED3631CC39C87B3E /* Pods-SnapInspect3-Info.plist */,
				DB11D3FD6BC33D77BB6D82183398392E /* Pods-SnapInspect3-umbrella.h */,
				F1E1C31E6F9833AF2280F8283372F395 /* Pods-SnapInspect3.debug.xcconfig */,
				B42EE2D57EB703D1BF8AB71C3B9FDB31 /* Pods-SnapInspect3.release.xcconfig */,
			);
			name = "Pods-SnapInspect3";
			path = "Target Support Files/Pods-SnapInspect3";
			sourceTree = "<group>";
		};
		C56DB110F133090AE6D5115ED22B6229 /* WordPress-Editor-iOS */ = {
			isa = PBXGroup;
			children = (
				FF81762FE99A9978A8B94631E05653D7 /* AutoPProcessor.swift */,
				B9A442ED36519EB4C4BAF837A19B5A49 /* CaptionShortcodeInputProcessor.swift */,
				68DBF6A14B2F3292CB8AE931B00D4AA7 /* CaptionShortcodeOutputProcessor.swift */,
				BF341CD0B675238BFEC0443733F1BBD9 /* CommentNode+Gutenberg.swift */,
				F975BDE7BFE498FFEAD0E12DEC01670B /* EmbedURLProcessor.swift */,
				DD9025ADA9F348524356867F4D7C2AF2 /* GalleryAttachment.swift */,
				CF4ECA173328DA930216D26C29718794 /* GalleryAttachmentToElementConverter.swift */,
				B85C045D7629FC0E880202F650B37877 /* GalleryElementConverter.swift */,
				09D5992C26C541102E2F4D3CEA3ABFB2 /* GalleryElementToTagConverter.swift */,
				8034E723B38DF6B087138D044FD8D296 /* GalleryShortcodeInputProcessor.swift */,
				423A40F8AF0C61C887E96EEE9820403F /* GallerySupportedAttribute.swift */,
				42BE4BB4FA24D3AB56CF7DFD6A2A1E50 /* GutenbergAttributeDecoder.swift */,
				CC301C3181DDEFDAA75801B80996AB23 /* GutenbergAttributeEncoder.swift */,
				F96508A77801158BFFDF20B1046D8011 /* GutenbergAttributeNames.swift */,
				D9A2F0252AC9448237DEEFA677853A89 /* GutenbergInputHTMLTreeProcessor.swift */,
				D7748D5F8C9CABE568A6A1AAB5F7FF5E /* GutenbergOutputHTMLTreeProcessor.swift */,
				1C3C646B55D8159F3245C63650B4CC41 /* Gutenblock.swift */,
				D7B8CB609AB33A42406F276FE6C1D1A9 /* GutenblockConverter.swift */,
				C8F6CBB1764E09A11791565EA0D7FC7A /* GutenpackAttachment.swift */,
				7240A1FFFCBE40594C39FA603F44590E /* GutenpackAttachmentRenderer.swift */,
				166C2FDE683B09A9E70C7CBDB25649B6 /* GutenpackAttachmentToElementConverter.swift */,
				C5198901E04E501A833F0FF49F1EAFAE /* GutenpackConverter.swift */,
				6DBE0C1F3C2495032E38C84CD46909A7 /* ImageAttachment+WordPress.swift */,
				093DAD6CB8CBFCC6E95A59F369AF655A /* MediaAttachment+WordPress.swift */,
				4D3E0AA31BFC20022F1FD3C1A73CD590 /* OptionsTablePresenter.swift */,
				C33AD5851F6175CE30C9A55A7BEA503D /* OptionsTableViewController.swift */,
				0A580597DC2DF8CA22F274EBF1A56C78 /* RemovePProcessor.swift */,
				6BE43D121C2414F242B3AC1077A2B67B /* ShortcodeProcessor.swift */,
				93A3ECEAA852629251E567DD02905F1E /* SpecialTagAttachmentRenderer.swift */,
				B7F581F8772EBEEC50F4C0F8F3D1DBF9 /* String+RegEx.swift */,
				0D3DA541B91685DAC2E73BCF60E667CD /* VideoAttachment+WordPress.swift */,
				AB5FD88DF442EF74E7ADC537ED171E54 /* VideoShortcodeProcessor.swift */,
				4D41FB80E50F514C1979F3200F232C77 /* WordPressInputCustomizer.swift */,
				3661F17A73E95C0DE5265638C5025A94 /* WordPressOutputCustomizer.swift */,
				DC95FC2B3239D664802460A2BAD3E045 /* WordPressPasteboardDelegate.swift */,
				5CA57AAD93FB7DA49B17A49D9BA1F5FE /* WordPressPlugin.swift */,
				4E170486B02F2237ECF75D121C412DAD /* Resources */,
				05A48C37CADBF2B91CD0EEEC18644875 /* Support Files */,
			);
			name = "WordPress-Editor-iOS";
			path = "WordPress-Editor-iOS";
			sourceTree = "<group>";
		};
		C8B0AA9F2B5F46212DEE592DB5C87F96 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				423D933DAFF2D5FEF249E738E07D5ADD /* IQKeyboardManager.modulemap */,
				55F36AD1259D4BB93C53F2402D2EBD95 /* IQKeyboardManager-dummy.m */,
				2719DFEA52EB04A9F9238C185EB5ED5F /* IQKeyboardManager-Info.plist */,
				3846ADF2FD96D5AB73C6E12A70C9C35A /* IQKeyboardManager-prefix.pch */,
				17B3917ED068AB56D3810991CF31059A /* IQKeyboardManager-umbrella.h */,
				1E74A73E3762665118306365F95453FC /* IQKeyboardManager.debug.xcconfig */,
				C1B424D9A4ED7DC7AF7B7C7B262C16D0 /* IQKeyboardManager.release.xcconfig */,
				7222CE33BB26754480137A5A06A24C53 /* ResourceBundle-IQKeyboardManager-IQKeyboardManager-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/IQKeyboardManager";
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				8275CCB4E8732497B1021FCBC240F3C2 /* Frameworks */,
				0D2984BBA92DCB344E9ADB4FAEC931B3 /* Pods */,
				85CE9BDE28E6FCB201352A1084A2537F /* Products */,
				931E52EA987558A8602B91E699CBDFED /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		E06F90E2F06C24D57D10C92303EA1079 /* UITextView+Placeholder */ = {
			isa = PBXGroup;
			children = (
				7DDBECEED799A7A43F80F855AA433018 /* UITextView+Placeholder.h */,
				8A9C455940ED58B87DB93AE3AF77AB40 /* UITextView+Placeholder.m */,
				EFF5E9E02F7FFD9F7174EDFCAE6E204B /* Support Files */,
			);
			name = "UITextView+Placeholder";
			path = "UITextView+Placeholder";
			sourceTree = "<group>";
		};
		E535AD8F04C5E4441DFA24A91712B7BB /* Resources */ = {
			isa = PBXGroup;
			children = (
				52075A5E43EA9D657F0C3D72D9E3723C /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		EFF5E9E02F7FFD9F7174EDFCAE6E204B /* Support Files */ = {
			isa = PBXGroup;
			children = (
				7A16A5E3CF3693344B4A482033D39E53 /* UITextView+Placeholder.modulemap */,
				8FAB08C499E17F0B00712D1A24584AD9 /* UITextView+Placeholder-dummy.m */,
				EABD0A4FAE87DDEB84E8CE005A706B75 /* UITextView+Placeholder-Info.plist */,
				E37011BCFBBA8A6BD4F0338BDB01D86C /* UITextView+Placeholder-prefix.pch */,
				18629905972CCFFCFA7F9DA8F8EC371F /* UITextView+Placeholder-umbrella.h */,
				C4EAAC03AA682321088C5C7E119902F4 /* UITextView+Placeholder.debug.xcconfig */,
				0AD9939574ACB24611ED895616022021 /* UITextView+Placeholder.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/UITextView+Placeholder";
			sourceTree = "<group>";
		};
		F78508D91C936154974A2598BFF8A09D /* CustomIOSAlertView */ = {
			isa = PBXGroup;
			children = (
				489C7F9CA1EBAD893492F836A60B3984 /* CustomIOSAlertView.h */,
				B17D712E52732810E6EBB6A84BDEC604 /* CustomIOSAlertView.m */,
				8F4A262D08DFBD4E49C3D5650041AC95 /* Support Files */,
			);
			name = CustomIOSAlertView;
			path = CustomIOSAlertView;
			sourceTree = "<group>";
		};
		FC927DCC664897A522D3B7FEB5829BCD /* AWSS3 */ = {
			isa = PBXGroup;
			children = (
				A59475F444CA3AC4FDC597B6C0C671CC /* AWSS3.h */,
				02134B9202DD57A98E9EE85B2EA527AE /* AWSS3Model.h */,
				E1318762F8470F95594D57AD21E65A92 /* AWSS3Model.m */,
				DCCC8562DCFD1E3FCC9A183247D40ED8 /* AWSS3PreSignedURL.h */,
				CDD7A04EDE672FB8DF3E2F05A8272A51 /* AWSS3PreSignedURL.m */,
				834D5D87CAFE0E36DDBBB77A63249B4A /* AWSS3RequestRetryHandler.h */,
				DF5C99563404E05B7CED21B339F1E03F /* AWSS3RequestRetryHandler.m */,
				530A85936D44B96232C8AE5E4FA9A7CF /* AWSS3Resources.h */,
				BED37574F7251385DF55FAB4D8BA7D1C /* AWSS3Resources.m */,
				B11BE74E22A12A4098F043BF3B407E34 /* AWSS3Serializer.h */,
				44779C7C477FE3FAFE32447C23B1FF27 /* AWSS3Serializer.m */,
				290B681D6D19B19A2AB302958798CEFE /* AWSS3Service.h */,
				5298F51E389DB6ECECC55D425173061F /* AWSS3Service.m */,
				AD7FFD189A8598CD3AAF4E564F7ACBED /* AWSS3TransferManager.h */,
				97178E4CD9890DD8945143B4F35552F6 /* AWSS3TransferManager.m */,
				61050C66A590F543EEF7CE93E4D0C371 /* AWSS3TransferUtility.h */,
				4BAB18119B32D5084251313A4A4A0E64 /* AWSS3TransferUtility.m */,
				E5B3325338CB083CF942BBE997C46E02 /* AWSS3TransferUtility+HeaderHelper.h */,
				0110D202E8D83129762D378FDDF143D4 /* AWSS3TransferUtility+HeaderHelper.m */,
				D819C408A8ADCF07B67904A8D572502E /* AWSS3TransferUtility+Validation.m */,
				877AF6C35718C06CF18D7634D6655A04 /* AWSS3TransferUtilityDatabaseHelper.h */,
				6A0DD016CF6AEC8D5E8AA37B55DEB66A /* AWSS3TransferUtilityDatabaseHelper.m */,
				139FA8BD67D5FBBEF0E0632A2BCF2CDD /* AWSS3TransferUtilityTasks.h */,
				1B8F6DF217C03167DEA1195A385C6E4D /* AWSS3TransferUtilityTasks.m */,
				79F18515CD6DD5DCF742A8C230CD84FD /* Support Files */,
			);
			name = AWSS3;
			path = AWSS3;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		0EF2526B5CC15C0E915F32190565F64C /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BA904ABA8ED36CC4E5EB2B2004CA1F18 /* MASCompositeConstraint.h in Headers */,
				37B890ABDC7DD441E6AA662325D412E6 /* MASConstraint.h in Headers */,
				7C5505A2D3F2A697A5F324787061F4B7 /* MASConstraint+Private.h in Headers */,
				813BE4C96A6D39C13EC50C6CD164F0AF /* MASConstraintMaker.h in Headers */,
				B680C2604BD8BC9644AE7C67BC46B9BB /* MASLayoutConstraint.h in Headers */,
				EC9B34262AED632D7EFB49804337648E /* Masonry.h in Headers */,
				B59E60FBC9665FC1061B88B8E6FD9FAF /* Masonry-umbrella.h in Headers */,
				C2068AEACC2D9C7F1FFE41AA25B12A68 /* MASUtilities.h in Headers */,
				05E2B7C1DB7528A0BBEA1521BE0DBAF1 /* MASViewAttribute.h in Headers */,
				5F45735DF355530CC955066D3C007E19 /* MASViewConstraint.h in Headers */,
				BF22D137EF6324675FA50080C5D93C00 /* NSArray+MASAdditions.h in Headers */,
				61507E402F1F7C58BF119995A0479A22 /* NSArray+MASShorthandAdditions.h in Headers */,
				DBA9500CBBA5FF6FCBBA115AE4D12152 /* NSLayoutConstraint+MASDebugAdditions.h in Headers */,
				AE7B02645B8F769CA5F215EE8F7CC5B0 /* View+MASAdditions.h in Headers */,
				772CF8E9CD02ECA4275B6173E2110E80 /* View+MASShorthandAdditions.h in Headers */,
				8C6C7E25C5A24C936F81823978190E96 /* ViewController+MASAdditions.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		216553A3943E5A08A7E405D5F9AEE9B9 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				37F4A6A58892C4E564DD54EB0E6F2BB9 /* WordPress-Editor-iOS-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3A532350EA8D0E4983ADC1E248E2755C /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B6CE5CB8D1E8F6BA89981E381A15C9D3 /* AWSSimpleDB.h in Headers */,
				73A23EF5AC075A85768E9EE448C0539A /* AWSSimpleDB-umbrella.h in Headers */,
				87A7E8CB9968B54066F8C0DC8403A84D /* AWSSimpleDBModel.h in Headers */,
				1A672ED193BC9C15B6D2C834DE290C7F /* AWSSimpleDBResources.h in Headers */,
				F3C4DCDD47543BCD85C33E2BFB94017C /* AWSSimpleDBService.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4E6E7343BD4EC4A73A85A123A454D5B8 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				55EAB8232F1416B795ACC9ABE8A8C60B /* AWSBolts.h in Headers */,
				F34C7DA40E83F15A848792C487BE9AC0 /* AWSCancellationToken.h in Headers */,
				5584F96BEF00BE7F9B5A115516C36B5A /* AWSCancellationTokenRegistration.h in Headers */,
				DC767EFA03E7E8E90612A00D6ABD8409 /* AWSCancellationTokenSource.h in Headers */,
				27458707BD64D8AB517D236B670BF054 /* AWSCategory.h in Headers */,
				C17E23EA7CB94CF9D81CA6A2199170A4 /* AWSClientContext.h in Headers */,
				FD4DBB5BE5BAC3F0410DFF70172CC90B /* AWSCocoaLumberjack.h in Headers */,
				2B3CC3063E0D51FE6BFC168CC0EE78B9 /* AWSCognitoIdentity.h in Headers */,
				5ADB604C209759FE6349991CA6D6095F /* AWSCognitoIdentity+Fabric.h in Headers */,
				141AA3770501900BBCD270C6FC43A958 /* AWSCognitoIdentityModel.h in Headers */,
				328F038CA4B20997FF19A8C670DCBF0D /* AWSCognitoIdentityResources.h in Headers */,
				8F5A68D306F8535EB095822EC9C481EC /* AWSCognitoIdentityService.h in Headers */,
				AC4EEC43C635548767A9CF8FE52335AB /* AWSCore.h in Headers */,
				BF93E5E0D4D22220CAA619BD3D6A2469 /* AWSCore-umbrella.h in Headers */,
				B35A5CD3A3A68D33BBB78CDBE06BD7A2 /* AWSCredentialsProvider.h in Headers */,
				186B93994773FC73AAF4384EAAAAE7ED /* AWSDDAbstractDatabaseLogger.h in Headers */,
				6DB1D70618CF8A0F33E75E7FD397C3AD /* AWSDDASLLogCapture.h in Headers */,
				9A7D153A7D4F200B9779B1084601AD4C /* AWSDDASLLogger.h in Headers */,
				5DAA92258EE62D8371641B30383D1149 /* AWSDDAssertMacros.h in Headers */,
				F8D47FB527D982DA43E870A088504A5A /* AWSDDContextFilterLogFormatter.h in Headers */,
				83DEC5A25771F8D7A58E1A4F626C3C8F /* AWSDDDispatchQueueLogFormatter.h in Headers */,
				3E50B8AB03669BEDC2C702F974119837 /* AWSDDFileLogger.h in Headers */,
				40C8F7152C7194608DBB8F0B5379CA92 /* AWSDDLegacyMacros.h in Headers */,
				7AF84C725A959E87E2AE069CCB62087D /* AWSDDLog.h in Headers */,
				A6D37161CA529498C758D7DF2F2BBF43 /* AWSDDLog+LOGV.h in Headers */,
				B80408732932015A5FCCA0D7E240E50A /* AWSDDLogMacros.h in Headers */,
				43D59F9203A119ADA3D29829BE4227B3 /* AWSDDMultiFormatter.h in Headers */,
				24D5D985D8325FAC4927DBBD2E167EF8 /* AWSDDOSLogger.h in Headers */,
				41036CB95CF0F10B8E667204DE604E44 /* AWSDDTTYLogger.h in Headers */,
				4AA5CC38B853C31501C69D7D4FD6CA99 /* AWSExecutor.h in Headers */,
				585E0A795B394F93D75D408AFC66A684 /* AWSEXTKeyPathCoding.h in Headers */,
				789B4B8A9289366110589815260C2C44 /* AWSEXTRuntimeExtensions.h in Headers */,
				90F2ACAAAFD2178B9624E59F2F98454B /* AWSEXTScope.h in Headers */,
				06A4D6EF860B9C6C1C08F31D98679C8C /* AWSFMDatabase.h in Headers */,
				602BC21CD18C6BBCF019E2A1300CA969 /* AWSFMDatabase+Private.h in Headers */,
				903DAF1F6469E0537564628DD141E653 /* AWSFMDatabaseAdditions.h in Headers */,
				DDDEE42EF767C17D9C31D99EE8399431 /* AWSFMDatabasePool.h in Headers */,
				5D5BAB5D75CC70996D46A73A54184DE8 /* AWSFMDatabaseQueue.h in Headers */,
				48A76FE1B092845BF44F304C02FB0203 /* AWSFMDB.h in Headers */,
				BCE64D07A736DC4115877184105A6359 /* AWSFMDB+AWSHelpers.h in Headers */,
				1A46F83233F438F9784FBA3597A9ABCC /* AWSFMResultSet.h in Headers */,
				FB43CBE2CA4DFCB42306090CA0351B87 /* AWSGeneric.h in Headers */,
				3A9B4A5BDD7C5D398E87C541A32AE6C5 /* AWSGZIP.h in Headers */,
				9B5350E66C4080EF67A8D3B0798CBB8C /* AWSIdentityProvider.h in Headers */,
				241E50AE70329797FA02B2824B1F4483 /* AWSInfo.h in Headers */,
				A45D3B525173E3BF3D1874FFBD38FAA1 /* AWSKSReachability.h in Headers */,
				3C4AEE2C983897CC79457CD04B4B39F7 /* AWSLogging.h in Headers */,
				220B1ECDDAF00DEC680813C69EE90CFA /* AWSMantle.h in Headers */,
				DD2E2E77E29589A3D40BE1ABCAEB3E1B /* AWSmetamacros.h in Headers */,
				22E4919703896BB1E502DDF2F29CCC1E /* AWSModel.h in Headers */,
				05A4AD131328F352F02B30FF4C3FC70E /* AWSMTLJSONAdapter.h in Headers */,
				2B40B09A5D909798F8DE56E5DBEA9EE2 /* AWSMTLManagedObjectAdapter.h in Headers */,
				53123E10EED954475D585D32A00BF4D2 /* AWSMTLModel.h in Headers */,
				C3E4B5284CE32F2A906C3FDB050BB10F /* AWSMTLModel+NSCoding.h in Headers */,
				C4E8F0B8D79C3CB823FA4134E0395C6F /* AWSMTLReflection.h in Headers */,
				F5593A9079FCE83FF3D1497FC3CB3E50 /* AWSMTLValueTransformer.h in Headers */,
				B0393F89A2B7C009A2485180C33FF531 /* AWSNetworking.h in Headers */,
				24B21140F5975144219214CED0A1B677 /* AWSNetworkingHelpers.h in Headers */,
				D81624EF7B3F4CBB6805A746D28276A0 /* AWSSerialization.h in Headers */,
				BCC2052AA36DDA1A437094153C40173D /* AWSService.h in Headers */,
				774EE7A080C432272A311B73A98B6EC0 /* AWSServiceEnum.h in Headers */,
				798C43E48CD423C85EB15603B8AF64E1 /* AWSSignature.h in Headers */,
				4C88652FD272955B4A627F4503D82617 /* AWSSTS.h in Headers */,
				8C5293636315107327511C4706E02869 /* AWSSTSModel.h in Headers */,
				8FDF46C01937AD224D17B02749DCB60F /* AWSSTSResources.h in Headers */,
				7D7576F49D628A82DBD1425A1157D106 /* AWSSTSService.h in Headers */,
				1FD498A9CD395BB6B0AF553D09A9DE07 /* AWSSynchronizedMutableDictionary.h in Headers */,
				807833A26E72FBE293BE8C1D47663554 /* AWSTask.h in Headers */,
				64BC2B856E9972A9D1CB23B5C50D9A81 /* AWSTaskCompletionSource.h in Headers */,
				9C0CB58BFF96BC49B2FC80F028DBD640 /* AWSTMCache.h in Headers */,
				01F8F2EA8144B8587E3691C78F1C60F4 /* AWSTMCacheBackgroundTaskManager.h in Headers */,
				4CC111CA5180A2E971D283026D7E5FE1 /* AWSTMDiskCache.h in Headers */,
				6E963F8B93F83C4C0C02060F74F1484F /* AWSTMMemoryCache.h in Headers */,
				3F38FD7AC1B97A972D40397A017A656E /* AWSUICKeyChainStore.h in Headers */,
				FF1E08069DCD8B9DBEE1BC6CB8AB24B9 /* AWSURLRequestRetryHandler.h in Headers */,
				57FD10F4B92DD290E280DE6132518574 /* AWSURLRequestSerialization.h in Headers */,
				8EDA722335D5E63EC02FEC6A7B6583FE /* AWSURLResponseSerialization.h in Headers */,
				E08AEE1EA3BD7FFDAD6C4EB77DFED690 /* AWSURLSessionManager.h in Headers */,
				8473CBA5295F8C35F24EB3F6A11ECC01 /* AWSValidation.h in Headers */,
				E5B4E349010EE3231276EDA4D80203F4 /* AWSXMLDictionary.h in Headers */,
				F64235CF66746719F1801082B7C681E8 /* AWSXMLWriter.h in Headers */,
				1D033AA874891F26C74B612FCECAAC62 /* FABAttributes.h in Headers */,
				369469F63D4702E7EB246DB0634F4F94 /* FABKitProtocol.h in Headers */,
				01DED22B5FF4908B3932E63CDB95DE5D /* Fabric.h in Headers */,
				701A6794B2E9008E30D65D449A20EA72 /* Fabric+FABKits.h in Headers */,
				51FBB691D503F5C8A063CF8B72631906 /* NSArray+AWSMTLManipulationAdditions.h in Headers */,
				81A4A9DD649CC8732FFA2658F8BA556C /* NSDictionary+AWSMTLManipulationAdditions.h in Headers */,
				872C8AEB1CCE54C7B4F28FD7917867AB /* NSError+AWSMTLModelException.h in Headers */,
				303BEF8D208D1A4FF76F8A124C365AD5 /* NSObject+AWSMTLComparisonAdditions.h in Headers */,
				D26D4ACE75E28BFD688A7FC34E7E2724 /* NSValueTransformer+AWSMTLInversionAdditions.h in Headers */,
				0B62E7CFFED5AB95373F9C1B0D7ED79B /* NSValueTransformer+AWSMTLPredefinedTransformerAdditions.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		66034C50B03081851637D151F1398D08 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8AC0B8D04477D2A90C0569725A916DB2 /* Pods-SnapInspect3Tests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D4C7C463093D83B7007BF12CC4598E2 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				15B6F25A01F1FF8F3308D6BB5CC0FCA1 /* Pods-SnapInspect3-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9EF23DDB8F73EC82999CC5D9EEA7D8F9 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E4D864469B4B100A1D4367FB4F7D4744 /* CustomIOSAlertView.h in Headers */,
				5658F281FBBE3F8F1369030129EEA4D2 /* CustomIOSAlertView-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B9FF5EEF84E6468407E07CCC8443D07E /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A7CD8657A6BA24F11EE4D69E1B109183 /* UITextView+Placeholder.h in Headers */,
				8C66DF5DC308823B9AE925936C0EEDD1 /* UITextView+Placeholder-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BB76FB4F5874A2A8FCBBF10E935F34AA /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BBC736908D19B1C1EADCF4FCA61A799A /* WordPress-Aztec-iOS-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C3DA937DAFC84047E44F932A933E587B /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9737FEFD384873E344D44FDA23E57B4A /* CYRLayoutManager.h in Headers */,
				9525717DB59E3A40DF24083E89652C7D /* CYRTextStorage.h in Headers */,
				8A68E9A4D8CF213E9155F1E732FFDC32 /* CYRTextView.h in Headers */,
				868B44496A68FFF6D364DD9548078DCC /* CYRToken.h in Headers */,
				F24267E4F85DF7F637AFD9501DA7451D /* DemoModalViewController.h in Headers */,
				D6284116E531C0CBC1D105F2FE889482 /* HRBrightnessCursor.h in Headers */,
				69DC58C397AFC977E33BBA462052A429 /* HRCgUtil.h in Headers */,
				A345B1A04A58BA0633FCA36EB93C6E12 /* HRColorCursor.h in Headers */,
				E2269D51AC0E34E1E8BCE30DA1C58CF1 /* HRColorPickerMacros.h in Headers */,
				85FDE1091AD58CF459CE9B76AA22F250 /* HRColorPickerView.h in Headers */,
				C4E861F81827FDA1C7C9A40C825170AA /* HRColorPickerViewController.h in Headers */,
				C943FF49078B48077D8C18CCF95EF92F /* HRColorUtil.h in Headers */,
				6955DF9A8A7817124BCEA8A80B064C55 /* ZSSBarButtonItem.h in Headers */,
				BE07851962EBF324076FE46BE140C369 /* ZSSColorViewController.h in Headers */,
				303C1DD88B45BBD8CD7DA1AA6AC67AD9 /* ZSSCustomButtonsViewController.h in Headers */,
				90DA11ED2C482EBF858FC8849E6A793E /* ZSSFontsViewController.h in Headers */,
				BEF52BF81ED0BA3209B667B5BBDE54A5 /* ZSSLargeViewController.h in Headers */,
				C4D098CE7C4639568056A9158326CAA8 /* ZSSPlaceholderViewController.h in Headers */,
				AE5AB9DB70374552A957C7537FE17F3B /* ZSSRichTextEditor.h in Headers */,
				2A8890C3CD6F19A439ACCDB9F2D61B47 /* ZSSRichTextEditor-umbrella.h in Headers */,
				6F1996C947A78940C56A417EDF23AB4C /* ZSSSelectiveViewController.h in Headers */,
				34F12F259EFE456D9AB87F623C2DFE86 /* ZSSTextView.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D89C5D94DF797A3BB5781D994B281126 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A313478E310288704A456C9CD331A80D /* AWSS3.h in Headers */,
				C1DFF7ADC039E3DCB58901C70783F5B4 /* AWSS3-umbrella.h in Headers */,
				6C464AEBFE8E85A9FFDE4B09C107C788 /* AWSS3Model.h in Headers */,
				5B8932C9DDF49E45E773E87920FA85D7 /* AWSS3PreSignedURL.h in Headers */,
				7E474EAFA5E2F9EB78F8FEC9E38B02A8 /* AWSS3RequestRetryHandler.h in Headers */,
				AEDAE00D2AB83EAC7A02BC87F7AE2833 /* AWSS3Resources.h in Headers */,
				F0988B5B3762B8B65EC2E3EF3F05F969 /* AWSS3Serializer.h in Headers */,
				D5100C41EC22C42FE239C5300F212254 /* AWSS3Service.h in Headers */,
				A9381834B25FCD33A5E5D39D0E0E7D99 /* AWSS3TransferManager.h in Headers */,
				6AD3E37014DE84F78EF20D20679FBFFA /* AWSS3TransferUtility.h in Headers */,
				A1E24C792F92594B3F3BF116F1C02BC2 /* AWSS3TransferUtility+HeaderHelper.h in Headers */,
				9FE8DB8B14D951A4B62248AC137C7B42 /* AWSS3TransferUtilityDatabaseHelper.h in Headers */,
				19F75BF66A04D712F9390236D00A1623 /* AWSS3TransferUtilityTasks.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		ECBAD71CACB0F1B5E879E5B32AAD5A2D /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CBA4E86157FC8473E597F0D7050B5E4C /* IQBarButtonItem.h in Headers */,
				F0F8EE59EDB86336E29CCA58CA100EDE /* IQKeyboardManager.h in Headers */,
				5C88E1B78D6724C19D070FCCE10704CB /* IQKeyboardManager-umbrella.h in Headers */,
				8C2CD923B27029D3C602AF7638F02DF6 /* IQKeyboardManagerConstants.h in Headers */,
				80BC5DE1CE16235D6166643E18874114 /* IQKeyboardManagerConstantsInternal.h in Headers */,
				836F7C86B67D8B9DC07E92DA3454E86C /* IQKeyboardReturnKeyHandler.h in Headers */,
				65DB6D055AAD5F89EFB33C554CA7F53B /* IQNSArray+Sort.h in Headers */,
				44339C00EEAC1F42F4BD9F5FC1C93FA0 /* IQPreviousNextView.h in Headers */,
				60F8F5D53A696B13D2E88CD5892A92CF /* IQTextView.h in Headers */,
				700BA5C6D7BFE8251E95B6AAC1AEADE7 /* IQTitleBarButtonItem.h in Headers */,
				3DABBFDABC03A66B4329A30A0C23BF44 /* IQToolbar.h in Headers */,
				4ACB6A663BCFB08A0A8158F6463CF563 /* IQUIScrollView+Additions.h in Headers */,
				F07D340DEDD5F2378761E235B3963348 /* IQUITextFieldView+Additions.h in Headers */,
				1D00181E61A74B66B145897150943E83 /* IQUIView+Hierarchy.h in Headers */,
				1B9D8386A8FFB1D6F356D2CF34C963C8 /* IQUIView+IQKeyboardToolbar.h in Headers */,
				F460B8D9827F2F2D722A47C28B2D4C95 /* IQUIViewController+Additions.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		05B2A835D60F78761395189914B88047 /* IQKeyboardManager-IQKeyboardManager */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A6897EFCA05D270738AB2D00B8924318 /* Build configuration list for PBXNativeTarget "IQKeyboardManager-IQKeyboardManager" */;
			buildPhases = (
				188F00C16FA196EA40994FAE8F1EB88C /* Sources */,
				2B1F764647943B12FE0420886AD8C0EE /* Frameworks */,
				DD1688245140D2BBD0DF6DD2DB5E4593 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "IQKeyboardManager-IQKeyboardManager";
			productName = IQKeyboardManager;
			productReference = A5B2838E4866923F3DA717B058EBFF5D /* IQKeyboardManager-IQKeyboardManager */;
			productType = "com.apple.product-type.bundle";
		};
		31F0084E2E60CA68AAF7E3224C77C86E /* AWSS3 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 892DC5D9947F6C1ED7F2241A62F9AF10 /* Build configuration list for PBXNativeTarget "AWSS3" */;
			buildPhases = (
				D89C5D94DF797A3BB5781D994B281126 /* Headers */,
				D6AAD65A5B083806F82A72522385C952 /* Sources */,
				351485811EA2D91881CB81DD3ED1CB04 /* Frameworks */,
				2B7E9B959C75365458D8B9F4B8E5E64B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				4C9A2B7124D1C36BE37F015714FF7336 /* PBXTargetDependency */,
			);
			name = AWSS3;
			productName = AWSS3;
			productReference = 1D216CE4A8B6F50D603960D17EE072C6 /* AWSS3 */;
			productType = "com.apple.product-type.framework";
		};
		52036133E4B9C2D1B79378F2A9B7BFE4 /* WordPress-Editor-iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2047E4C7B8EDB059FD20EA06658CB9F9 /* Build configuration list for PBXNativeTarget "WordPress-Editor-iOS" */;
			buildPhases = (
				216553A3943E5A08A7E405D5F9AEE9B9 /* Headers */,
				01ECAF374F4F5DF0ADEEE4DA1FC20F94 /* Sources */,
				90462950923247E9523FDB0E6B48F598 /* Frameworks */,
				5F238C099375761AD253ACE4964A2DD5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C1CDD8C75320565732873D1380734D4D /* PBXTargetDependency */,
			);
			name = "WordPress-Editor-iOS";
			productName = WordPressEditor;
			productReference = A630A05CA0CA688C3B11F4807D366C77 /* WordPress-Editor-iOS */;
			productType = "com.apple.product-type.framework";
		};
		55AF53E6C77A10ED4985E04D74A8878E /* Masonry */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AAA1F8799DB68036C3BE983C05FAA2C7 /* Build configuration list for PBXNativeTarget "Masonry" */;
			buildPhases = (
				0EF2526B5CC15C0E915F32190565F64C /* Headers */,
				DA0B6A6F9B3EDF226BF081DAC7E777E7 /* Sources */,
				12A799DC8ABB2C283ADDDED4421A5EAB /* Frameworks */,
				ECD6B9A8E754DF142B323DF2D7E0D112 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Masonry;
			productName = Masonry;
			productReference = 1FFED36A657123030ABB700256D73F15 /* Masonry */;
			productType = "com.apple.product-type.framework";
		};
		6AC97A319172C716659A6283A2D82401 /* WordPress-Aztec-iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 957A159903F40D56564D95C99EABDFC0 /* Build configuration list for PBXNativeTarget "WordPress-Aztec-iOS" */;
			buildPhases = (
				BB76FB4F5874A2A8FCBBF10E935F34AA /* Headers */,
				66BFD3D76C600C077E8B2E9A95E9917D /* Sources */,
				DAF44B18145F9D761F81C0BBD28FCFAA /* Frameworks */,
				15E46F0C28A2643681FA91176889EE1F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				BA2FA5A01DE526C4028CE7A13FA47A86 /* PBXTargetDependency */,
			);
			name = "WordPress-Aztec-iOS";
			productName = Aztec;
			productReference = 337B4ACD9A49A40116934009DFFB7FFD /* WordPress-Aztec-iOS */;
			productType = "com.apple.product-type.framework";
		};
		73305835456A90447978F6D2F459C81A /* AWSSimpleDB */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F85814F662D635876EFB8EE2979FD3D8 /* Build configuration list for PBXNativeTarget "AWSSimpleDB" */;
			buildPhases = (
				3A532350EA8D0E4983ADC1E248E2755C /* Headers */,
				82511632882DBA9D19E90B11F4C6D40E /* Sources */,
				377B59795CF36DFDA02F63A09E32D27A /* Frameworks */,
				1A3A3662C2BF061CEC7C2D3126049EDD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				6ED5D2F052690EE0607594ACB4FF6246 /* PBXTargetDependency */,
			);
			name = AWSSimpleDB;
			productName = AWSSimpleDB;
			productReference = D78EA46DC425CA54AC914AA35DB7AC9E /* AWSSimpleDB */;
			productType = "com.apple.product-type.framework";
		};
		94DBE1F02563D90F416A7DB9558489B2 /* UITextView+Placeholder */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 239B9A75CC69D3964BA7F7A7410D589D /* Build configuration list for PBXNativeTarget "UITextView+Placeholder" */;
			buildPhases = (
				B9FF5EEF84E6468407E07CCC8443D07E /* Headers */,
				AD3174C2DBD17EB12B62B5F7DC837E16 /* Sources */,
				A56843A19DB4E4EDBB19F2C2DF79CA3B /* Frameworks */,
				3EDBA999EF8B2EEAD6A7B95C5A7D8B01 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "UITextView+Placeholder";
			productName = UITextView_Placeholder;
			productReference = F0C7FE88342E3772A8A133204A1E1910 /* UITextView+Placeholder */;
			productType = "com.apple.product-type.framework";
		};
		9B172FACE90046AA5E100E650B6109DD /* AWSCore */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1F170271388A077284D01A4A6CFE110F /* Build configuration list for PBXNativeTarget "AWSCore" */;
			buildPhases = (
				4E6E7343BD4EC4A73A85A123A454D5B8 /* Headers */,
				D1BA6B02A0339B331261C54EFAF13627 /* Sources */,
				4359F1CE9D099ABD7E09B5506FDE3086 /* Frameworks */,
				7D0479452B9CF662C925B0841FF657D9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AWSCore;
			productName = AWSCore;
			productReference = CCFB8F03867DB892EFC47B53186590C4 /* AWSCore */;
			productType = "com.apple.product-type.framework";
		};
		A8A26D58F4832E00EBE5951F0425C233 /* WordPress-Aztec-iOS-WordPress-Aztec-iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 45E6DA44288B44F53737AD256C91A6E1 /* Build configuration list for PBXNativeTarget "WordPress-Aztec-iOS-WordPress-Aztec-iOS" */;
			buildPhases = (
				87E05147F162775FF8CE466FDB1A03C9 /* Sources */,
				D1D37E786C94FD0CCF4E902C379B6A6D /* Frameworks */,
				64C2FE7EDD08E8BB0B842EB7FB82C71C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "WordPress-Aztec-iOS-WordPress-Aztec-iOS";
			productName = "WordPress-Aztec-iOS";
			productReference = C417EB24EA443F14B2EF5F35E63FA887 /* WordPress-Aztec-iOS-WordPress-Aztec-iOS */;
			productType = "com.apple.product-type.bundle";
		};
		AE386F1E55E7ABE13CA772EE75B8A9E9 /* Pods-SnapInspect3Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8747B4F8F5B4A7D1C561F461428A3347 /* Build configuration list for PBXNativeTarget "Pods-SnapInspect3Tests" */;
			buildPhases = (
				66034C50B03081851637D151F1398D08 /* Headers */,
				763E378FC392D26C93EEC77143F42CB9 /* Sources */,
				13AFF7075F479858CD5252F8DEA9B974 /* Frameworks */,
				03AEFB8E31C91C98EEAF69C236A2FF1C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				630E0BBA52572042756AC549624AC936 /* PBXTargetDependency */,
			);
			name = "Pods-SnapInspect3Tests";
			productName = Pods_SnapInspect3Tests;
			productReference = 65516CBFEC117BA6CB815E9F1AB8D668 /* Pods-SnapInspect3Tests */;
			productType = "com.apple.product-type.framework";
		};
		D7940BE184F37BA203D5586A4D2F87BF /* CustomIOSAlertView */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B57ABFF6818342A0B1A06435E66262CF /* Build configuration list for PBXNativeTarget "CustomIOSAlertView" */;
			buildPhases = (
				9EF23DDB8F73EC82999CC5D9EEA7D8F9 /* Headers */,
				374273EAB6BDEBA1F86F93675774D810 /* Sources */,
				FDF19883169DA0ADE6042BDD6F71170E /* Frameworks */,
				3CFE1372C4A53C95960A57A1973450FF /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = CustomIOSAlertView;
			productName = CustomIOSAlertView;
			productReference = D7556B2EB464D0DE8451B690BBFB5BB3 /* CustomIOSAlertView */;
			productType = "com.apple.product-type.framework";
		};
		DFF29EDE35ED89B0D385581C6035677E /* Pods-SnapInspect3 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 76939D389CDD9B4B104211E988FDE353 /* Build configuration list for PBXNativeTarget "Pods-SnapInspect3" */;
			buildPhases = (
				8D4C7C463093D83B7007BF12CC4598E2 /* Headers */,
				6CA777BE5374B0934E36110242648213 /* Sources */,
				3D16B15FA43160E3AAFCEBED7B52C7FE /* Frameworks */,
				65CFB189D6FD8676C41FDA66F77BECC1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				0FDD7FA84D3DE20ECBEA522C358E4278 /* PBXTargetDependency */,
				745CBDE752A0068F8286DF678B8DF1D5 /* PBXTargetDependency */,
				A7C21281FEDEB62F5E0654995A9C1030 /* PBXTargetDependency */,
				E976809C923D5ABCF2500626F033A972 /* PBXTargetDependency */,
				BDDB59D8EE0999BA91C5F97447FC5D48 /* PBXTargetDependency */,
				656B1925513049D94076B2EECF0C0523 /* PBXTargetDependency */,
				3231CAE586ECC7F88AA83BDDD0C7EBF5 /* PBXTargetDependency */,
				F279B5127A0B7B329825D449F69F2AD5 /* PBXTargetDependency */,
				0B8A11FDCB421BC05345065A79CC9F77 /* PBXTargetDependency */,
				E42BD9FA799C1F6A974E925E680C5F20 /* PBXTargetDependency */,
			);
			name = "Pods-SnapInspect3";
			productName = Pods_SnapInspect3;
			productReference = 063F586E7A8CB9B81ACDB8D916030C1B /* Pods-SnapInspect3 */;
			productType = "com.apple.product-type.framework";
		};
		EBF557DDD39B493EA318729B2036BAF5 /* ZSSRichTextEditor */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3574A7132E688330A708249089C9CF6D /* Build configuration list for PBXNativeTarget "ZSSRichTextEditor" */;
			buildPhases = (
				C3DA937DAFC84047E44F932A933E587B /* Headers */,
				8FFE90F650764269C1443F13063C2291 /* Sources */,
				4532A8BBDD5896C0299C7C1BDC26306F /* Frameworks */,
				4471E552E34FA0D38ADDF209918B5B9B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ZSSRichTextEditor;
			productName = ZSSRichTextEditor;
			productReference = 76E4C6B4DCC6284EF00F55C768956C69 /* ZSSRichTextEditor */;
			productType = "com.apple.product-type.framework";
		};
		FBA456CB50E371584C11231929A0971E /* IQKeyboardManager */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5FE03105CF3A887F508DDE7D404339E3 /* Build configuration list for PBXNativeTarget "IQKeyboardManager" */;
			buildPhases = (
				ECBAD71CACB0F1B5E879E5B32AAD5A2D /* Headers */,
				F6BE32C3CD0010A8BFCEE863F9ED6940 /* Sources */,
				7F025C68D7AB2CBEAA20335DC4A82BD9 /* Frameworks */,
				FFD11E7589893A9E2CC6A5C3CCAFEDE2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8FB10D82E473D5236AA7819FD2087167 /* PBXTargetDependency */,
			);
			name = IQKeyboardManager;
			productName = IQKeyboardManager;
			productReference = 98527D7196957AAB07B79E2E2AFDE23E /* IQKeyboardManager */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 15.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = 85CE9BDE28E6FCB201352A1084A2537F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				9B172FACE90046AA5E100E650B6109DD /* AWSCore */,
				31F0084E2E60CA68AAF7E3224C77C86E /* AWSS3 */,
				73305835456A90447978F6D2F459C81A /* AWSSimpleDB */,
				D7940BE184F37BA203D5586A4D2F87BF /* CustomIOSAlertView */,
				FBA456CB50E371584C11231929A0971E /* IQKeyboardManager */,
				05B2A835D60F78761395189914B88047 /* IQKeyboardManager-IQKeyboardManager */,
				55AF53E6C77A10ED4985E04D74A8878E /* Masonry */,
				DFF29EDE35ED89B0D385581C6035677E /* Pods-SnapInspect3 */,
				AE386F1E55E7ABE13CA772EE75B8A9E9 /* Pods-SnapInspect3Tests */,
				94DBE1F02563D90F416A7DB9558489B2 /* UITextView+Placeholder */,
				6AC97A319172C716659A6283A2D82401 /* WordPress-Aztec-iOS */,
				A8A26D58F4832E00EBE5951F0425C233 /* WordPress-Aztec-iOS-WordPress-Aztec-iOS */,
				52036133E4B9C2D1B79378F2A9B7BFE4 /* WordPress-Editor-iOS */,
				EBF557DDD39B493EA318729B2036BAF5 /* ZSSRichTextEditor */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		03AEFB8E31C91C98EEAF69C236A2FF1C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		15E46F0C28A2643681FA91176889EE1F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D98C9F46D8E0D313A05D5425BF4D1F17 /* WordPress-Aztec-iOS-WordPress-Aztec-iOS in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1A3A3662C2BF061CEC7C2D3126049EDD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2B7E9B959C75365458D8B9F4B8E5E64B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3CFE1372C4A53C95960A57A1973450FF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3EDBA999EF8B2EEAD6A7B95C5A7D8B01 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4471E552E34FA0D38ADDF209918B5B9B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5FDAF7732974188E3D0B50A2F696FB76 /* editor.html in Resources */,
				75EB437F3246ECF525733A7A5E94A04C /* jQuery.js in Resources */,
				B3EBB552619D153A3F7151BA722979AE /* JSBeautifier.js in Resources */,
				4D3A3222F4A8BEB1BC5367976AF846CA /* ZSSbgcolor.png in Resources */,
				4F58029FAF77F861BF22EF08B2076DE9 /* <EMAIL> in Resources */,
				F5AF0B57618D1FCEF95B63EC56B32B31 /* ZSSbold.png in Resources */,
				5A947F2B74BD4B630643D3AE235FB43A /* <EMAIL> in Resources */,
				0CEBAFB8F0A4ED507209D6A7CE97E34F /* ZSScenterjustify.png in Resources */,
				5BDBD885C47A9B46BF960B5025D2AB46 /* <EMAIL> in Resources */,
				526F818D64DE833C1B5C976244F0695F /* ZSSclearstyle.png in Resources */,
				78EE858C9438D7A57262E1D8C1C28279 /* <EMAIL> in Resources */,
				0A732B35B3EF8F5CD4ADB67E331EA746 /* ZSSfonts.png in Resources */,
				F6B55C9AF3EE2C05B7CB9B789DEBE693 /* <EMAIL> in Resources */,
				3F85BA3939138B6C58A0C773F2131120 /* ZSSforcejustify.png in Resources */,
				A7E9F40DFD86B9C7C7C613573FA6E8E5 /* <EMAIL> in Resources */,
				50BCABADC6A5FC1B00A253D9E958B0E2 /* ZSSh1.png in Resources */,
				37505B5201C5E07923545B7BD57DA15C /* <EMAIL> in Resources */,
				965F959DF0E9CFBDC150FC23AC864756 /* ZSSh2.png in Resources */,
				B7FB71BE01B35CB7365714B8295ADCA7 /* <EMAIL> in Resources */,
				BD2FE1DBB66E37D35547112691C48523 /* ZSSh3.png in Resources */,
				1B51ADD2EDB1E350C06D929B5B1E6854 /* <EMAIL> in Resources */,
				BE842CEEDABE17213E6F91148CF71E99 /* ZSSh4.png in Resources */,
				D4B9CF817B3CFDF6BA6043F2992749C6 /* <EMAIL> in Resources */,
				3BAA42836EC51CF1AF92923E22AB8B4D /* ZSSh5.png in Resources */,
				6D2CD6AF47A76D19B82D3D1197C012A1 /* <EMAIL> in Resources */,
				0482E5528F3925EF665500E39BB1C0A6 /* ZSSh6.png in Resources */,
				8EC6FCC5B184197D9CCC197D9C85D799 /* <EMAIL> in Resources */,
				97468BBAC22E7CA577EE9C82C4E91D62 /* ZSShorizontalrule.png in Resources */,
				914D0554C48F665CDE79438831C55367 /* <EMAIL> in Resources */,
				CDAF632A690DD9EEF2AB046DD584E8B1 /* ZSSimage.png in Resources */,
				BBE8416060003393BE06B4399A7250E7 /* <EMAIL> in Resources */,
				59ACEFE4AF81DDAA750154064380E73F /* ZSSimageDevice.png in Resources */,
				C48C4B93E5B299C99086570EEE83D72C /* <EMAIL> in Resources */,
				9867966D2470B7B58A082B9F8B6CA516 /* ZSSindent.png in Resources */,
				5C48B14FC860F81F25F746255400C745 /* <EMAIL> in Resources */,
				4F300B14FA0C4375225BEFCD3DEC4657 /* ZSSinsertkeyword.png in Resources */,
				1004D81C7EA9ADDECBFAFEA1A4EFD5FC /* <EMAIL> in Resources */,
				9A2C74A50EADDE69ACCE0F8D2AC1F6CB /* ZSSitalic.png in Resources */,
				27BF49265939C1A43E8BFE8881927B85 /* <EMAIL> in Resources */,
				38ED8E58A5DD0B04D6186FF129475A98 /* ZSSkeyboard.png in Resources */,
				BDB32861714959A4E6EFB3678C325BD4 /* <EMAIL> in Resources */,
				B68DDB2E7B4B989FB73F34A327464621 /* ZSSleftjustify.png in Resources */,
				0ABB381D036C9E9FBB0FF69B2EC0B09D /* <EMAIL> in Resources */,
				04F5ECB5E38E86CEB03051ED472D7A1C /* ZSSlink.png in Resources */,
				6AE3A8CE0601F3F782B6AD38C5880515 /* <EMAIL> in Resources */,
				58891050646DFF567142A2248F729593 /* ZSSorderedlist.png in Resources */,
				F1FF83938200DE7CA5CEA7473B5391EF /* <EMAIL> in Resources */,
				337428F0C302779F354FB3795BFDD14C /* ZSSoutdent.png in Resources */,
				ECD2F320A154DB0933465C19D5DD6308 /* <EMAIL> in Resources */,
				5650CCA6210351DD5AB497F94199D25E /* ZSSparagraph.png in Resources */,
				A75C33AF3EDFAF211341C4A13216F061 /* <EMAIL> in Resources */,
				EDB309CB9B92BEA6210FB7636B775F1B /* ZSSpicker.png in Resources */,
				2E900991B442CF013FE5FDEB52BDF01F /* <EMAIL> in Resources */,
				B9DF825AA587B24F869F613287E77526 /* ZSSquicklink.png in Resources */,
				8A2C82BF46159957EF14B3FAA9CAA31E /* <EMAIL> in Resources */,
				650A547EE1D74EB78102C879C0E36062 /* ZSSredo.png in Resources */,
				2D51AB19413B1498C6D19BD8056346E4 /* <EMAIL> in Resources */,
				E39471067B9E8F3C1EB91B5C891D3EEE /* ZSSRichTextEditor.js in Resources */,
				795D3BEBAAC12C0563A31122B87745B3 /* ZSSrightjustify.png in Resources */,
				E0142D33A5DD2015DEE26C7FF21F9E79 /* <EMAIL> in Resources */,
				E8D76196E54CD3234F5DF7D9AA461A5E /* ZSSstrikethrough.png in Resources */,
				445DBF3AFB999F2CEAC8A17BD815BA4B /* <EMAIL> in Resources */,
				AC5A88137B59BE44BCA4BC33A3066BB1 /* ZSSsubscript.png in Resources */,
				5CEE042E3D4069FB5FC86C2A1564E357 /* <EMAIL> in Resources */,
				D363DF6224959D3F83B5F0BB683C6802 /* ZSSsuperscript.png in Resources */,
				6EC7F112735AF0F25CFA074F07F98D1A /* <EMAIL> in Resources */,
				84CED2C84465FBC7D02D260BFC7701E9 /* ZSStable.png in Resources */,
				CDAEEECAAEE3C96F1AEFBFC25D853B3A /* <EMAIL> in Resources */,
				D463CB7B759689BEA93954BCAF7C04A6 /* ZSStextcolor.png in Resources */,
				56FF76F3268C38BE9F6A4F9436D11E81 /* <EMAIL> in Resources */,
				E71721C3F30059935DA983CABFB184D0 /* ZSSunderline.png in Resources */,
				F06AF8BF57F20FEFC9E2546548DFB075 /* <EMAIL> in Resources */,
				1B18CBE55D207EAF26CC2E64ED51F2D4 /* ZSSundo.png in Resources */,
				1B4BE98867AB5C6C1727863627501724 /* <EMAIL> in Resources */,
				310661ECEAA2BB615E330BA92C07DA83 /* ZSSunlink.png in Resources */,
				0B6EAE3F11A8E293D8E6FB74888D3F05 /* <EMAIL> in Resources */,
				BABED818770BC4AE6A92AC75F75D1E41 /* ZSSunorderedlist.png in Resources */,
				0A173B6C6A84D15CF06A6F375AC7E003 /* <EMAIL> in Resources */,
				E6C856B08B5E0F06F0814B3F1FD16754 /* ZSSviewsource.png in Resources */,
				030637F260D9D0D4973D0A98579919FB /* <EMAIL> in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5F238C099375761AD253ACE4964A2DD5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E6FC855D8ADEF6D46159054E5D90BA33 /* aztec.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64C2FE7EDD08E8BB0B842EB7FB82C71C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				05751DF8BF5E831964692A625FE68B3B /* html_colors.json in Resources */,
				0C6912494A9947FCA4178CC755E8385C /* Media.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		65CFB189D6FD8676C41FDA66F77BECC1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D0479452B9CF662C925B0841FF657D9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DD1688245140D2BBD0DF6DD2DB5E4593 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6A4CDFA9DAEE59CB9A11C961F299DE23 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		ECD6B9A8E754DF142B323DF2D7E0D112 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FFD11E7589893A9E2CC6A5C3CCAFEDE2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				626401380ADC61520BB5627C76521683 /* IQKeyboardManager-IQKeyboardManager in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		01ECAF374F4F5DF0ADEEE4DA1FC20F94 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EC73F4B2541F0E9810745D6AE9CC46CD /* AutoPProcessor.swift in Sources */,
				4F6B73DA488D67FE1627D308791355FA /* CaptionShortcodeInputProcessor.swift in Sources */,
				BE6A8F2FD75CF2EC56E66254578255D7 /* CaptionShortcodeOutputProcessor.swift in Sources */,
				89E71DB5847F98EF0725D2640369D1C8 /* CommentNode+Gutenberg.swift in Sources */,
				DA15A3FFA27B101B8818A8808BAC5E40 /* EmbedURLProcessor.swift in Sources */,
				CD48256D9CECAE4321B1822C075CDB83 /* GalleryAttachment.swift in Sources */,
				29E5B0BA27995891088BC3EE80A3F210 /* GalleryAttachmentToElementConverter.swift in Sources */,
				4F57D3D03FC1432F435E44621D37DF83 /* GalleryElementConverter.swift in Sources */,
				2074386B87C7099A9D09D9C6A1BDE570 /* GalleryElementToTagConverter.swift in Sources */,
				0632BA4B6FC7B9A0061AFCF4A03E63A6 /* GalleryShortcodeInputProcessor.swift in Sources */,
				C943C0B1691AF7849173FA461B61988E /* GallerySupportedAttribute.swift in Sources */,
				F3CEB7F204897849B5F99DB240AE4531 /* GutenbergAttributeDecoder.swift in Sources */,
				86AB7E862B4735E0098CB3B42B861242 /* GutenbergAttributeEncoder.swift in Sources */,
				0D560328328A83B262378B1C6C0AD513 /* GutenbergAttributeNames.swift in Sources */,
				9728EE5EE2E026019110143CBCEDF46F /* GutenbergInputHTMLTreeProcessor.swift in Sources */,
				1E63FA190EEB23418E740047316B21B7 /* GutenbergOutputHTMLTreeProcessor.swift in Sources */,
				04651828EBE0C82C8F16C615DCF315EC /* Gutenblock.swift in Sources */,
				B5D50443C405878F94FD61CF8EF15BAB /* GutenblockConverter.swift in Sources */,
				070E042675A16B066CF5CAD507D02422 /* GutenpackAttachment.swift in Sources */,
				FE3F696FDD39748DB6A6F6444990413F /* GutenpackAttachmentRenderer.swift in Sources */,
				DF9A356185FA86299967F25E5F804380 /* GutenpackAttachmentToElementConverter.swift in Sources */,
				4A4A77BD2E94D2ABDAE9CD7030F234E4 /* GutenpackConverter.swift in Sources */,
				F5A5A99010BC92F28398F2C2991F7292 /* ImageAttachment+WordPress.swift in Sources */,
				09018F6A45DF1AA778CC2BF150961B41 /* MediaAttachment+WordPress.swift in Sources */,
				F2C7366946814C115C9156674B8A2F0A /* OptionsTablePresenter.swift in Sources */,
				8F724103188BCE82B3F2CDAE352EC7E7 /* OptionsTableViewController.swift in Sources */,
				9F125786F14008560614A87EA6D0972E /* RemovePProcessor.swift in Sources */,
				9E1E85FB0A5EC9B4C5388FFC076BEEE9 /* ShortcodeProcessor.swift in Sources */,
				33794D097908172BBA7B92E1F9516D9E /* SpecialTagAttachmentRenderer.swift in Sources */,
				06116B9D867C77EAE024448AEDBD8419 /* String+RegEx.swift in Sources */,
				E36D96643CA562A360678779E886DE84 /* VideoAttachment+WordPress.swift in Sources */,
				2C34E61BB8B0B039156E9D34E916997C /* VideoShortcodeProcessor.swift in Sources */,
				B7672C65570BD20FFA8701121D7940C9 /* WordPress-Editor-iOS-dummy.m in Sources */,
				830CEDBF82E0BA859502785FF8220D17 /* WordPressInputCustomizer.swift in Sources */,
				D11351DF62441C422DF2701BE35E70CC /* WordPressOutputCustomizer.swift in Sources */,
				183C1181055ABA6B4C0D2390CCB50328 /* WordPressPasteboardDelegate.swift in Sources */,
				B34EE475E952BBC64EB55AFA53C4597E /* WordPressPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		188F00C16FA196EA40994FAE8F1EB88C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		374273EAB6BDEBA1F86F93675774D810 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F81563CBB9710F03F6F0598528ED1242 /* CustomIOSAlertView.m in Sources */,
				D92A610A227F6A9EBA216425314B6871 /* CustomIOSAlertView-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		66BFD3D76C600C077E8B2E9A95E9917D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2A0ED9538B30D2FFE508DDE819D65064 /* Array+Attribute.swift in Sources */,
				E70423B9FFC8CFBB53D07C6DC6A1B344 /* Array+Helpers.swift in Sources */,
				A9B2027CC35F5B532BE2E2525E3E9964 /* Array+ShortcodeAttribute.swift in Sources */,
				E50EF621D72D2DB7BCEC579674BEBAD5 /* Assets.swift in Sources */,
				4D328F05AFEECC3B3637A7A986ABC5DE /* AttachmentElementConverter.swift in Sources */,
				9575B0742742FAA12CDA3BC1B376AB84 /* AttachmentToElementConverter.swift in Sources */,
				7A1E340800248BB4861CC0B0516D8AF0 /* Attribute.swift in Sources */,
				FDE46805D0553B751AFEC3AB4987D805 /* AttributedStringParser.swift in Sources */,
				32889E620480C1E5F756E0024E9F2C28 /* AttributedStringSerializer.swift in Sources */,
				C55D53CAC027E68AF48FF45532B14D0A /* AttributeFormatter.swift in Sources */,
				65A2BA2675C3CA9E080E1C343FC7F069 /* AttributeType.swift in Sources */,
				E3793B5E3EEFBCC94978DBF0D24BDC5D /* Blockquote.swift in Sources */,
				8C725CA50D6E6EF78F9621388F3C8E2F /* BlockquoteFormatter.swift in Sources */,
				99B897E377C14B9DA2919F83AE1D7B15 /* BoldCSSAttributeMatcher.swift in Sources */,
				4EC9586C6C8A1895D5AE8B31206DB829 /* BoldElementAttributeConverter.swift in Sources */,
				E75E4792B035CB5BE30A1CD3FB452753 /* BoldFormatter.swift in Sources */,
				92F6F3DD7E45E46652CB0073E3B59A72 /* BoldStringAttributeConverter.swift in Sources */,
				9CAA66D83679BF07317C824F8D96EB2A /* BoldWithShadowForHeadingFormatter.swift in Sources */,
				BC91A11F62D0440D2125FD242E05DF61 /* BRElementConverter.swift in Sources */,
				6C106C8524251E4416501C098E59DC44 /* Character+Name.swift in Sources */,
				ADD8499452068F0DB9943E8D514FCDD2 /* CiteElementConverter.swift in Sources */,
				D8BF432D975194053857506253DC9CF9 /* CiteFormatter.swift in Sources */,
				8B0DC8B6CFB0EB49CE574C04FFBD2295 /* CiteStringAttributeConverter.swift in Sources */,
				F294B393CC184A77803BB708DF749E23 /* CLinkedListToArrayConverter.swift in Sources */,
				110B7DB758908362F74A9BDDD338E040 /* CodeFormatter.swift in Sources */,
				4A1715498623DEF75C4A64811C4B107E /* ColorFormatter.swift in Sources */,
				DAF8078CFCC5E871992E33695057E109 /* ColorProvider.swift in Sources */,
				DF8E74A399AD6822FD419B2876338BB6 /* CommentAttachment.swift in Sources */,
				21B73FE17E887AFF2291B8C84DD3FCE6 /* CommentAttachmentRenderer.swift in Sources */,
				259F6DC8E50BDC0347C06EC6B6B08426 /* CommentAttachmentToElementConverter.swift in Sources */,
				55D769D7645327B00D156902A545EFB1 /* CommentNode.swift in Sources */,
				1AAD31BFE81567EB979CB63D802CECDB /* ConditionalItalicStringAttributeConverter.swift in Sources */,
				EAAA3B37B65F7EA48AD636418393FFCD /* ConditionalStringAttributeConverter.swift in Sources */,
				844431C020362D51B4434A2FC9739A40 /* Configuration.swift in Sources */,
				991641A92E2DC37CF52C2C24100150A7 /* Converter.swift in Sources */,
				DE8A00F3E5149DA3D5D36CBCA08B748C /* CSSAttribute.swift in Sources */,
				8EE639DB09DA0F5D92EEFC116EB3ADE8 /* CSSAttributeMatcher.swift in Sources */,
				31624D1655D06E52FD7F2264215DAC9D /* CSSAttributeType.swift in Sources */,
				35A281E7977D414DC43845F9B1CCB9EE /* CSSParser.swift in Sources */,
				0BC3A2CF6ADD87DC7C8FFC0A395A3A7A /* Dictionary+AttributedStringKey.swift in Sources */,
				FFA7F53A0DE854AC8C3368A1C27A2EAE /* DocumentReadingOptionKey+Swift4.swift in Sources */,
				3E893A0B7968D6ED2691A1461B50C184 /* DocumentType+Swift4.swift in Sources */,
				EDC30424370FA6E922920A3E8C9A1F85 /* EditorView.swift in Sources */,
				034CD848D2155FCE0CAB851C48C6AAA3 /* Element.swift in Sources */,
				9115B521394E139980D44B53FB5E4648 /* ElementAttributeConverter.swift in Sources */,
				2C58B4897C0EF6AFBD8E2123F45A3674 /* ElementConverter.swift in Sources */,
				E84E42135263CCD3C82E86A9644C5402 /* ElementNode.swift in Sources */,
				F56F2A1CDFC69C3EF4D3F2BB364684A4 /* ElementToStringConverter.swift in Sources */,
				058EB1DE8B45EDF0210443E276AF4DD0 /* Figcaption.swift in Sources */,
				73857DCAC405D113628199EAEBA0D5B4 /* FigcaptionElementConverter.swift in Sources */,
				3B7DDA8AC1B0A07757E9C51E10F6CF68 /* FigcaptionFormatter.swift in Sources */,
				29A7C810544A8C1E17E7A7F426C653B7 /* Figure.swift in Sources */,
				A32243824F1BF0E26ADD06F1E96C6299 /* FigureElementConverter.swift in Sources */,
				164991A2E2D34C0F2C523F0E2C71A3D9 /* FigureFormatter.swift in Sources */,
				6EC1C79506E536FCDB8751B0CE79EFA4 /* FontFormatter.swift in Sources */,
				978E98D633D142FA5B824D51522F947A /* FontProvider.swift in Sources */,
				210FE6E33C9634152F2FAE9F835CC846 /* ForegroundColorCSSAttributeMatcher.swift in Sources */,
				BA6E99D5CC61856BD39E9695D3F99631 /* ForegroundColorElementAttributeConverter.swift in Sources */,
				E49C49F4C1B0B978559155B7C188AC3F /* FormatBar.swift in Sources */,
				3C760C3FE2AC9D793C214631FCBE2C61 /* FormatBarDelegate.swift in Sources */,
				04945BCD44194BE55B2A418B712C4BF2 /* FormatBarItem.swift in Sources */,
				1D7B7EA80CCAE79F4D5AEAA5D1F959D2 /* FormatterElementConverter.swift in Sources */,
				475103394C72DD39AC32E1436F7B27F9 /* FormattingIdentifier.swift in Sources */,
				5E5B93A19F7A6026A6A0A81CF86C0F07 /* GenericElementConverter.swift in Sources */,
				DF7B35F9570D6662374B19ACC42A8677 /* GenericElementToTagConverter.swift in Sources */,
				09583B75374F627C545A7DE7E018A68B /* Header.swift in Sources */,
				59773F5FB73B0ACD94C54A8C0F0F6A51 /* HeaderFormatter.swift in Sources */,
				DE871EA5386975267F95C22DF8951FE2 /* HRElementConverter.swift in Sources */,
				97C43A8009B9B2FCD3C9C2FE128CF299 /* HTMLAttachment.swift in Sources */,
				10D9C4C0CBD0BAD49E6F9F00372794F9 /* HTMLAttachmentRenderer.swift in Sources */,
				01DC449236DDCEF360B520DA88817B21 /* HTMLAttachmentToElementConverter.swift in Sources */,
				F6D83EFB94ADE1AA7FFAF7FC5762BC31 /* HTMLConverter.swift in Sources */,
				71CD6579C3A6219C624227FE1F1CCF31 /* HTMLDiv.swift in Sources */,
				C739921A2B30A851A2BD1853502E0AC1 /* HTMLDivFormatter.swift in Sources */,
				F884345D750741F026D26BFEAD4775BA /* HTMLLi.swift in Sources */,
				E72E0788A8229863460334C38C36122A /* HTMLParagraph.swift in Sources */,
				40C7F79E381710C696018B3708705129 /* HTMLParagraphFormatter.swift in Sources */,
				13F10E57BCFADEDB7ED1A5FB3B0499F2 /* HTMLParser.swift in Sources */,
				63AF101F0F9330BD239E2DC04388EDB9 /* HTMLPre.swift in Sources */,
				57B14356117BD81BCCF624E1B9F3132A /* HTMLProcessor.swift in Sources */,
				A0A1E1303751DCE2387A20EAABBA5F8A /* HTMLRepresentation.swift in Sources */,
				876499F256D618D225A49F6F4540E84A /* HTMLSerializer.swift in Sources */,
				12485E8698801B7D1D6E86EE6F281147 /* HTMLStorage.swift in Sources */,
				BC9682FB2976A1213BD81037D2D927D7 /* HTMLStyleToggler.swift in Sources */,
				2F595B210C812A369B0AF981070CB8ED /* HTMLTreeProcessor.swift in Sources */,
				6AB59672EB46D8DBEF528068DDA1E650 /* ImageAttachment.swift in Sources */,
				7D94877A130FB7BD0DEBE3864D9DC34E /* ImageAttachmentToElementConverter.swift in Sources */,
				CA2D80A774CA224E57F8E2B29D55B46E /* ImageElementConverter.swift in Sources */,
				C637103F229839E3C7653AD690A38F77 /* InAttributeConverter.swift in Sources */,
				1E68CACCD0ECEE396BAA327A5723F54D /* InAttributesConverter.swift in Sources */,
				56FE480583EF9BFE2399DD13AB373524 /* InNodeConverter.swift in Sources */,
				4244E57DDB307CC9DF3504127F11AB25 /* InNodesConverter.swift in Sources */,
				9D703DC51FCC257F549BDB2995678CB1 /* ItalicCSSAttributeMatcher.swift in Sources */,
				B949ADDC04FED68131A8AC7032AAD9DA /* ItalicElementAttributeConverter.swift in Sources */,
				FF4AC76147DBA55CEAB7D3D3FB274145 /* ItalicFormatter.swift in Sources */,
				E8C1D0E835CDF2A13512B19D15E6CAEF /* ItalicStringAttributeConverter.swift in Sources */,
				85F1ABBC8FBB1CADB94E0BC5E8BCA9B0 /* LayoutManager.swift in Sources */,
				867C76F0B293E910A28E6315CEDCEC79 /* LIElementConverter.swift in Sources */,
				FF639263CDF715D5B9DFD37C3119BF2E /* LiFormatter.swift in Sources */,
				9BECE8C7E24EAB4217E61A71C059C354 /* LineAttachment.swift in Sources */,
				EB11A8EFA070117F8E6AF08345772E43 /* LineAttachmentToElementConverter.swift in Sources */,
				099D041DA665DD316A654B5E03EBC1C6 /* LinkFormatter.swift in Sources */,
				58E78B2E98A60A4B97AFB535F3E19188 /* MainAttributesConverter.swift in Sources */,
				971BB790BC554E0B09D724EDFCC2D505 /* MarkFormatter.swift in Sources */,
				5005561A2A6042A85A801C8281692F57 /* MarkStringAttributeConverter.swift in Sources */,
				87BD0ED1231289CC370D1BED587C2808 /* MediaAttachment.swift in Sources */,
				9A4E8B8F7B384E1024551E4B2956DED7 /* Metrics.swift in Sources */,
				75AEF67E5F8AB55E76DFC131A7F8DB46 /* Node.swift in Sources */,
				7C12C640B065FFA719630032C644FAA5 /* NSAttributedString+Analyzers.swift in Sources */,
				CF1222F500945786CCFCDA54C1E99D00 /* NSAttributedString+Archive.swift in Sources */,
				85C86F48862DB2AB748C52A9A2F06D25 /* NSAttributedString+Attachments.swift in Sources */,
				2E1D8B0F196A7DA6FB4FAB9C8D61C4DA /* NSAttributedString+CharacterName.swift in Sources */,
				3E3249FFCF66B83AFDD9990FCC189DAA /* NSAttributedString+FontTraits.swift in Sources */,
				0339FAEF99C33FC0808EFD62475558FA /* NSAttributedString+Lists.swift in Sources */,
				6B01DFACAE3F722287F0EEEA44F55CC0 /* NSAttributedString+ParagraphRange.swift in Sources */,
				59F0BBB962FB497BFE96925764E48A86 /* NSAttributedString+ReplaceOcurrences.swift in Sources */,
				A6B72FFA55B83340E0C13A4A80A7BE42 /* NSAttributedStringKey+Aztec.swift in Sources */,
				7A27FADBDCF99241F59F265FF0CA9AD2 /* NSAttributedStringKey+Conversion.swift in Sources */,
				0BE5B1A7E0E9DEACB4FC73BA9ECB1261 /* NSBundle+AztecBundle.swift in Sources */,
				3DD36E03DC267312472F4AFEEB4F48B5 /* NSLayoutManager+Attachments.swift in Sources */,
				2B030BC09EFFEEC5146511D89702FD75 /* NSMutableAttributedString+ParagraphProperty.swift in Sources */,
				5AA3BB263DE0333ADDB76512B16052BB /* NSMutableAttributedString+ReplaceAttributes.swift in Sources */,
				85F83BA89EB4818CE233B0F26C765699 /* NSMutableAttributedString+ReplaceOcurrences.swift in Sources */,
				322384803FDF05A58830635062175F0F /* NSRange+Helpers.swift in Sources */,
				2B6BF126C9593B7268BCEB5DE91E8991 /* NSTextingResult+Helpers.swift in Sources */,
				E20C0FD106A9ACB4550067DBA0136B5C /* ParagraphAttributeFormatter.swift in Sources */,
				0A38B0E0A5B9A395F5DD5EBAD5E782BF /* ParagraphProperty.swift in Sources */,
				1D8946CC661CF711BD7A131BAA2C87FB /* ParagraphPropertyConverter.swift in Sources */,
				6A25226823CB61420042CF3CD3F0624A /* ParagraphStyle.swift in Sources */,
				A51C2B3362B81EFC72BC857DF4A3639B /* PipelineProcessor.swift in Sources */,
				3C0D5BD1C5607ECDA844921623290CCB /* Plugin.swift in Sources */,
				E3CAC3EB76F64237998894723BE79D4A /* PluginInputCustomizer.swift in Sources */,
				21A03B6C8F7D979F8D3E5CB19408DF17 /* PluginManager.swift in Sources */,
				4158FA2AF7DDD63B5D4C2D07803EC9F0 /* PluginOutputCustomizer.swift in Sources */,
				982A7960ED18DF6C5AE1B2EBD213BE92 /* PreFormatter.swift in Sources */,
				FE13C0BF9DD3F59EC7AFC6C7ED0F653D /* Processor.swift in Sources */,
				53EC4B257A2FC015D67073EBD6848286 /* RegexProcessor.swift in Sources */,
				FB9CA60B34A59B4B706A43DB45584D48 /* RenderableAttachment.swift in Sources */,
				46285DB9A9659B075E62101B92A2056D /* ShortcodeAttribute.swift in Sources */,
				31F304FC20613FEFD839E34047F449C6 /* ShortcodeAttributeParser.swift in Sources */,
				AD420933AFCC3FE6222431010C769FDA /* ShortcodeAttributeSerializer.swift in Sources */,
				8BB6E710E3B30F2A55F103A42DD819EA /* StandardAttributeFormatter.swift in Sources */,
				D1961090D2875CF29600806A9E68E3F0 /* StrikethroughFormatter.swift in Sources */,
				43002A8C6B0C044DD1EDE22B69C94EF9 /* String+CharacterName.swift in Sources */,
				AEBDA1DC1ABCC7597027680352E71ECE /* String+EndOfLine.swift in Sources */,
				D476043570D7A008BC1942D342B0DF52 /* String+HTML.swift in Sources */,
				536A941A1556BDE2F78BF1F29AF6DD3D /* String+Paragraph.swift in Sources */,
				EC565C6DB1038E063EE2F9BD1A4A40EC /* String+RangeConversion.swift in Sources */,
				41812F4826E8FE8B1DF49C9618765B8E /* StringAttributeConverter.swift in Sources */,
				64168D7B65F965A5049DDDFD0F135EF9 /* StringUTF16+RangeConversion.swift in Sources */,
				AC704E2E012388ABB6E37F82BA957247 /* SubscriptFormatter.swift in Sources */,
				CEAA9762AE07F50730E944B5D920DDE4 /* SubscriptStringAttributeConverter.swift in Sources */,
				D0D1BD33A1A80BDDD3780CD504DB6B8D /* SuperscriptFormatter.swift in Sources */,
				5433AC8EF416EE85DA1093791A436133 /* SuperscriptStringAttributeConverter.swift in Sources */,
				11F1B3548B4DF50398011DD4C43F4CA6 /* TextList.swift in Sources */,
				E2CDB512511CC1F09A5423A8FA8F89B9 /* TextListFormatter.swift in Sources */,
				552A1101F65922522B26A4CABC9EFF55 /* TextNode.swift in Sources */,
				6569A960A49775D6B8318AFD3F8B90C6 /* TextStorage.swift in Sources */,
				5E6525FB520A5CA2D5469D36082FFD7E /* TextView.swift in Sources */,
				72B80401465DECACC88FDA41632CFB29 /* TextViewPasteboardDelegate.swift in Sources */,
				86F16E50281882B6295EF4AD221BDD80 /* UIColor+Parsers.swift in Sources */,
				B32A180FD32A261DA68D1FD79F156BDB /* UIFont+Emoji.swift in Sources */,
				A20D034B03C106C7D38E0FFB8CD9A604 /* UIFont+Traits.swift in Sources */,
				738DB8BF34AB20E6434800B3F8C00819 /* UIImage+Resize.swift in Sources */,
				2D2433756EB19A309DBBD3BA73D15AC4 /* UILayoutPriority+Swift4.swift in Sources */,
				4A340A4E33D5D579C58D05B9C79AFD34 /* UIPasteboard+Helpers.swift in Sources */,
				AF272FD4749D8EEE9E1CB04DEF2B8D05 /* UIStackView+Helpers.swift in Sources */,
				89062974ED32988F0BC23CC85B4CBE10 /* UITextView+Delegate.swift in Sources */,
				3866F0220817D2128801820110DA7F3F /* UITextView+Undoable.swift in Sources */,
				0D51D8E37835CEE44502FC03B1DAB29C /* UnderlineCSSAttributeMatcher.swift in Sources */,
				D9FAC810A4A1400D2440E63E86FF711E /* UnderlineElementAttributeConverter.swift in Sources */,
				8EBBD67CB954963E8656B0045977B23A /* UnderlineFormatter.swift in Sources */,
				CE86F407A7227375840D78435FC62703 /* UnderlineStringAttributeConverter.swift in Sources */,
				75C09A86FB5D2868ACF88226BED8A9DC /* UnsupportedHTML.swift in Sources */,
				C02E1CD7E9187FEB26192768E0CD6C6C /* VideoAttachment.swift in Sources */,
				E8CE8DBDEDBD5568227076DAD6F5BA48 /* VideoAttachmentToElementConverter.swift in Sources */,
				8D611CB96055154EC8015D5EB805C81B /* VideoElementConverter.swift in Sources */,
				CC778579A2260EE731A4021117AFC713 /* WordPress-Aztec-iOS-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6CA777BE5374B0934E36110242648213 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				71BB0CD56FD004B6F58703DABDF8A22E /* Pods-SnapInspect3-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		763E378FC392D26C93EEC77143F42CB9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				882947BB173441CE5D8B6164DFFB62DB /* Pods-SnapInspect3Tests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		82511632882DBA9D19E90B11F4C6D40E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F658ADF464810FEB16192B131870012F /* AWSSimpleDB-dummy.m in Sources */,
				99EDAE39FF008B799A3F4F69D055A1AD /* AWSSimpleDBModel.m in Sources */,
				7CA477E561782E082D0FED5A3C54BACA /* AWSSimpleDBResources.m in Sources */,
				B44332DF9791582EB5D01FBC3869C580 /* AWSSimpleDBService.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		87E05147F162775FF8CE466FDB1A03C9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8FFE90F650764269C1443F13063C2291 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EBA5FCF696B3C5D8597B8CA750372464 /* CYRLayoutManager.m in Sources */,
				DBD304ABFB503805597E751AD69C2237 /* CYRTextStorage.m in Sources */,
				5E7EAC95F11E9BDC426E72B44C4AEEEB /* CYRTextView.m in Sources */,
				E17DF855124E38FB6414775AF4AFF8B7 /* CYRToken.m in Sources */,
				B80B5829186C6B4752D705434E044903 /* DemoModalViewController.m in Sources */,
				5094BFE7A4D11AEE41E02B05462C05A3 /* HRBrightnessCursor.m in Sources */,
				55258B9086E0153797111EE693C7FA9F /* HRCgUtil.m in Sources */,
				9EFB2F3288942DAB2EEF71B94F9D40D2 /* HRColorCursor.m in Sources */,
				D239C23EA20929C3E3CDC4F6B4390FBD /* HRColorPickerView.m in Sources */,
				FE777530D958BFCDC7DD5296F501D71D /* HRColorPickerViewController.m in Sources */,
				B353A64CF634761A870BE48726245D48 /* HRColorUtil.m in Sources */,
				E48B8286E7F64AD30F2D57ECA6E24DCC /* ZSSBarButtonItem.m in Sources */,
				11A30E7E9BC41582363F71CAE1B4EC19 /* ZSSColorViewController.m in Sources */,
				0D11CB16C46BB1A8D75C2A84C138A7FE /* ZSSCustomButtonsViewController.m in Sources */,
				604947531E91D67BB34FEAFC013EC129 /* ZSSFontsViewController.m in Sources */,
				20F1A81AF4194F3CE3307103FCA04942 /* ZSSLargeViewController.m in Sources */,
				A82BF9C0FFB4FD595F04EE3FEBB40608 /* ZSSPlaceholderViewController.m in Sources */,
				803EF0A1FD80636406D149170416B003 /* ZSSRichTextEditor.m in Sources */,
				FE4599BC32C0FED7C1836929AD278DCA /* ZSSRichTextEditor-dummy.m in Sources */,
				BEDD2C53CFCFD37B2C804A8A61FA14F9 /* ZSSSelectiveViewController.m in Sources */,
				5E9970A8DE3387A036E3B095BBE8C4EA /* ZSSTextView.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD3174C2DBD17EB12B62B5F7DC837E16 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E8AB268A35E2DD8014E090056A07637D /* UITextView+Placeholder.m in Sources */,
				664D3EB5DF5505F5023301FE1F5E7110 /* UITextView+Placeholder-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D1BA6B02A0339B331261C54EFAF13627 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6E39A4EB58A31F35447212C65DD3869C /* AWSBolts.m in Sources */,
				193A04DEB07B3F650348A077D40137A1 /* AWSCancellationToken.m in Sources */,
				48D75E24074D80B0BA803FCA10775BFA /* AWSCancellationTokenRegistration.m in Sources */,
				1AECE3C34F7B9484EC41D92B2E1015D0 /* AWSCancellationTokenSource.m in Sources */,
				069D75321C432E6823ECF8F2948A4864 /* AWSCategory.m in Sources */,
				3FB4B354C1F53E7A9308FBCA75C37115 /* AWSClientContext.m in Sources */,
				F6DEEAB7965DA6574F2C54C63E696543 /* AWSCognitoIdentity+Fabric.m in Sources */,
				622A271F4187B19DAF81A6F1D72F50E6 /* AWSCognitoIdentityModel.m in Sources */,
				2D220977FEC0B146B32A9C3C728D6E63 /* AWSCognitoIdentityResources.m in Sources */,
				A7E75FD5F699882C1263AFC6D122BE8C /* AWSCognitoIdentityService.m in Sources */,
				5072430BCA1A650B644E622032451698 /* AWSCore-dummy.m in Sources */,
				7CC2A6BBCBE24CD6D5E179BEED44252C /* AWSCredentialsProvider.m in Sources */,
				66EB7E0A586898E120B786D39F994AED /* AWSDDAbstractDatabaseLogger.m in Sources */,
				845C507ABE2B03F5D11BB487BB222D92 /* AWSDDASLLogCapture.m in Sources */,
				AC1172EDD1E0A78AD513726266B67444 /* AWSDDASLLogger.m in Sources */,
				6E40A3DB67C930B119788FD5A9D3DF43 /* AWSDDContextFilterLogFormatter.m in Sources */,
				87592BE5C65DF3C576CA265AB83EEE55 /* AWSDDDispatchQueueLogFormatter.m in Sources */,
				CF29255890AA94D02FF0C561D9F15828 /* AWSDDFileLogger.m in Sources */,
				7D4AA1998E543A91DF981C207CFB149E /* AWSDDLog.m in Sources */,
				A32E6E097C189BC3B7592AF1C021E136 /* AWSDDMultiFormatter.m in Sources */,
				4012858D3B6D619D1B744CE3DFF2FB91 /* AWSDDOSLogger.m in Sources */,
				DD198D339C0F331BA234521737C0C75A /* AWSDDTTYLogger.m in Sources */,
				B2CEAB042B37AECB6B71FBCD06FDA088 /* AWSExecutor.m in Sources */,
				6930BE61268C1F6272DFACF4E52A3A1D /* AWSEXTRuntimeExtensions.m in Sources */,
				FBC75C96033593C07244089F619A5DC6 /* AWSEXTScope.m in Sources */,
				FB3DE04B6D6287AC80225D779E96F283 /* AWSFMDatabase.m in Sources */,
				C90B079736EAE092E00B2CA32B85E098 /* AWSFMDatabaseAdditions.m in Sources */,
				FA5C5008DE4C7939DAFB39D9C4CA3263 /* AWSFMDatabasePool.m in Sources */,
				9D9D4CB9606AD0AD8A1478E0D0E58E07 /* AWSFMDatabaseQueue.m in Sources */,
				A26DB9A491AD68A6EC98D92E8807E093 /* AWSFMDB+AWSHelpers.m in Sources */,
				9B01FCF4AF533D61C2ACB289D60B6457 /* AWSFMResultSet.m in Sources */,
				5FFB8A7F1C40843FD6389CDC60CCC56E /* AWSGZIP.m in Sources */,
				76111D9BC887DCE9740F90BF364017B0 /* AWSIdentityProvider.m in Sources */,
				213DC578F2EE16517BEF36C4B95E5092 /* AWSInfo.m in Sources */,
				0F35A3A3F485DF5DC51E0B380F8FEDC2 /* AWSKSReachability.m in Sources */,
				E8E58C13CE260EB592F8515E95B3DD91 /* AWSLogging.m in Sources */,
				5ACDD551C2B54B08C7EE7B014B5D3313 /* AWSModel.m in Sources */,
				6B7E7C48122CAB38B392E8CDB6A52616 /* AWSMTLJSONAdapter.m in Sources */,
				8CC5AE27ADEFA04C22B42387222CF565 /* AWSMTLManagedObjectAdapter.m in Sources */,
				6786E92B2BF6586958D89CA1D3253747 /* AWSMTLModel.m in Sources */,
				B36C32A1DC9FA7F1BBE5747ADF19C476 /* AWSMTLModel+NSCoding.m in Sources */,
				C8E9DD78F3BCEFFC1F6567FA456AA399 /* AWSMTLReflection.m in Sources */,
				9394131A1C5D08CE746B01BD86A82519 /* AWSMTLValueTransformer.m in Sources */,
				1E859AE3EB53C31F1986BF63404729B1 /* AWSNetworking.m in Sources */,
				278DD16D9413D3BBB24AF62C8FDE2557 /* AWSNetworkingHelpers.m in Sources */,
				36D41C33D818887857ADDDB0E3719A00 /* AWSSerialization.m in Sources */,
				8E4031687D3A4C4918E33C83BF8F96FB /* AWSService.m in Sources */,
				CE2E2CF660578E6E00E9F1A4473A9933 /* AWSSignature.m in Sources */,
				FC6BCE4E74D68EF40D222DF549423FEF /* AWSSTSModel.m in Sources */,
				00AAB7239E9937CA81595C36F7CD6340 /* AWSSTSResources.m in Sources */,
				038D589E80BCA3B48827FA2431CE98A5 /* AWSSTSService.m in Sources */,
				DC603CBCE47B96B6AA145E256889F552 /* AWSSynchronizedMutableDictionary.m in Sources */,
				F4ABE5F340FD7875EFED790228E19913 /* AWSTask.m in Sources */,
				B76D424D237C9FABBC186BE713212404 /* AWSTaskCompletionSource.m in Sources */,
				FFFEFCA8AA921F2536E85C88BEB4EAE8 /* AWSTMCache.m in Sources */,
				92E9697A733031FDED1687E7F61C4AF4 /* AWSTMDiskCache.m in Sources */,
				39E0497C7835224C88697B39AC00321F /* AWSTMMemoryCache.m in Sources */,
				F198043A3A9295E676BABBA41232222A /* AWSUICKeyChainStore.m in Sources */,
				B9367C57A23B8343D653FFEC56CD8080 /* AWSURLRequestRetryHandler.m in Sources */,
				F80FEA299E2688461BDE931344E97373 /* AWSURLRequestSerialization.m in Sources */,
				00B4CA3A6CCF5C9F25A5D1B4F980665C /* AWSURLResponseSerialization.m in Sources */,
				2BF47C5182157A41F63F7E09A7A7DA00 /* AWSURLSessionManager.m in Sources */,
				F33CF65B2495C391B5A4C42E10621CEF /* AWSValidation.m in Sources */,
				9497C7563ABA0B6947FEAAF202FFEF37 /* AWSXMLDictionary.m in Sources */,
				20137A91484861CBE35A7D29B3E36171 /* AWSXMLWriter.m in Sources */,
				774AC97029AFA0549641698C225651DE /* NSArray+AWSMTLManipulationAdditions.m in Sources */,
				F928B02E247F56A754330690C9DF2D4E /* NSDictionary+AWSMTLManipulationAdditions.m in Sources */,
				4B6F8EFA53E31ED8C40A21FD14A6A91F /* NSError+AWSMTLModelException.m in Sources */,
				ABE9B46FE6152C2ACA00C28C0DE09203 /* NSObject+AWSMTLComparisonAdditions.m in Sources */,
				F18AB87ACDA15E30A7E211C4201FA1B1 /* NSValueTransformer+AWSMTLInversionAdditions.m in Sources */,
				A6E0BD27A057E2413926A6D4946BA265 /* NSValueTransformer+AWSMTLPredefinedTransformerAdditions.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D6AAD65A5B083806F82A72522385C952 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				93D7616E5D511399D50F717F8AA989E9 /* AWSS3-dummy.m in Sources */,
				AFEE6655A6C465107D2148D3BD592589 /* AWSS3Model.m in Sources */,
				542E4DBEEED286E4BDE43FD357469BB4 /* AWSS3PreSignedURL.m in Sources */,
				4FF321DBF88514A1D22A74BBDE8110F8 /* AWSS3RequestRetryHandler.m in Sources */,
				6F50F24D94076A2CEC79A86DBAB070A4 /* AWSS3Resources.m in Sources */,
				299D896F2D9EC42D4F03EBED59E703E0 /* AWSS3Serializer.m in Sources */,
				7BC2C981E201CEB0CD73B1F6D3F301FD /* AWSS3Service.m in Sources */,
				ED4C5B96740B71F0350D9F4A45A91F67 /* AWSS3TransferManager.m in Sources */,
				24EB215D83D4B29CD4C759752B1E552D /* AWSS3TransferUtility.m in Sources */,
				1E6DD16D765A5A22706F411ECD0CE2E2 /* AWSS3TransferUtility+HeaderHelper.m in Sources */,
				39A80900512EA85A514A7135C391EAA8 /* AWSS3TransferUtility+Validation.m in Sources */,
				95301BC363DADC43EE7C9EE38536D802 /* AWSS3TransferUtilityDatabaseHelper.m in Sources */,
				BC27F264D7813C128E1000996D1637D3 /* AWSS3TransferUtilityTasks.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DA0B6A6F9B3EDF226BF081DAC7E777E7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8FF7B6477BFA6E6ABA168E1417291D5F /* MASCompositeConstraint.m in Sources */,
				DF2B15402CE105F5A8CE48BBDCFFD5DD /* MASConstraint.m in Sources */,
				F6D1C960368EB1E067ABD0BFF707FC56 /* MASConstraintMaker.m in Sources */,
				C9E19D164C26414115CC969ED9A303C1 /* MASLayoutConstraint.m in Sources */,
				56E800EB3B2BE8AE0BA45A30974D7920 /* Masonry-dummy.m in Sources */,
				E930A5612DC6D120BE040AD17C6D1BCD /* MASViewAttribute.m in Sources */,
				C2FE60A10C792613E45031AE6E851ECB /* MASViewConstraint.m in Sources */,
				5B08596E856E4CC2F34A8A2372F9F764 /* NSArray+MASAdditions.m in Sources */,
				C8EC35DFB0945DBE2F2FF9ECFE6D9711 /* NSLayoutConstraint+MASDebugAdditions.m in Sources */,
				D788BA4B9E8186271BA75CA52B30502C /* View+MASAdditions.m in Sources */,
				C857B8D2D0BAA5A8A764F9E1C4B85807 /* ViewController+MASAdditions.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F6BE32C3CD0010A8BFCEE863F9ED6940 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				27F69C6688D86564B0A02D52835EF86E /* IQBarButtonItem.m in Sources */,
				240202AD1C01F1C9F0E89B816F9972CA /* IQKeyboardManager.m in Sources */,
				2A329296A4DC3BBA7E27EF8F0EAFC8BD /* IQKeyboardManager-dummy.m in Sources */,
				7EA54D82094944541E95CB5041773FC4 /* IQKeyboardReturnKeyHandler.m in Sources */,
				6AC6DB566533BB3C51FA22C7133A4977 /* IQNSArray+Sort.m in Sources */,
				000B7CF92F1A0FC654C3FD59E8D83952 /* IQPreviousNextView.m in Sources */,
				312AE7AE9A6E3EB30FD084F241146810 /* IQTextView.m in Sources */,
				66D1162BD854BC15E3200936A304D01C /* IQTitleBarButtonItem.m in Sources */,
				5DC8C4F0F23982E81CC41542FB030088 /* IQToolbar.m in Sources */,
				9B2E19FB0E083B2DF5BCEE7AEA7D23B7 /* IQUIScrollView+Additions.m in Sources */,
				0744FF91BD34CBF21804DD175227A3D0 /* IQUITextFieldView+Additions.m in Sources */,
				2D2C84E3A1976DED48341393B35E1BC6 /* IQUIView+Hierarchy.m in Sources */,
				607E23B19FD8700ED4BA434C686CABED /* IQUIView+IQKeyboardToolbar.m in Sources */,
				62FCD4A967D743DE07DA80DACF5B14DB /* IQUIViewController+Additions.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0B8A11FDCB421BC05345065A79CC9F77 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "WordPress-Editor-iOS";
			target = 52036133E4B9C2D1B79378F2A9B7BFE4 /* WordPress-Editor-iOS */;
			targetProxy = 337C12EA4FD5F8D96550CEC40F65B3A4 /* PBXContainerItemProxy */;
		};
		0FDD7FA84D3DE20ECBEA522C358E4278 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AWSCore;
			target = 9B172FACE90046AA5E100E650B6109DD /* AWSCore */;
			targetProxy = EE4254DC3B35AFE25782BECA702A8E3B /* PBXContainerItemProxy */;
		};
		3231CAE586ECC7F88AA83BDDD0C7EBF5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "UITextView+Placeholder";
			target = 94DBE1F02563D90F416A7DB9558489B2 /* UITextView+Placeholder */;
			targetProxy = 221C9093E65AAEC78475F3AD83B7CC1A /* PBXContainerItemProxy */;
		};
		4C9A2B7124D1C36BE37F015714FF7336 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AWSCore;
			target = 9B172FACE90046AA5E100E650B6109DD /* AWSCore */;
			targetProxy = D22D5E51179B8DF8191151BF0E60C6B3 /* PBXContainerItemProxy */;
		};
		630E0BBA52572042756AC549624AC936 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Pods-SnapInspect3";
			target = DFF29EDE35ED89B0D385581C6035677E /* Pods-SnapInspect3 */;
			targetProxy = 202A1847EA969B6AC48BBE01CF14F148 /* PBXContainerItemProxy */;
		};
		656B1925513049D94076B2EECF0C0523 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Masonry;
			target = 55AF53E6C77A10ED4985E04D74A8878E /* Masonry */;
			targetProxy = 525CB0F3309F31002B26932423EC966E /* PBXContainerItemProxy */;
		};
		6ED5D2F052690EE0607594ACB4FF6246 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AWSCore;
			target = 9B172FACE90046AA5E100E650B6109DD /* AWSCore */;
			targetProxy = 229E2A4F1CD5CE5EDEB127D39BAF579E /* PBXContainerItemProxy */;
		};
		745CBDE752A0068F8286DF678B8DF1D5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AWSS3;
			target = 31F0084E2E60CA68AAF7E3224C77C86E /* AWSS3 */;
			targetProxy = 9511534A1F67CB2BF0D6A087ED88F6E3 /* PBXContainerItemProxy */;
		};
		8FB10D82E473D5236AA7819FD2087167 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "IQKeyboardManager-IQKeyboardManager";
			target = 05B2A835D60F78761395189914B88047 /* IQKeyboardManager-IQKeyboardManager */;
			targetProxy = 95681C64DB782E8C1132D164068E0448 /* PBXContainerItemProxy */;
		};
		A7C21281FEDEB62F5E0654995A9C1030 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AWSSimpleDB;
			target = 73305835456A90447978F6D2F459C81A /* AWSSimpleDB */;
			targetProxy = 50E8815F47855252EB1614194FEA34CD /* PBXContainerItemProxy */;
		};
		BA2FA5A01DE526C4028CE7A13FA47A86 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "WordPress-Aztec-iOS-WordPress-Aztec-iOS";
			target = A8A26D58F4832E00EBE5951F0425C233 /* WordPress-Aztec-iOS-WordPress-Aztec-iOS */;
			targetProxy = E9933646FA10D2631CF1FEF0324B8891 /* PBXContainerItemProxy */;
		};
		BDDB59D8EE0999BA91C5F97447FC5D48 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardManager;
			target = FBA456CB50E371584C11231929A0971E /* IQKeyboardManager */;
			targetProxy = B217EB9DA02506F3FE9FC58A87ACAA01 /* PBXContainerItemProxy */;
		};
		C1CDD8C75320565732873D1380734D4D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "WordPress-Aztec-iOS";
			target = 6AC97A319172C716659A6283A2D82401 /* WordPress-Aztec-iOS */;
			targetProxy = 115CDEC6E289AEF158614FDC959B431F /* PBXContainerItemProxy */;
		};
		E42BD9FA799C1F6A974E925E680C5F20 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = ZSSRichTextEditor;
			target = EBF557DDD39B493EA318729B2036BAF5 /* ZSSRichTextEditor */;
			targetProxy = DCC8A206ED064479F1D5D878F593A827 /* PBXContainerItemProxy */;
		};
		E976809C923D5ABCF2500626F033A972 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = CustomIOSAlertView;
			target = D7940BE184F37BA203D5586A4D2F87BF /* CustomIOSAlertView */;
			targetProxy = 4AD33A56E73F81E17394FBB41AAD2464 /* PBXContainerItemProxy */;
		};
		F279B5127A0B7B329825D449F69F2AD5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "WordPress-Aztec-iOS";
			target = 6AC97A319172C716659A6283A2D82401 /* WordPress-Aztec-iOS */;
			targetProxy = 5BA3EE8DE43395370A8CB5DCF31CE249 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0D2B56A6F215C7EAD6E434940556307C /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C1B424D9A4ED7DC7AF7B7C7B262C16D0 /* IQKeyboardManager.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardManager";
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				IBSC_MODULE = IQKeyboardManager;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardManager/ResourceBundle-IQKeyboardManager-IQKeyboardManager-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				PRODUCT_NAME = IQKeyboardManager;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		151B83C38C7EF5C18AFC88CB5E31DC94 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1E74A73E3762665118306365F95453FC /* IQKeyboardManager.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardManager";
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				IBSC_MODULE = IQKeyboardManager;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardManager/ResourceBundle-IQKeyboardManager-IQKeyboardManager-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				PRODUCT_NAME = IQKeyboardManager;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		173FA297633A84FF9281F9226B6596DC /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5962947B21FF09EF8535877A731122F6 /* WordPress-Aztec-iOS.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/WordPress-Aztec-iOS/WordPress-Aztec-iOS-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/WordPress-Aztec-iOS/WordPress-Aztec-iOS-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/WordPress-Aztec-iOS/WordPress-Aztec-iOS.modulemap";
				PRODUCT_MODULE_NAME = Aztec;
				PRODUCT_NAME = Aztec;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		1D468000FB63B323BE02836990068238 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C7B6DB1429448F12BC797DA3929048F1 /* Masonry.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/Masonry/Masonry-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/Masonry/Masonry-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/Masonry/Masonry.modulemap";
				PRODUCT_MODULE_NAME = Masonry;
				PRODUCT_NAME = Masonry;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		2A489F99CFA7CA6E92D5287E0AEF05E3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9447A291573AA1FBEA2C01F4787364C2 /* CustomIOSAlertView.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/CustomIOSAlertView/CustomIOSAlertView-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/CustomIOSAlertView/CustomIOSAlertView-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/CustomIOSAlertView/CustomIOSAlertView.modulemap";
				PRODUCT_MODULE_NAME = CustomIOSAlertView;
				PRODUCT_NAME = CustomIOSAlertView;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		2C9F7FB62781162A8B7582587962F5A9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B42EE2D57EB703D1BF8AB71C3B9FDB31 /* Pods-SnapInspect3.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-SnapInspect3/Pods-SnapInspect3-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-SnapInspect3/Pods-SnapInspect3.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		31A45F7BF226D38974F538D67D429BEE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F05BAE02252D74D7B681179D52313356 /* ZSSRichTextEditor.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/ZSSRichTextEditor/ZSSRichTextEditor-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/ZSSRichTextEditor/ZSSRichTextEditor-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/ZSSRichTextEditor/ZSSRichTextEditor.modulemap";
				PRODUCT_MODULE_NAME = ZSSRichTextEditor;
				PRODUCT_NAME = ZSSRichTextEditor;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		32CDEED2F7F89B35639029DA5A1A9ACE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C704F55FD3FC1E0B66E7F3AAD1B60B07 /* WordPress-Aztec-iOS.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/WordPress-Aztec-iOS/WordPress-Aztec-iOS-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/WordPress-Aztec-iOS/WordPress-Aztec-iOS-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/WordPress-Aztec-iOS/WordPress-Aztec-iOS.modulemap";
				PRODUCT_MODULE_NAME = Aztec;
				PRODUCT_NAME = Aztec;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		41EC616CC40BEFDD0A0C33DF1708767F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9D99A603CF49DDE72E4979503156B621 /* AWSSimpleDB.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AWSSimpleDB/AWSSimpleDB-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AWSSimpleDB/AWSSimpleDB-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AWSSimpleDB/AWSSimpleDB.modulemap";
				PRODUCT_MODULE_NAME = AWSSimpleDB;
				PRODUCT_NAME = AWSSimpleDB;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		44ED85B1BDCC9E4BDC1AA238F48FA97D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5962947B21FF09EF8535877A731122F6 /* WordPress-Aztec-iOS.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/WordPress-Aztec-iOS";
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				IBSC_MODULE = Aztec;
				INFOPLIST_FILE = "Target Support Files/WordPress-Aztec-iOS/ResourceBundle-WordPress-Aztec-iOS-WordPress-Aztec-iOS-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				PRODUCT_NAME = "WordPress-Aztec-iOS";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		494323C9235057D6B0FD870F497944E7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1E74A73E3762665118306365F95453FC /* IQKeyboardManager.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardManager/IQKeyboardManager-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardManager/IQKeyboardManager-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardManager/IQKeyboardManager.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardManager;
				PRODUCT_NAME = IQKeyboardManager;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		4BC7450F9457737EE3E637BA155B56F7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		5E649202D5D7E62D5550FA3BE2A4F760 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BBBB244FFE50AAE6DB1130886DF23032 /* CustomIOSAlertView.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/CustomIOSAlertView/CustomIOSAlertView-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/CustomIOSAlertView/CustomIOSAlertView-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/CustomIOSAlertView/CustomIOSAlertView.modulemap";
				PRODUCT_MODULE_NAME = CustomIOSAlertView;
				PRODUCT_NAME = CustomIOSAlertView;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		6EB4D3C4687ED3AF7BC642A530F081BB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C1B424D9A4ED7DC7AF7B7C7B262C16D0 /* IQKeyboardManager.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardManager/IQKeyboardManager-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardManager/IQKeyboardManager-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardManager/IQKeyboardManager.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardManager;
				PRODUCT_NAME = IQKeyboardManager;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		76062912012EC792C8CD7ACAD21E386F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E810F8340D1D7BE3CB183C7BD2288F34 /* AWSCore.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AWSCore/AWSCore-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AWSCore/AWSCore-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AWSCore/AWSCore.modulemap";
				PRODUCT_MODULE_NAME = AWSCore;
				PRODUCT_NAME = AWSCore;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		7E2500C32BFD918290FED9D38F03EECF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5E1FC42EBC42141342AB83C301E52271 /* WordPress-Editor-iOS.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/WordPress-Editor-iOS/WordPress-Editor-iOS-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/WordPress-Editor-iOS/WordPress-Editor-iOS-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/WordPress-Editor-iOS/WordPress-Editor-iOS.modulemap";
				PRODUCT_MODULE_NAME = WordPressEditor;
				PRODUCT_NAME = WordPressEditor;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		7E3F438C258619B226F2DA453A5EE316 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3DA3759212250A359947AC1E937476A7 /* AWSCore.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AWSCore/AWSCore-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AWSCore/AWSCore-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AWSCore/AWSCore.modulemap";
				PRODUCT_MODULE_NAME = AWSCore;
				PRODUCT_NAME = AWSCore;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		8B5A46FF8D3C1289CDEE3BAFACABCD2A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		906B2AAA2124302D202B72D0F79836F6 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C4EAAC03AA682321088C5C7E119902F4 /* UITextView+Placeholder.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/UITextView+Placeholder/UITextView+Placeholder-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/UITextView+Placeholder/UITextView+Placeholder-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/UITextView+Placeholder/UITextView+Placeholder.modulemap";
				PRODUCT_MODULE_NAME = UITextView_Placeholder;
				PRODUCT_NAME = UITextView_Placeholder;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		95F86E5218A940FA3DF7B56C12FCC4EB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CDFF82EA0C1F246765CD4A311891D7C3 /* Pods-SnapInspect3Tests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-SnapInspect3Tests/Pods-SnapInspect3Tests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-SnapInspect3Tests/Pods-SnapInspect3Tests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		A17DB6B50AB9CA07B000E3B92DC219C9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0AD9939574ACB24611ED895616022021 /* UITextView+Placeholder.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/UITextView+Placeholder/UITextView+Placeholder-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/UITextView+Placeholder/UITextView+Placeholder-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/UITextView+Placeholder/UITextView+Placeholder.modulemap";
				PRODUCT_MODULE_NAME = UITextView_Placeholder;
				PRODUCT_NAME = UITextView_Placeholder;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		A23E74CAA346A5C6F63A9B11A1CD7D4C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F1E1C31E6F9833AF2280F8283372F395 /* Pods-SnapInspect3.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-SnapInspect3/Pods-SnapInspect3-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-SnapInspect3/Pods-SnapInspect3.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		B593D91DCD1190C2F498CFED3D05ABB5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5E0C197C502079E60EEE592311CFA30B /* Pods-SnapInspect3Tests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-SnapInspect3Tests/Pods-SnapInspect3Tests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-SnapInspect3Tests/Pods-SnapInspect3Tests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		CECB034CA2D7CE2D103BC407485A783E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FAE342899099CA96E4C99DF48AD45A7F /* AWSS3.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AWSS3/AWSS3-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AWSS3/AWSS3-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AWSS3/AWSS3.modulemap";
				PRODUCT_MODULE_NAME = AWSS3;
				PRODUCT_NAME = AWSS3;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		CECC5694E635C8311E213453B80E51AF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F216A56A86E2A5A208E098F355013AD0 /* Masonry.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/Masonry/Masonry-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/Masonry/Masonry-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/Masonry/Masonry.modulemap";
				PRODUCT_MODULE_NAME = Masonry;
				PRODUCT_NAME = Masonry;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		D03AEC080704512984F11ABE702BF64D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 756F66AADA06C45EA43815C641BEB5C7 /* AWSS3.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AWSS3/AWSS3-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AWSS3/AWSS3-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AWSS3/AWSS3.modulemap";
				PRODUCT_MODULE_NAME = AWSS3;
				PRODUCT_NAME = AWSS3;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		DA119A98978F7C133A83918593AC7BED /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8E95744135DE0D8AA27F6AE43903438E /* ZSSRichTextEditor.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/ZSSRichTextEditor/ZSSRichTextEditor-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/ZSSRichTextEditor/ZSSRichTextEditor-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/ZSSRichTextEditor/ZSSRichTextEditor.modulemap";
				PRODUCT_MODULE_NAME = ZSSRichTextEditor;
				PRODUCT_NAME = ZSSRichTextEditor;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		DB447F1F2B06FF8A082B0CFF551BFFA7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 78D5531F362D6B1C64A1CAD7A6BBBCA7 /* AWSSimpleDB.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AWSSimpleDB/AWSSimpleDB-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AWSSimpleDB/AWSSimpleDB-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AWSSimpleDB/AWSSimpleDB.modulemap";
				PRODUCT_MODULE_NAME = AWSSimpleDB;
				PRODUCT_NAME = AWSSimpleDB;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		E02771FC11F1768E466B6A341ED4DB01 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D80FD5776C34FB276B14EA221751D4D9 /* WordPress-Editor-iOS.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/WordPress-Editor-iOS/WordPress-Editor-iOS-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/WordPress-Editor-iOS/WordPress-Editor-iOS-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/WordPress-Editor-iOS/WordPress-Editor-iOS.modulemap";
				PRODUCT_MODULE_NAME = WordPressEditor;
				PRODUCT_NAME = WordPressEditor;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		E523BCD509159E0FECC12581C1C59D8F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C704F55FD3FC1E0B66E7F3AAD1B60B07 /* WordPress-Aztec-iOS.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/WordPress-Aztec-iOS";
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				IBSC_MODULE = Aztec;
				INFOPLIST_FILE = "Target Support Files/WordPress-Aztec-iOS/ResourceBundle-WordPress-Aztec-iOS-WordPress-Aztec-iOS-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				PRODUCT_NAME = "WordPress-Aztec-iOS";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1F170271388A077284D01A4A6CFE110F /* Build configuration list for PBXNativeTarget "AWSCore" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				76062912012EC792C8CD7ACAD21E386F /* Debug */,
				7E3F438C258619B226F2DA453A5EE316 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2047E4C7B8EDB059FD20EA06658CB9F9 /* Build configuration list for PBXNativeTarget "WordPress-Editor-iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E02771FC11F1768E466B6A341ED4DB01 /* Debug */,
				7E2500C32BFD918290FED9D38F03EECF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		239B9A75CC69D3964BA7F7A7410D589D /* Build configuration list for PBXNativeTarget "UITextView+Placeholder" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				906B2AAA2124302D202B72D0F79836F6 /* Debug */,
				A17DB6B50AB9CA07B000E3B92DC219C9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3574A7132E688330A708249089C9CF6D /* Build configuration list for PBXNativeTarget "ZSSRichTextEditor" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				31A45F7BF226D38974F538D67D429BEE /* Debug */,
				DA119A98978F7C133A83918593AC7BED /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		45E6DA44288B44F53737AD256C91A6E1 /* Build configuration list for PBXNativeTarget "WordPress-Aztec-iOS-WordPress-Aztec-iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				44ED85B1BDCC9E4BDC1AA238F48FA97D /* Debug */,
				E523BCD509159E0FECC12581C1C59D8F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4BC7450F9457737EE3E637BA155B56F7 /* Debug */,
				8B5A46FF8D3C1289CDEE3BAFACABCD2A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5FE03105CF3A887F508DDE7D404339E3 /* Build configuration list for PBXNativeTarget "IQKeyboardManager" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				494323C9235057D6B0FD870F497944E7 /* Debug */,
				6EB4D3C4687ED3AF7BC642A530F081BB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		76939D389CDD9B4B104211E988FDE353 /* Build configuration list for PBXNativeTarget "Pods-SnapInspect3" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A23E74CAA346A5C6F63A9B11A1CD7D4C /* Debug */,
				2C9F7FB62781162A8B7582587962F5A9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8747B4F8F5B4A7D1C561F461428A3347 /* Build configuration list for PBXNativeTarget "Pods-SnapInspect3Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B593D91DCD1190C2F498CFED3D05ABB5 /* Debug */,
				95F86E5218A940FA3DF7B56C12FCC4EB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		892DC5D9947F6C1ED7F2241A62F9AF10 /* Build configuration list for PBXNativeTarget "AWSS3" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D03AEC080704512984F11ABE702BF64D /* Debug */,
				CECB034CA2D7CE2D103BC407485A783E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		957A159903F40D56564D95C99EABDFC0 /* Build configuration list for PBXNativeTarget "WordPress-Aztec-iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				173FA297633A84FF9281F9226B6596DC /* Debug */,
				32CDEED2F7F89B35639029DA5A1A9ACE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A6897EFCA05D270738AB2D00B8924318 /* Build configuration list for PBXNativeTarget "IQKeyboardManager-IQKeyboardManager" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				151B83C38C7EF5C18AFC88CB5E31DC94 /* Debug */,
				0D2B56A6F215C7EAD6E434940556307C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AAA1F8799DB68036C3BE983C05FAA2C7 /* Build configuration list for PBXNativeTarget "Masonry" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1D468000FB63B323BE02836990068238 /* Debug */,
				CECC5694E635C8311E213453B80E51AF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B57ABFF6818342A0B1A06435E66262CF /* Build configuration list for PBXNativeTarget "CustomIOSAlertView" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2A489F99CFA7CA6E92D5287E0AEF05E3 /* Debug */,
				5E649202D5D7E62D5550FA3BE2A4F760 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F85814F662D635876EFB8EE2979FD3D8 /* Build configuration list for PBXNativeTarget "AWSSimpleDB" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB447F1F2B06FF8A082B0CFF551BFFA7 /* Debug */,
				41EC616CC40BEFDD0A0C33DF1708767F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
