#ifdef __OBJC__
#import <UIKit/UIKit.h>
#else
#ifndef FOUNDATION_EXPORT
#if defined(__cplusplus)
#define FOUNDATION_EXPORT extern "C"
#else
#define FOUNDATION_EXPORT extern
#endif
#endif
#endif

#import "AWSS3.h"
#import "AWSS3Model.h"
#import "AWSS3PreSignedURL.h"
#import "AWSS3RequestRetryHandler.h"
#import "AWSS3Resources.h"
#import "AWSS3Serializer.h"
#import "AWSS3Service.h"
#import "AWSS3TransferManager.h"
#import "AWSS3TransferUtility+HeaderHelper.h"
#import "AWSS3TransferUtility.h"
#import "AWSS3TransferUtilityDatabaseHelper.h"
#import "AWSS3TransferUtilityTasks.h"

FOUNDATION_EXPORT double AWSS3VersionNumber;
FOUNDATION_EXPORT const unsigned char AWSS3VersionString[];

