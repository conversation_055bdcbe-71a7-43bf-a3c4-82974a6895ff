ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/AWSCore" "${PODS_CONFIGURATION_BUILD_DIR}/AWSS3" "${PODS_CONFIGURATION_BUILD_DIR}/AWSSimpleDB" "${PODS_CONFIGURATION_BUILD_DIR}/CustomIOSAlertView" "${PODS_CONFIGURATION_BUILD_DIR}/IQKeyboardManager" "${PODS_CONFIGURATION_BUILD_DIR}/Masonry" "${PODS_CONFIGURATION_BUILD_DIR}/UITextView+Placeholder" "${PODS_CONFIGURATION_BUILD_DIR}/WordPress-Aztec-iOS" "${PODS_CONFIGURATION_BUILD_DIR}/WordPress-Editor-iOS" "${PODS_CONFIGURATION_BUILD_DIR}/ZSSRichTextEditor"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/AWSCore/AWSCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/AWSS3/AWSS3.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/AWSSimpleDB/AWSSimpleDB.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/CustomIOSAlertView/CustomIOSAlertView.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/IQKeyboardManager/IQKeyboardManager.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Masonry/Masonry.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/UITextView+Placeholder/UITextView_Placeholder.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/WordPress-Aztec-iOS/Aztec.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/WordPress-Editor-iOS/WordPressEditor.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ZSSRichTextEditor/ZSSRichTextEditor.framework/Headers" /usr/include/libxml2
LD_RUNPATH_SEARCH_PATHS = $(inherited) /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks'
LIBRARY_SEARCH_PATHS = $(inherited) "${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}" /usr/lib/swift
OTHER_LDFLAGS = $(inherited) -l"sqlite3" -l"xml2" -l"z" -framework "AWSCore" -framework "AWSS3" -framework "AWSSimpleDB" -framework "Aztec" -framework "CoreGraphics" -framework "CoreText" -framework "CustomIOSAlertView" -framework "Foundation" -framework "IQKeyboardManager" -framework "Masonry" -framework "QuartzCore" -framework "Security" -framework "SystemConfiguration" -framework "UIKit" -framework "UITextView_Placeholder" -framework "WordPressEditor" -framework "ZSSRichTextEditor"
OTHER_MODULE_VERIFIER_FLAGS = $(inherited) "-F${PODS_CONFIGURATION_BUILD_DIR}/AWSCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/AWSS3" "-F${PODS_CONFIGURATION_BUILD_DIR}/AWSSimpleDB" "-F${PODS_CONFIGURATION_BUILD_DIR}/CustomIOSAlertView" "-F${PODS_CONFIGURATION_BUILD_DIR}/IQKeyboardManager" "-F${PODS_CONFIGURATION_BUILD_DIR}/Masonry" "-F${PODS_CONFIGURATION_BUILD_DIR}/UITextView+Placeholder" "-F${PODS_CONFIGURATION_BUILD_DIR}/WordPress-Aztec-iOS" "-F${PODS_CONFIGURATION_BUILD_DIR}/WordPress-Editor-iOS" "-F${PODS_CONFIGURATION_BUILD_DIR}/ZSSRichTextEditor"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
