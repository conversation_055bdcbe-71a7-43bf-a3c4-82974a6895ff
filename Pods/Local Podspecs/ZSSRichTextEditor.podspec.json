{"name": "ZSSRichTextEditor", "version": "*******", "summary": "ZSSRichTextEditor is a beautiful Rich Text WYSIWYG Editor for iOS.", "description": "`ZSSRichTextEditor` is a beautiful Rich Text `WYSIWYG Editor` for `iOS`. It includes all of the standard editor tools one would expect from a `WYSIWYG` editor as well as an amazing source view with syntax highlighting.", "homepage": "https://github.com/nnhubbard/ZSSRichTextEditor", "screenshots": ["https://camo.githubusercontent.com/2bcf02776f39cae560c57793adbd5eaf4fff9223/687474703a2f2f662e636c2e6c792f6974656d732f304c3363304e337531343251325330763159306f2f64656d6f312e676966", "https://camo.githubusercontent.com/3f9c01eba9c69d030a69faaa1a2e01a733244627/687474703a2f2f636c2e6c792f696d6167652f3369343134303367323030422f64656d6f2e676966"], "license": "MIT", "authors": {"Nic Hubbard": "<EMAIL>"}, "platforms": {"ios": "8.0"}, "source": {"git": "https://github.com/nnhubbard/ZSSRichTextEditor.git", "tag": "*******"}, "source_files": "**/*.{h,m}", "exclude_files": ["**/ZSSDemo*.{h,m}", "**/ZSSAppDelegate*.{h,m}", "**/main.m"], "resources": ["**/ZSS*.png", "**/ZSSRichTextEditor.js", "**/editor.html", "**/jQuery.js", "**/JSBeautifier.js"], "frameworks": ["CoreGraphics", "CoreText"], "requires_arc": true}