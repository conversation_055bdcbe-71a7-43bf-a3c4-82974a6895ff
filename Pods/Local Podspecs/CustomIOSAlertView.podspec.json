{"name": "CustomIOSAlertView", "version": "0.9.5", "summary": "Custom UIAlertView. Continue adding images and UIViews to dialogs on iOS7+.", "description": "The addSubview is not available in UIAlertView in iOS7+. The view hierarchy for this\nclass is private and must not be modified. As a solution, this class creates an iOS-style dialog which\nyou can extend with any UIViews or buttons. The animations and the looks are copied too and no images\nor other resources are needed.", "homepage": "https://github.com/wimagguc/ios-custom-alertview", "screenshots": "https://github.com/wimagguc/ios-custom-alertview/raw/master/Docs/screen.png", "license": {"type": "MIT", "file": "LICENSE.md"}, "authors": {"Richard Dancsi": "<EMAIL>"}, "social_media_url": "http://twitter.com/wimagguc", "platforms": {"ios": null}, "source": {"git": "https://github.com/wimagguc/ios-custom-alertview.git", "tag": "0.9.5"}, "source_files": "CustomIOSAlertView/CustomIOSAlertView/View/**/*.{h,m}", "requires_arc": true}