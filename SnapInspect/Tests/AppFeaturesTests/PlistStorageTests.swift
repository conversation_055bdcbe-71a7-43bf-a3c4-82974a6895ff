//
//  PlistStorageTests.swift
//  SnapInspect
//
//  Created by <PERSON> on 5/27/25.
//

@testable import AppFeatures
@testable import Extensions
import XCTest

/// Simple test class for PlistStorage
public class PlistStorageTests {
    public static func runAllTests() {
        print("🧪 Running PlistStorage tests...")
        
        // Create a test storage with a unique filename
        let testStorage = PlistStorage(
            fileName: "test_preferences_\(UUID().uuidString)",
            inDirectory: .searchDirectory(.documentDirectory)!
        )
        
        // Clean up any existing data
        testStorage.clearAll()
        testStorage.synchronize()
        
        var allTestsPassed = true
        
        allTestsPassed = testStringStorage(testStorage) && allTestsPassed
        allTestsPassed = testBoolStorage(testStorage) && allTestsPassed
        allTestsPassed = testIntStorage(testStorage) && allTestsPassed
        allTestsPassed = testDoubleStorage(testStorage) && allTestsPassed
        allTestsPassed = testCodableStorage(testStorage) && allTestsPassed
        allTestsPassed = testRemoveAndClear(testStorage) && allTestsPassed
        allTestsPassed = testPersistence(testStorage) && allTestsPassed
        
        // Clean up test file
        do {
            try testStorage.deletePlistFile()
        } catch {
            print("⚠️ Warning: Could not delete test plist file: \(error)")
        }
        
        if allTestsPassed {
            print("✅ All PlistStorage tests passed!")
        } else {
            print("❌ Some PlistStorage tests failed!")
        }
    }
    
    private static func testStringStorage(_ storage: PlistStorage) -> Bool {
        print("Testing string storage...")
        
        // Test setting and getting string
        let success = storage.setString("test_value", forKey: "test_key")
        guard success else {
            print("❌ Failed to set string value")
            return false
        }
        
        let retrieved = storage.getString(forKey: "test_key")
        guard retrieved == "test_value" else {
            print("❌ Retrieved string value doesn't match. Expected: test_value, Got: \(String(describing: retrieved))")
            return false
        }
        
        // Test setting nil (removal)
        _ = storage.setString(nil, forKey: "test_key")
        let nilRetrieved = storage.getString(forKey: "test_key")
        guard nilRetrieved == nil else {
            print("❌ String value should be nil after setting to nil. Got: \(String(describing: nilRetrieved))")
            return false
        }
        
        print("✅ String storage test passed")
        return true
    }
    
    private static func testBoolStorage(_ storage: PlistStorage) -> Bool {
        print("Testing bool storage...")
        
        // Test setting and getting bool
        let success = storage.setBool(true, forKey: "test_bool")
        guard success else {
            print("❌ Failed to set bool value")
            return false
        }
        
        let retrieved = storage.getBool(forKey: "test_bool")
        guard retrieved == true else {
            print("❌ Retrieved bool value doesn't match. Expected: true, Got: \(retrieved)")
            return false
        }
        
        // Test false value
        _ = storage.setBool(false, forKey: "test_bool")
        let falseRetrieved = storage.getBool(forKey: "test_bool")
        guard falseRetrieved == false else {
            print("❌ Retrieved bool value doesn't match. Expected: false, Got: \(falseRetrieved)")
            return false
        }
        
        // Test string-based boolean (backward compatibility)
        _ = storage.setString("1", forKey: "test_string_bool")
        let stringBool = storage.getBool(forKey: "test_string_bool")
        guard stringBool == true else {
            print("❌ String '1' should be parsed as true. Got: \(stringBool)")
            return false
        }
        
        print("✅ Bool storage test passed")
        return true
    }
    
    private static func testIntStorage(_ storage: PlistStorage) -> Bool {
        print("Testing int storage...")
        
        // Test setting and getting int
        let testValue = 42
        let success = storage.setInt(testValue, forKey: "test_int")
        guard success else {
            print("❌ Failed to set int value")
            return false
        }
        
        let retrieved = storage.getInt(forKey: "test_int")
        guard retrieved == testValue else {
            print("❌ Retrieved int value doesn't match. Expected: \(testValue), Got: \(retrieved)")
            return false
        }
        
        // Test string-based int (backward compatibility)
        _ = storage.setString("123", forKey: "test_string_int")
        let stringInt = storage.getInt(forKey: "test_string_int")
        guard stringInt == 123 else {
            print("❌ String '123' should be parsed as 123. Got: \(stringInt)")
            return false
        }
        
        print("✅ Int storage test passed")
        return true
    }
    
    private static func testDoubleStorage(_ storage: PlistStorage) -> Bool {
        print("Testing double storage...")
        
        // Test setting and getting double
        let testValue = 3.14159
        let success = storage.setDouble(testValue, forKey: "test_double")
        guard success else {
            print("❌ Failed to set double value")
            return false
        }
        
        let retrieved = storage.getDouble(forKey: "test_double")
        guard abs(retrieved - testValue) < 0.0001 else {
            print("❌ Retrieved double value doesn't match. Expected: \(testValue), Got: \(retrieved)")
            return false
        }
        
        // Test string-based double (backward compatibility)
        _ = storage.setString("2.718", forKey: "test_string_double")
        let stringDouble = storage.getDouble(forKey: "test_string_double")
        guard abs(stringDouble - 2.718) < 0.0001 else {
            print("❌ String '2.718' should be parsed as 2.718. Got: \(stringDouble)")
            return false
        }
        
        print("✅ Double storage test passed")
        return true
    }
    
    private static func testCodableStorage(_ storage: PlistStorage) -> Bool {
        print("Testing Codable storage...")
        
        struct TestStruct: Codable, Equatable {
            let name: String
            let age: Int
        }
        
        let testValue = TestStruct(name: "John", age: 30)
        let success = storage.setValue(testValue, forKey: "test_codable")
        guard success else {
            print("❌ Failed to set Codable value")
            return false
        }
        
        let retrieved: TestStruct? = storage.getValue(forKey: "test_codable")
        guard let retrieved = retrieved, retrieved == testValue else {
            print("❌ Retrieved Codable value doesn't match. Expected: \(testValue), Got: \(String(describing: retrieved))")
            return false
        }
        
        print("✅ Codable storage test passed")
        return true
    }
    
    private static func testRemoveAndClear(_ storage: PlistStorage) -> Bool {
        print("Testing remove and clear...")
        
        // Set some values
        _ = storage.setString("test1", forKey: "key1")
        _ = storage.setString("test2", forKey: "key2")
        _ = storage.setString("test3", forKey: "key3")
        
        // Test remove
        storage.remove(forKey: "key2")
        
        guard storage.getString(forKey: "key1") == "test1" else {
            print("❌ Key1 should still exist")
            return false
        }
        
        guard storage.getString(forKey: "key2") == nil else {
            print("❌ Key2 should be removed")
            return false
        }
        
        guard storage.getString(forKey: "key3") == "test3" else {
            print("❌ Key3 should still exist")
            return false
        }
        
        // Test clear all
        storage.clearAll()
        
        guard storage.getString(forKey: "key1") == nil else {
            print("❌ All keys should be cleared")
            return false
        }
        
        guard storage.getString(forKey: "key3") == nil else {
            print("❌ All keys should be cleared")
            return false
        }
        
        print("✅ Remove and clear test passed")
        return true
    }
    
    private static func testPersistence(_ storage: PlistStorage) -> Bool {
        print("Testing persistence...")
        
        // Set some values
        _ = storage.setString("persistent_value", forKey: "persistent_key")
        _ = storage.setInt(999, forKey: "persistent_int")
        
        // Force synchronization
        storage.synchronize()
        
        // Give it a moment to write to disk
        Thread.sleep(forTimeInterval: 0.1)
        
        // Create a new storage instance with the same file
        let filePath = storage.filePath
        let newStorage = PlistStorage(plistURL: URL(fileURLWithPath: filePath))
        
        // Give it a moment to load from disk
        Thread.sleep(forTimeInterval: 0.1)
        
        guard newStorage.getString(forKey: "persistent_key") == "persistent_value" else {
            print("❌ String value should persist across instances")
            return false
        }
        
        guard newStorage.getInt(forKey: "persistent_int") == 999 else {
            print("❌ Int value should persist across instances")
            return false
        }
        
        print("✅ Persistence test passed")
        return true
    }
}
