//
//  PrefsManagerTests.swift
//  SnapInspect3Tests
//
//  Created by <PERSON> on 2025/05/24.
//

@testable import AppFeatures
import XCTest

// MARK: - Mock Storage for Testing

class MockStorage: PrefsStorageProtocol {
    private var storage: [String: Any] = [:]
    private var shouldFailOperations = false
    
    func getString(forKey key: String) -> String? {
        return storage[key] as? String
    }
    
    func setString(_ value: String?, forKey key: String) -> Bool {
        if shouldFailOperations { return false }
        storage[key] = value
        return true
    }
    
    func getBool(forKey key: String) -> Bool {
        return storage[key] as? Bool ?? false
    }
    
    func setBool(_ value: Bool, forKey key: String) -> Bool {
        if shouldFailOperations { return false }
        storage[key] = value
        return true
    }
    
    func getInt(forKey key: String) -> Int {
        return storage[key] as? Int ?? 0
    }
    
    func setInt(_ value: Int, forKey key: String) -> <PERSON>ol {
        if shouldFailOperations { return false }
        storage[key] = value
        return true
    }
    
    func getDouble(forKey key: String) -> Double {
        return storage[key] as? Double ?? 0.0
    }
    
    func setDouble(_ value: Double, forKey key: String) -> Bo<PERSON> {
        if shouldFailOperations { return false }
        storage[key] = value
        return true
    }
    
    func remove(forKey key: String) {
        storage.removeValue(forKey: key)
    }
    
    func synchronize() {
        // No-op for mock
    }
    
    func clearAll() {
        storage.removeAll()
    }
    
    // Helper method for testing
    func getAllKeys() -> [String] {
        return Array(storage.keys)
    }
    
    func setValue<T>(_ value: T?, forKey key: String) -> Bool where T: Decodable, T: Encodable {
        if shouldFailOperations { return false }
        storage[key] = value
        return true
    }
    
    func getValue<T>(forKey key: String) -> T? where T: Decodable, T: Encodable {
        storage[key] as? T
    }
    
    func setFailureMode(_ shouldFail: Bool) {
        shouldFailOperations = shouldFail
    }
}

// MARK: - Test Cases

class PrefsManagerTests: XCTestCase {
    var mockStorage: MockStorage!
    var prefsManager: PrefsManager!
    
    override func setUp() {
        super.setUp()
        mockStorage = MockStorage()
        prefsManager = PrefsManager(storage: mockStorage)
    }
    
    override func tearDown() {
        mockStorage = nil
        prefsManager = nil
        super.tearDown()
    }
    
    // MARK: - Basic String Operations
    
    func testStringOperations() throws {
        let key = "test_string_key"
        let value = "test_value"
        
        // Test setting string
        try prefsManager.setString(value, forKey: key)
        
        // Test getting string
        let retrievedValue = try prefsManager.getString(forKey: key)
        XCTAssertEqual(retrievedValue, value)
        
        // Test setting nil
        try prefsManager.setString(nil, forKey: key)
        let nilValue = try prefsManager.getString(forKey: key)
        XCTAssertNil(nilValue)
    }
    
    // MARK: - Boolean Operations
    
    func testBooleanOperations() throws {
        let key = "test_bool_key"
        
        // Test setting true
        try prefsManager.setBool(true, forKey: key)
        let retrievedTrue = try prefsManager.getBool(forKey: key)
        XCTAssertTrue(retrievedTrue)
        
        // Test setting false
        try prefsManager.setBool(false, forKey: key)
        let retrievedFalse = try prefsManager.getBool(forKey: key)
        XCTAssertFalse(retrievedFalse)
        
        // Test backward compatibility - bools stored as strings
        let stringValue = try prefsManager.getString(forKey: key)
        XCTAssertEqual(stringValue, "0") // false stored as "0"
        
        // Test string-to-bool conversion
        try prefsManager.setString("1", forKey: key)
        let boolFromString = try prefsManager.getBool(forKey: key)
        XCTAssertTrue(boolFromString)
    }
    
    // MARK: - Integer Operations
    
    func testIntegerOperations() throws {
        let key = "test_int_key"
        let value = 42
        
        // Test setting int
        try prefsManager.setInt(value, forKey: key)
        
        // Test getting int
        let retrievedValue = try prefsManager.getInt(forKey: key)
        XCTAssertEqual(retrievedValue, value)
        
        // Test backward compatibility - ints stored as strings
        let stringValue = try prefsManager.getString(forKey: key)
        XCTAssertEqual(stringValue, "42")
        
        // Test zero value
        try prefsManager.setInt(0, forKey: key)
        let zeroValue = try prefsManager.getInt(forKey: key)
        XCTAssertEqual(zeroValue, 0)
    }
    
    // MARK: - Double Operations
    
    func testDoubleOperations() throws {
        let key = "test_double_key"
        let value = 3.14159
        
        // Test setting double
        try prefsManager.setDouble(value, forKey: key)
        
        // Test getting double
        let retrievedValue = try prefsManager.getDouble(forKey: key)
        XCTAssertEqual(retrievedValue, value, accuracy: 0.00001)
        
        // Test backward compatibility - doubles stored as strings
        let stringValue = try prefsManager.getString(forKey: key)
        XCTAssertEqual(stringValue, String(value))
    }
    
    // MARK: - Codable Value Operations
    
    func testCodableValueOperations() throws {
        struct TestStruct: Codable, Equatable {
            let name: String
            let age: Int
            let isActive: Bool
        }
        
        let key = "test_codable_key"
        let value = TestStruct(name: "John", age: 30, isActive: true)
        
        // Test setting codable value
        try prefsManager.setValue(value, forKey: key)
        
        // Test getting codable value
        let retrievedValue: TestStruct? = try prefsManager.getValue(forKey: key)
        XCTAssertNotNil(retrievedValue)
        XCTAssertEqual(retrievedValue, value)
        
        // Test with array
        let testArray = [
            TestStruct(name: "Alice", age: 25, isActive: false),
            TestStruct(name: "Bob", age: 35, isActive: true)
        ]
        try prefsManager.setValue(testArray, forKey: "test_array")
        
        let retrievedArray: [TestStruct]? = try prefsManager.getValue(forKey: "test_array")
        XCTAssertNotNil(retrievedArray)
        XCTAssertEqual(retrievedArray?.count, 2)
        XCTAssertEqual(retrievedArray?[0], testArray[0])
        XCTAssertEqual(retrievedArray?[1], testArray[1])
        
        // Test with nil value for nonexistent key
        let nilValue: TestStruct? = try prefsManager.getValue(forKey: "nonexistent_key")
        XCTAssertNil(nilValue)
    }
    
    // MARK: - Remove Operations
    
    func testRemoveOperations() throws {
        let key = "test_remove_key"
        let value = "test_value"
        
        // Set a value
        try prefsManager.setString(value, forKey: key)
        XCTAssertNotNil(try prefsManager.getString(forKey: key))
        
        // Remove the value
        try prefsManager.remove(forKey: key)
        let removedValue = try prefsManager.getString(forKey: key)
        XCTAssertNil(removedValue)
    }
    
    // MARK: - Objective-C Compatibility
    
    func testObjectiveCCompatibility() {
        let key = "test_objc_key"
        let stringValue = "test_value"
        let boolValue = true
        
        // Test string operations
        let stringSuccess = prefsManager.ifSavePref(key, sValue: stringValue)
        XCTAssertTrue(stringSuccess)
        
        let retrievedString = prefsManager.ifGetPref(key)
        XCTAssertEqual(retrievedString, stringValue)
        
        // Test boolean operations
        let boolSuccess = prefsManager.ifSavePref(key, sValue: boolValue ? "1" : "0")
        XCTAssertTrue(boolSuccess)
        
        let retrievedBool = prefsManager.ifGetPrefBool(key)
        XCTAssertEqual(retrievedBool, boolValue)
        
        // Test default value
        prefsManager.ifRemovePref(key)
        let defaultBool = prefsManager.ifGetPrefBool(key, defaultValue: true)
        XCTAssertTrue(defaultBool)
        
        // Test nil handling
        let nilValue = prefsManager.ifGetPref("nonexistent_key")
        XCTAssertNil(nilValue)
        
        // Test removing preferences
        prefsManager.ifSavePref("temp_key", sValue: "temp_value")
        XCTAssertNotNil(prefsManager.ifGetPref("temp_key"))
        prefsManager.ifRemovePref("temp_key")
        XCTAssertNil(prefsManager.ifGetPref("temp_key"))
    }
    
    // MARK: - Error Handling
    
    func testErrorHandling() {
        // Test storage failure handling
        mockStorage.setFailureMode(true)
        
        // String operations should throw
        XCTAssertThrowsError(try prefsManager.setString("test", forKey: "test"))
        
        // Bool operations should throw
        XCTAssertThrowsError(try prefsManager.setBool(true, forKey: "test"))
        
        // Int operations should throw
        XCTAssertThrowsError(try prefsManager.setInt(42, forKey: "test"))
        
        // Double operations should throw
        XCTAssertThrowsError(try prefsManager.setDouble(3.14, forKey: "test"))
        
        // Codable operations should throw
        XCTAssertThrowsError(try prefsManager.setValue("test", forKey: "test"))
        
        // Reset storage
        mockStorage.setFailureMode(false)
        
        // Operations should work again
        XCTAssertNoThrow(try prefsManager.setString("test", forKey: "test"))
        XCTAssertNoThrow(try prefsManager.getString(forKey: "test"))
        XCTAssertNoThrow(try prefsManager.remove(forKey: "test"))
    }
    
    // MARK: - Objective-C Error Resilience
    
    func testObjectiveCErrorResilience() {
        // Objective-C methods should not throw, but return default values on error
        mockStorage.setFailureMode(true)
        
        let saveResult = prefsManager.ifSavePref("test", sValue: "value")
        XCTAssertFalse(saveResult)
        
        let stringResult = prefsManager.ifGetPref("test")
        XCTAssertNil(stringResult)
        
        let boolResult = prefsManager.ifGetPrefBool("test")
        XCTAssertFalse(boolResult)
        
        let boolWithDefaultResult = prefsManager.ifGetPrefBool("test", defaultValue: true)
        XCTAssertTrue(boolWithDefaultResult)
        
        // Remove should not crash
        prefsManager.ifRemovePref("test")
    }
    
    // MARK: - Performance Tests
    
    func testPerformance() {
        // Test setting many preferences
        measure {
            for i in 0..<1000 {
                prefsManager.ifSavePref("key_\(i)", sValue: "value_\(i)")
            }
        }
    }
    
    // MARK: - Thread Safety Tests
    
    func testThreadSafety() {
        let expectation = XCTestExpectation(description: "Concurrent operations")
        let iterations = 100
        var completedOperations = 0
        
        // Perform concurrent read/write operations
        for i in 0..<iterations {
            DispatchQueue.global().async {
                let key = "thread_test_\(i % 10)" // Use limited keys to increase contention
                let value = "value_\(i)"
                
                // Write operation
                self.prefsManager.ifSavePref(key, sValue: value)
                
                // Read operation
                _ = self.prefsManager.ifGetPref(key)
                
                DispatchQueue.main.async {
                    completedOperations += 1
                    if completedOperations == iterations {
                        expectation.fulfill()
                    }
                }
            }
        }
        
        wait(for: [expectation], timeout: 10.0)
        XCTAssertEqual(completedOperations, iterations)
    }
}

// MARK: - UserDefaultsStorage Tests

class UserDefaultsStorageTests: XCTestCase {
    var storage: UserDefaultsStorage!
    var testDefaults: UserDefaults!
    
    override func setUp() {
        super.setUp()
        testDefaults = UserDefaults(suiteName: "test.preferences")!
        storage = UserDefaultsStorage(userDefaults: testDefaults)
    }
    
    override func tearDown() {
        testDefaults.removePersistentDomain(forName: "test.preferences")
        storage = nil
        testDefaults = nil
        super.tearDown()
    }
    
    func testBasicOperations() {
        // String operations
        XCTAssertTrue(storage.setString("test_value", forKey: "test_string"))
        XCTAssertEqual(storage.getString(forKey: "test_string"), "test_value")
        
        // Boolean operations
        XCTAssertTrue(storage.setBool(true, forKey: "test_bool"))
        XCTAssertTrue(storage.getBool(forKey: "test_bool"))
        
        // Integer operations
        XCTAssertTrue(storage.setInt(42, forKey: "test_int"))
        XCTAssertEqual(storage.getInt(forKey: "test_int"), 42)
        
        // Double operations
        XCTAssertTrue(storage.setDouble(3.14, forKey: "test_double"))
        XCTAssertEqual(storage.getDouble(forKey: "test_double"), 3.14, accuracy: 0.001)
        
        // Remove operation
        storage.remove(forKey: "test_string")
        XCTAssertNil(storage.getString(forKey: "test_string"))
        
        // Clear all
        storage.clearAll()
        XCTAssertFalse(storage.getBool(forKey: "test_bool"))
        XCTAssertEqual(storage.getInt(forKey: "test_int"), 0)
    }
    
    func testCodableOperations() {
        struct TestData: Codable, Equatable {
            let name: String
            let value: Int
        }
        
        let testData = TestData(name: "test", value: 123)
        
        XCTAssertTrue(storage.setValue(testData, forKey: "test_codable"))
        let retrieved: TestData? = storage.getValue(forKey: "test_codable")
        XCTAssertEqual(retrieved, testData)
        
        // Test with nil
        XCTAssertTrue(storage.setValue(nil as TestData?, forKey: "test_codable"))
        let nilRetrieved: TestData? = storage.getValue(forKey: "test_codable")
        XCTAssertNil(nilRetrieved)
    }
}
