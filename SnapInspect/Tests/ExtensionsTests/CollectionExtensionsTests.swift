//
//  CollectionExtensionsTests.swift
//  ExtensionsTests
//
//  Created by <PERSON> on 2024/8/31.
//

@testable import Extensions
import XCTest

final class CollectionExtensionsTests: XCTestCase {

    // MARK: - Collection

    func testSafeSubscript() {
        let array = [1, 2, 3]
        XCTAssertEqual(array[safe: 1], 2)
        XCTAssertNil(array[safe: 5])
    }

    func testDefaultSubscript() {
        let array = ["a", "b", "c"]
        XCTAssertEqual(array[1, default: "x"], "b")
        XCTAssertEqual(array[5, default: "x"], "x")
    }

    func testSortedByKeyPathAscending() {
        struct Person { let name: String; let age: Int }
        let people = [
            Person(name: "<PERSON>", age: 30),
            Person(name: "<PERSON>", age: 25),
            Person(name: "<PERSON>", age: 35)
        ]
        let sorted = people.sorted(by: \.age)
        XCTAssertEqual(sorted.map { $0.name }, ["<PERSON>", "<PERSON>", "<PERSON>"])
    }

    func testSortedByKeyPathDescending() {
        struct Person { let name: String; let age: Int }
        let people = [
            Person(name: "<PERSON>", age: 30),
            Person(name: "<PERSON>", age: 25),
            <PERSON>(name: "<PERSON>", age: 35)
        ]
        let sorted = people.sorted(by: \.age, ascending: false)
        XCTAssertEqual(sorted.map { $0.name }, ["Charlie", "Alice", "Bob"])
    }

    func testSplitByLength() {
        let array = [1, 2, 3, 4, 5]
        XCTAssertEqual(array.split(by: 2), [[1, 2], [3, 4], [5]])
        XCTAssertEqual(array.split(by: 10), [array])
        XCTAssertEqual(array.split(by: 1), [[1], [2], [3], [4], [5]])
    }

    func testIsNotEmpty() {
        XCTAssertTrue([1].isNotEmpty)
        XCTAssertFalse([Int]().isNotEmpty)
    }

    // MARK: - Sequence where Element: Hashable

    func testUniqued() {
        let array = [1, 2, 2, 3, 1]
        XCTAssertEqual(array.uniqued(), [1, 2, 3])
    }

    func testUniquedByKeyPath() {
        struct Person: Hashable { let id: Int; let name: String }
        let people = [Person(id: 1, name: "Alice"), Person(id: 2, name: "Bob"), Person(id: 1, name: "Alice")]
        let result = people.uniqued(by: \.id)
        XCTAssertEqual(result.map { $0.id }, [1, 2])
    }

    // MARK: - Sequence

    func testRemoveDuplicatesByKeyPath() {
        struct Person: Hashable { let id: Int; let name: String }
        let people = [Person(id: 1, name: "Alice"), Person(id: 2, name: "Bob"), Person(id: 1, name: "Alice")]
        let result = people.removeDuplicates(by: \.id)
        XCTAssertEqual(result.map { $0.id }, [1, 2])
    }

    func testGroupByKeyPath() {
        struct Person { let age: Int; let name: String }
        let people = [Person(age: 30, name: "Alice"), Person(age: 25, name: "Bob"), Person(age: 30, name: "Charlie")]
        let grouped = people.group(by: \.age)
        XCTAssertEqual(grouped[30]?.map { $0.name }, ["Alice", "Charlie"])
        XCTAssertEqual(grouped[25]?.map { $0.name }, ["Bob"])
    }

    // MARK: - NSArray

    func testNSArraySplitByLength() {
        let nsArray: NSArray = [1, 2, 3, 4]
        let splitResult = nsArray.split(by: 3)
        let result = splitResult.compactMap { $0 as? [Int] }
        XCTAssertEqual(result, [[1, 2, 3], [4]])
    }
}