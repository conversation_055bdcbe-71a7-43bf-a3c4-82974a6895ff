@testable import Extensions
import XCTest
import SwiftDate

final class DateExtensionsTests: XCTestCase {
    
    func testNSDateFormatDateWithoutDST() {
        // Test the static method
        let dateString = "2024-08-01T10:30:00Z"
        let inputFormat = "yyyy-MM-dd'T'HH:mm:ss'Z'"
        let outputFormat = "yyyy-MM-dd HH:mm:ss"
        
        let formattedDate = NSDate.formatDateWithoutDST(dateString: dateString, 
                                                        inputFormat: inputFormat, 
                                                        outputFormat: outputFormat)
        
        XCTAssertNotNil(formattedDate, "Formatted date should not be nil")
        
        // Test the instance method
        let date = NSDate(timeIntervalSince1970: 1690000000) // July 22, 2023
        let instanceFormattedDate = date.formatDateWithoutDST(outputFormat: "yyyy-MM-dd")
        
        XCTAssertNotNil(instanceFormattedDate)
        // The exact string depends on the timezone, but we can check the format
        XCTAssertTrue(instanceFormattedDate.count == 10, "Should be in yyyy-MM-dd format")
        XCTAssertTrue(instanceFormattedDate.contains("-"), "Should contain hyphens")
    }
    
    func testDateUTCDate() {
        let utcDate = Date.utcDate()
        XCTAssertNotNil(utcDate, "UTC date should not be nil")
        
        // Verify it's in UTC by comparing components
        if let utcDate = utcDate {
            var calendar = Calendar.current
            calendar.timeZone = TimeZone(identifier: "UTC")!
            
            let now = Date()
            
            let utcComponents = calendar.dateComponents([.year, .month, .day], from: utcDate)
            let nowComponents = calendar.dateComponents([.year, .month, .day], from: now)
            
            XCTAssertEqual(utcComponents.year, nowComponents.year)
            XCTAssertEqual(utcComponents.month, nowComponents.month)
            XCTAssertEqual(utcComponents.day, nowComponents.day)
        }
    }
    
    func testNSStringFormatDateWithoutDST() {
        let nsString = "2024-08-01T10:30:00Z" as NSString
        let inputFormat = "yyyy-MM-dd'T'HH:mm:ss'Z'"
        let outputFormat = "yyyy-MM-dd HH:mm:ss"
        
        let formattedDate = nsString.formatDateWithoutDST(inputFormat: inputFormat, outputFormat: outputFormat)
        
        XCTAssertNotNil(formattedDate, "Formatted date should not be nil")
    }
    
    func testStringToDate() {
        let dateString = "2024-08-01 10:30:00"
        let formats = ["yyyy-MM-dd HH:mm:ss", "yyyy/MM/dd HH:mm", "dd MMM yyyy"]
        
        let dateInRegion = dateString.toDate(possibleFormats: formats)
        
        XCTAssertNotNil(dateInRegion, "Should parse the date successfully")
        
        if let dateInRegion = dateInRegion {
            XCTAssertEqual(dateInRegion.year, 2024)
            XCTAssertEqual(dateInRegion.month, 8)
            XCTAssertEqual(dateInRegion.day, 1)
            XCTAssertEqual(dateInRegion.hour, 10)
            XCTAssertEqual(dateInRegion.minute, 30)
        }
        
        // Test with incorrect format
        let invalidDateString = "not a date"
        let invalidDateInRegion = invalidDateString.toDate(possibleFormats: formats)
        
        XCTAssertNil(invalidDateInRegion, "Should return nil for invalid date string")
    }
} 