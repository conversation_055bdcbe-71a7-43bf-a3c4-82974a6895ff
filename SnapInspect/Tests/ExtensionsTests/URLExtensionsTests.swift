@testable import Extensions
import XCTest
import SDWebImage

final class URLExtensionsTests: XCTestCase {
    
    func testFixingSchemeIfNeeded() {
        // Test URL without scheme
        let urlWithoutScheme = URL(string: "example.com")!
        let fixedURL = urlWithoutScheme.fixingSchemeIfNeeded()
        XCTAssertEqual(fixedURL?.absoluteString, "https://example.com")
        
        // Test URL that already has a scheme
        let urlWithScheme = URL(string: "https://example.com")!
        let alreadyFixedURL = urlWithScheme.fixingSchemeIfNeeded()
        XCTAssertEqual(alreadyFixedURL?.absoluteString, "https://example.com")
    }
    
    func testIsLocalFileURL() {
        // Test local file URL
        let localURL = URL(string: "file:///path/to/file.txt")!
        XCTAssertTrue(localURL.isLocalFileURL)
        
        // Test remote URL
        let remoteURL = URL(string: "https://example.com")!
        XCTAssertFalse(remoteURL.isLocalFileURL)
    }
    
    func testIsRemoteURL() {
        // Test HTTP URL
        let httpURL = URL(string: "http://example.com")!
        XCTAssertTrue(httpURL.isRemoteURL)
        
        // Test HTTPS URL
        let httpsURL = URL(string: "https://example.com")!
        XCTAssertTrue(httpsURL.isRemoteURL)
        
        // Test local file URL
        let fileURL = URL(string: "file:///path/to/file.txt")!
        XCTAssertFalse(fileURL.isRemoteURL)
    }
    
    func testHasCachedImage() {
        // Test with non-cached URL
        let nonCachedURL = URL(string: "https://example.com/image.jpg")!
        XCTAssertFalse(nonCachedURL.hasCachedImage)
        
        // Test with cached URL (requires mocking SDImageCache)
        let cachedURL = URL(string: "https://example.com/cached-image.jpg")!
        
        // Store a dummy image in the cache
        let dummyData = Data([0, 1, 2, 3])
        SDImageCache.shared.storeImageData(dummyData, forKey: cachedURL.absoluteString, completion: nil)
        
        // Verify cache exists
        XCTAssertTrue(cachedURL.hasCachedImage)
        
        // Clean up
        SDImageCache.shared.removeImage(forKey: cachedURL.absoluteString)
    }
}
