//
//  UserDefaultsExtensions.swift
//  AppFeatures
//
//  Created by <PERSON> on 2024/8/1.
//

import Foundation

public extension UserDefaults {
    func setCodableValue<T: Codable>(_ value: T?, forKey key: String) {
        guard let value else {
            removeObject(forKey: key)
            return
        }
        do {
            let data = try JSONEncoder().encode(value)
            set(data, forKey: key)
        } catch {
            print("Failed to encode value for key \(key): \(error)")
        }
    }

    func codableValue<T: Codable>(forKey key: String) -> T? {
        guard let data = object(forKey: key) as? Data else { return nil }
        return try? JSONDecoder().decode(T.self, from: data)
    }
}
