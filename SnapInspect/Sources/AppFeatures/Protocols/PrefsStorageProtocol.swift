//
//  PrefsStorageProtocol.swift
//  SnapInspect
//
//  Created by <PERSON> on 5/26/25.
//

import Foundation

// MARK: - Storage Protocol

public protocol PrefsStorageProtocol {
    func getString(forKey key: String) -> String?
    func setString(_ value: String?, forKey key: String) -> Bool
    func getBool(forKey key: String) -> Bool
    func setBool(_ value: Bool, forKey key: String) -> Bool
    func getInt(forKey key: String) -> Int
    func setInt(_ value: Int, forKey key: String) -> Bool
    func getDouble(forKey key: String) -> Double
    func setDouble(_ value: Double, forKey key: String) -> Bool
    func remove(forKey key: String)
    func synchronize()
    func clearAll()

    func setValue<T: Codable>(_ value: T?, forKey key: String) -> Bool
    func getValue<T: Codable>(forKey key: String) -> T?
}
