// AppEnvironment

// MARK: - Environment

import Dependencies
import DependenciesMacros
import FMDB
import UIKit

@DependencyClient
public struct AppEnvironment {
    // MARK: - Public Properties

    public let accountService: AccountService
    public let keychainService: KeychainService

    // MARK: - Operations

    public var runSQL: (@escaping (FMDatabase) -> Void) -> Void = { _ in }
    public var openURL: @Sendable (URL, [UIApplication.OpenExternalURLOptionsKey: Any]) async -> Bool = { _, _ in false }
    public var currencyFormat: (Double) -> String = { _ in "" }
    public var decimalFormat: (Double) -> String = { _ in "" }
    public var prefsOps: (@escaping (PrefsManager) -> Void) -> Void = { _ in }
}

public extension DependencyValues {
    var env: AppEnvironment {
        get { self[AppEnvironment.self] }
        set { self[AppEnvironment.self] = newValue }
    }
}

extension AppEnvironment: TestDepend<PERSON>K<PERSON> {
    public static let previewValue = Self(
        accountService: AccountManager(rootURL: .searchDirectory(.documentDirectory)!),
        keychainService: KeychainManager(),
        runSQL: { _ in },
        openURL: { _, _ in false },
        currencyFormat: { _ in "" },
        decimalFormat: { _ in "" },
        prefsOps: { _ in }
    )
    public static let testValue = Self(
        accountService: AccountManager(rootURL: .searchDirectory(.documentDirectory)!),
        keychainService: KeychainManager(),
        runSQL: { _ in },
        openURL: { _, _ in false },
        currencyFormat: { _ in "" },
        decimalFormat: { _ in "" },
        prefsOps: { _ in }
    )
}
