//
//  KeychainManager.swift
//  SnapInspect
//
//  Created by <PERSON> on 5/23/25.
//

import Foundation
import KeychainAccess

private let kTokenServicePrefix = "com.snapinspect.token"
private let kCurrentAccountIdKey = "com.snapinspect.current_account_id"

@objc
public protocol KeychainService: AnyObject {
    func saveToken(_ token: String, forCustomerID customerID: Int)
    func getToken(forCustomerID customerID: Int) -> String?
    func removeToken(forCustomerID customerID: Int)
    func saveCurrentAccountId(_ accountId: Int)
    func getCurrentAccountId() -> NSNumber?
    func removeCurrentAccountId()
    func clearAllTokens()
}

@objc
public class KeychainManager: NSObject, KeychainService {
    private let keychain = Keychain(service: kTokenServicePrefix)
    private let currentAccountKeychain = Keychain(service: kCurrentAccountIdKey)
    
    // MARK: - Token Management
    
    public func saveToken(_ token: String, forCustomerID customerID: Int) {
        let key = "\(customerID)"
        do {
            try keychain.set(token, key: key)
        } catch {
            print("Failed to save token for customer \(customerID): \(error)")
        }
    }
    
    public func getToken(forCustomerID customerID: Int) -> String? {
        let key = "\(customerID)"
        do {
            return try keychain.get(key)
        } catch {
            print("Failed to get token for customer \(customerID): \(error)")
            return nil
        }
    }
    
    public func removeToken(forCustomerID customerID: Int) {
        let key = "\(customerID)"
        do {
            try keychain.remove(key)
        } catch {
            print("Failed to remove token for customer \(customerID): \(error)")
        }
    }
    
    // MARK: - Current Account Management
    
    public func saveCurrentAccountId(_ accountId: Int) {
        do {
            try currentAccountKeychain.set("\(accountId)", key: "current_id")
        } catch {
            print("Failed to save current account ID \(accountId): \(error)")
        }
    }
    
    public func getCurrentAccountId() -> NSNumber? {
        do {
            guard let idString = try currentAccountKeychain.get("current_id") else {
                return nil
            }
            if let intValue = Int(idString) {
                return NSNumber(value: intValue)
            }
            return nil
        } catch {
            print("Failed to get current account ID: \(error)")
            return nil
        }
    }
    
    public func removeCurrentAccountId() {
        do {
            try currentAccountKeychain.remove("current_id")
        } catch {
            print("Failed to remove current account ID: \(error)")
        }
    }
    
    // MARK: - Cleanup
    
    public func clearAllTokens() {
        do {
            try keychain.removeAll()
            try currentAccountKeychain.removeAll()
        } catch {
            print("Failed to clear all keychain data: \(error)")
        }
    }
} 
