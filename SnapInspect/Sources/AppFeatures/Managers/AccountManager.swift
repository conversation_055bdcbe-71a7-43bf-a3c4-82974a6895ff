//
//  AccountManager.swift
//  SnapInspect
//
//  Created by <PERSON> on 5/23/25.
//

import Dependencies
import Foundation
import KeychainAccess

private let kAllLoginAccountsKey = "all_login_accounts"
private let kCurrentAccountId = "current_account_id"
// Don't change this alias, it's used to encrypt the user's token
private let kEnctryptAlias = "k9mX7pQ2vN8rL4wE6tY3"

@objc
public class LoginAccount: NSObject, Codable {
    public enum UserRole: Int, Codable {
        case main = 0
        case sub = 1
    }

    public let userRole: UserRole
    @objc public let iCustomerID: Int
    @objc public let iCompanyID: Int
    @objc public let sName: String
    @objc public let sEmail: String
    @objc public let sFirstName: String
    @objc public let sLastName: String

    public init(
        userRole: UserRole, iCustomerID: Int, iCompanyID: Int, sName: String,
        sEmail: String, sFirstName: String, sLastName: String
    ) {
        self.userRole = userRole
        self.iCustomerID = iCustomerID
        self.iCompanyID = iCompanyID
        self.sName = sName
        self.sEmail = sEmail
        self.sFirstName = sFirstName
        self.sLastName = sLastName
        super.init()
    }
}

// MARK: - Login User Factory

public extension LoginAccount {
    @objc
    static func createMainAccount(
        iCustomerID: Int, iCompanyID: Int, sName: String, sEmail: String,
        sFirstName: String, sLastName: String
    ) -> LoginAccount {
        let account = LoginAccount(
            userRole: .main, iCustomerID: iCustomerID, iCompanyID: iCompanyID,
            sName: sName, sEmail: sEmail, sFirstName: sFirstName, sLastName: sLastName
        )
        return account
    }
    
    @objc
    var isMainAccount: Bool {
        userRole == .main
    }

    @objc
    var isSubAccount: Bool {
        userRole == .sub
    }
}

// MARK: - Account Manager

@objc
public protocol AccountService: AnyObject {
    func setMainAccountWhenNoAccountExists(_ mainAccount: LoginAccount, sToken: String)
    func initCurrentAccount()
    func processMainAccount(_ dictionary: [String: Any])
    func processSubAccountsJson(_ jsonString: String)
    func removeSubAccounts()
    func removeAllAccounts()

    // Current account is the account that is currently logged in,
    // Might be main account or sub account
    var currentAccount: LoginAccount? { get }
    func setCurrentAccount(id: Int)

    // Available accounts that's all available accounts except the current account
    var availableAccounts: [LoginAccount] { get }
}

@objc
public class AccountManager: NSObject, AccountService {
    @Dependency(\.env.prefsOps) private var prefsOps
    @Dependency(\.env.keychainService) private var keychainService
    @objc public var currentAccount: LoginAccount?
    
    // Updates the current account with the given id, using the main account as a fallback
    @objc public func setCurrentAccount(id: Int) {
        currentAccount = allAccounts.first(where: { $0.iCustomerID == id })
        
        // Save the current account ID to the keychain
        keychainService.saveCurrentAccountId(id)
    }

    // Available accounts that's all available accounts except the current account
    @objc public var availableAccounts: [LoginAccount] {
        allAccounts.filter { $0.iCustomerID != currentAccount?.iCustomerID }
    }

    @objc
    public func setMainAccountWhenNoAccountExists(_ mainAccount: LoginAccount, sToken: String) {
        guard allAccounts.isEmpty else {
            print("Main account already exists, no need to update.")
            return
        }

        var existingAccounts = allAccounts
        // Remove the main account from the existing accounts
        existingAccounts.removeAll(where: \.isMainAccount)

        prefsOps { prefs in
            existingAccounts.append(mainAccount)
            try? prefs.setValue(existingAccounts, forKey: kAllLoginAccountsKey)
        }

        // Save token to keychain
        keychainService.saveToken(sToken, forCustomerID: mainAccount.iCustomerID)

        setCurrentAccount(id: mainAccount.iCustomerID)
    }
    
    @objc public func initCurrentAccount() {
        guard let currentAccountID = keychainService.getCurrentAccountId()?.intValue else {
            return
        }
        setCurrentAccount(id: currentAccountID)
    }

    @objc public func processMainAccount(_ dictionary: [String: Any]) {
        let iCustomerID = dictionary[PrefsKeys.iCustomerID] as? Int ?? 0
        let sToken = dictionary[PrefsKeys.sToken] as? String ?? ""
        
        var existingAccounts = allAccounts
        // Remove existing main account tokens from keychain
        existingAccounts
            .filter(\.isMainAccount)
            .map(\.iCustomerID)
            .forEach(keychainService.removeToken(forCustomerID:))

        // Remove the main account from the existing accounts
        existingAccounts.removeAll(where: \.isMainAccount)

        // Save token to keychain
        keychainService.saveToken(sToken, forCustomerID: iCustomerID)
        
        prefsOps { prefs in
            existingAccounts.append(
                LoginAccount(
                    userRole: .main,
                    iCustomerID: iCustomerID,
                    iCompanyID: dictionary[PrefsKeys.iCompanyID] as? Int ?? 0,
                    sName: dictionary[PrefsKeys.sName] as? String ?? "",
                    sEmail: dictionary[PrefsKeys.sEmail] as? String ?? "",
                    sFirstName: dictionary[PrefsKeys.sFirstName] as? String ?? "",
                    sLastName: dictionary[PrefsKeys.sLastName] as? String ?? ""
                )
            )
            try? prefs.setValue(existingAccounts, forKey: kAllLoginAccountsKey)
        }
    }

    @objc public func processSubAccountsJson(_ sLogins: String) {
        // Only main account can process sub account json
        guard let currentAccount, currentAccount.isMainAccount else {
            return
        }
        
        guard let loginsArray = sLogins.jsonValue as? [[String: Any]] else {
            print("Failed to parse sLogins as array JSON")
            return
        }
        
        var existingAccounts = allAccounts
        // Remove existing sub account tokens from keychain
        existingAccounts
            .filter(\.isSubAccount)
            .map(\.iCustomerID)
            .forEach(keychainService.removeToken(forCustomerID:))

        // Remove the sub accounts from the existing accounts
        existingAccounts.removeAll(where: \.isSubAccount)
        
        prefsOps { prefs in
            // Process each sub-account in the array
            for dict in loginsArray {
                let iCustomerID = dict[PrefsKeys.iCustomerID] as? Int ?? 0
                let sToken = dict[PrefsKeys.sToken] as? String ?? ""
                
                // Save token to keychain
                self.keychainService.saveToken(sToken, forCustomerID: iCustomerID)
                
                let subAccount = LoginAccount(
                    userRole: .sub,
                    iCustomerID: iCustomerID,
                    iCompanyID: dict[PrefsKeys.iCompanyID] as? Int ?? 0,
                    sName: dict[PrefsKeys.sName] as? String ?? "",
                    sEmail: dict[PrefsKeys.sEmail] as? String ?? "",
                    sFirstName: dict[PrefsKeys.sFirstName] as? String ?? "",
                    sLastName: dict[PrefsKeys.sLastName] as? String ?? ""
                )
                existingAccounts.append(subAccount)
            }
            
            try? prefs.setValue(existingAccounts, forKey: kAllLoginAccountsKey)
        }
    }

    @objc public func removeSubAccounts() {
        // Remove tokens for sub accounts from keychain
        let subAccounts = allAccounts.filter { $0.isSubAccount }
        for account in subAccounts {
            self.keychainService.removeToken(forCustomerID: account.iCustomerID)
        }
        
        removeAllAccounts(keepMainAccount: true)
    }

    @objc public func removeAllAccounts() {
        // Remove all tokens from keychain
        self.keychainService.clearAllTokens()
        
        removeAllAccounts(keepMainAccount: false)
    }
}

// MARK: - Private Extensions

private extension AccountManager {
    @objc private func removeAllAccounts(keepMainAccount: Bool) {
        prefsOps { prefs in
            if keepMainAccount {
                let accounts = self.allAccounts.filter { $0.userRole == .main }
                try? prefs.setValue(accounts, forKey: kAllLoginAccountsKey)
            } else {
                try? prefs.setValue([LoginAccount](), forKey: kAllLoginAccountsKey)
            }
        }
    }

    private var allAccounts: [LoginAccount] {
        var accounts: [LoginAccount] = []
        prefsOps { prefs in
            let results: [LoginAccount]? = try? prefs.getValue(forKey: kAllLoginAccountsKey)
            accounts = results ?? []
        }
        return accounts
    }
}

// MARK: - Account Errors

public enum AccountError: Error, LocalizedError {
    case directoryCreationFailed
    case databaseSetupFailed
    case defaultDatabaseNotFound
    case preferenceSaveFailed
    case preferencesLoadFailed
    case invalidUser

    public var errorDescription: String? {
        switch self {
        case .directoryCreationFailed:
            return "Failed to create account directory"
        case .databaseSetupFailed:
            return "Failed to setup account database"
        case .defaultDatabaseNotFound:
            return "Default database not found in bundle"
        case .preferenceSaveFailed:
            return "Failed to save account preferences"
        case .preferencesLoadFailed:
            return "Failed to load account preferences"
        case .invalidUser:
            return "Invalid user account"
        }
    }
}
