//
//  AccountManager.swift
//  SnapInspect
//
//  Created by <PERSON> on 5/23/25.
//

import Dependencies
import Extensions
import Foundation
import KeychainAccess

private let kAllLoginAccountsKey = "login_accounts"

@objc
public class LoginAccount: NSObject, Codable {
    public enum UserRole: Int, Codable {
        case main = 0
        case sub = 1
    }

    public let userRole: UserRole
    @objc public let iCustomerID: Int
    @objc public let iCompanyID: Int
    @objc public let sName: String
    @objc public let sEmail: String
    @objc public let sFirstName: String
    @objc public let sLastName: String

    public init(
        userRole: UserRole, iCustomerID: Int, iCompanyID: Int, sName: String,
        sEmail: String, sFirstName: String, sLastName: String
    ) {
        self.userRole = userRole
        self.iCustomerID = iCustomerID
        self.iCompanyID = iCompanyID
        self.sName = sName
        self.sEmail = sEmail
        self.sFirstName = sFirstName
        self.sLastName = sLastName
        super.init()
    }
}

// MARK: - Login User Factory

public extension LoginAccount {
    @objc
    static func createMainAccount(
        iCustomerID: Int, iCompanyID: Int, sName: String, sEmail: String,
        sFirstName: String, sLastName: String
    ) -> LoginAccount {
        let account = LoginAccount(
            userRole: .main, iCustomerID: iCustomerID, iCompanyID: iCompanyID,
            sName: sName, sEmail: sEmail, sFirstName: sFirstName, sLastName: sLastName
        )
        return account
    }

    @objc
    var isMainAccount: Bool {
        userRole == .main
    }

    @objc
    var isSubAccount: Bool {
        userRole == .sub
    }
}

// MARK: - Account Manager

@objc
public protocol AccountService: AnyObject {
    func setMainAccountWhenNoAccountExists(_ mainAccount: LoginAccount, sToken: String)
    func initCurrentAccount()
    func processMainAccount(_ dictionary: [String: Any])
    func processSubAccountsJson(_ jsonString: String)
    func removeSubAccounts()
    func removeAllAccounts()

    // Current account is the account that is currently logged in,
    // Might be main account or sub account
    var currentAccount: LoginAccount? { get }
    func setCurrentAccount(id: Int)

    // Available accounts that's all available accounts except the current account
    var availableAccounts: [LoginAccount] { get }
}

@objc
public class AccountManager: NSObject, AccountService {
    @Dependency(\.env.prefsOps) private var prefsOps
    @Dependency(\.env.keychainService) private var keychainService

    @objc public var currentAccount: LoginAccount?

    private var accountsPlistPrefs: PrefsManager

    public init(rootURL: URL?) {
        let plistDirectory = rootURL ?? .searchDirectory(.documentDirectory)!
        accountsPlistPrefs = .init(
            storage: PlistStorage(fileName: "accounts", inDirectory: plistDirectory)
        )
    }

    // Updates the current account with the given id, using the main account as a fallback
    @objc public func setCurrentAccount(id: Int) {
        currentAccount = allAccounts.first(where: { $0.iCustomerID == id })

        // Save the current account ID to the keychain
        keychainService.saveCurrentAccountId(id)

        // Save the current account to the user defaults
        syncCurrentAccountToPrefs()
    }

    // Available accounts that's all available accounts except the current account
    @objc public var availableAccounts: [LoginAccount] {
        allAccounts.filter { $0.iCustomerID != currentAccount?.iCustomerID }
    }

    @objc
    public func setMainAccountWhenNoAccountExists(_ mainAccount: LoginAccount, sToken: String) {
        guard allAccounts.isEmpty else {
            print("Main account already exists, no need to update.")
            return
        }

        var existingAccounts = allAccounts
        // Remove the main account from the existing accounts
        existingAccounts.removeAll(where: \.isMainAccount)

        existingAccounts.append(mainAccount)
        try? accountsPlistPrefs.setValue(existingAccounts, forKey: kAllLoginAccountsKey)

        // Save token to keychain
        keychainService.saveToken(sToken, forCustomerID: mainAccount.iCustomerID)

        setCurrentAccount(id: mainAccount.iCustomerID)
    }

    @objc public func initCurrentAccount() {
        let currentAccountID = keychainService.getCurrentAccountId()
        guard currentAccountID > 0 else {
            return
        }
        setCurrentAccount(id: currentAccountID)
    }

    @objc public func processMainAccount(_ dictionary: [String: Any]) {
        let iCustomerID = dictionary[PrefsKeys.iCustomerID] as? Int ?? 0
        let sToken = dictionary[PrefsKeys.sToken] as? String ?? ""

        var existingAccounts = allAccounts
        // Remove existing main account tokens from keychain
        existingAccounts
            .filter(\.isMainAccount)
            .map(\.iCustomerID)
            .forEach(keychainService.removeToken(forCustomerID:))

        // Remove the main account from the existing accounts
        existingAccounts.removeAll(where: \.isMainAccount)

        // Save token to keychain
        keychainService.saveToken(sToken, forCustomerID: iCustomerID)

        // save accounts to plist
        existingAccounts.append(
            LoginAccount(
                userRole: .main,
                iCustomerID: iCustomerID,
                iCompanyID: dictionary[PrefsKeys.iCompanyID] as? Int ?? 0,
                sName: dictionary[PrefsKeys.sName] as? String ?? "",
                sEmail: dictionary[PrefsKeys.sEmail] as? String ?? "",
                sFirstName: dictionary[PrefsKeys.sFirstName] as? String ?? "",
                sLastName: dictionary[PrefsKeys.sLastName] as? String ?? ""
            )
        )
        try? accountsPlistPrefs.setValue(existingAccounts, forKey: kAllLoginAccountsKey)
    }

    @objc public func processSubAccountsJson(_ sLogins: String) {
        // Only main account can process sub account json
        guard let currentAccount, currentAccount.isMainAccount else {
            return
        }

        guard let loginsArray = sLogins.jsonValue as? [[String: Any]] else {
            print("Failed to parse sLogins as array JSON")
            return
        }

        var existingAccounts = allAccounts
        // Remove existing sub account tokens from keychain
        existingAccounts
            .filter(\.isSubAccount)
            .map(\.iCustomerID)
            .forEach(keychainService.removeToken(forCustomerID:))

        // Remove the sub accounts from the existing accounts
        existingAccounts.removeAll(where: \.isSubAccount)

        // Process each sub-account in the array
        for dict in loginsArray {
            let iCustomerID = dict[PrefsKeys.iCustomerID] as? Int ?? 0
            let sToken = dict[PrefsKeys.sToken] as? String ?? ""

            // Save token to keychain
            keychainService.saveToken(sToken, forCustomerID: iCustomerID)

            let fullName = dict[PrefsKeys.sName] as? String ?? ""
            let nameComponents = fullName.components(separatedBy: .whitespaces).filter { !$0.isEmpty }
            
            let subAccount = LoginAccount(
                userRole: .sub,
                iCustomerID: iCustomerID,
                iCompanyID: dict[PrefsKeys.iCompanyID] as? Int ?? 0,
                sName: fullName,
                sEmail: dict[PrefsKeys.sEmail] as? String ?? "",
                sFirstName: {
                    if let firstName = dict[PrefsKeys.sFirstName] as? String, !firstName.isEmpty {
                        return firstName
                    }
                    return nameComponents.first ?? ""
                }(),
                sLastName: {
                    if let lastName = dict[PrefsKeys.sLastName] as? String, !lastName.isEmpty {
                        return lastName
                    }
                    return nameComponents.count > 1 ? nameComponents.last ?? "" : ""
                }()
            )
            existingAccounts.append(subAccount)
        }

        try? accountsPlistPrefs.setValue(existingAccounts, forKey: kAllLoginAccountsKey)
    }

    @objc public func removeSubAccounts() {
        // Remove tokens for sub accounts from keychain
        let subAccounts = allAccounts.filter { $0.isSubAccount }
        for account in subAccounts {
            keychainService.removeToken(forCustomerID: account.iCustomerID)
        }

        removeAllAccounts(keepMainAccount: true)
    }

    @objc public func removeAllAccounts() {
        // Remove all tokens from keychain
        keychainService.clearAllTokens()

        removeAllAccounts(keepMainAccount: false)
    }
}

// MARK: - Private Extensions

private extension AccountManager {
    private func removeAllAccounts(keepMainAccount: Bool) {
        let accountsToKeep: [LoginAccount] = keepMainAccount 
            ? allAccounts.filter { $0.userRole == .main }
            : []

        try? accountsPlistPrefs.setValue(accountsToKeep, forKey: kAllLoginAccountsKey)
    }

    private var allAccounts: [LoginAccount] {
        do {
            return try accountsPlistPrefs.getValue(forKey: kAllLoginAccountsKey) ?? []
        } catch {
            print("Failed to load accounts from plist: \(error)")
            return []
        }
    }

    private func syncCurrentAccountToPrefs() {
        guard let currentAccount else { return }

        prefsOps { prefs in
            prefs.ifSavePref(PrefsKeys.iCustomerID, sValue: "\(currentAccount.iCustomerID)")
            prefs.ifSavePref(PrefsKeys.iCompanyID, sValue: "\(currentAccount.iCompanyID)")
            prefs.ifSavePref(PrefsKeys.sName, sValue: currentAccount.sName)
            prefs.ifSavePref(PrefsKeys.sEmail, sValue: currentAccount.sEmail)
            prefs.ifSavePref(PrefsKeys.sFirstName, sValue: currentAccount.sFirstName)
            prefs.ifSavePref(PrefsKeys.sLastName, sValue: currentAccount.sLastName)
        }
    }
}

// MARK: - Account Errors

public enum AccountError: Error, LocalizedError {
    case directoryCreationFailed
    case databaseSetupFailed
    case defaultDatabaseNotFound
    case preferenceSaveFailed
    case preferencesLoadFailed
    case invalidUser

    public var errorDescription: String? {
        switch self {
        case .directoryCreationFailed:
            return "Failed to create account directory"
        case .databaseSetupFailed:
            return "Failed to setup account database"
        case .defaultDatabaseNotFound:
            return "Default database not found in bundle"
        case .preferenceSaveFailed:
            return "Failed to save account preferences"
        case .preferencesLoadFailed:
            return "Failed to load account preferences"
        case .invalidUser:
            return "Invalid user account"
        }
    }
}
