//
//  UserDefaultsStorage.swift
//  SnapInspect
//
//  Created by <PERSON> on 5/26/25.
//

import Foundation

// MARK: - UserDefaults Storage Implementation

public class UserDefaultsStorage: PrefsStorageProtocol {
    private let userDefaults: UserDefaults
    
    public init(userDefaults: UserDefaults = .standard) {
        self.userDefaults = userDefaults
    }
    
    public func getString(forKey key: String) -> String? {
        return userDefaults.string(forKey: key)
    }
    
    public func setString(_ value: String?, forKey key: String) -> Bool {
        userDefaults.set(value, forKey: key)
        return true
    }
    
    public func getBool(forKey key: String) -> Bool {
        return userDefaults.bool(forKey: key)
    }
    
    public func setBool(_ value: Bool, forKey key: String) -> Bool {
        userDefaults.set(value, forKey: key)
        return true
    }
    
    public func getInt(forKey key: String) -> Int {
        return userDefaults.integer(forKey: key)
    }
    
    public func setInt(_ value: Int, forKey key: String) -> Bool {
        userDefaults.set(value, forKey: key)
        return true
    }
    
    public func getDouble(forKey key: String) -> Double {
        return userDefaults.double(forKey: key)
    }
    
    public func setDouble(_ value: Double, forKey key: String) -> Bool {
        userDefaults.set(value, forKey: key)
        return true
    }
    
    public func remove(forKey key: String) {
        userDefaults.removeObject(forKey: key)
    }
    
    public func synchronize() {
        userDefaults.synchronize()
    }
    
    public func clearAll() {
        userDefaults.dictionaryRepresentation()
            .keys.forEach(remove(forKey:))
        synchronize()
    }

    public func setValue<T: Codable>(_ value: T?, forKey key: String) -> Bool {
        userDefaults.setCodableValue(value, forKey: key)
        return true
    }

    public func getValue<T: Codable>(forKey key: String) -> T? {
        return userDefaults.codableValue(forKey: key)
    }
}
