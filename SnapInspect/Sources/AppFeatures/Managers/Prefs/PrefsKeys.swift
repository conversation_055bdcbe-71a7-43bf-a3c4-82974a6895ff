//
//  PrefsKeys.swift
//  SnapInspect3
//
//  Created by <PERSON> on 2025/05/24.
//

import Foundation

/**
 * Centralized preference keys for the entire application.
 * This ensures consistency and helps avoid key conflicts.
 */
@objc
public class PrefsKeys: NSObject {
    
    // MARK: - Authentication & User Account
    
    /// Current customer ID
    @objc public static let iCustomerID = "iCustomerID"
    
    /// Authentication token
    @objc public static let sToken = "sToken"
    
    /// User email address
    @objc public static let sEmail = "sEmail"
    
    /// User name
    @objc public static let sName = "sName"
    
    /// User password (encrypted)
    @objc public static let sPassword = "sPassword"
    
    /// User first name
    @objc public static let sFirstName = "sFirstName"
    
    /// User last name
    @objc public static let sLastName = "sLastName"
    
    /// Company ID
    @objc public static let iCompanyID = "iCompanyID"
    
    /// File customer ID
    @objc public static let iFileCustomerID = "iFileCustomerID"
    
    /// Company name
    @objc public static let sCompanyName = "sCompanyName"
    
    /// Login as another user flag
    @objc public static let bLoginAs = "bLoginAs"
    
    /// User role
    @objc public static let sRole = "sRole"
    
    /// Custom role configuration
    @objc public static let sCustomRole = "sCustomRole"
    
    /// Intercom hash for authentication
    @objc public static let sIntercomHash = "sIntercomHash"
    
    // MARK: - Sync & Data Management
    
    /// Main sync date
    @objc public static let sSyncDate = "sSyncDate"
    
    /// Schedule sync date
    @objc public static let sSCSync = "SC_Sync"
    
    /// Sync flag
    @objc public static let bSync = "bSync"
    
    /// Project sync date (from Constants)
    @objc public static let sProjectSyncDate = "sProjectSyncDate"
    
    /// Force sync projects flag (from Constants)
    @objc public static let kForceSyncProjects = "kForceSyncProjects"
    
    /// Products sync date (from Constants)
    @objc public static let kSyncProductsDate = "kSyncProductsDate"
    
    /// Property layouts sync date (from Constants)
    @objc public static let kSyncPropertyLayoutsDate = "kSyncPropertyLayoutsDate"
    
    /// My tasks sync date (from Constants)
    @objc public static let kSyncMyTasksDate = "kSyncMyTasksDate"
    
    // MARK: - App Settings & Preferences
    
    /// App version for upgrade tracking
    @objc public static let sCurrentAppVer = "sCurrentAppVer"
    
    /// Tutorial completion flag
    @objc public static let bTutorial = "bTutorial"
    
    /// Auto login preference (from if_SettingOptionType)
    @objc public static let bSavePassword = "bSavePassword"
    
    /// Save photos to camera roll (from if_SettingOptionType)
    @objc public static let bSaveCamera = "bSaveCamera"
    
    /// Display instruction preference (from if_SettingOptionType)
    @objc public static let bDisplayInstruction = "bDisplayInstruction"
    
    /// Enable Word feature (from if_SettingOptionType)
    @objc public static let bEnableWord = "bEnableWord"
    
    /// Photo stamp preference (from if_SettingOptionType)
    @objc public static let bPhotoStamp = "bPhotoStamp"
    
    /// Photo geo tag preference (from if_SettingOptionType)
    @objc public static let bPhotoGeoTag = "bPhotoGeoTag"
    
    /// Use local email client (from if_SettingOptionType)
    @objc public static let bLocalEmailClient = "bLocalEmailClient"
    
    /// Display inspection timer (from if_SettingOptionType)
    @objc public static let bDisplayInspectionTimer = "bDisplayInspectionTimer"
    
    /// New inspection UI preference (from if_SettingOptionType)
    @objc public static let bNewInspectionUI = "bNewInspectionUI"
    
    /// Camera zoom level (from Constants)
    @objc public static let kCameraZoomLevel = "kCameraZoomLevel"
    
    /// Update photo comment flag (from Constants)
    @objc public static let kUpdatePhotoComment = "bUpdateComment"
    
    // MARK: - Video Recording
    
    /// Video duration preference
    @objc public static let kVideoDuration = "VideoDuration"
    
    /// Set time label preference
    @objc public static let kSetTimeLab = "SetTimeLab"
    
    /// Timer value preference
    @objc public static let kTimerValue = "TimerValue"
    
    // MARK: - Feature Flags & Configurations
    
    /// Kiosk mode flag
    @objc public static let bKiosk = "bKiosk"
    
    /// Tenant tool mode flag
    @objc public static let bTToolMode = "bTToolMode"
    
    /// Request inspection mode flag
    @objc public static let bRInsMode = "bRInsMode"
    
    /// Event enabled flag
    @objc public static let bEventEnabled = "bEventEnabled"
    
    /// Multi-family feature flag
    @objc public static let bMultiFamily = "bMultiFamily"
    
    /// Room feature flag
    @objc public static let bRoom = "bRoom"
    
    /// Company review flag
    @objc public static let bCompanyReview = "bCompanyReview"
    
    /// Company group flag
    @objc public static let bCompanyGroup = "bCompanyGroup"
    
    /// Notification preference
    @objc public static let bNotification = "bNotification"
    
    /// Logging flag
    @objc public static let bLog = "bLog"
    
    // MARK: - Business Logic & Configuration
    
    /// Plan code
    @objc public static let sPlanCode = "sPlanCode"
    
    /// Plan name
    @objc public static let sPlanName = "sPlanName"
    
    /// Plan details
    @objc public static let sPlanDetails = "sPlanDetails"
    
    /// Country ID
    @objc public static let iCountryID = "iCountryID"
    
    /// Industry
    @objc public static let sIndustry = "sIndustry"
    
    /// Industry ID
    @objc public static let iIndustryID = "iIndustryID"
    
    /// File email
    @objc public static let sFileEmail = "sFileEmail"
    
    /// Quick phrase version ID
    @objc public static let iQPVerID = "iQPVerID"
    
    /// JSON status
    @objc public static let jsonStatus = "jsonStatus"
    
    // MARK: - Asset & Layout Management
    
    /// Selected asset view ID (from Constants)
    @objc public static let kSelectedAssetViewID = "kSelectedAssetViewID"
    
    /// Asset attributes (from Constants)
    @objc public static let kAssetAttributes = "_AssetAttributes"
    
    /// Asset attributes 2 (from Constants)
    @objc public static let kAssetAttributes2 = "_Attributes2"
    
    /// Contact type (from Constants)
    @objc public static let kContactType = "sContactType"
    
    /// Property layout area only (from Constants)
    @objc public static let kProLayoutAreaOnly = "_ProLayout_AreaOnly"
    
    /// Bypass compulsory flag (from Constants)
    @objc public static let kByPassCompulsory = "_bByPass_Compulsory"
    
    /// Product auto load item (from Constants)
    @objc public static let kProductAutoLoadItem = "bProduct_AutoLoadItem"
    
    /// Product feature enabled (from Constants)
    @objc public static let kEnableProduct = "_bCosting"
    
    /// Project sync date (from Constants)
    @objc public static let sKeyProjectSyncDate = "sKeyProjectSyncDate"
    
    // MARK: - Permissions & Access Control
    
    /// Enable project permission (from Constants)
    @objc public static let kEnableProject = "enableProject"
    
    /// Disable edit asset permission (from Constants)
    @objc public static let kDisableEditAsset = "disableEditAsset"
    
    /// Enable quick edit hide permission (from Constants)
    @objc public static let kEnableQuickEditHide = "enableQuickEditHide"
    
    /// Disable new inspection permission (from Constants)
    @objc public static let kDisableNewInspection = "disableNewInspection"
    
    /// Use asset layout V2 flag (from Constants)
    @objc public static let kUseAssetLayoutV2 = "useAssetLayoutV2"
    
    /// Hidden inspection type IDs (from Constants)
    @objc public static let kHideInsTypeID = "hideInsTypeID"
    
    /// Folder permission (from Constants)
    @objc public static let kFolderPermission = "kFolderPermission"
    
    /// Folder configuration (from Constants)
    @objc public static let kFolder = "kFolder"
    
    /// Floor plan configuration (from Constants)
    @objc public static let kFloorPlan = "kFloorPlan"
    
    /// Enable tasks feature (from Constants)
    @objc public static let kEnableTasks = "enableTasks"
    
    
    // MARK: - Validation Methods
    
    /**
     * Get all critical preference keys that should exist for proper app functionality.
     * @return Array of critical preference keys
     */
    @objc
    public static func getCriticalKeys() -> [String] {
        return [
            iCustomerID,
            sToken,
            sEmail,
            iCompanyID,
            sSyncDate
        ]
    }
    
    /**
     * Get all authentication-related preference keys.
     * @return Array of authentication preference keys
     */
    @objc
    public static func getAuthenticationKeys() -> [String] {
        return [
            iCustomerID,
            sToken,
            sEmail,
            sPassword,
            sFirstName,
            sLastName,
            iCompanyID,
            sCompanyName,
            sRole,
            sCustomRole
        ]
    }
    
    /**
     * Get all sync-related preference keys.
     * @return Array of sync preference keys
     */
    @objc
    public static func getSyncKeys() -> [String] {
        return [
            sSyncDate,
            sSCSync,
            bSync,
            sProjectSyncDate,
            kSyncProductsDate,
            kSyncPropertyLayoutsDate,
            kSyncMyTasksDate
        ]
    }
    
    /**
     * Get all user setting preference keys.
     * @return Array of user setting preference keys
     */
    @objc
    public static func getUserSettingKeys() -> [String] {
        return [
            bSavePassword,
            bSaveCamera,
            bDisplayInstruction,
            bEnableWord,
            bPhotoStamp,
            bPhotoGeoTag,
            bLocalEmailClient,
            bDisplayInspectionTimer,
            bNewInspectionUI,
            kCameraZoomLevel
        ]
    }
    
    /**
     * Get all feature flag preference keys.
     * @return Array of feature flag preference keys
     */
    @objc
    public static func getFeatureFlagKeys() -> [String] {
        return [
            bKiosk,
            bTToolMode,
            bRInsMode,
            bEventEnabled,
            bMultiFamily,
            bRoom,
            kEnableProject,
            kEnableTasks,
            kUseAssetLayoutV2,
            kEnableProduct
        ]
    }
    
    /**
     * Check if a key is account-specific (should be isolated per account).
     * @param key The preference key to check
     * @return YES if the key should be account-specific
     */
    @objc
    public static func isAccountSpecific(_ key: String) -> Bool {
        // Keys that should NOT be account-specific (global app settings)
        let globalKeys = Set([
            bSavePassword,
            bSaveCamera,
            bDisplayInstruction,
            bEnableWord,
            bPhotoStamp,
            bPhotoGeoTag,
            bLocalEmailClient,
            bDisplayInspectionTimer,
            bNewInspectionUI,
            kCameraZoomLevel,
            sCurrentAppVer,
            bTutorial
        ])
        
        return !globalKeys.contains(key)
    }
} 
