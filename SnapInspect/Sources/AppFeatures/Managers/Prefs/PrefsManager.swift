//
//  PrefsManager.swift
//  SnapInspect3
//
//  Created by <PERSON> on 2025/05/24.
//

import Extensions
import Foundation

// MARK: - Error Types

public enum PrefsError: Error {
    case keyNotFound
    case invalidValueType
    case storageError(String)
    case accountNotFound
}

// MARK: - PrefsManager

@objc
public class PrefsManager: NSObject {
    private var storage: PrefsStorageProtocol
    private let queue = DispatchQueue(label: "com.snapinspect.prefsmanager", qos: .utility)
    
    public init(storage: PrefsStorageProtocol) {
        self.storage = storage
        super.init()
    }
    
    // MARK: - Type-Safe API
    
    public func getString(forKey key: String) throws -> String? {
        return storage.getString(forKey: key)
    }
    
    public func setString(_ value: String?, forKey key: String) throws {
        guard storage.setString(value, forKey: key) else {
            throw PrefsError.storageError("Failed to save string value for key: \(key)")
        }
        storage.synchronize()
    }
    
    public func getBool(forKey key: String) throws -> Bool {
        if let stringValue = storage.getString(forKey: key) {
            return stringValue.boolValue
        }
        return storage.getBool(forKey: key)
    }
    
    public func setBool(_ value: Bool, forKey key: String) throws {
        // Store as string for backward compatibility with existing boolean preferences
        let stringValue = value ? "1" : "0"
        guard storage.setString(stringValue, forKey: key) else {
            throw PrefsError.storageError("Failed to save bool value for key: \(key)")
        }
        storage.synchronize()
    }
    
    public func getInt(forKey key: String) throws -> Int {
        if let stringValue = storage.getString(forKey: key) {
            return Int(stringValue) ?? 0
        }
        return storage.getInt(forKey: key)
    }
    
    public func setInt(_ value: Int, forKey key: String) throws {
        // Store as string for backward compatibility
        let stringValue = String(value)
        guard storage.setString(stringValue, forKey: key) else {
            throw PrefsError.storageError("Failed to save int value for key: \(key)")
        }
        storage.synchronize()
    }
    
    public func getDouble(forKey key: String) throws -> Double {
        if let stringValue = storage.getString(forKey: key) {
            return Double(stringValue) ?? 0.0
        }
        return storage.getDouble(forKey: key)
    }
    
    public func setDouble(_ value: Double, forKey key: String) throws {
        // Store as string for backward compatibility
        let stringValue = String(value)
        guard storage.setString(stringValue, forKey: key) else {
            throw PrefsError.storageError("Failed to save double value for key: \(key)")
        }
        storage.synchronize()
    }
    
    public func setValue<T: Codable>(_ value: T, forKey key: String) throws {
        guard storage.setValue(value, forKey: key) else {
            throw PrefsError.storageError("Failed to save value for key: \(key)")
        }
        storage.synchronize()
    }
    
    public func getValue<T: Codable>(forKey key: String) throws -> T? {
        return storage.getValue(forKey: key)
    }
    
    public func remove(forKey key: String) throws {
        storage.remove(forKey: key)
        storage.synchronize()
    }
    
    @objc
    public func clearAllPreferences() {
        queue.sync {
            storage.clearAll()
        }
    }
}

// MARK: - Backward Compatibility Methods (Objective-C Interface)

public extension PrefsManager {
    /// used to mirror CommonHelper.IFGetPref
    @objc
    func ifGetPref(_ key: String) -> String? {
        return queue.sync {
            do {
                return try getString(forKey: key)
            } catch {
                return nil
            }
        }
    }
    
    /// used to mirror CommonHelper.IFSavePref
    @objc
    @discardableResult
    func ifSavePref(_ key: String, sValue value: String?) -> Bool {
        return queue.sync {
            do {
                try setString(value, forKey: key)
                return true
            } catch {
                return false
            }
        }
    }
    
    /// used to mirror CommonHelper.IFGetPref_Bool
    @objc
    func ifGetPrefBool(_ key: String) -> Bool {
        return queue.sync {
            do {
                return try getBool(forKey: key)
            } catch {
                return false
            }
        }
    }
    
    /// used to mirror CommonHelper.IFGetPref_Bool with default value
    @objc
    func ifGetPrefBool(_ key: String, defaultValue: Bool) -> Bool {
        return queue.sync {
            do {
                let value = try getString(forKey: key)
                return value?.boolValue ?? defaultValue
            } catch {
                return defaultValue
            }
        }
    }
    
    /// used to mirror CommonHelper.IFRemovePref
    @objc
    func ifRemovePref(_ key: String) {
        queue.sync {
            try? remove(forKey: key)
        }
    }
}
