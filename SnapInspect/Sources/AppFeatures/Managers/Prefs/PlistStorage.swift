//
//  PlistStorage.swift
//  SnapInspect
//
//  Created by <PERSON> on 5/27/25.
//

import Foundation

/// A storage implementation that persists preferences to a property list file
public class PlistStorage: PrefsStorageProtocol {
    private let plistURL: URL
    private let queue = DispatchQueue(label: "com.snapinspect.pliststorage", attributes: .concurrent)
    private var cache: [String: Any] = [:]

    // MARK: - Initialization

    /// Initialize with a custom plist file URL
    /// - Parameter plistURL: The URL where the plist file should be stored
    public init(plistURL: URL) {
        self.plistURL = plistURL
        loadFromDisk()
    }

    /// Initialize with a filename in a specific directory
    /// - Parameter fileName: The name of the plist file (without extension)
    /// - Parameter directoryURL: The URL of the directory where the plist file should be stored
    public init(fileName: String, inDirectory directoryURL: URL) {
        // Ensure directory exists
        if !FileManager.default.fileExists(atPath: directoryURL.path) {
            do {
                try FileManager.default.createDirectory(at: directoryURL, withIntermediateDirectories: true, attributes: nil)
            } catch {
                print("PlistStorage: Failed to create directory \(directoryURL.path): \(error)")
            }
        }
        plistURL = directoryURL.appendingPathComponent("\(fileName).plist")
        loadFromDisk()
    }

    // MARK: - Private Methods

    private func loadFromDisk() {
        queue.async(flags: .barrier) {
            if FileManager.default.fileExists(atPath: self.plistURL.path) {
                do {
                    let data = try Data(contentsOf: self.plistURL)
                    if let plist = try PropertyListSerialization.propertyList(from: data, options: [], format: nil) as? [String: Any] {
                        self.cache = plist
                    }
                } catch {
                    print("PlistStorage: Failed to load plist from \(self.plistURL.path): \(error)")
                    self.cache = [:]
                }
            } else {
                self.cache = [:]
            }
        }
    }

    private func saveToDisk() {
        queue.async(flags: .barrier) {
            do {
                let data = try PropertyListSerialization.data(fromPropertyList: self.cache, format: .xml, options: 0)
                try data.write(to: self.plistURL)
            } catch {
                print("PlistStorage: Failed to save plist to \(self.plistURL.path): \(error)")
            }
        }
    }

    // MARK: - PrefsStorageProtocol Implementation

    public func getString(forKey key: String) -> String? {
        return queue.sync {
            cache[key] as? String
        }
    }

    public func setString(_ value: String?, forKey key: String) -> Bool {
        queue.async(flags: .barrier) {
            if let value = value {
                self.cache[key] = value
            } else {
                self.cache.removeValue(forKey: key)
            }
            self.synchronize()
        }
        return true
    }

    public func getBool(forKey key: String) -> Bool {
        return queue.sync {
            if let stringValue = cache[key] as? String {
                // Handle string representations of booleans for backward compatibility
                return stringValue.lowercased() == "true" || stringValue == "1" || stringValue.lowercased() == "yes"
            }
            return cache[key] as? Bool ?? false
        }
    }

    public func setBool(_ value: Bool, forKey key: String) -> Bool {
        queue.async(flags: .barrier) {
            self.cache[key] = value
            self.synchronize()
        }
        return true
    }

    public func getInt(forKey key: String) -> Int {
        return queue.sync {
            if let stringValue = cache[key] as? String {
                return Int(stringValue) ?? 0
            }
            return cache[key] as? Int ?? 0
        }
    }

    public func setInt(_ value: Int, forKey key: String) -> Bool {
        queue.async(flags: .barrier) {
            self.cache[key] = value
            self.synchronize()
        }
        return true
    }

    public func getDouble(forKey key: String) -> Double {
        return queue.sync {
            if let stringValue = cache[key] as? String {
                return Double(stringValue) ?? 0.0
            }
            return cache[key] as? Double ?? 0.0
        }
    }

    public func setDouble(_ value: Double, forKey key: String) -> Bool {
        queue.async(flags: .barrier) {
            self.cache[key] = value
            self.synchronize()
        }
        return true
    }

    public func remove(forKey key: String) {
        queue.async(flags: .barrier) {
            self.cache.removeValue(forKey: key)
            self.synchronize()
        }
    }

    public func synchronize() {
        saveToDisk()
    }

    public func clearAll() {
        queue.async(flags: .barrier) {
            self.cache.removeAll()
            self.synchronize()
        }
    }

    public func setValue<T: Codable>(_ value: T?, forKey key: String) -> Bool {
        guard let value = value else {
            remove(forKey: key)
            return true
        }

        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(value)
            let string = String(data: data, encoding: .utf8)
            return setString(string, forKey: key)
        } catch {
            print("PlistStorage: Failed to encode value for key \(key): \(error)")
            return false
        }
    }

    public func getValue<T: Codable>(forKey key: String) -> T? {
        guard let string = getString(forKey: key),
              let data = string.data(using: .utf8)
        else {
            return nil
        }

        do {
            let decoder = JSONDecoder()
            return try decoder.decode(T.self, from: data)
        } catch {
            print("PlistStorage: Failed to decode value for key \(key): \(error)")
            return nil
        }
    }

    // MARK: - Dictionary Access

    /// Retrieves all preferences as a dictionary.
    /// - Returns: A dictionary containing all key-value pairs.
    public func getAllPreferences() -> [String: Any] {
        return queue.sync { cache }
    }
}

// MARK: - Additional Utilities

public extension PlistStorage {
    /// Get the file path of the plist file
    var filePath: String {
        return plistURL.path
    }

    /// Check if the plist file exists on disk
    var fileExists: Bool {
        return FileManager.default.fileExists(atPath: plistURL.path)
    }

    /// Get the size of the plist file in bytes
    var fileSize: Int64 {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: plistURL.path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }

    /// Delete the plist file from disk
    func deletePlistFile() throws {
        try FileManager.default.removeItem(at: plistURL)
        queue.async(flags: .barrier) {
            self.cache.removeAll()
        }
    }
}
