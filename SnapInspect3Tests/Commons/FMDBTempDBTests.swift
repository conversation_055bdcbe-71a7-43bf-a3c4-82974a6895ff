//
//  FMDBTempDBTests.swift
//  SnapInspect3
//
//  Created by <PERSON> on 2024/7/10.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

import XCTest
@testable import SnapInspect3

protocol FMDBTempDBTestable {
    static func populateDatabase(_ db: FMDatabase)
}

class FMDBTempDBTests: XCTestCase {
    private static let testDatabasePath = "\(NSTemporaryDirectory())tmp.db"
    private static let populatedDatabasePath = "\(NSTemporaryDirectory())tmp-populated.db"
    
    override class func setUp() {
        super.setUp()
        
        // Delete old populated database
        let fileManager = FileManager.default
        try? fileManager.removeItem(atPath: populatedDatabasePath)
        
        if let test = self as? FMDBTempDBTestable.Type {
            let db = FMDatabase(path: populatedDatabasePath)
            
            db.open()
            test.populateDatabase(db)
            db.close()
        }
    }
    
    var db: FMDatabase!
    
    override func setUp() {
        super.setUp()
        
        // Delete the old database
        let fileManager = FileManager.default
        try? fileManager.removeItem(atPath: FMDBTempDBTests.testDatabasePath)
        
        if self is FMDBTempDBTestable {
            try? fileManager.copyItem(atPath: FMDBTempDBTests.populatedDatabasePath, toPath: FMDBTempDBTests.testDatabasePath)
        }
        
        db = FMDatabase(path: FMDBTempDBTests.testDatabasePath)
        
        XCTAssertTrue(db.open(), "Wasn't able to open database")
        db.shouldCacheStatements = true
        
        // Set the environment to use the test database
        environment.runSQL = { (block: (FMDatabase) -> Void) in
            block(self.db)
        }
    }
    
    override func tearDown() {
        super.tearDown()
        
        db.close()
    }
    
    var databasePath: String {
        return FMDBTempDBTests.testDatabasePath
    }
    
    func testDatabasePath() {
        XCTAssertEqual(databasePath, FMDBTempDBTests.testDatabasePath)
    }
}
