//
//  db_ProductTests.swift
//  SnapInspect3
//
//  Created by <PERSON> on 2024/7/10.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

@testable import SnapInspect3
import XCTest

class db_ProductTests: FMDBTempDBTests {
    private let productsJsonString = """
    {
      "success": true,
      "lsProduct": [
        {
          "iProductID": 1,
          "sCheckListID": null,
          "iCompanyID": 6349,
          "sSKU": "9987",
          "sName": "Door",
          "sModel": "Handlle Left",
          "sDesp": "No Desp",
          "sAssociateArea": null,
          "sAssociateItem": "[\\"Door\\",\\"Gate\\",\\"Window\\"]",
          "dUnitCost": 60,
          "bAllowEdit": true,
          "bOneOffCost": true,
          "sProductCategory": null,
          "sUnitName": "",
          "sURL": "https://www.google.com",
          "sImage": "",
          "sCustom1": null,
          "sCustom2": null,
          "bArchive": false,
          "bDeleted": false,
          "iCreatedBy": 12,
          "iUpdatedBy": 12,
          "dtUpdate": "2024-07-08 23:35:12",
          "dtDateTime": "2024-07-08 23:35:12"
        },
        {
          "iProductID": 2,
          "sCheckListID": null,
          "iCompanyID": 6349,
          "sSKU": "9986",
          "sName": "Door",
          "sModel": "Handle right",
          "sDesp": "no dep",
          "sAssociateArea": null,
          "sAssociateItem": "[\\"Door\\",\\"Gate\\"]",
          "dUnitCost": 61,
          "bAllowEdit": true,
          "bOneOffCost": false,
          "sProductCategory": null,
          "sUnitName": "",
          "sURL": "https://www.msn.com",
          "sImage": null,
          "sCustom1": null,
          "sCustom2": null,
          "bArchive": false,
          "bDeleted": false,
          "iCreatedBy": 12,
          "iUpdatedBy": 12,
          "dtUpdate": "2024-07-08 23:36:09",
          "dtDateTime": "2024-07-08 23:36:09"
        }
      ]
    }
    """

    private let products: [O_Product] = [
        O_Product(
            iProductID: 0,
            iSProductID: 1,
            sCheckListID: "",
            iCompanyID: 6349,
            sSKU: "9987",
            sName: "Door",
            sModel: "Handlle Left",
            sDesp: "No Desp",
            sAssociateArea: "",
            sAssociateItem: "[\"Door\",\"Gate\",\"Window\"]",
            dUnitCost: 60,
            bAllowEdit: true,
            bOneOffCost: true,
            sProductCategory: "",
            sUnitName: "",
            sURL: "https://www.google.com",
            sImage: "",
            sCustom1: "",
            sCustom2: "",
            bArchive: false,
            bDeleted: false,
            iCreatedBy: 12,
            iUpdatedBy: 12,
            dtUpdate: "2024-07-08 23:35:12".toDate(yyyyMMddHHmmss)?.date ?? .date1970,
            dtDateTime: "2024-07-08 23:35:12".toDate(yyyyMMddHHmmss)?.date ?? .date1970
        ),

        O_Product(
            iProductID: 0,
            iSProductID: 2,
            sCheckListID: "",
            iCompanyID: 6349,
            sSKU: "9986",
            sName: "Door",
            sModel: "Handle right",
            sDesp: "no dep",
            sAssociateArea: "",
            sAssociateItem: "[\"Door\",\"Gate\"]",
            dUnitCost: 61,
            bAllowEdit: true,
            bOneOffCost: false,
            sProductCategory: "",
            sUnitName: "",
            sURL: "https://www.msn.com",
            sImage: "",
            sCustom1: "",
            sCustom2: "",
            bArchive: false,
            bDeleted: false,
            iCreatedBy: 12,
            iUpdatedBy: 12,
            dtUpdate: "2024-07-08 23:36:09".toDate(yyyyMMddHHmmss)?.date ?? .date1970,
            dtDateTime: "2024-07-08 23:36:09".toDate(yyyyMMddHHmmss)?.date ?? .date1970
        )
    ]
    
    func testJsonToProducts() {
        guard let jsonData = productsJsonString.data(using: .utf8),
               let jsonObject = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
               let lsProduct = jsonObject["lsProduct"] as? [[String: Any]]
        else {
            XCTFail("Failed to parse JSON")
            return
        }
        
        XCTAssertEqual(products, lsProduct.map(O_Product.init(dictionary:)))
    }
    
    func testSaveProduct() {
        db_Product.save(product: products[0])
        let savedProducts = db_Product.getProducts()
        XCTAssertEqual(savedProducts.count, 1)
        
        var expectedProduct = products[0]
        expectedProduct.iProductID = savedProducts.first(where: { expectedProduct.iSProductID == $0.iSProductID })?.iProductID ?? 0
        XCTAssertEqual(expectedProduct, savedProducts.first)
    }

    func testSaveProducts() {
        db_Product.save(products: products)
        let savedProducts = db_Product.getProducts()
        let expectedProducts = products.map {
            var copy = $0
            copy.iProductID = savedProducts.first(where: { copy.iSProductID == $0.iSProductID })?.iProductID ?? 0
            return copy
        }
        XCTAssertEqual(expectedProducts, savedProducts)
    }
    
    func testUpdateProducts() {
        db_Product.save(products: products)
        let updatedProducts = products.map {
            var copy = $0
            copy.sName = "Updated \(copy.iSProductID)"
            return copy
        }
        
        db_Product.save(products: updatedProducts)
        let updatedSavedProducts = db_Product.getProducts()
        let expectedSavedProducts = updatedProducts.map {
            var copy = $0
            copy.iProductID = updatedSavedProducts.first(where: { copy.iSProductID == $0.iSProductID })?.iProductID ?? 0
            return copy
        }
        XCTAssertEqual(expectedSavedProducts, updatedSavedProducts)
    }
    
    func testGetProductsWithInsItemTitles() {
        db_Product.save(products: products)
        let allProducts = db_Product.getProducts()
        
        let productsWithTitles = db_Product.getProducts(withInsItemTitles: ["Door"], keyword: "")
        XCTAssertEqual(allProducts.filter { product in product.sAssociateItem.contains("\"Door\"") }, productsWithTitles)
        
        let productsWithTitleWindow = db_Product.getProducts(withInsItemTitles: ["Window"], keyword: "win")
        let expectedProductsWithTitleWindow = allProducts.filter { product in
            product.sAssociateItem.contains("\"Window\"") && (product.sName.contains("win") || product.sSKU.contains("win") || product.sModel.contains("win") || product.sDesp.contains("win"))
        }
        XCTAssertEqual(expectedProductsWithTitleWindow, productsWithTitleWindow)
        
        let productsWithTitleWindowOrDoor = db_Product.getProducts(withInsItemTitles: ["Window", "Door"], keyword: "win")
        let expectedProductsWithTitleWindowOrDoor = allProducts.filter { product in
            (product.sAssociateItem.contains("\"Window\"") || product.sAssociateItem.contains("\"Door\"")) && (product.sName.contains("win") || product.sSKU.contains("win") || product.sModel.contains("win") || product.sDesp.contains("win"))
        }
        XCTAssertEqual(expectedProductsWithTitleWindowOrDoor, productsWithTitleWindowOrDoor)
        
        let productsWithTitleNotHaving = db_Product.getProducts(withInsItemTitles: ["NotHaving"], keyword: "not")
        XCTAssertEqual([], productsWithTitleNotHaving)
    }

    func testGetProductsQuery_NoTitles_ReturnsSelectAllQuery() {
        let query = db_Product.getProductsQuery(withInsItemTitles: [], keyword: "")
        XCTAssertEqual(query, "SELECT * FROM ai_Product WHERE bDeleted = 0")
    }

    func testGetProductsQuery_WithTitles_ReturnsFilteredQuery() {
        let titles = ["Title1", "Title2"]
        let query = db_Product.getProductsQuery(withInsItemTitles: titles, keyword: "titl")
        XCTAssertEqual(query, """
        SELECT * FROM ai_Product WHERE bDeleted = 0 AND (sAssociateItem LIKE '%"Title1"%' OR sAssociateItem LIKE '%"Title2"%') AND (sName LIKE '%titl%' OR sSKU LIKE '%titl%' OR sModel LIKE '%titl%' OR sDesp LIKE '%titl%')
        """)
    }

    func testGetProductsQuery_SingleTitle_ReturnsFilteredQuery() {
        let titles = ["Title1"]
        let query = db_Product.getProductsQuery(withInsItemTitles: titles, keyword: "titl")
        XCTAssertEqual(query, "SELECT * FROM ai_Product WHERE bDeleted = 0 AND (sAssociateItem LIKE '%\"Title1\"%') AND (sName LIKE '%titl%' OR sSKU LIKE '%titl%' OR sModel LIKE '%titl%' OR sDesp LIKE '%titl%')")
    }
    
    func testGetProductWithID() {
        XCTAssertTrue(products.count > 0)
        db_Product.save(products: products)
        let product = db_Product.getProduct(withID: 1)
        XCTAssertNotNil(product)
        XCTAssertEqual(products[0].iSProductID, product?.iSProductID)
    }
}

extension db_ProductTests: FMDBTempDBTestable {
    static func populateDatabase(_ db: FMDatabase) {
        try? db.executeUpdate(sCreateTableProduct, values: nil)
    }
}
