//
//  db_ProductCostTests.swift
//  SnapInspect3
//
//  Created by <PERSON> on 2024/7/11.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

@testable import SnapInspect3
import XCTest

class db_ProductCostTests: FMDBTempDBTests {
    private let itemProductCost1 = O_ProductCost(dictionary: [
        "iCostingID": 1, "iSCostingID": 2, "iAssetID": 3, "iInspectionID": 4, "iInsItemID": 100,
        "iPInsItemID": 200, "iTaskID": 300, "iProductID": 301, "dUnit": 1, "dUnitCost": 10,
        "dTotalCost": 10, "sNotes": "Test 1", "sCustom1": "", "sCustom2": "", "sCustom3": "",
        "bDeleted": false, "iCreatedBy": 1, "iUpdatedBy": 1, "dtUpdate": "", "dtDateTime": ""
    ])

    private let itemProductCost2 = O_ProductCost(dictionary: [
        "iCostingID": 2, "iSCostingID": 2, "iAssetID": 3, "iInspectionID": 4, "iInsItemID": 100,
        "iPInsItemID": 200, "iTaskID": 300, "iProductID": 303, "dUnit": 2, "dUnitCost": 22,
        "dTotalCost": 44, "sNotes": "Test 2", "sCustom1": "", "sCustom2": "", "sCustom3": "",
        "bDeleted": false, "iCreatedBy": 1, "iUpdatedBy": 1, "dtUpdate": "", "dtDateTime": ""
    ])

    private let itemProductCost3 = O_ProductCost(dictionary: [
        "iCostingID": 3, "iSCostingID": 2, "iAssetID": 3, "iInspectionID": 4, "iInsItemID": 102,
        "iPInsItemID": 202, "iTaskID": 302, "iProductID": 303, "dUnit": 3, "dUnitCost": 33,
        "dTotalCost": 99, "sNotes": "Test 3", "sCustom1": "", "sCustom2": "", "sCustom3": "",
        "bDeleted": false, "iCreatedBy": 1, "iUpdatedBy": 1, "dtUpdate": "", "dtDateTime": ""
    ])

    func testSaveSingleProductCost() {
        db_ProductCost.save(productCost: itemProductCost1)
        XCTAssertEqual(db_ProductCost.getProductCosts(), [itemProductCost1])
    }

    func testSaveMultipleProductCosts() {
        let costs = [itemProductCost1, itemProductCost2, itemProductCost3]
        db_ProductCost.save(productCosts: costs)
        XCTAssertEqual(db_ProductCost.getProductCosts(), costs)
    }

    func testGetProductCostWithID() {
        db_ProductCost.save(productCosts: [itemProductCost1, itemProductCost2, itemProductCost3])

        // Test with non-existing ID
        let result = db_ProductCost.getProductCost(iCostingID: 191919)
        XCTAssertEqual(result, nil)

        // Test with existing ID
        let result1 = db_ProductCost.getProductCost(iCostingID: 1)
        XCTAssertEqual(result1, itemProductCost1)

        // Test with iPInsItemID
        let resultsWithPItemID200 = db_ProductCost.getProductCosts(iPInsItemID: 200)
        XCTAssertEqual(resultsWithPItemID200, [itemProductCost1, itemProductCost2])

        // Test with iProductID
        let resultsWithProductID301 = db_ProductCost.getProductCosts(iProductID: 303)
        XCTAssertEqual(resultsWithProductID301, [itemProductCost2, itemProductCost3])

        // Test with iInsItemID
        let resultsWithInsItemID100 = db_ProductCost.getProductCosts(iInsItemID: 100)
        XCTAssertEqual(resultsWithInsItemID100, [itemProductCost1, itemProductCost2])
    }

    func testUpdateProductCost() {
        db_ProductCost.save(productCost: itemProductCost1)
        var copiedItemProductCost1 = itemProductCost1
        copiedItemProductCost1.sNotes = "Updated Test 1"

        db_ProductCost.save(productCost: copiedItemProductCost1)
        XCTAssertEqual(db_ProductCost.getProductCost(iCostingID: 1), copiedItemProductCost1)
        XCTAssertEqual(db_ProductCost.getProductCosts(), [copiedItemProductCost1])
    }

    func testDeleteProductCost() {
        db_ProductCost.save(productCosts: [itemProductCost1, itemProductCost2])
        XCTAssertEqual(db_ProductCost.getProductCosts(), [itemProductCost1, itemProductCost2])
        db_ProductCost.delete(iCostingID: 1)
        XCTAssertEqual(db_ProductCost.getProductCosts(), [itemProductCost2])
    }

    func testGetTotalCost() {
        db_ProductCost.save(productCosts: [itemProductCost1, itemProductCost2, itemProductCost3])
        XCTAssertEqual(db_ProductCost.getTotalCost(iInsItemID: 100), 10 + 44)
        XCTAssertEqual(db_ProductCost.getTotalCost(iInsItemID: 102), 99)

        var copiedItemProductCost1 = itemProductCost1
        copiedItemProductCost1.updateUnit(10)
        db_ProductCost.save(productCost: copiedItemProductCost1)
        XCTAssertEqual(db_ProductCost.getTotalCost(iInsItemID: 100), 10 * 10 + 44)

        var copiedItemProductCost2 = itemProductCost2
        copiedItemProductCost2.updateUnitCost(100)
        copiedItemProductCost2.updateUnit(5)
        db_ProductCost.save(productCost: copiedItemProductCost2)

        XCTAssertEqual(db_ProductCost.getTotalCost(iInsItemID: 100), 10 * 10 + 100 * 5)
    }

    func testGetItemProductsJson() {
        db_ProductCost.save(productCosts: [itemProductCost1, itemProductCost2, itemProductCost3])
        let json = db_ProductCost.getItemProductsJson(iInsItemID: 100)
        let expectedJson = """
        [
          {
            "iCostingID": 0,
            "iSCostingID": 2,
            "iAssetID": 3,
            "iInspectionID": 4,
            "iInsItemID": 100,
            "iPInsItemID": 200,
            "iTaskID": 300,
            "iProductID": 301,
            "dUnit": 1,
            "dUnitCost": 10,
            "dTotalCost": 10,
            "sNotes": "Test 1",
            "sCustom1": "",
            "sCustom2": "",
            "sCustom3": "",
            "bDeleted": 0,
            "iCreatedBy": 1,
            "iUpdatedBy": 1,
            "dtUpdate": "",
            "dtDateTime": ""
          },
          {
            "iCostingID": 0,
            "iSCostingID": 2,
            "iAssetID": 3,
            "iInspectionID": 4,
            "iInsItemID": 100,
            "iPInsItemID": 200,
            "iTaskID": 300,
            "iProductID": 303,
            "dUnit": 2,
            "dUnitCost": 22,
            "dTotalCost": 44,
            "sNotes": "Test 2",
            "sCustom1": "",
            "sCustom2": "",
            "sCustom3": "",
            "bDeleted": 0,
            "iCreatedBy": 1,
            "iUpdatedBy": 1,
            "dtUpdate": "",
            "dtDateTime": ""
          }
        ]
        """

        let itemProducts = CommonJson.oJsonString(toArray: json) as? [[String: Any]]
        let expectedItemProducts = CommonJson.oJsonString(toArray: expectedJson) as? [[String: Any]]

        XCTAssertEqual(
            itemProducts?.map(O_ProductCost.init(dictionary:)),
            expectedItemProducts?.map(O_ProductCost.init(dictionary:))
        )
    }

    func testWriteItemProductsXml() {
        // Setup
        db_ProductCost.save(productCosts: [itemProductCost1, itemProductCost2, itemProductCost3])
        let xmlWriter = XMLWriter()
        xmlWriter.writeStartDocument()
        
        // Test with existing item ID
        db_ProductCost.writeItemProductsXml(xmlWriter: xmlWriter, iInsItemID: 100)
        
        // Verify XML contains the correct costs
        let xml: String = (xmlWriter.toString() ?? "") as String
        XCTAssertTrue(xml.contains("<Costs>"))
        XCTAssertTrue(xml.contains("<Cost iProductID=\"301\" dUnit=\"\(String(itemProductCost1.dUnit))\" dUnitCost=\"\(environment.decimalFormat(itemProductCost1.dUnitCost))\" dTotalCost=\"\(environment.decimalFormat(itemProductCost1.dTotalCost))\">"))
        XCTAssertTrue(xml.contains("<Cost iProductID=\"303\" dUnit=\"\(String(itemProductCost2.dUnit))\" dUnitCost=\"\(environment.decimalFormat(itemProductCost2.dUnitCost))\" dTotalCost=\"\(environment.decimalFormat(itemProductCost2.dTotalCost))\">"))
        XCTAssertTrue(xml.contains("<sNotes><![CDATA[Test 1]]></sNotes>"))
        XCTAssertTrue(xml.contains("<sNotes><![CDATA[Test 2]]></sNotes>"))
        // Should not include item with different iInsItemID
        XCTAssertFalse(xml.contains("<sNotes><![CDATA[Test 3]]></sNotes>"))
        XCTAssertTrue(xml.contains("</Costs>"))
        
        // Test with non-existing item ID
        let emptyXmlWriter = XMLWriter()
        emptyXmlWriter.writeStartDocument()
        db_ProductCost.writeItemProductsXml(xmlWriter: emptyXmlWriter, iInsItemID: 999)
        
        // Verify no costs element is written when no matching costs
        let emptyXml: String = (emptyXmlWriter.toString() ?? "") as String
        XCTAssertFalse(emptyXml.contains("<Costs>"))
    }
}

extension db_ProductCostTests: FMDBTempDBTestable {
    static func populateDatabase(_ db: FMDatabase) {
        try? db.executeUpdate(sCreateTableProductCost, values: nil)
    }
}
