//
//  db_TasksTests.swift
//  SnapInspect3
//
//  Created by <PERSON> on 10/14/24.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

@testable import SnapInspect3
import XCTest

class db_TasksTests: FMDBTempDBTests {

    func testUpdateLastSync() {
        // Given
        let assetID = 1
        let lastSync = "2024-03-15 10:00:00"

        // When
        db_Tasks.updateLastSync(withAssetID: assetID, dtLastSync: lastSync)

        // Then
        let updatedTask = db_Tasks.updateAssetTask(withAssetID: assetID)
        XCTAssertEqual(updatedTask?.dtLastSync, lastSync)
    }

    func testSaveTask() {
        // Given
        var task: O_Task = .dummy
        XCTAssert(task.iNotificationID == 0)

        // When
        db_Tasks.save(task: task)

        // Then
        let savedTask = db_Tasks.getTask(withSTaskID: task.iSNotificationID)
        XCTAssertNotNil(savedTask)
        task.iNotificationID = savedTask?.iNotificationID ?? 0
        XCTAssertEqual(savedTask, task)
    }

}

extension db_TasksTests: FMDBTempDBTestable {
    static func populateDatabase(_ db: FMDatabase) {
        try? db.executeUpdate(sCreateTableUpdateAssetTask, values: nil)
        try? db.executeUpdate(sCreateTableTask, values: nil)
    }
}
